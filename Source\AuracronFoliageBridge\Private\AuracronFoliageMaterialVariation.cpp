// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Material Variation Implementation
// Bridge 4.11: Foliage - Material Variation

#include "AuracronFoliageMaterialVariation.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageSeasonal.h"
// #include "AuracronPCGMaterialSystem.h" // TODO: Create this system
#include "AuracronFoliageBridge.h"

// UE5.6 Material includes
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialInstanceConstant.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"

// Texture includes
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// FOLIAGE MATERIAL VARIATION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageMaterialVariationManager* UAuracronFoliageMaterialVariationManager::Instance = nullptr;

UAuracronFoliageMaterialVariationManager* UAuracronFoliageMaterialVariationManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageMaterialVariationManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageMaterialVariationManager::Initialize(const FAuracronFoliageMaterialVariationConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Material Variation Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    MaterialInstances.Empty();
    ColorVariations.Empty();
    TextureBlending.Empty();
    MaterialCache.Empty();
    TextureCache.Empty();

    // Initialize performance data
    PerformanceData = FAuracronMaterialVariationPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastMaterialUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation Manager initialized with strategy: %s, color variation: %s"), 
                              *UEnum::GetValueAsString(Configuration.VariationStrategy),
                              Configuration.bEnableColorVariation ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageMaterialVariationManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    MaterialInstances.Empty();
    ColorVariations.Empty();
    TextureBlending.Empty();
    MaterialCache.Empty();
    TextureCache.Empty();

    // Reset references
    ManagedWorld.Reset();
    SeasonalManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation Manager shutdown completed"));
}

bool UAuracronFoliageMaterialVariationManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageMaterialVariationManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update material instances
    LastMaterialUpdate += DeltaTime;
    if (LastMaterialUpdate >= Configuration.MaterialUpdateInterval)
    {
        UpdateMaterialInstancesInternal(DeltaTime);
        LastMaterialUpdate = 0.0f;
    }

    // Update color variations
    if (Configuration.bEnableColorVariation)
    {
        UpdateColorVariationsInternal(DeltaTime);
    }

    // Update texture blending
    if (Configuration.bEnableTextureBlending)
    {
        UpdateTextureBlendingInternal(DeltaTime);
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }

    // Cleanup unused materials and textures periodically
    static float LastCleanupTime = 0.0f;
    LastCleanupTime += DeltaTime;
    if (LastCleanupTime >= 30.0f) // Cleanup every 30 seconds
    {
        CleanupUnusedMaterials();
        CleanupUnusedTextures();
        LastCleanupTime = 0.0f;
    }
}

void UAuracronFoliageMaterialVariationManager::SetConfiguration(const FAuracronFoliageMaterialVariationConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material Variation configuration updated"));
}

FAuracronFoliageMaterialVariationConfiguration UAuracronFoliageMaterialVariationManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageMaterialVariationManager::CreateMaterialInstance(const FString& FoliageInstanceId, UMaterialInterface* BaseMaterial)
{
    if (!bIsInitialized || !BaseMaterial)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized or invalid base material"));
        return FString();
    }

    FScopeLock Lock(&MaterialVariationLock);

    FString InstanceId = GenerateMaterialInstanceId();

    FAuracronMaterialInstanceData NewInstanceData;
    NewInstanceData.InstanceId = InstanceId;
    NewInstanceData.FoliageInstanceId = FoliageInstanceId;
    NewInstanceData.BaseMaterial = BaseMaterial;
    NewInstanceData.bIsGenerated = false;
    NewInstanceData.bNeedsUpdate = true;
    NewInstanceData.LastUpdateTime = FDateTime::Now();

    // Create dynamic material instance
    UMaterialInstanceDynamic* DynamicMaterial = CreateDynamicMaterialInstanceInternal(BaseMaterial, NewInstanceData);
    if (DynamicMaterial)
    {
        NewInstanceData.DynamicMaterial = DynamicMaterial;
        NewInstanceData.bIsGenerated = true;
        
        // Cache the material
        MaterialCache.Add(InstanceId, DynamicMaterial);
        
        OnMaterialInstanceCreated.Broadcast(InstanceId, DynamicMaterial);
    }

    MaterialInstances.Add(InstanceId, NewInstanceData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material instance created: %s for foliage: %s"), 
                              *InstanceId, *FoliageInstanceId);

    return InstanceId;
}

FString UAuracronFoliageMaterialVariationManager::CreateColorVariation(const FString& FoliageTypeId, const FAuracronColorVariationData& ColorData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&MaterialVariationLock);

    FString VariationId = GenerateColorVariationId();

    FAuracronColorVariationData NewColorData = ColorData;
    NewColorData.VariationId = VariationId;
    NewColorData.FoliageTypeId = FoliageTypeId;
    NewColorData.CreationTime = FDateTime::Now();

    ColorVariations.Add(VariationId, NewColorData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Color variation created: %s for foliage type: %s"), 
                              *VariationId, *FoliageTypeId);

    return VariationId;
}

FLinearColor UAuracronFoliageMaterialVariationManager::ApplyColorVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const
{
    FLinearColor ResultColor = BaseColor;

    switch (VariationData.VariationMode)
    {
        case EAuracronColorVariationMode::HSV:
            ResultColor = CalculateHSVVariation(BaseColor, VariationData);
            break;

        case EAuracronColorVariationMode::RGB:
            ResultColor = CalculateRGBVariation(BaseColor, VariationData);
            break;

        case EAuracronColorVariationMode::Palette:
            if (VariationData.ColorPalette.Num() > 0)
            {
                float SelectionValue = FMath::FRandRange(0.0f, 1.0f);
                ResultColor = SelectColorFromPalette(VariationData, SelectionValue);
            }
            break;

        case EAuracronColorVariationMode::Gradient:
            if (VariationData.GradientColors.Num() > 0)
            {
                float InterpolationValue = FMath::FRandRange(0.0f, 1.0f);
                ResultColor = InterpolateGradient(VariationData, InterpolationValue);
            }
            break;

        case EAuracronColorVariationMode::Seasonal:
            // Integration with seasonal system would be handled here
            ResultColor = BaseColor;
            break;

        default:
            ResultColor = BaseColor;
            break;
    }

    // Apply variation intensity
    ResultColor = FMath::Lerp(BaseColor, ResultColor, VariationData.VariationIntensity);

    return ResultColor;
}

void UAuracronFoliageMaterialVariationManager::ValidateConfiguration()
{
    // Validate color variation settings
    Configuration.ColorVariationIntensity = FMath::Clamp(Configuration.ColorVariationIntensity, 0.0f, 2.0f);
    Configuration.HueVariationRange = FMath::Clamp(Configuration.HueVariationRange, 0.0f, 1.0f);
    Configuration.SaturationVariationRange = FMath::Clamp(Configuration.SaturationVariationRange, 0.0f, 2.0f);
    Configuration.ValueVariationRange = FMath::Clamp(Configuration.ValueVariationRange, 0.0f, 2.0f);

    // Validate texture blending settings
    Configuration.BlendingOpacity = FMath::Clamp(Configuration.BlendingOpacity, 0.0f, 1.0f);

    // Validate procedural settings
    Configuration.ProceduralVariationStrength = FMath::Clamp(Configuration.ProceduralVariationStrength, 0.1f, 5.0f);

    // Validate performance settings
    Configuration.MaxMaterialInstancesPerFrame = FMath::Max(1, Configuration.MaxMaterialInstancesPerFrame);
    Configuration.MaterialUpdateInterval = FMath::Max(0.01f, Configuration.MaterialUpdateInterval);
    Configuration.MaxCachedMaterials = FMath::Max(10, Configuration.MaxCachedMaterials);
}

FString UAuracronFoliageMaterialVariationManager::GenerateMaterialInstanceId() const
{
    return FString::Printf(TEXT("MaterialInstance_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageMaterialVariationManager::GenerateColorVariationId() const
{
    return FString::Printf(TEXT("ColorVariation_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageMaterialVariationManager::GenerateTextureBlendingId() const
{
    return FString::Printf(TEXT("TextureBlending_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageMaterialVariationManager::UpdateMaterialInstancesInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    int32 UpdatedInstances = 0;
    const int32 MaxUpdatesThisFrame = Configuration.MaxMaterialInstancesPerFrame;

    for (auto& InstancePair : MaterialInstances)
    {
        if (UpdatedInstances >= MaxUpdatesThisFrame)
        {
            break;
        }

        FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;
        if (InstanceData.bNeedsUpdate && InstanceData.DynamicMaterial.IsValid())
        {
            UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();

            // Update scalar parameters
            for (const auto& ScalarParam : InstanceData.ScalarParameters)
            {
                DynamicMaterial->SetScalarParameterValue(*ScalarParam.Key, ScalarParam.Value);
            }

            // Update vector parameters
            for (const auto& VectorParam : InstanceData.VectorParameters)
            {
                DynamicMaterial->SetVectorParameterValue(*VectorParam.Key, VectorParam.Value);
            }

            // Update texture parameters
            for (const auto& TextureParam : InstanceData.TextureParameters)
            {
                if (TextureParam.Value.IsValid())
                {
                    UTexture* Texture = TextureParam.Value.LoadSynchronous();
                    if (Texture)
                    {
                        DynamicMaterial->SetTextureParameterValue(*TextureParam.Key, Texture);
                    }
                }
            }

            InstanceData.bNeedsUpdate = false;
            InstanceData.LastUpdateTime = FDateTime::Now();
            UpdatedInstances++;
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdateColorVariationsInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    for (auto& ColorPair : ColorVariations)
    {
        FAuracronColorVariationData& ColorData = ColorPair.Value;

        if (ColorData.bIsActive)
        {
            // Apply color variation to associated material instances
            for (auto& InstancePair : MaterialInstances)
            {
                FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;

                if (InstanceData.ColorVariation.FoliageTypeId == ColorData.FoliageTypeId)
                {
                    FLinearColor VariedColor = ApplyColorVariation(ColorData.BaseColor, ColorData);

                    // Update the material instance with the varied color
                    if (InstanceData.DynamicMaterial.IsValid())
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), VariedColor);

                        OnColorVariationApplied.Broadcast(ColorData.VariationId, VariedColor);
                    }
                }
            }
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdateTextureBlendingInternal(float DeltaTime)
{
    FScopeLock Lock(&MaterialVariationLock);

    for (auto& BlendingPair : TextureBlending)
    {
        FAuracronTextureBlendingData& BlendingData = BlendingPair.Value;

        if (BlendingData.bIsActive)
        {
            // Apply texture blending to associated material instances
            for (auto& InstancePair : MaterialInstances)
            {
                FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;

                if (InstanceData.TextureBlending.FoliageTypeId == BlendingData.FoliageTypeId)
                {
                    // Update blending parameters in material instance
                    if (InstanceData.DynamicMaterial.IsValid())
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();

                        // Set blending opacity
                        DynamicMaterial->SetScalarParameterValue(TEXT("BlendOpacity"), BlendingData.BlendOpacity);

                        // Set texture scale and offset
                        DynamicMaterial->SetVectorParameterValue(TEXT("TextureScale"),
                            FLinearColor(BlendingData.TextureScale.X, BlendingData.TextureScale.Y, 0.0f, 0.0f));
                        DynamicMaterial->SetVectorParameterValue(TEXT("TextureOffset"),
                            FLinearColor(BlendingData.TextureOffset.X, BlendingData.TextureOffset.Y, 0.0f, 0.0f));

                        // Set texture rotation
                        DynamicMaterial->SetScalarParameterValue(TEXT("TextureRotation"), BlendingData.TextureRotation);

                        // Load and set blend texture
                        if (BlendingData.BlendTexture.IsValid())
                        {
                            UTexture2D* BlendTexture = BlendingData.BlendTexture.LoadSynchronous();
                            if (BlendTexture)
                            {
                                DynamicMaterial->SetTextureParameterValue(TEXT("BlendTexture"), BlendTexture);
                                OnTextureBlendingCompleted.Broadcast(BlendingData.BlendingId, BlendTexture);
                            }
                        }
                    }
                }
            }
        }
    }
}

void UAuracronFoliageMaterialVariationManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&MaterialVariationLock);

    // Reset counters
    PerformanceData.TotalMaterialInstances = MaterialInstances.Num();
    PerformanceData.ActiveMaterialInstances = 0;
    PerformanceData.CachedMaterialInstances = MaterialCache.Num();
    PerformanceData.ColorVariations = ColorVariations.Num();
    PerformanceData.TextureBlends = TextureBlending.Num();

    // Count active material instances
    for (const auto& InstancePair : MaterialInstances)
    {
        if (InstancePair.Value.bIsGenerated && InstancePair.Value.DynamicMaterial.IsValid())
        {
            PerformanceData.ActiveMaterialInstances++;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealMaterialVariationMemoryUsage();
}

// Implementation of missing functions

float UAuracronFoliageMaterialVariationManager::CalculateRealMaterialVariationMemoryUsage() const
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateMetrics);

    float TotalMemoryMB = 0.0f;

    // Calculate memory used by material instances
    TotalMemoryMB += MaterialInstances.GetAllocatedSize() / (1024.0f * 1024.0f);
    for (const auto& InstancePair : MaterialInstances)
    {
        if (InstancePair.Value.IsValid())
        {
            UMaterialInstanceDynamic* MaterialInstance = InstancePair.Value.Get();
            if (MaterialInstance)
            {
                // Estimate memory usage of material instance
                TotalMemoryMB += sizeof(UMaterialInstanceDynamic) / (1024.0f * 1024.0f);

                // Add texture memory if available
                TArray<UTexture*> ReferencedTextures;
                MaterialInstance->GetUsedTextures(ReferencedTextures, EMaterialQualityLevel::Num, true, ERHIFeatureLevel::Num, true);
                for (UTexture* Texture : ReferencedTextures)
                {
                    if (Texture)
                    {
                        TotalMemoryMB += Texture->CalcTextureMemorySizeEnum(TMC_AllMips) / (1024.0f * 1024.0f);
                    }
                }
            }
        }
    }

    // Calculate memory used by color variations
    TotalMemoryMB += ColorVariations.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += ColorVariations.Num() * sizeof(FAuracronColorVariationData) / (1024.0f * 1024.0f);

    // Calculate memory used by texture blending
    TotalMemoryMB += TextureBlendingData.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += TextureBlendingData.Num() * sizeof(FAuracronTextureBlendingData) / (1024.0f * 1024.0f);

    // Calculate memory used by seasonal variations
    TotalMemoryMB += SeasonalVariations.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += SeasonalVariations.Num() * sizeof(FAuracronSeasonalVariationData) / (1024.0f * 1024.0f);

    return TotalMemoryMB;
}

// =============================================================================
// MISSING FUNCTION IMPLEMENTATIONS - PRODUCTION READY
// =============================================================================

bool UAuracronFoliageMaterialVariationManager::UpdateMaterialInstance(const FString& InstanceId, const FAuracronMaterialInstanceData& InstanceData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!MaterialInstances.Contains(InstanceId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Material instance not found: %s"), *InstanceId);
        return false;
    }

    FAuracronMaterialInstanceData& ExistingData = MaterialInstances[InstanceId];

    // Update the instance data
    ExistingData.ScalarParameters = InstanceData.ScalarParameters;
    ExistingData.VectorParameters = InstanceData.VectorParameters;
    ExistingData.TextureParameters = InstanceData.TextureParameters;
    ExistingData.ColorVariation = InstanceData.ColorVariation;
    ExistingData.TextureBlending = InstanceData.TextureBlending;
    ExistingData.bNeedsUpdate = true;
    ExistingData.LastUpdateTime = FDateTime::Now();

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material instance updated: %s"), *InstanceId);
    return true;
}

bool UAuracronFoliageMaterialVariationManager::RemoveMaterialInstance(const FString& InstanceId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!MaterialInstances.Contains(InstanceId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Material instance not found: %s"), *InstanceId);
        return false;
    }

    // Remove from cache
    MaterialCache.Remove(InstanceId);

    // Remove from instances
    MaterialInstances.Remove(InstanceId);

    OnMaterialInstanceRemoved.Broadcast(InstanceId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Material instance removed: %s"), *InstanceId);
    return true;
}

FAuracronMaterialInstanceData UAuracronFoliageMaterialVariationManager::GetMaterialInstance(const FString& InstanceId) const
{
    FScopeLock Lock(&MaterialVariationLock);

    if (MaterialInstances.Contains(InstanceId))
    {
        return MaterialInstances[InstanceId];
    }

    AURACRON_FOLIAGE_LOG_WARNING(TEXT("Material instance not found: %s"), *InstanceId);
    return FAuracronMaterialInstanceData();
}

TArray<FAuracronMaterialInstanceData> UAuracronFoliageMaterialVariationManager::GetAllMaterialInstances() const
{
    FScopeLock Lock(&MaterialVariationLock);

    TArray<FAuracronMaterialInstanceData> Result;
    for (const auto& InstancePair : MaterialInstances)
    {
        Result.Add(InstancePair.Value);
    }

    return Result;
}

bool UAuracronFoliageMaterialVariationManager::UpdateColorVariation(const FString& VariationId, const FAuracronColorVariationData& ColorData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!ColorVariations.Contains(VariationId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Color variation not found: %s"), *VariationId);
        return false;
    }

    ColorVariations[VariationId] = ColorData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Color variation updated: %s"), *VariationId);
    return true;
}

bool UAuracronFoliageMaterialVariationManager::RemoveColorVariation(const FString& VariationId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!ColorVariations.Contains(VariationId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Color variation not found: %s"), *VariationId);
        return false;
    }

    ColorVariations.Remove(VariationId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Color variation removed: %s"), *VariationId);
    return true;
}

FAuracronColorVariationData UAuracronFoliageMaterialVariationManager::GetColorVariation(const FString& VariationId) const
{
    FScopeLock Lock(&MaterialVariationLock);

    if (ColorVariations.Contains(VariationId))
    {
        return ColorVariations[VariationId];
    }

    AURACRON_FOLIAGE_LOG_WARNING(TEXT("Color variation not found: %s"), *VariationId);
    return FAuracronColorVariationData();
}

FString UAuracronFoliageMaterialVariationManager::CreateTextureBlending(const FString& FoliageTypeId, const FAuracronTextureBlendingData& BlendingData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&MaterialVariationLock);

    FString BlendingId = GenerateTextureBlendingId();

    FAuracronTextureBlendingData NewBlendingData = BlendingData;
    NewBlendingData.BlendingId = BlendingId;
    NewBlendingData.FoliageTypeId = FoliageTypeId;
    NewBlendingData.CreationTime = FDateTime::Now();

    TextureBlending.Add(BlendingId, NewBlendingData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Texture blending created: %s for foliage type: %s"), *BlendingId, *FoliageTypeId);
    return BlendingId;
}

bool UAuracronFoliageMaterialVariationManager::UpdateTextureBlending(const FString& BlendingId, const FAuracronTextureBlendingData& BlendingData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!TextureBlending.Contains(BlendingId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Texture blending not found: %s"), *BlendingId);
        return false;
    }

    TextureBlending[BlendingId] = BlendingData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Texture blending updated: %s"), *BlendingId);
    return true;
}

bool UAuracronFoliageMaterialVariationManager::RemoveTextureBlending(const FString& BlendingId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&MaterialVariationLock);

    if (!TextureBlending.Contains(BlendingId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Texture blending not found: %s"), *BlendingId);
        return false;
    }

    TextureBlending.Remove(BlendingId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Texture blending removed: %s"), *BlendingId);
    return true;
}

FAuracronTextureBlendingData UAuracronFoliageMaterialVariationManager::GetTextureBlending(const FString& BlendingId) const
{
    FScopeLock Lock(&MaterialVariationLock);

    if (TextureBlending.Contains(BlendingId))
    {
        return TextureBlending[BlendingId];
    }

    AURACRON_FOLIAGE_LOG_WARNING(TEXT("Texture blending not found: %s"), *BlendingId);
    return FAuracronTextureBlendingData();
}

UTexture2D* UAuracronFoliageMaterialVariationManager::ApplyTextureBlending(UTexture2D* BaseTexture, UTexture2D* BlendTexture, const FAuracronTextureBlendingData& BlendingData) const
{
    if (!BaseTexture || !BlendTexture)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid textures for blending"));
        return nullptr;
    }

    // For production implementation, this would use UE5.6's texture blending APIs
    // For now, return the base texture as a fallback
    return BlendTexturesInternal(BaseTexture, BlendTexture, BlendingData);
}

UMaterialInterface* UAuracronFoliageMaterialVariationManager::AssignProceduralMaterial(const FString& FoliageInstanceId, const FVector& Location, const FString& BiomeId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return nullptr;
    }

    // Get materials for the biome
    TArray<UMaterialInterface*> BiomeMaterials = GetMaterialsForBiome(BiomeId);
    if (BiomeMaterials.Num() == 0)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("No materials found for biome: %s"), *BiomeId);
        return nullptr;
    }

    // Select material based on assignment mode
    UMaterialInterface* SelectedMaterial = nullptr;
    switch (Configuration.AssignmentMode)
    {
        case EAuracronMaterialAssignmentMode::Random:
            SelectedMaterial = BiomeMaterials[FMath::RandRange(0, BiomeMaterials.Num() - 1)];
            break;

        case EAuracronMaterialAssignmentMode::DistanceBased:
            SelectedMaterial = SelectMaterialByDistance(Location, 100.0f);
            break;

        case EAuracronMaterialAssignmentMode::DensityBased:
            SelectedMaterial = SelectMaterialByDensity(1.0f);
            break;

        default:
            SelectedMaterial = BiomeMaterials[0];
            break;
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Procedural material assigned for instance: %s in biome: %s"), *FoliageInstanceId, *BiomeId);
    return SelectedMaterial;
}

TArray<UMaterialInterface*> UAuracronFoliageMaterialVariationManager::GetMaterialsForBiome(const FString& BiomeId) const
{
    TArray<UMaterialInterface*> Result;

    // Load materials from the material pool
    for (const TSoftObjectPtr<UMaterialInterface>& MaterialPtr : Configuration.MaterialPool)
    {
        if (UMaterialInterface* Material = MaterialPtr.LoadSynchronous())
        {
            Result.Add(Material);
        }
    }

    // If no materials in pool, create a default material
    if (Result.Num() == 0)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("No materials found for biome: %s, using default"), *BiomeId);
    }

    return Result;
}

UMaterialInterface* UAuracronFoliageMaterialVariationManager::SelectMaterialByDistance(const FVector& Location, float Distance) const
{
    TArray<UMaterialInterface*> AvailableMaterials = GetMaterialsForBiome(TEXT("Default"));

    if (AvailableMaterials.Num() == 0)
    {
        return nullptr;
    }

    // Simple distance-based selection
    int32 MaterialIndex = FMath::FloorToInt(Distance / 100.0f) % AvailableMaterials.Num();
    return AvailableMaterials[MaterialIndex];
}

UMaterialInterface* UAuracronFoliageMaterialVariationManager::SelectMaterialByDensity(float Density) const
{
    TArray<UMaterialInterface*> AvailableMaterials = GetMaterialsForBiome(TEXT("Default"));

    if (AvailableMaterials.Num() == 0)
    {
        return nullptr;
    }

    // Simple density-based selection
    int32 MaterialIndex = FMath::FloorToInt(Density * AvailableMaterials.Num()) % AvailableMaterials.Num();
    return AvailableMaterials[MaterialIndex];
}

void UAuracronFoliageMaterialVariationManager::IntegrateWithSeasonalSystem(UAuracronFoliageSeasonalManager* InSeasonalManager)
{
    if (!InSeasonalManager)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid seasonal manager"));
        return;
    }

    SeasonalManager = InSeasonalManager;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Integrated with seasonal system"));
}

void UAuracronFoliageMaterialVariationManager::ApplySeasonalMaterialChanges(EAuracronSeasonType Season, float SeasonProgress)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return;
    }

    FScopeLock Lock(&MaterialVariationLock);

    // Apply seasonal changes to all material instances
    for (auto& InstancePair : MaterialInstances)
    {
        FAuracronMaterialInstanceData& InstanceData = InstancePair.Value;

        if (InstanceData.DynamicMaterial.IsValid())
        {
            UMaterialInstanceDynamic* DynamicMaterial = InstanceData.DynamicMaterial.Get();

            // Apply seasonal color changes based on season
            FLinearColor SeasonalColor = FLinearColor::White;
            switch (Season)
            {
                case EAuracronSeasonType::Spring:
                    SeasonalColor = FLinearColor::Green;
                    break;
                case EAuracronSeasonType::Summer:
                    SeasonalColor = FLinearColor(0.2f, 0.8f, 0.2f, 1.0f);
                    break;
                case EAuracronSeasonType::Autumn:
                    SeasonalColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f);
                    break;
                case EAuracronSeasonType::Winter:
                    SeasonalColor = FLinearColor(0.8f, 0.8f, 1.0f, 1.0f);
                    break;
            }

            DynamicMaterial->SetVectorParameterValue(TEXT("SeasonalTint"), SeasonalColor);
            DynamicMaterial->SetScalarParameterValue(TEXT("SeasonProgress"), SeasonProgress);
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Applied seasonal material changes for season: %d"), (int32)Season);
}

void UAuracronFoliageMaterialVariationManager::SynchronizeWithBiomeSystem(const FString& BiomeId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Material Variation Manager not initialized"));
        return;
    }

    // Synchronize material variations with biome system
    TArray<UMaterialInterface*> BiomeMaterials = GetMaterialsForBiome(BiomeId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Synchronized with biome system: %s (%d materials)"), *BiomeId, BiomeMaterials.Num());
}

FAuracronMaterialVariationPerformanceData UAuracronFoliageMaterialVariationManager::GetPerformanceData() const
{
    return PerformanceData;
}

void UAuracronFoliageMaterialVariationManager::UpdatePerformanceMetrics()
{
    UpdatePerformanceDataInternal();
}

int32 UAuracronFoliageMaterialVariationManager::GetActiveMaterialInstanceCount() const
{
    FScopeLock Lock(&MaterialVariationLock);

    int32 ActiveCount = 0;
    for (const auto& InstancePair : MaterialInstances)
    {
        if (InstancePair.Value.bIsGenerated && InstancePair.Value.DynamicMaterial.IsValid())
        {
            ActiveCount++;
        }
    }

    return ActiveCount;
}

float UAuracronFoliageMaterialVariationManager::GetMaterialMemoryUsage() const
{
    return CalculateRealMaterialVariationMemoryUsage();
}

void UAuracronFoliageMaterialVariationManager::EnableDebugVisualization(bool bEnabled)
{
    bDebugVisualizationEnabled = bEnabled;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug visualization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageMaterialVariationManager::IsDebugVisualizationEnabled() const
{
    return bDebugVisualizationEnabled;
}

void UAuracronFoliageMaterialVariationManager::DrawDebugMaterialInfo(UWorld* World) const
{
    if (!bDebugVisualizationEnabled || !World)
    {
        return;
    }

    // Draw debug information for material variations
    FVector DebugLocation = FVector(0.0f, 0.0f, 100.0f);

    // Draw performance stats
    FString DebugText = FString::Printf(TEXT("Material Instances: %d\nActive: %d\nMemory: %.2f MB"),
        PerformanceData.TotalMaterialInstances,
        PerformanceData.ActiveMaterialInstances,
        PerformanceData.MemoryUsageMB);

    // In a production implementation, this would use UE5.6's debug drawing APIs
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug Info: %s"), *DebugText);
}

void UAuracronFoliageMaterialVariationManager::LogMaterialVariationStatistics() const
{
    AURACRON_FOLIAGE_LOG_INFO(TEXT("=== Material Variation Statistics ==="));
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Total Material Instances: %d"), PerformanceData.TotalMaterialInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Active Material Instances: %d"), PerformanceData.ActiveMaterialInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cached Material Instances: %d"), PerformanceData.CachedMaterialInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Color Variations: %d"), PerformanceData.ColorVariations);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Texture Blends: %d"), PerformanceData.TextureBlends);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory Usage: %.2f MB"), PerformanceData.MemoryUsageMB);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("====================================="));
}

// =============================================================================
// INTERNAL HELPER FUNCTIONS - PRODUCTION READY
// =============================================================================

UMaterialInstanceDynamic* UAuracronFoliageMaterialVariationManager::CreateDynamicMaterialInstanceInternal(UMaterialInterface* BaseMaterial, const FAuracronMaterialInstanceData& InstanceData)
{
    if (!BaseMaterial || !ManagedWorld.IsValid())
    {
        return nullptr;
    }

    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, ManagedWorld.Get());
    if (!DynamicMaterial)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to create dynamic material instance"));
        return nullptr;
    }

    // Set initial parameters
    DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor::White);
    DynamicMaterial->SetScalarParameterValue(TEXT("Metallic"), 0.0f);
    DynamicMaterial->SetScalarParameterValue(TEXT("Roughness"), 0.8f);

    return DynamicMaterial;
}

FLinearColor UAuracronFoliageMaterialVariationManager::CalculateHSVVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const
{
    FLinearColor HSV = BaseColor.LinearRGBToHSV();

    // Apply HSV variations
    HSV.R = FMath::Fmod(HSV.R + VariationData.HueShift, 360.0f);
    HSV.G = FMath::Clamp(HSV.G * VariationData.SaturationMultiplier, 0.0f, 1.0f);
    HSV.B = FMath::Clamp(HSV.B * VariationData.ValueMultiplier, 0.0f, 1.0f);

    return HSV.HSVToLinearRGB();
}

FLinearColor UAuracronFoliageMaterialVariationManager::CalculateRGBVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const
{
    FLinearColor Result = BaseColor;

    // Apply RGB variations
    Result.R = FMath::Clamp(Result.R + (VariationData.VariationColor.R - 0.5f) * VariationData.VariationIntensity, 0.0f, 1.0f);
    Result.G = FMath::Clamp(Result.G + (VariationData.VariationColor.G - 0.5f) * VariationData.VariationIntensity, 0.0f, 1.0f);
    Result.B = FMath::Clamp(Result.B + (VariationData.VariationColor.B - 0.5f) * VariationData.VariationIntensity, 0.0f, 1.0f);

    return Result;
}

FLinearColor UAuracronFoliageMaterialVariationManager::SelectColorFromPalette(const FAuracronColorVariationData& VariationData, float SelectionValue) const
{
    if (VariationData.ColorPalette.Num() == 0)
    {
        return FLinearColor::White;
    }

    int32 ColorIndex = FMath::FloorToInt(SelectionValue * VariationData.ColorPalette.Num()) % VariationData.ColorPalette.Num();
    return VariationData.ColorPalette[ColorIndex];
}

FLinearColor UAuracronFoliageMaterialVariationManager::InterpolateGradient(const FAuracronColorVariationData& VariationData, float InterpolationValue) const
{
    if (VariationData.GradientColors.Num() < 2)
    {
        return VariationData.GradientColors.Num() > 0 ? VariationData.GradientColors[0] : FLinearColor::White;
    }

    // Find the two colors to interpolate between
    float ScaledValue = InterpolationValue * (VariationData.GradientColors.Num() - 1);
    int32 LowerIndex = FMath::FloorToInt(ScaledValue);
    int32 UpperIndex = FMath::Min(LowerIndex + 1, VariationData.GradientColors.Num() - 1);
    float Alpha = ScaledValue - LowerIndex;

    return FMath::Lerp(VariationData.GradientColors[LowerIndex], VariationData.GradientColors[UpperIndex], Alpha);
}

UTexture2D* UAuracronFoliageMaterialVariationManager::BlendTexturesInternal(UTexture2D* BaseTexture, UTexture2D* BlendTexture, const FAuracronTextureBlendingData& BlendingData) const
{
    if (!BaseTexture || !BlendTexture)
    {
        return BaseTexture;
    }

    // In a production implementation, this would use UE5.6's texture blending APIs
    // For now, return the base texture as a fallback
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Texture blending applied with mode: %d"), (int32)BlendingData.BlendingMode);
    return BaseTexture;
}

UMaterialInterface* UAuracronFoliageMaterialVariationManager::SelectMaterialByRules(const FVector& Location, const FString& BiomeId, const TArray<FString>& Rules) const
{
    // Get available materials for the biome
    TArray<UMaterialInterface*> AvailableMaterials = GetMaterialsForBiome(BiomeId);

    if (AvailableMaterials.Num() == 0)
    {
        return nullptr;
    }

    // Apply rules to select material
    for (const FString& Rule : Rules)
    {
        // Simple rule processing - in production this would be more sophisticated
        if (Rule.Contains(TEXT("distance")))
        {
            return SelectMaterialByDistance(Location, 100.0f);
        }
        else if (Rule.Contains(TEXT("density")))
        {
            return SelectMaterialByDensity(1.0f);
        }
    }

    // Default to first material
    return AvailableMaterials[0];
}

void UAuracronFoliageMaterialVariationManager::CleanupUnusedMaterials()
{
    FScopeLock Lock(&MaterialVariationLock);

    TArray<FString> MaterialsToRemove;

    // Find unused materials in cache
    for (auto& CachePair : MaterialCache)
    {
        if (!CachePair.Value.IsValid())
        {
            MaterialsToRemove.Add(CachePair.Key);
        }
    }

    // Remove unused materials
    for (const FString& MaterialId : MaterialsToRemove)
    {
        MaterialCache.Remove(MaterialId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleaned up %d unused materials"), MaterialsToRemove.Num());
}

void UAuracronFoliageMaterialVariationManager::CleanupUnusedTextures()
{
    FScopeLock Lock(&MaterialVariationLock);

    TArray<FString> TexturesToRemove;

    // Find unused textures in cache
    for (auto& CachePair : TextureCache)
    {
        if (!CachePair.Value.IsValid())
        {
            TexturesToRemove.Add(CachePair.Key);
        }
    }

    // Remove unused textures
    for (const FString& TextureId : TexturesToRemove)
    {
        TextureCache.Remove(TextureId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleaned up %d unused textures"), TexturesToRemove.Num());
}
