/**
 * Auracron Sigil System Build Configuration
 * 
 * Advanced sigil fusion system with 150 archetype combinations
 * Integrates with UE 5.6 Gameplay Ability System and Enhanced Input
 * 
 * Features:
 * - 3 Sigil Types: <PERSON><PERSON><PERSON> (Defense), <PERSON><PERSON> (Damage), <PERSON><PERSON><PERSON> (Support)
 * - 150 Unique Archetype Combinations (5x5x6)
 * - Fusion 2.0 Engine with dynamic combinations
 * - Native GameplayTags integration
 * - Enhanced Input System integration
 * - Performance optimized for production
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

using UnrealBuildTool;

public class AuracronSigilSystem : ModuleRules
{
    public AuracronSigilSystem(ReadOnlyTargetRules Target) : base(Target)
    {
        // Use the latest C++ standard for UE 5.6
        CppStandard = CppStandardVersion.Cpp20;
        
        // Enable modern UE 5.6 features
        bEnableExceptions = false;
        bUseRTTI = false;
        CppCompileWarningSettings.UndefinedIdentifierWarningLevel = WarningLevel.Warning;
        
        // PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Core dependencies for UE 5.6
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "SlateCore",
            "Slate",
            "UMG",
            "InputCore",
            "EnhancedInput",           // UE 5.6 Enhanced Input System
            "GameplayAbilities",      // Gameplay Ability System
            "GameplayTags",           // Native GameplayTags
            "GameplayTasks",          // Gameplay Tasks
            "NetCore",                // Networking core
            "ReplicationGraph",       // Advanced replication
            "Niagara",               // VFX system
            "AudioMixer",            // Audio system
            "PhysicsCore",           // Physics integration
            "Chaos",                 // Chaos physics
            "RenderCore",            // Rendering system
            "RHI",                   // Render Hardware Interface
            "ApplicationCore"        // Application framework
        });

        // Private dependencies for internal systems
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Json",                  // JSON parsing
            "JsonUtilities",         // JSON utilities
            "AssetRegistry",         // Asset management
            "DeveloperSettings",     // Settings framework
            "EngineSettings",        // Engine settings
            "GameplayDebugger",     // Gameplay debugger
            "AIModule",             // AI integration
            "NavigationSystem",     // Navigation mesh
            "AnimGraphRuntime",     // Animation system
            "AnimationCore",        // Animation core
            "SignalProcessing",     // Signal processing
            "MeshDescription",      // Mesh description framework
            "StaticMeshDescription", // Static mesh description
            "Networking",           // Networking core
            "Sockets"               // Socket system
        });

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",             // Editor integration
                "ToolMenus",            // Tool menus
                "EditorStyle",          // Editor styling
                "EditorWidgets",        // Editor widgets
                "PropertyEditor",       // Property editor
                "ContentBrowser",       // Content browser
                "AssetTools",           // Asset tools
                "KismetCompiler",       // Blueprint compilation
                "BlueprintGraph",       // Blueprint graph
                "MessageLog"            // Message logging
            });
        }

        // Include paths are managed automatically by the module system in UE 5.6
        // No need to manually specify include paths for engine modules

        // Platform specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "MetalRHI"  // Metal support (Mac)
            });
        }

        // Preprocessor definitions for UE 5.6
        PublicDefinitions.AddRange(new string[]
        {
            "AURACRON_SIGIL_SYSTEM_VERSION=100",
            "UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6=0",
            "UE_ENABLE_ICU=1"
        });

        // Optimization settings for production
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.AddRange(new string[]
            {
                "AURACRON_SIGIL_OPTIMIZE=1",
                "AURACRON_SIGIL_DEBUG=0"
            });
        }
        else
        {
            PublicDefinitions.AddRange(new string[]
            {
                "AURACRON_SIGIL_OPTIMIZE=0", 
                "AURACRON_SIGIL_DEBUG=1"
            });
        }
    }
}
