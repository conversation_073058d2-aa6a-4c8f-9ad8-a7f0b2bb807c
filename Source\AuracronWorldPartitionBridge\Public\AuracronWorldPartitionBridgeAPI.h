// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge API Definitions
// Production-ready API definitions for UE5.6 World Partition Bridge module

#pragma once

#include "CoreMinimal.h"

// Module API export/import macros for AuracronWorldPartitionBridge
#ifndef AURACRONWORLDPARTITIONBRIDGE_API
    #ifdef AURACRONWORLDPARTITIONBRIDGE_EXPORTS
        #define AURACRONWORLDPARTITIONBRIDGE_API DLLEXPORT
    #else
        #define AURACRONWORLDPARTITIONBRIDGE_API DLLIMPORT
    #endif
#endif

// Ensure compatibility with UE5.6 module system
#ifndef AURACRONWORLDPART<PERSON>IONBRIDGE_EXPORTS
    #if defined(IMPLEMENT_MODULE)
        #define AURACRON<PERSON>OR<PERSON>PARTITIONBRIDGE_EXPORTS 1
    #endif
#endif