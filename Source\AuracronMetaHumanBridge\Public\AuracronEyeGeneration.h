#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Math/Vector.h"
#include "Math/Color.h"
#include "Engine/StaticMesh.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"

// Forward declarations
class UStaticMesh;
class USkeletalMesh;
class UMaterialInterface;
class UMaterialInstanceDynamic;
class UTexture2D;

#include "AuracronEyeGeneration.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronEyeGeneration, Log, All);

/**
 * Enumeration for different eye types
 */
UENUM(BlueprintType)
enum class EEyeType : uint8
{
    Human       UMETA(DisplayName = "Human"),
    Creature    UMETA(DisplayName = "Creature"),
    Robotic     UMETA(DisplayName = "Robotic"),
    Fantasy     UMETA(DisplayName = "Fantasy"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Enumeration for eye colors
 */
UENUM(BlueprintType)
enum class EEyeColor : uint8
{
    Brown       UMETA(DisplayName = "Brown"),
    Blue        UMETA(DisplayName = "Blue"),
    Green       UMETA(DisplayName = "Green"),
    Hazel       UMETA(DisplayName = "Hazel"),
    Gray        UMETA(DisplayName = "Gray"),
    Amber       UMETA(DisplayName = "Amber"),
    Red         UMETA(DisplayName = "Red"),
    Purple      UMETA(DisplayName = "Purple"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Enumeration for eye texture types
 */
UENUM(BlueprintType)
enum class EEyeTextureType : uint8
{
    Iris        UMETA(DisplayName = "Iris"),
    Sclera      UMETA(DisplayName = "Sclera"),
    Cornea      UMETA(DisplayName = "Cornea"),
    Pupil       UMETA(DisplayName = "Pupil"),
    Combined    UMETA(DisplayName = "Combined")
};

/**
 * Enumeration for eye texture quality
 */
UENUM(BlueprintType)
enum class EEyeTextureQuality : uint8
{
    Low         UMETA(DisplayName = "Low"),
    Medium      UMETA(DisplayName = "Medium"),
    High        UMETA(DisplayName = "High"),
    Ultra       UMETA(DisplayName = "Ultra"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Structure for eye geometry data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeGeometryData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float EyeballRadius = 1.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float CorneaRadius = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float IrisRadius = 0.6f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float PupilRadius = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float CorneaHeight = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    int32 EyeballSubdivisions = 32;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    int32 CorneaSubdivisions = 16;

    FEyeGeometryData()
    {
        EyeballRadius = 1.2f;
        CorneaRadius = 0.8f;
        IrisRadius = 0.6f;
        PupilRadius = 0.2f;
        CorneaHeight = 0.1f;
        EyeballSubdivisions = 32;
        CorneaSubdivisions = 16;
    }
};

/**
 * Structure for eye material data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeMaterialData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* EyeballMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* CorneaMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* IrisTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* ScleraTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* CorneaTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor IrisColor = FLinearColor::Blue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor ScleraColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float IrisRoughness = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float CorneaRoughness = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float EyeballMetallic = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float CorneaRefraction = 1.376f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float SubsurfaceScattering = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float PupilSize = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float ScleraRoughness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float CorneaIOR = 1.376f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float BloodShotIntensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Wetness = 0.7f;

    FEyeMaterialData()
    {
        EyeballMaterial = nullptr;
        CorneaMaterial = nullptr;
        IrisTexture = nullptr;
        ScleraTexture = nullptr;
        IrisColor = FLinearColor::Blue;
        ScleraColor = FLinearColor::White;
        IrisRoughness = 0.1f;
        CorneaRoughness = 0.0f;
        EyeballMetallic = 0.0f;
        CorneaRefraction = 1.376f;
        SubsurfaceScattering = 0.3f;
    }
};

/**
 * Emotional states that affect eye behavior
 */
UENUM(BlueprintType)
enum class EEyeEmotionalState : uint8
{
    Calm        UMETA(DisplayName = "Calm"),
    Fear        UMETA(DisplayName = "Fear"),
    Surprise    UMETA(DisplayName = "Surprise"),
    Excitement  UMETA(DisplayName = "Excitement"),
    Anger       UMETA(DisplayName = "Anger"),
    Concentration UMETA(DisplayName = "Concentration"),
    Happy       UMETA(DisplayName = "Happy"),
    Tiredness   UMETA(DisplayName = "Tiredness"),
    Sadness     UMETA(DisplayName = "Sadness"),
    Joy         UMETA(DisplayName = "Joy"),
    Pain        UMETA(DisplayName = "Pain")
};

/**
 * Health states that affect eye appearance
 */
UENUM(BlueprintType)
enum class EHealthState : uint8
{
    Normal      UMETA(DisplayName = "Normal"),
    Dehydrated  UMETA(DisplayName = "Dehydrated"),
    Allergic    UMETA(DisplayName = "Allergic"),
    Tired       UMETA(DisplayName = "Tired"),
    Sick        UMETA(DisplayName = "Sick")
};

/**
 * Structure for eye animation data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeAnimationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableBlinking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float BlinkFrequency = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float BlinkDuration = 0.15f;

    // ========================================
    // Pupil Animation Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil Animation")
    bool bAnimatePupilDilation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil Animation")
    float BasePupilSize = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil Animation")
    float LightIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil Animation")
    float PupilAnimationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil Animation")
    float PupilSizeVariation = 0.1f;

    // ========================================
    // Eye Movement Animation Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eye Movement")
    bool bAnimateEyeMovement = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eye Movement")
    FVector2D EyeMovementSpeed = FVector2D(1.0f, 0.8f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eye Movement")
    FVector2D EyeMovementRange = FVector2D(0.1f, 0.08f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eye Movement")
    float SaccadeFrequency = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eye Movement")
    float SaccadeAmplitude = 0.05f;

    // ========================================
    // Blinking Animation Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blinking")
    bool bAnimateBlinking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blinking")
    float BlinkIntensity = 1.0f;

    // ========================================
    // Wetness Animation Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wetness")
    bool bAnimateWetness = true;

    // ========================================
    // Physiological Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physiology")
    float BreathingRate = 0.25f; // breaths per second

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physiology")
    float HeartRate = 72.0f; // beats per minute

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physiology")
    float FatigueLevel = 0.0f; // 0.0 to 1.0

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion")
    EEyeEmotionalState EmotionalState = EEyeEmotionalState::Calm;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    EHealthState HealthState = EHealthState::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableEyeTracking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float EyeTrackingSpeed = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MaxEyeRotation = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnablePupilDilation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float PupilDilationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MinPupilSize = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float MaxPupilSize = 0.4f;

    // ========================================
    // Additional Animation Properties
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wetness")
    float BaseWetness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float WindIntensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float DustLevel = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float BrightnessLevel = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Character")
    float Age = 25.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wetness")
    float WetnessVariationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wetness")
    float WetnessVariationAmount = 0.1f;

    FEyeAnimationData()
    {
        bEnableBlinking = true;
        BlinkFrequency = 3.0f;
        BlinkDuration = 0.15f;
        bEnableEyeTracking = true;
        EyeTrackingSpeed = 2.0f;
        MaxEyeRotation = 30.0f;
        bEnablePupilDilation = true;
        PupilDilationSpeed = 1.0f;
        MinPupilSize = 0.1f;
        MaxPupilSize = 0.4f;
    }
};

/**
 * Structure for eye LOD level data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeLODLevel
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float ResolutionScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float QualityScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableDetailTextures = true;

    FEyeLODLevel()
    {
        ResolutionScale = 1.0f;
        QualityScale = 1.0f;
        bEnableDetailTextures = true;
    }
};

/**
 * Structure for eye LOD data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeLODData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    TArray<FEyeLODLevel> LODLevels;

    FEyeLODData()
    {
        // Initialize with default LOD levels
        LODLevels.SetNum(4);
        LODLevels[0].ResolutionScale = 1.0f;   // LOD 0 - Full resolution
        LODLevels[1].ResolutionScale = 0.75f;  // LOD 1 - 75% resolution
        LODLevels[2].ResolutionScale = 0.5f;   // LOD 2 - 50% resolution
        LODLevels[3].ResolutionScale = 0.25f;  // LOD 3 - 25% resolution
    }
};

/**
 * Structure for iris data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FIrisData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    FLinearColor IrisColor = FLinearColor(0.4f, 0.2f, 0.1f, 1.0f); // Brown color

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float IrisRadius = 0.6f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float PatternIntensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float ColorVariation = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float ReflectionIntensity = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float IrisContrast = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    float IrisDetail = 0.7f;

    // Hash function for caching
    friend uint32 GetTypeHash(const FIrisData& IrisData)
    {
        uint32 Hash = 0;
        Hash = HashCombine(Hash, GetTypeHash(IrisData.IrisColor));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.IrisRadius));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.PatternIntensity));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.ColorVariation));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.ReflectionIntensity));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.IrisContrast));
        Hash = HashCombine(Hash, GetTypeHash(IrisData.IrisDetail));
        return Hash;
    }
};

/**
 * Structure for pupil data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FPupilData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil", meta = (ClampMin = "0.01", ClampMax = "1.0"))
    float PupilSize = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil")
    FLinearColor PupilColor = FLinearColor::Black;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PupilSharpness = 0.95f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PupilReflection = 0.1f;

    FPupilData()
    {
        PupilSize = 0.3f;
        PupilColor = FLinearColor::Black;
        PupilSharpness = 0.95f;
        PupilReflection = 0.1f;
    }

    friend uint32 GetTypeHash(const FPupilData& PupilData)
    {
        uint32 Hash = 0;
        Hash = HashCombine(Hash, GetTypeHash(PupilData.PupilSize));
        Hash = HashCombine(Hash, GetTypeHash(PupilData.PupilColor));
        Hash = HashCombine(Hash, GetTypeHash(PupilData.PupilSharpness));
        Hash = HashCombine(Hash, GetTypeHash(PupilData.PupilReflection));
        return Hash;
    }
};

/**
 * Structure for sclera data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FScleraData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sclera")
    FLinearColor ScleraColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sclera")
    float VeinIntensity = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sclera")
    float BloodshotLevel = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sclera")
    float WetnessLevel = 0.7f;

    // Hash function for caching
    friend uint32 GetTypeHash(const FScleraData& ScleraData)
    {
        uint32 Hash = 0;
        Hash = HashCombine(Hash, GetTypeHash(ScleraData.ScleraColor));
        Hash = HashCombine(Hash, GetTypeHash(ScleraData.VeinIntensity));
        Hash = HashCombine(Hash, GetTypeHash(ScleraData.BloodshotLevel));
        Hash = HashCombine(Hash, GetTypeHash(ScleraData.WetnessLevel));
        return Hash;
    }
};

/**
 * Structure for cornea data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FCorneaData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cornea")
    float CorneaRadius = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cornea")
    float ReflectionIntensity = 0.9f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cornea")
    float RefractionIndex = 1.376f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cornea")
    float CausticsIntensity = 0.5f;
};

/**
 * Structure for eye generation parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEyeGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    FString EyeName = TEXT("DefaultEye");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    EEyeType EyeType = EEyeType::Human;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    EEyeColor EyeColor = EEyeColor::Brown;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    EEyeTextureType TextureType = EEyeTextureType::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    FEyeGeometryData GeometryData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Iris")
    FIrisData IrisData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pupil")
    FPupilData PupilData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sclera")
    FScleraData ScleraData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cornea")
    FCorneaData CorneaData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FEyeMaterialData MaterialData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableAnimation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    FEyeAnimationData AnimationData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bGenerateLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevels = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EEyeTextureQuality Quality = EEyeTextureQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (EditCondition = "Quality == EEyeTextureQuality::Custom"))
    FIntPoint CustomResolution = FIntPoint(1024, 1024);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Seed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    int32 TextureSize = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bGenerateMipmaps = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture")
    bool bSRGB = true;

    // ========================================
    // Additional Properties for UE 5.6 Compatibility
    // ========================================

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    FEyeLODData LODData;

    FEyeGenerationParameters()
    {
        EyeName = TEXT("DefaultEye");
        EyeType = EEyeType::Human;
        EyeColor = EEyeColor::Brown;
        bEnableAnimation = true;
        bGenerateLODs = true;
        MaxLODLevels = 3;
        TextureSize = 512;
        bGenerateMipmaps = true;
        bSRGB = true;
    }
};

/**
 * Structure for generated eye data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FGeneratedEyeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UStaticMesh* EyeballMesh = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UStaticMesh* CorneaMesh = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UMaterialInstanceDynamic* EyeballMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    UMaterialInstanceDynamic* CorneaMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    TArray<UStaticMesh*> LODMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    FString GenerationHash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generated")
    float GenerationTime = 0.0f;

    FGeneratedEyeData()
    {
        EyeballMesh = nullptr;
        CorneaMesh = nullptr;
        EyeballMaterial = nullptr;
        CorneaMaterial = nullptr;
        GenerationTime = 0.0f;
    }
};

/**
 * Eye generation system for MetaHuman Bridge
 * Provides advanced eye generation with realistic materials and animation support
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronEyeGeneration
{
public:
    FAuracronEyeGeneration();
    ~FAuracronEyeGeneration();

    // ========================================
    // Core Eye Generation Methods
    // ========================================

    /**
     * Generate eye assets with the specified parameters
     * @param Parameters - Eye generation parameters
     * @return Generated eye data or empty struct if failed
     */
    FGeneratedEyeData GenerateEyeAssets(const FEyeGenerationParameters& Parameters);

    /**
     * Generate eye texture based on color and type
     * @param EyeColor - The desired eye color
     * @param EyeType - The type of eye
     * @param TextureSize - Size of the generated texture
     * @return Generated eye texture or nullptr if failed
     */
    UTexture2D* GenerateEyeTexture(EEyeColor EyeColor, EEyeType EyeType, int32 TextureSize = 512);

    // ========================================
    // Cache Management
    // ========================================

    /**
     * Clear the eye asset cache
     */
    void ClearEyeAssetCache();

    /**
     * Get cache memory usage in bytes
     */
    int32 GetEyeAssetCacheMemoryUsage() const { return EyeAssetCacheMemoryUsage; }

    /**
     * Get total eye generation time
     */
    float GetTotalEyeGenerationTime() const { return TotalEyeGenerationTime; }

    // ========================================
    // Animation and Dynamic Methods
    // ========================================

    /**
     * Apply eye animation to material instance
     * @param EyeMaterial - Material instance to animate
     * @param AnimationData - Animation parameters
     * @return true if animation was applied successfully
     */
    bool ApplyEyeAnimation(UMaterialInstanceDynamic* EyeMaterial, const FEyeAnimationData& AnimationData);

    /**
     * Calculate animated pupil size based on lighting and emotion
     * @param AnimationData - Animation parameters
     * @param CurrentTime - Current world time
     * @return Calculated pupil size
     */
    float CalculateAnimatedPupilSize(const FEyeAnimationData& AnimationData, float CurrentTime);

    /**
     * Calculate animated eye offset for eye movement
     * @param AnimationData - Animation parameters
     * @param CurrentTime - Current world time
     * @return Calculated eye offset
     */
    FVector2D CalculateAnimatedEyeOffset(const FEyeAnimationData& AnimationData, float CurrentTime);

    /**
     * Calculate animated blink amount
     * @param AnimationData - Animation parameters
     * @param CurrentTime - Current world time
     * @return Calculated blink amount
     */
    float CalculateAnimatedBlinkAmount(const FEyeAnimationData& AnimationData, float CurrentTime);

    /**
     * Calculate animated wetness amount
     * @param AnimationData - Animation parameters
     * @param CurrentTime - Current world time
     * @return Calculated wetness amount
     */
    float CalculateAnimatedWetness(const FEyeAnimationData& AnimationData, float CurrentTime);

    /**
     * Update eye texture cache statistics
     */
    void UpdateEyeTextureCacheStats();

    /**
     * Clear eye texture cache
     */
    void ClearEyeTextureCache();

    /**
     * Update eye generation statistics
     * @param OperationName - Name of the operation
     * @param ExecutionTime - Time taken to execute
     * @param bSuccess - Whether the operation was successful
     */
    void UpdateEyeGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    /**
     * Get world context
     * @return World context for this eye generation instance
     */
    UWorld* GetWorld() const;

private:
    // ========================================
    // Internal Implementation Methods
    // ========================================

    UStaticMesh* GenerateEyeballMesh(const FEyeGeometryData& GeometryData);
    UStaticMesh* GenerateCorneaMesh(const FEyeGeometryData& GeometryData);
    UMaterialInstanceDynamic* CreateEyeballMaterial(const FEyeMaterialData& MaterialData);
    UMaterialInstanceDynamic* CreateCorneaMaterial(const FEyeMaterialData& MaterialData);
    bool GenerateEyeLODs(FGeneratedEyeData& EyeData, const FEyeGenerationParameters& Parameters);
    bool GenerateEyeLODs(UTexture2D* EyeTexture, const FEyeLODData& LODData);
    bool GenerateEyeLODLevel(UTexture2D* EyeTexture, int32 LODIndex, FIntPoint LODResolution, const FEyeLODLevel& LODLevel);
    void ScaleTextureData(const void* SourceData, int32 SourceWidth, int32 SourceHeight, void* DestData, int32 DestWidth, int32 DestHeight, float QualityScale);

    // Texture generation methods
    UTexture2D* GenerateIrisTexture(const FIrisData& IrisData, EEyeTextureQuality Quality);
    UTexture2D* GenerateScleraTexture(const FScleraData& ScleraData, EEyeTextureQuality Quality);
    UTexture2D* GenerateCorneaTexture(const FCorneaData& CorneaData, EEyeTextureQuality Quality);
    UMaterialInstanceDynamic* CreateEyeMaterial(const FEyeMaterialData& MaterialData, UMaterialInterface* BaseMaterial);

    // Texture processing methods
    UTexture2D* GenerateEyeTexture(const FEyeGenerationParameters& Parameters);
    void ApplyIrisPattern(UTexture2D* Texture, const FIrisData& IrisData);
    void ApplyIrisColorVariation(UTexture2D* Texture, const FIrisData& IrisData);
    void ApplyIrisReflection(UTexture2D* Texture, const FIrisData& IrisData);
    void ApplyScleraVeins(UTexture2D* Texture, const FScleraData& ScleraData);
    void ApplyScleraBloodshot(UTexture2D* Texture, const FScleraData& ScleraData);
    void ApplyScleraWetness(UTexture2D* Texture, const FScleraData& ScleraData);
    void ScaleTextureData(void* SourceData, int32 SourceWidth, int32 SourceHeight, void* TargetData, int32 TargetWidth, int32 TargetHeight, float ScaleFactor);
    void ApplyCorneaReflection(UTexture2D* Texture, const FCorneaData& CorneaData);
    void ApplyCorneaRefraction(UTexture2D* Texture, const FCorneaData& CorneaData);
    void ApplyCorneaCaustics(UTexture2D* Texture, const FCorneaData& CorneaData);

    // Material helper methods
    UMaterialInterface* GetDefaultEyeMaterial();

    // Helper methods
    bool ValidateEyeGenerationParameters(const FEyeGenerationParameters& Parameters, FString& OutError);
    FString CalculateEyeGenerationHash(const FEyeGenerationParameters& Parameters);
    FLinearColor GetEyeColorFromEnum(EEyeColor EyeColor);
    UMaterialInterface* GetDefaultEyeballMaterial();
    UMaterialInterface* GetDefaultCorneaMaterial();
    void UpdateEyeAssetCacheStats();
    int32 EstimateEyeAssetMemoryUsage(const FGeneratedEyeData& EyeData);
    FIntPoint GetQualityResolution(EEyeTextureQuality Quality);
    int32 GetMaxTextureSizeForPlatform();
    bool GenerateEyeTextureToRenderTarget(const FEyeGenerationParameters& Parameters, UTextureRenderTarget2D* RenderTarget);
    UTexture2D* CreateTextureFromRenderTarget(UTextureRenderTarget2D* RenderTarget, const FString& TextureName);
    void ApplyEyeTexturePostProcessing(UTexture2D* Texture, const FEyeGenerationParameters& Parameters);
    void UpdateEyeTextureCacheStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    // ========================================
    // Member Variables
    // ========================================

    mutable FCriticalSection EyeGenerationMutex;
    TMap<FString, FGeneratedEyeData> EyeAssetCache;
    TMap<FString, TWeakObjectPtr<UTexture2D>> EyeTextureCache;
    TMap<FString, TWeakObjectPtr<UMaterialInstanceDynamic>> EyeMaterialCache;
    TMap<FString, FString> EyeGenerationStats;
    int32 EyeAssetCacheMemoryUsage;
    int64 EyeTextureCacheMemoryUsage;
    float TotalEyeGenerationTime;
};
