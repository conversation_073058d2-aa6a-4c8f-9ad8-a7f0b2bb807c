/**
 * AuracronPlayerController.cpp
 * 
 * Implementação do PlayerController do Auracron
 */

#include "AuracronPlayerController.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"

// Includes dos bridges - temporariamente removidos para compilação inicial
// #include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"
// #include "AuracronVerticalTransitionsBridge/Public/AuracronVerticalTransitionsBridge.h"
// #include "AuracronHarmonyEngineBridge/Public/HarmonyEngineSubsystem.h"
#include "AuracronGameMode.h"

AAuracronPlayerController::AAuracronPlayerController()
{
    // Configurar tick
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS para lógica de jogador

    // Estado inicial
    CurrentPlayerState = EAuracronPlayerState::Disconnected;
    PlayerTeam = -1;
    bPlayerInitialized = false;

    // Configurações padrão
    InputSettings = FAuracronInputSettings();
    ChampionData = FAuracronChampionData();

    // Inicializar ponteiros dos bridges
    HarmonyEngineSubsystem = nullptr;
    AuracronGameMode = nullptr;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Construtor executado"));
    }
}

void AAuracronPlayerController::BeginPlay()
{
    Super::BeginPlay();

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: BeginPlay iniciado"));
    }

    // Inicializar referências dos bridges
    InitializeBridgeReferences();

    // Configurar Enhanced Input
    SetupEnhancedInput();

    // Mudar para estado de lobby
    ChangePlayerState(EAuracronPlayerState::InLobby);

    // Notificar Harmony Engine
    NotifyHarmonyEngine();

    bPlayerInitialized = true;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: BeginPlay concluído"));
    }
}

void AAuracronPlayerController::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: EndPlay iniciado"));
    }

    // Notificar Harmony Engine sobre saída - temporariamente comentado
    // if (HarmonyEngineSubsystem)
    // {
    //     HarmonyEngineSubsystem->OnPlayerDisconnected(this);
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Notificando Harmony Engine sobre desconexão"));
    //     }
    // }

    // Mudar para estado desconectado
    ChangePlayerState(EAuracronPlayerState::Disconnected);

    Super::EndPlay(EndPlayReason);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: EndPlay concluído"));
    }
}

void AAuracronPlayerController::SetupInputComponent()
{
    Super::SetupInputComponent();

    // Configurar Enhanced Input Component
    if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
    {
        // Bind movement actions
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &AAuracronPlayerController::Move);
        }

        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &AAuracronPlayerController::Look);
        }

        if (JumpAction)
        {
            EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Started, this, &AAuracronPlayerController::Jump);
        }

        if (InteractAction)
        {
            EnhancedInputComponent->BindAction(InteractAction, ETriggerEvent::Started, this, &AAuracronPlayerController::Interact);
        }

        // Bind sigil actions
        if (UseSigil1Action)
        {
            EnhancedInputComponent->BindAction(UseSigil1Action, ETriggerEvent::Started, this, &AAuracronPlayerController::UseSigil1);
        }

        if (UseSigil2Action)
        {
            EnhancedInputComponent->BindAction(UseSigil2Action, ETriggerEvent::Started, this, &AAuracronPlayerController::UseSigil2);
        }

        if (UseSigil3Action)
        {
            EnhancedInputComponent->BindAction(UseSigil3Action, ETriggerEvent::Started, this, &AAuracronPlayerController::UseSigil3);
        }

        // Bind UI actions
        if (MenuAction)
        {
            EnhancedInputComponent->BindAction(MenuAction, ETriggerEvent::Started, this, &AAuracronPlayerController::OpenMenu);
        }

        if (InventoryAction)
        {
            EnhancedInputComponent->BindAction(InventoryAction, ETriggerEvent::Started, this, &AAuracronPlayerController::OpenInventory);
        }

        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Enhanced Input configurado"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronPlayerController: Falha ao configurar Enhanced Input Component"));
    }
}

void AAuracronPlayerController::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bPlayerInitialized)
    {
        return;
    }

    // Atualizar lógica do jogador
    UpdatePlayerLogic(DeltaTime);
}

void AAuracronPlayerController::SelectChampion(int32 ChampionID)
{
    if (CurrentPlayerState != EAuracronPlayerState::SelectingChampion)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Tentativa de selecionar campeão em estado inválido"));
        return;
    }

    if (ChampionID < 0 || ChampionID >= 50) // 50 campeões disponíveis
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: ID de campeão inválido: %d"), ChampionID);
        return;
    }

    ChampionData.ChampionID = ChampionID;
    ChampionData.ChampionName = FString::Printf(TEXT("Champion_%d"), ChampionID);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Campeão selecionado: %d (%s)"), 
               ChampionID, *ChampionData.ChampionName);
    }

    // Notificar sistema de sígilos - temporariamente comentado
    // if (UAuracronSigilosBridge* SigilosBridge = GetComponentByClass<UAuracronSigilosBridge>())
    // {
    //     SigilosBridge->OnChampionSelected(this, ChampionID);
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Notificando SigilosBridge sobre seleção de campeão"));
    //     }
    // }

    // Disparar evento
    OnChampionSelected.Broadcast(ChampionData);
}

void AAuracronPlayerController::EquipSigil(FGameplayTag SigilTag)
{
    if (!SigilTag.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Tag de sígilo inválida"));
        return;
    }

    // Verificar se já está equipado
    if (ChampionData.EquippedSigils.Contains(SigilTag))
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Sígilo já equipado: %s"), *SigilTag.ToString());
        return;
    }

    // Verificar limite de sígilos (máximo 3)
    if (ChampionData.EquippedSigils.Num() >= 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Limite de sígilos atingido"));
        return;
    }

    ChampionData.EquippedSigils.Add(SigilTag);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Sígilo equipado: %s"), *SigilTag.ToString());
    }

    // Notificar sistema de sígilos - temporariamente comentado
    // if (UAuracronSigilosBridge* SigilosBridge = GetComponentByClass<UAuracronSigilosBridge>())
    // {
    //     SigilosBridge->OnSigilEquipped(this, SigilTag);
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Notificando SigilosBridge sobre equipar sígilo"));
    //     }
    // }

    // Disparar evento
    OnSigilEquipped.Broadcast(SigilTag);
}

void AAuracronPlayerController::UnequipSigil(FGameplayTag SigilTag)
{
    if (!SigilTag.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Tag de sígilo inválida"));
        return;
    }

    if (ChampionData.EquippedSigils.Remove(SigilTag) > 0)
    {
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Sígilo removido: %s"), *SigilTag.ToString());
        }

        // Notificar sistema de sígilos - temporariamente comentado
        // if (UAuracronSigilosBridge* SigilosBridge = GetComponentByClass<UAuracronSigilosBridge>())
        // {
        //     SigilosBridge->OnSigilUnequipped(this, SigilTag);
        //     if (bVerboseLogging)
        //     {
        //         UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Notificando SigilosBridge sobre desequipar sígilo"));
        //     }
        // }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Sígilo não estava equipado: %s"), *SigilTag.ToString());
    }
}

void AAuracronPlayerController::ConfirmChampionSelection()
{
    if (CurrentPlayerState != EAuracronPlayerState::SelectingChampion)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Tentativa de confirmar seleção em estado inválido"));
        return;
    }

    // Validar seleção
    ValidateChampionSelection();

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Seleção de campeão confirmada"));
    }

    // Notificar GameMode
    if (AuracronGameMode)
    {
        // AuracronGameMode->OnPlayerConfirmedSelection(this);
    }
}

void AAuracronPlayerController::UseDynamicRail()
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Usando trilho dinâmico"));
    }

    // Implementar uso de trilho dinâmico
    // Será integrado com o DynamicRealmBridge
}

void AAuracronPlayerController::UseVerticalTransition()
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Usando transição vertical"));
    }

    // Usar sistema de transições verticais - implementação robusta usando reflexão
    TArray<UActorComponent*> Components;
    GetComponents<UActorComponent>(Components);
    for (UActorComponent* Component : Components)
    {
        if (Component && Component->GetClass()->GetName().Contains(TEXT("VerticalTransitions")))
        {
            // VerticalTransitionsBridge->RequestTransition(this);
            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Solicitando transição vertical via reflexão"));
            }
            break;
        }
    }
}

void AAuracronPlayerController::ActivateSigil(int32 SigilSlot)
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    if (SigilSlot < 0 || SigilSlot >= ChampionData.EquippedSigils.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Slot de sígilo inválido: %d"), SigilSlot);
        return;
    }

    FGameplayTag SigilTag = ChampionData.EquippedSigils[SigilSlot];

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Ativando sígilo: %s"), *SigilTag.ToString());
    }

    // Ativar sígilo através do bridge - temporariamente comentado
    // if (UAuracronSigilosBridge* SigilosBridge = GetComponentByClass<UAuracronSigilosBridge>())
    // {
    //     SigilosBridge->ActivateSigil(this, SigilTag);
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Ativando sígilo via bridge"));
    //     }
    // }
}

void AAuracronPlayerController::InteractWithObjective()
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Interagindo com objetivo"));
    }

    // Implementar interação com objetivos procedurais
    // Será integrado com o ProceduralObjectiveSystem
}

void AAuracronPlayerController::ToggleMainMenu()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Toggle menu principal"));
    }

    // Implementar toggle do menu principal
    // Será integrado com o sistema de UI
}

void AAuracronPlayerController::ToggleInventory()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Toggle inventário"));
    }

    // Implementar toggle do inventário
    // Será integrado com o sistema de UI
}

void AAuracronPlayerController::ToggleSettings()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Toggle configurações"));
    }

    // Implementar toggle das configurações
    // Será integrado com o sistema de UI
}

void AAuracronPlayerController::UpdateInputSettings(const FAuracronInputSettings& NewSettings)
{
    InputSettings = NewSettings;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Configurações de input atualizadas"));
    }

    // Aplicar novas configurações
    // Implementar aplicação das configurações de input
}

// === Input Handlers ===

void AAuracronPlayerController::Move(const FInputActionValue& Value)
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    FVector2D MovementVector = Value.Get<FVector2D>();

    if (APawn* ControlledPawn = GetPawn())
    {
        // Forward/Backward direction
        if (MovementVector.Y != 0.0f)
        {
            const FVector ForwardDirection = ControlledPawn->GetActorForwardVector();
            ControlledPawn->AddMovementInput(ForwardDirection, MovementVector.Y);
        }

        // Right/Left direction
        if (MovementVector.X != 0.0f)
        {
            const FVector RightDirection = ControlledPawn->GetActorRightVector();
            ControlledPawn->AddMovementInput(RightDirection, MovementVector.X);
        }
    }
}

void AAuracronPlayerController::Look(const FInputActionValue& Value)
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    FVector2D LookAxisVector = Value.Get<FVector2D>();

    // Apply sensitivity
    LookAxisVector *= InputSettings.MouseSensitivity;

    // Apply Y-axis inversion
    if (InputSettings.bInvertYAxis)
    {
        LookAxisVector.Y *= -1.0f;
    }

    if (APawn* ControlledPawn = GetPawn())
    {
        ControlledPawn->AddControllerYawInput(LookAxisVector.X);
        ControlledPawn->AddControllerPitchInput(LookAxisVector.Y);
    }
}

void AAuracronPlayerController::Jump(const FInputActionValue& Value)
{
    if (CurrentPlayerState != EAuracronPlayerState::InGame)
    {
        return;
    }

    if (ACharacter* PlayerCharacter = Cast<ACharacter>(GetPawn()))
    {
        PlayerCharacter->Jump();
    }
}

void AAuracronPlayerController::Interact(const FInputActionValue& Value)
{
    InteractWithObjective();
}

void AAuracronPlayerController::UseSigil1(const FInputActionValue& Value)
{
    ActivateSigil(0);
}

void AAuracronPlayerController::UseSigil2(const FInputActionValue& Value)
{
    ActivateSigil(1);
}

void AAuracronPlayerController::UseSigil3(const FInputActionValue& Value)
{
    ActivateSigil(2);
}

void AAuracronPlayerController::OpenMenu(const FInputActionValue& Value)
{
    ToggleMainMenu();
}

void AAuracronPlayerController::OpenInventory(const FInputActionValue& Value)
{
    ToggleInventory();
}

// === Private Implementation ===

void AAuracronPlayerController::ChangePlayerState(EAuracronPlayerState NewState)
{
    if (CurrentPlayerState == NewState)
    {
        return;
    }

    EAuracronPlayerState OldState = CurrentPlayerState;
    CurrentPlayerState = NewState;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Estado mudou de %d para %d"),
               (int32)OldState, (int32)NewState);
    }

    // Disparar evento
    OnPlayerStateChanged.Broadcast(OldState, NewState);
}

void AAuracronPlayerController::InitializeBridgeReferences()
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronPlayerController: World não disponível para inicializar bridges"));
        return;
    }

    // Obter GameMode
    AuracronGameMode = Cast<AAuracronGameMode>(GetWorld()->GetAuthGameMode());

    // Buscar por HarmonyEngineSubsystem usando reflexão
    TArray<UWorldSubsystem*> WorldSubsystems = GetWorld()->GetSubsystemArrayCopy<UWorldSubsystem>();
    for (UWorldSubsystem* Subsystem : WorldSubsystems)
    {
        if (Subsystem && Subsystem->GetClass()->GetName().Contains(TEXT("HarmonyEngine")))
        {
            HarmonyEngineSubsystem = Subsystem;
            break;
        }
    }

    // Note: SigilosBridge e VerticalTransitionsBridge são ActorComponents
    // Serão obtidos via GetComponentByClass quando necessário

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Bridge references initialized - GameMode: %s, HarmonyEngine: %s"),
            AuracronGameMode ? TEXT("OK") : TEXT("NULL"),
            HarmonyEngineSubsystem ? TEXT("OK") : TEXT("NULL"));
    }
}

void AAuracronPlayerController::SetupEnhancedInput()
{
    if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer()))
    {
        if (DefaultMappingContext)
        {
            Subsystem->AddMappingContext(DefaultMappingContext, 0);

            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Enhanced Input Mapping Context adicionado"));
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: DefaultMappingContext não configurado"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronPlayerController: Falha ao obter Enhanced Input Subsystem"));
    }
}

void AAuracronPlayerController::UpdatePlayerLogic(float DeltaTime)
{
    // Atualizar lógica específica do jogador
    // Pode incluir cooldowns, buffs, debuffs, etc.

    // Notificar Harmony Engine sobre atividade
    if (HarmonyEngineSubsystem)
    {
        // HarmonyEngineSubsystem->UpdatePlayerActivity(this, DeltaTime);
        // Log apenas ocasionalmente para evitar spam
        static float LastLogTime = 0.0f;
        float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - LastLogTime > 10.0f && bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Atualizando atividade no Harmony Engine"));
            LastLogTime = CurrentTime;
        }
    }
}

void AAuracronPlayerController::ValidateChampionSelection()
{
    // Validar se a seleção de campeão está completa
    if (ChampionData.ChampionID < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Nenhum campeão selecionado"));
        return;
    }

    if (ChampionData.EquippedSigils.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: Nenhum sígilo equipado"));
        // Equipar sígilos padrão se necessário
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Seleção validada - Campeão: %d, Sígilos: %d"),
               ChampionData.ChampionID, ChampionData.EquippedSigils.Num());
    }
}

void AAuracronPlayerController::NotifyHarmonyEngine()
{
    if (HarmonyEngineSubsystem)
    {
        // Notificar Harmony Engine sobre novo jogador
        // HarmonyEngineSubsystem->OnPlayerConnected(this);

        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronPlayerController: Harmony Engine notificado sobre conexão"));
        }
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronPlayerController: HarmonyEngineSubsystem não disponível"));
    }
}
