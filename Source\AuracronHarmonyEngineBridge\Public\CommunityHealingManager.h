#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "Subsystems/WorldSubsystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "CommunityHealingManager.generated.h"

class APlayerController;
class UAbilitySystemComponent;

UENUM(BlueprintType)
enum class EHealingSessionType : uint8
{
    PeerSupport         UMETA(DisplayName = "Peer Support"),
    Mentorship          UMETA(DisplayName = "Mentorship"),
    GroupTherapy        UMETA(DisplayName = "Group Therapy"),
    CrisisIntervention  UMETA(DisplayName = "Crisis Intervention"),
    CelebrationCircle   UMETA(DisplayName = "Celebration Circle")
};

UENUM(BlueprintType)
enum class EHealingStatus : uint8
{
    Pending             UMETA(DisplayName = "Pending"),
    Active              UMETA(DisplayName = "Active"),
    Completed           UMETA(DisplayName = "Completed"),
    Cancelled           UMETA(DisplayName = "Cancelled"),
    Failed              UMETA(DisplayName = "Failed")
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHealingSession
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString SessionID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EHealingSessionType SessionType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EHealingStatus Status;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString VictimPlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> HealerPlayerIDs;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime StartTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime EndTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HealingProgress;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString HealingGoal;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer SessionTags;

    // Additional properties for robust implementation
    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString CancellationReason;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> AssignedHealers;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Progress;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastUpdateTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float EffectivenessScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Duration;

    // Timer handle for session management
    FTimerHandle SessionTimer;

    FHealingSession()
    {
        SessionID = FGuid::NewGuid().ToString();
        SessionType = EHealingSessionType::PeerSupport;
        Status = EHealingStatus::Pending;
        VictimPlayerID = TEXT("");
        StartTime = FDateTime::Now();
        EndTime = FDateTime::MinValue();
        HealingProgress = 0.0f;
        HealingGoal = TEXT("");
        CancellationReason = TEXT("");
        Progress = 0.0f;
        LastUpdateTime = FDateTime::Now();
        EffectivenessScore = 0.0f;
        Duration = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHealerProfile
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HealingSkillLevel;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SuccessfulHealingSessions;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AverageSessionRating;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<EHealingSessionType> Specializations;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsAvailable;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastActiveTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer HealerTags;

    // Additional properties for robust implementation
    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SkillLevel;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SessionsCompleted;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SessionsFailed;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SuccessRate;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TotalHealingTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ExperiencePoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TotalRewardsEarned;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AverageRating;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<EHealingSessionType> SpecializedTypes;

    FHealerProfile()
    {
        PlayerID = TEXT("");
        HealingSkillLevel = 0.0f;
        SuccessfulHealingSessions = 0;
        AverageSessionRating = 0.0f;
        bIsAvailable = true;
        LastActiveTime = FDateTime::Now();
        SkillLevel = 0.5f;
        SessionsCompleted = 0;
        SessionsFailed = 0;
        SuccessRate = 0.0f;
        TotalHealingTime = 0.0f;
        ExperiencePoints = 0.0f;
        TotalRewardsEarned = 0.0f;
        AverageRating = 3.0f;
    }
};

/**
 * Wrapper struct for TArray to use in TMap with UPROPERTY
 */
USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FPlayerHealingHistory
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> SessionIDs;

    FPlayerHealingHistory()
    {
        SessionIDs.Empty();
    }
};

/**
 * Community Healing Manager
 * Manages peer support, mentorship programs, and community healing initiatives
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UCommunityHealingManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UCommunityHealingManager();

    // Core Healing Functions
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FString InitiateHealingSession(const FString& VictimPlayerID, EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool AddHealerToSession(const FString& SessionID, const FString& HealerPlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool CompleteHealingSession(const FString& SessionID, float SuccessRating);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void CancelHealingSession(const FString& SessionID, const FString& Reason);

    // Healer Management
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void RegisterHealer(const FString& PlayerID, const TArray<EHealingSessionType>& Specializations);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void UnregisterHealer(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FString> FindAvailableHealers(EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FString FindBestMatchedHealer(const FString& VictimPlayerID, EHealingSessionType SessionType);

    // Session Management
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FHealingSession> GetActiveHealingSessions();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    FHealingSession GetHealingSession(const FString& SessionID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool IsPlayerInHealingSession(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    void UpdateHealingProgress(const FString& SessionID, float Progress);

    // Analytics and Reporting
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    float GetCommunityHealingEffectiveness();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    int32 GetTotalHealingSessionsCompleted();

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FString> GetTopHealers(int32 Count = 10);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    float GetHealerRating(const FString& HealerPlayerID);

    // Additional functions for robust implementation
    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    TArray<FString> GetAvailableHealers(EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    float GetHealerEffectiveness(const FString& HealerID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool CreateHealingSession(const FString& SessionID, const FString& VictimID, const FString& HealerID, EHealingSessionType SessionType);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool StartHealingSession(const FString& SessionID);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool EndHealingSession(const FString& SessionID, const FString& Reason);

    UFUNCTION(BlueprintCallable, Category = "Community Healing")
    bool IsPlayerHealer(const FString& SessionID, const FString& PlayerID);

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    int32 MaxConcurrentSessions;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    float MaxSessionDuration;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    float MinHealerSkillLevel;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    int32 MaxHealersPerSession;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    bool bEnableAutomaticMatching;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Healing Config")
    bool bEnableSessionRecording;

    // Data Storage
    UPROPERTY()
    TMap<FString, FHealingSession> ActiveSessions;

    UPROPERTY()
    TMap<FString, FHealerProfile> RegisteredHealers;

    UPROPERTY()
    TMap<FString, FHealingSession> CompletedSessions;

    UPROPERTY()
    TMap<FString, FPlayerHealingHistory> PlayerHealingHistory;

    // Session Timers
    UPROPERTY()
    TMap<FString, FTimerHandle> SessionTimers;

    // Additional properties for robust implementation
    UPROPERTY()
    TArray<FString> AvailableHealers;

    UPROPERTY()
    int32 TotalSessionsCancelled;

    UPROPERTY()
    int32 TotalHealersUnregistered;

    UPROPERTY()
    int32 TotalSessionsCompleted;

    // Delegates for events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealingSessionCancelled, const FString&, SessionID, const FString&, Reason);
    UPROPERTY(BlueprintAssignable)
    FOnHealingSessionCancelled OnHealingSessionCancelled;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealerUnregistered, const FString&, HealerID, float, SkillLevel);
    UPROPERTY(BlueprintAssignable)
    FOnHealerUnregistered OnHealerUnregistered;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealingMilestoneReached, const FString&, SessionID, float, Milestone);
    UPROPERTY(BlueprintAssignable)
    FOnHealingMilestoneReached OnHealingMilestoneReached;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealingProgressUpdated, const FString&, SessionID, float, Progress);
    UPROPERTY(BlueprintAssignable)
    FOnHealingProgressUpdated OnHealingProgressUpdated;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealingSessionCompleted, const FString&, SessionID, float, EffectivenessScore);
    UPROPERTY(BlueprintAssignable)
    FOnHealingSessionCompleted OnHealingSessionCompleted;

private:
    // Session management helpers
    bool ValidateHealingRequest(const FString& VictimPlayerID, EHealingSessionType SessionType);
    bool ValidateHealerEligibility(const FString& HealerPlayerID, EHealingSessionType SessionType);
    void StartSessionTimer(const FString& SessionID);
    void OnSessionTimeout(const FString& SessionID);
    
    // Matching algorithms
    float CalculateHealerCompatibility(const FString& HealerPlayerID, const FString& VictimPlayerID);
    TArray<FString> RankHealersByCompatibility(const TArray<FString>& AvailableHealers, const FString& VictimPlayerID);
    bool IsHealerSpecializedFor(const FString& HealerPlayerID, EHealingSessionType SessionType);
    
    // Progress tracking
    void MonitorSessionProgress(const FString& SessionID);
    void UpdateHealerStatistics(const FString& HealerPlayerID, float SessionRating);
    void RecordSessionOutcome(const FHealingSession& Session, bool bSuccessful);
    
    // Quality assurance
    bool ValidateSessionQuality(const FString& SessionID);
    void ProvideHealerFeedback(const FString& HealerPlayerID, const FString& Feedback);
    void EscalateIfNeeded(const FString& SessionID);
    
    // Data persistence
    void SaveHealingData();
    void LoadHealingData();
    void ArchiveCompletedSessions();
    
    // Utility functions
    FString GenerateSessionID();
    FString GenerateHealingGoal(EHealingSessionType SessionType, const FString& VictimPlayerID);
    float CalculateInitialHealerSkill(const FString& PlayerID);
    EHealingSessionType DetermineOptimalSessionType(const FString& VictimPlayerID);
    float CalculateSessionSuccessProbability(const FHealingSession& Session);

    // Additional helper functions for robust implementation
    void NotifyHealerOfCancellation(const FString& HealerID, const FString& SessionID, const FString& Reason);
    void NotifyVictimOfCancellation(const FString& VictimID, const FString& SessionID, const FString& Reason);
    void NotifyVictimOfHealerChange(const FString& VictimID, const FString& SessionID, const FString& HealerID, const FString& Reason);
    void TryAssignHealersToSession(const FString& SessionID);
    void NotifyMilestoneReached(const FString& SessionID, float Milestone);
    void AwardProgressReward(const FString& HealerID, float Milestone);
    void CompleteHealingSession(const FString& SessionID);
    void NotifyHealerOfAssignment(const FString& HealerID, const FString& SessionID);
    void NotifyHealerOfMilestone(const FString& HealerID, const FString& SessionID, float Milestone);
    void NotifyVictimOfMilestone(const FString& VictimID, const FString& SessionID, float Milestone);
    void NotifyHealerOfSkillIncrease(const FString& HealerID, float NewSkillLevel);
    void AwardCompletionReward(const FString& HealerID, const FHealingSession& Session);
    void NotifyHealerOfCompletion(const FString& HealerID, const FString& SessionID);
    void NotifyVictimOfCompletion(const FString& VictimID, const FString& SessionID);
};
