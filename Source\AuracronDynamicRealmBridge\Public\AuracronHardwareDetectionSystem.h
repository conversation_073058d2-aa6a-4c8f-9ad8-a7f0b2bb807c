/**
 * AuracronHardwareDetectionSystem.h
 * 
 * Sistema avançado de detecção automática de hardware para otimização
 * dinâmica baseada nas capacidades do dispositivo.
 * 
 * Implementa detecção inteligente de:
 * - CPU (cores, frequência, arquitetura)
 * - GPU (memória, capacidades, driver)
 * - RAM (total, disponível, velocidade)
 * - Armazenamento (tipo, velocidade)
 * - Plataforma (mobile, PC, console)
 * 
 * Usa UE 5.6 APIs modernas para benchmark automático e otimização.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/Engine.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "HAL/PlatformFilemanager.h"
#include "RHI.h"
#include "RenderCore.h"
#include "RendererInterface.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/GameUserSettings.h"
#include "Engine/UserInterfaceSettings.h"
#include "AuracronHardwareDetectionSystem.generated.h"

/**
 * Níveis de hardware detectados
 */
UENUM(BlueprintType)
enum class EHardwareTier : uint8
{
    Unknown         UMETA(DisplayName = "Unknown"),
    Low             UMETA(DisplayName = "Low"),
    Medium          UMETA(DisplayName = "Medium"),
    High            UMETA(DisplayName = "High"),
    Entry           UMETA(DisplayName = "Entry Level"),
    MidRange        UMETA(DisplayName = "Mid Range"),
    HighEnd         UMETA(DisplayName = "High End"),
    Enthusiast      UMETA(DisplayName = "Enthusiast"),
    Server          UMETA(DisplayName = "Server")
};

/**
 * Tipos de plataforma
 */
UENUM(BlueprintType)
enum class EPlatformType : uint8
{
    Unknown         UMETA(DisplayName = "Unknown"),
    Windows         UMETA(DisplayName = "Windows"),
    Mac             UMETA(DisplayName = "Mac"),
    Linux           UMETA(DisplayName = "Linux"),
    Android         UMETA(DisplayName = "Android"),
    iOS             UMETA(DisplayName = "iOS"),
    Console         UMETA(DisplayName = "Console"),
    WebBrowser      UMETA(DisplayName = "Web Browser")
};

/**
 * Capacidades de GPU
 */
UENUM(BlueprintType)
enum class EGPUCapabilities : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    DirectX11       UMETA(DisplayName = "DirectX 11"),
    DirectX12       UMETA(DisplayName = "DirectX 12"),
    Vulkan          UMETA(DisplayName = "Vulkan"),
    Metal           UMETA(DisplayName = "Metal"),
    RayTracing      UMETA(DisplayName = "Ray Tracing"),
    DLSS            UMETA(DisplayName = "DLSS"),
    FSR             UMETA(DisplayName = "FSR"),
    XeSS            UMETA(DisplayName = "XeSS")
};

/**
 * Informações de CPU
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareCPUInfo
{
    GENERATED_BODY()

    /** Número de cores físicos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    int32 PhysicalCores;

    /** Número de cores lógicos (com hyperthreading) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    int32 LogicalCores;

    /** Número total de cores (compatibilidade) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    int32 CoreCount;

    /** Frequência base em MHz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    float BaseFrequencyMHz;

    /** Frequência base em GHz (compatibilidade) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    float BaseClockGHz;

    /** Frequência máxima em MHz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    float MaxFrequencyMHz;

    /** Nome do processador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    FString ProcessorName;

    /** Arquitetura (x64, ARM, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    FString Architecture;

    /** Suporte a instruções especiais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CPU Info")
    TArray<FString> SupportedInstructions;

    FHardwareCPUInfo()
    {
        PhysicalCores = 0;
        LogicalCores = 0;
        CoreCount = 0;
        BaseFrequencyMHz = 0.0f;
        BaseClockGHz = 0.0f;
        MaxFrequencyMHz = 0.0f;
        ProcessorName = TEXT("Unknown");
        Architecture = TEXT("Unknown");
    }
};

/**
 * Informações de GPU
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareGPUInfo
{
    GENERATED_BODY()

    /** Nome da GPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    FString GPUName;

    /** Fabricante (NVIDIA, AMD, Intel) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    FString Vendor;

    /** Memória de vídeo em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    int32 VideoMemoryMB;

    /** Memória de vídeo em GB (compatibilidade) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    float VRAMSizeGB;

    /** Versão do driver */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    FString DriverVersion;

    /** API gráfica suportada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    FString GraphicsAPI;

    /** Capacidades especiais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    TArray<EGPUCapabilities> Capabilities;

    /** Suporte a ray tracing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    bool bSupportsRayTracing;

    /** Suporte a compute shaders */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    bool bSupportsComputeShaders;

    /** Suporte a tessellation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Info")
    bool bSupportsTessellation;

    FHardwareGPUInfo()
    {
        GPUName = TEXT("Unknown");
        Vendor = TEXT("Unknown");
        VideoMemoryMB = 0;
        VRAMSizeGB = 0.0f;
        DriverVersion = TEXT("Unknown");
        GraphicsAPI = TEXT("Unknown");
        bSupportsRayTracing = false;
        bSupportsComputeShaders = false;
        bSupportsTessellation = false;
    }
};

/**
 * Informações de memória
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareMemoryInfo
{
    GENERATED_BODY()

    /** Memória total em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    int32 TotalMemoryMB;

    /** Memória total em GB (compatibilidade) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    float TotalRAMGB;

    /** Memória disponível em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    int32 AvailableMemoryMB;

    /** Memória usada em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    int32 UsedMemoryMB;

    /** Velocidade da memória em MHz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    float MemorySpeedMHz;

    /** Tipo de memória (DDR4, DDR5, LPDDR, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Info")
    FString MemoryType;

    FHardwareMemoryInfo()
    {
        TotalMemoryMB = 0;
        TotalRAMGB = 0.0f;
        AvailableMemoryMB = 0;
        UsedMemoryMB = 0;
        MemorySpeedMHz = 0.0f;
        MemoryType = TEXT("Unknown");
    }
};

/**
 * Informações de armazenamento
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareStorageInfo
{
    GENERATED_BODY()

    /** Espaço total em GB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Storage Info")
    float TotalSpaceGB;

    /** Espaço disponível em GB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Storage Info")
    float AvailableSpaceGB;

    /** Tipo de armazenamento (SSD, HDD, NVMe) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Storage Info")
    FString StorageType;

    /** Velocidade de leitura em MB/s */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Storage Info")
    float ReadSpeedMBps;

    /** Velocidade de escrita em MB/s */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Storage Info")
    float WriteSpeedMBps;

    FHardwareStorageInfo()
    {
        TotalSpaceGB = 0.0f;
        AvailableSpaceGB = 0.0f;
        StorageType = TEXT("Unknown");
        ReadSpeedMBps = 0.0f;
        WriteSpeedMBps = 0.0f;
    }
};

/**
 * Resultados de benchmark de hardware
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareBenchmarkResults
{
    GENERATED_BODY()

    /** Score de CPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benchmark Results")
    float CPUScore;

    /** Score de GPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benchmark Results")
    float GPUScore;

    /** Score de memória */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benchmark Results")
    float MemoryScore;

    /** Score de armazenamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benchmark Results")
    float StorageScore;

    FHardwareBenchmarkResults()
    {
        CPUScore = 0.0f;
        GPUScore = 0.0f;
        MemoryScore = 0.0f;
        StorageScore = 0.0f;
    }
};

/**
 * Perfil completo de hardware
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwareProfile
{
    GENERATED_BODY()

    /** Tipo de plataforma */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    EPlatformType PlatformType;

    /** Tier de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    EHardwareTier HardwareTier;

    /** Informações de CPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FHardwareCPUInfo CPUInfo;

    /** Informações de GPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FHardwareGPUInfo GPUInfo;

    /** Informações de memória */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FHardwareMemoryInfo MemoryInfo;

    /** Informações de armazenamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FHardwareStorageInfo StorageInfo;

    /** Score de performance geral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    float OverallPerformanceScore;

    /** Score de performance específico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    float PerformanceScore;

    /** Resultados de benchmark */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FHardwareBenchmarkResults BenchmarkResults;

    /** Timestamp da detecção */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Profile")
    FDateTime DetectionTimestamp;

    FHardwareProfile()
    {
        PlatformType = EPlatformType::Unknown;
        HardwareTier = EHardwareTier::Unknown;
        OverallPerformanceScore = 0.0f;
        PerformanceScore = 0.0f;
        DetectionTimestamp = FDateTime::Now();
    }
};

/**
 * Métricas de performance em tempo real para hardware detection
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FHardwarePerformanceMetrics
{
    GENERATED_BODY()

    /** Timestamp da medição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    FDateTime Timestamp;

    /** Taxa de quadros por segundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float FrameRate;

    /** Tempo de frame em milissegundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float FrameTime;

    /** Uso de memória em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float MemoryUsageMB;

    /** Uso de memória GPU em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float GPUMemoryUsageMB;

    FHardwarePerformanceMetrics()
    {
        Timestamp = FDateTime::Now();
        FrameRate = 0.0f;
        FrameTime = 0.0f;
        MemoryUsageMB = 0.0f;
        GPUMemoryUsageMB = 0.0f;
    }
};



/**
 * Configurações de qualidade recomendadas
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FRecommendedQualitySettings
{
    GENERATED_BODY()

    /** Nível de qualidade geral */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 OverallQualityLevel;

    /** Qualidade de texturas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 TextureQuality;

    /** Qualidade de sombras */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 ShadowQuality;

    /** Qualidade de efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 EffectsQuality;

    /** Qualidade de pós-processamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 PostProcessQuality;

    /** Anti-aliasing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 AntiAliasingQuality;

    /** Qualidade de distância de visão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 ViewDistanceQuality;

    /** Qualidade de folhagem */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 FoliageQuality;

    /** Qualidade de shading */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 ShadingQuality;

    /** Resolução de renderização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    float RenderScale;

    /** FPS alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    int32 TargetFPS;

    /** Habilitar Lumen */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bEnableLumen;

    /** Habilitar Nanite */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bEnableNanite;

    /** Habilitar Ray Tracing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bEnableRayTracing;

    /** Habilitar DLSS/FSR */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bEnableUpscaling;

    FRecommendedQualitySettings()
    {
        OverallQualityLevel = 2; // Medium
        TextureQuality = 2;
        ShadowQuality = 2;
        EffectsQuality = 2;
        PostProcessQuality = 2;
        AntiAliasingQuality = 2;
        ViewDistanceQuality = 2;
        FoliageQuality = 2;
        ShadingQuality = 2;
        RenderScale = 1.0f;
        TargetFPS = 60;
        bEnableLumen = false;
        bEnableNanite = false;
        bEnableRayTracing = false;
        bEnableUpscaling = false;
    }
};

/**
 * Sistema de detecção automática de hardware
 */
UCLASS(BlueprintType)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronHardwareDetectionSystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // === Core Detection ===
    
    /** Executar detecção completa de hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void PerformHardwareDetection();

    /** Obter perfil de hardware atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    FHardwareProfile GetCurrentHardwareProfile() const;

    /** Obter configurações de qualidade recomendadas */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    FRecommendedQualitySettings GetRecommendedQualitySettings() const;

    /** Aplicar configurações de qualidade recomendadas */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void ApplyRecommendedQualitySettings();

    // === Benchmark ===
    
    /** Executar benchmark rápido */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void PerformQuickBenchmark();

    /** Executar benchmark completo */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void PerformFullBenchmark();

    /** Obter score do último benchmark */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    float GetLastBenchmarkScore() const;

    // === Monitoring ===
    
    /** Iniciar monitoramento de performance */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void StartPerformanceMonitoring();

    /** Parar monitoramento de performance */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void StopPerformanceMonitoring();

    /** Obter métricas de performance atuais */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    TMap<FString, float> GetCurrentPerformanceMetrics() const;

    // === Events ===
    
    /** Evento quando detecção é completada */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void OnHardwareDetectionCompleted(const FHardwareProfile& Profile);

    /** Evento quando benchmark é completado */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void OnBenchmarkCompleted(float Score);

    /** Evento quando configurações são aplicadas */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void OnQualitySettingsApplied(const FRecommendedQualitySettings& Settings);

protected:
    // === Configuration ===
    
    /** Perfil de hardware atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FHardwareProfile CurrentHardwareProfile;

    /** Configurações recomendadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FRecommendedQualitySettings RecommendedSettings;

    /** Score do último benchmark */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float LastBenchmarkScore;

    /** Monitoramento ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bPerformanceMonitoringActive;

private:
    // === Detection Implementation ===
    void DetectCPUInfo();
    void DetectGPUInfo();
    void DetectMemoryInfo();
    void DetectStorageInfo();
    void DetectPlatformInfo();
    void CalculateHardwareTier();
    void CalculatePerformanceScore();
    
    // === Benchmark Implementation ===
    void RunCPUBenchmark();
    void RunGPUBenchmark();
    void RunMemoryBenchmark();
    void RunStorageBenchmark();
    
    // === Quality Settings Implementation ===
    void CalculateRecommendedSettings();
    void ApplyCPUOptimizations();
    void ApplyGPUOptimizations();
    void ApplyMemoryOptimizations();
    
    // === Monitoring Implementation ===
    void UpdatePerformanceMetrics();
    void AnalyzePerformanceTrends();
    
    // === Utility Methods ===
    EHardwareTier DetermineHardwareTier(const FHardwareProfile& Profile);
    float CalculateOverallScore(const FHardwareProfile& Profile) const;
    bool IsHighEndGPU(const FString& GPUName);
    bool IsMobileDevice() const;
    
    // === Cached Data ===
    TArray<FHardwarePerformanceMetrics> CachedPerformanceMetrics;
    TArray<float> BenchmarkHistory;
    FDateTime LastDetectionTime;
    
    // === Timers ===
    FTimerHandle MonitoringTimer;
    FTimerHandle BenchmarkTimer;
    
    // === State ===
    bool bDetectionCompleted;
    bool bBenchmarkInProgress;
};
