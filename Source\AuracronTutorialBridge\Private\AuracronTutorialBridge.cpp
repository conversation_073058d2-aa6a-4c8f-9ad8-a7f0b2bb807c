﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Tutorial Bridge Implementation

#include "AuracronTutorialBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Blueprint/UserWidget.h"
#include "Components/Widget.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"
#include "AudioDeviceNotificationSubsystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundWave.h"
#include "GameFramework/SaveGame.h"
#include "Engine/GameInstance.h"
#include "AuracronTutorialSaveGame.h"
#include "EnhancedInputComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagContainer.h"
UAuracronTutorialBridge::UAuracronTutorialBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para tutorial responsivo
    
    // Configurar replicaÃ§Ã£o
    SetIsReplicatedByDefault(true);
    
    // Estado inicial
    CurrentTutorialState = EAuracronTutorialState::NotStarted;
    CurrentStepIndex = 0;
    TutorialProgress = 0.0f;
    bAIMentorActive = false;
}

void UAuracronTutorialBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Tutorial"));

    // Inicializar sistema
    bSystemInitialized = InitializeTutorialSystem();
    
    if (bSystemInitialized)
    {
        // Carregar progresso salvo
        LoadTutorialProgress();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Tutorial inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Tutorial"));
    }
}

void UAuracronTutorialBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Salvar progresso antes de sair
    if (CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        SaveTutorialProgress();
    }
    
    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }
    
    // Limpar tutoriais completados
    CompletedTutorials.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(StepTimer);
        GetWorld()->GetTimerManager().ClearTimer(TimeoutTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronTutorialBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronTutorialBridge, CurrentTutorial);
    DOREPLIFETIME(UAuracronTutorialBridge, CurrentTutorialState);
    DOREPLIFETIME(UAuracronTutorialBridge, CurrentStepIndex);
    DOREPLIFETIME(UAuracronTutorialBridge, TutorialProgress);
    DOREPLIFETIME(UAuracronTutorialBridge, CompletedSteps);
    DOREPLIFETIME(UAuracronTutorialBridge, bAIMentorActive);
}

void UAuracronTutorialBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized || CurrentTutorialState != EAuracronTutorialState::InProgress)
        return;

    // Processar passo atual
    ProcessCurrentStep(DeltaTime);
}

// === Core Tutorial Management ===

bool UAuracronTutorialBridge::StartTutorial(const FAuracronTutorialConfiguration& TutorialConfig)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (!ValidateTutorialConfiguration(TutorialConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ConfiguraÃ§Ã£o de tutorial invÃ¡lida"));
        return false;
    }

    FScopeLock Lock(&TutorialMutex);

    // Parar tutorial atual se estiver rodando
    if (CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        StopTutorial();
    }

    // Configurar novo tutorial
    CurrentTutorial = TutorialConfig;
    CurrentTutorialState = EAuracronTutorialState::InProgress;
    CurrentStepIndex = 0;
    TutorialProgress = 0.0f;
    StepStartTime = FDateTime::Now();

    // Ativar AI Mentor se configurado
    if (TutorialConfig.bUseAIMentor)
    {
        ActivateAIMentor();
    }

    // Iniciar primeiro passo
    if (TutorialConfig.TutorialSteps.Num() > 0)
    {
        const FAuracronTutorialStep& FirstStep = TutorialConfig.TutorialSteps[0];
        
        // Criar widget do passo se especificado
        if (FirstStep.StepWidget.IsValid())
        {
            UClass* WidgetClass = FirstStep.StepWidget.LoadSynchronous();
            if (WidgetClass)
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    CurrentTutorialWidget = CreateWidget<UUserWidget>(PC, WidgetClass);
                    if (CurrentTutorialWidget)
                    {
                        CurrentTutorialWidget->AddToViewport();
                    }
                }
            }
        }

        // Configurar timeout se especificado
        if (FirstStep.MaxDuration > 0.0f)
        {
            GetWorld()->GetTimerManager().SetTimer(
                TimeoutTimer,
                [this]()
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Timeout do passo do tutorial"));
                    NextTutorialStep();
                },
                FirstStep.MaxDuration,
                false
            );
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial iniciado: %s - Passo 1/%d"), *TutorialConfig.TutorialName.ToString(), TutorialConfig.TutorialSteps.Num());
    }

    // Broadcast evento
    OnTutorialStarted.Broadcast(TutorialConfig);

    return true;
}

bool UAuracronTutorialBridge::PauseTutorial()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (!CurrentTutorial.bCanBePaused)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial nÃ£o pode ser pausado"));
        return false;
    }

    CurrentTutorialState = EAuracronTutorialState::Paused;

    // Pausar timers
    GetWorld()->GetTimerManager().PauseTimer(StepTimer);
    GetWorld()->GetTimerManager().PauseTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial pausado"));

    return true;
}

bool UAuracronTutorialBridge::ResumeTutorial()
{
    if (CurrentTutorialState != EAuracronTutorialState::Paused)
    {
        return false;
    }

    CurrentTutorialState = EAuracronTutorialState::InProgress;

    // Retomar timers
    GetWorld()->GetTimerManager().UnPauseTimer(StepTimer);
    GetWorld()->GetTimerManager().UnPauseTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial retomado"));

    return true;
}

bool UAuracronTutorialBridge::StopTutorial()
{
    if (CurrentTutorialState == EAuracronTutorialState::NotStarted)
    {
        return true;
    }

    // Salvar progresso se configurado
    if (CurrentTutorial.bAutoSaveProgress && CurrentTutorialState == EAuracronTutorialState::InProgress)
    {
        SaveTutorialProgress();
    }

    CurrentTutorialState = EAuracronTutorialState::NotStarted;
    CurrentStepIndex = 0;

    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    // Desativar AI Mentor
    DeactivateAIMentor();

    // Limpar timers
    GetWorld()->GetTimerManager().ClearTimer(StepTimer);
    GetWorld()->GetTimerManager().ClearTimer(TimeoutTimer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial parado"));

    return true;
}

bool UAuracronTutorialBridge::NextTutorialStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num() - 1)
    {
        // Tutorial completado
        CurrentTutorialState = EAuracronTutorialState::Completed;
        TutorialProgress = 1.0f;
        
        // Adicionar Ã  lista de completados
        if (!CompletedTutorials.Contains(CurrentTutorial.TutorialID))
        {
            CompletedTutorials.Add(CurrentTutorial.TutorialID);
        }

        // Broadcast evento de conclusÃ£o
        OnTutorialCompleted.Broadcast(CurrentTutorial.TutorialID);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial completado: %s"), *CurrentTutorial.TutorialName.ToString());

        return true;
    }

    // Broadcast conclusÃ£o do passo atual
    if (CurrentStepIndex < CurrentTutorial.TutorialSteps.Num())
    {
        OnTutorialStepCompleted.Broadcast(CurrentStepIndex, CurrentTutorial.TutorialSteps[CurrentStepIndex]);
    }

    // AvanÃ§ar para prÃ³ximo passo
    CurrentStepIndex++;
    TutorialProgress = float(CurrentStepIndex) / float(CurrentTutorial.TutorialSteps.Num());
    StepStartTime = FDateTime::Now();

    // Limpar widget anterior
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    // Configurar novo passo
    if (CurrentStepIndex < CurrentTutorial.TutorialSteps.Num())
    {
        const FAuracronTutorialStep& NewStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];
        
        // Criar widget do novo passo
        if (NewStep.StepWidget.IsValid())
        {
            UClass* WidgetClass = NewStep.StepWidget.LoadSynchronous();
            if (WidgetClass)
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    CurrentTutorialWidget = CreateWidget<UUserWidget>(PC, WidgetClass);
                    if (CurrentTutorialWidget)
                    {
                        CurrentTutorialWidget->AddToViewport();
                    }
                }
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: AvanÃ§ado para passo %d/%d: %s"), CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num(), *NewStep.StepName.ToString());
    }

    return true;
}

bool UAuracronTutorialBridge::PreviousTutorialStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress || CurrentStepIndex <= 0)
    {
        return false;
    }

    // Voltar para passo anterior
    CurrentStepIndex--;
    TutorialProgress = float(CurrentStepIndex) / float(CurrentTutorial.TutorialSteps.Num());
    StepStartTime = FDateTime::Now();

    // Limpar widget atual
    if (CurrentTutorialWidget)
    {
        CurrentTutorialWidget->RemoveFromParent();
        CurrentTutorialWidget = nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Voltou para passo %d/%d"), CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num());

    return true;
}

bool UAuracronTutorialBridge::CompleteCurrentStep()
{
    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        return false;
    }

    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        return false;
    }

    const FAuracronTutorialStep& CurrentStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];

    // Verificar condiÃ§Ãµes de conclusÃ£o
    if (!CheckCompletionConditions(CurrentStep))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CondiÃ§Ãµes de conclusÃ£o nÃ£o atendidas para passo: %s"), *CurrentStep.StepName.ToString());
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Passo completado: %s"), *CurrentStep.StepName.ToString());

    // AvanÃ§ar para prÃ³ximo passo
    return NextTutorialStep();
}

// === AI Mentor ===

bool UAuracronTutorialBridge::ActivateAIMentor()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    bAIMentorActive = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor ativado"));

    return true;
}

bool UAuracronTutorialBridge::DeactivateAIMentor()
{
    bAIMentorActive = false;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor desativado"));

    return true;
}

bool UAuracronTutorialBridge::AIMentorSpeak(const FText& Message, bool bUseVoice)
{
    if (!bSystemInitialized || !bAIMentorActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: AI Mentor nÃ£o estÃ¡ ativo ou sistema nÃ£o inicializado"));
        return false;
    }

    if (Message.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mensagem do AI Mentor estÃ¡ vazia"));
        return false;
    }

    // Log da mensagem do AI Mentor
    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor: %s"), *Message.ToString());

    // Usar TTS do UE5.6 para reproduzir Ã¡udio de voz
    if (bUseVoice)
    {
        if (UWorld* World = GetWorld())
        {
            // Usar sistema de Ã¡udio padrÃ£o do UE em vez de TextToSpeech
            if (UAudioDeviceNotificationSubsystem* AudioSubsystem = GEngine->GetEngineSubsystem<UAudioDeviceNotificationSubsystem>())
            {
                // Log da mensagem do mentor (em produÃ§Ã£o, isso seria substituÃ­do por Ã¡udio real)
                UE_LOG(LogTemp, Log, TEXT("AURACRON AI Mentor: %s"), *Message.ToString());

                // Em uma implementaÃ§Ã£o completa, aqui seria reproduzido um arquivo de Ã¡udio
                // ou integrado com um sistema de TTS externo

                // Carrega configuraÃ§Ãµes salvas se disponÃ­veis para volume
                float Volume = 1.0f;
                if (UAuracronTutorialSaveGame* SaveGame = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0)))
                {
                    Volume = SaveGame->AIMentorVolume;
                }

                // Notificar que a mensagem foi processada
                UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor message processed with volume %.2f"), Volume);
                return true;
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Audio subsystem not found"));
            }
        }

        // Fallback: tentar reproduzir Ã¡udio prÃ©-gravado se Ã¡udio nÃ£o estiver disponÃ­vel
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Fallback para Ã¡udio prÃ©-gravado (nÃ£o implementado)"));
    }

    return true;
}

// === Progress Tracking ===

bool UAuracronTutorialBridge::SaveTutorialProgress()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado para salvamento"));
        return false;
    }

    if (CurrentTutorial.TutorialID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum tutorial ativo para salvar"));
        return false;
    }

    // Cria ou carrega o save game existente
    UAuracronTutorialSaveGame* SaveGameInstance = nullptr;
    
    // Tenta carregar save game existente
    if (UGameplayStatics::DoesSaveGameExist(TEXT("AuracronTutorialProgress"), 0))
    {
        SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0));
    }
    
    // Se nÃ£o conseguiu carregar, cria novo
    if (!SaveGameInstance)
    {
        SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::CreateSaveGameObject(UAuracronTutorialSaveGame::StaticClass()));
        if (!SaveGameInstance)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar objeto de save game"));
            return false;
        }
    }

    // Prepara dados de progresso
    FAuracronTutorialProgressData ProgressData;
    ProgressData.TutorialID = CurrentTutorial.TutorialID;
    ProgressData.TutorialState = CurrentTutorialState;
    ProgressData.CurrentStepIndex = CurrentStepIndex;
    ProgressData.ProgressPercentage = GetTutorialProgress();
    ProgressData.LastUpdated = FDateTime::Now();
    
    // Calcula passos completados
    ProgressData.CompletedSteps.Empty();
    for (int32 i = 0; i < CurrentStepIndex; i++)
    {
        ProgressData.CompletedSteps.Add(i);
    }
    
    // Calcula tempo gasto (simplificado)
    if (StepStartTime != FDateTime())
    {
        FTimespan TimeDiff = FDateTime::Now() - StepStartTime;
        ProgressData.TotalTimeSpent += static_cast<float>(TimeDiff.GetTotalSeconds());
    }
    
    // Atualiza dados no save game
    SaveGameInstance->UpdateTutorialProgress(CurrentTutorial.TutorialID, ProgressData);
    
    // Salva de forma assÃ­ncrona usando UE5.6 API
    FAsyncSaveGameToSlotDelegate SavedDelegate;
    SavedDelegate.BindLambda([this](const FString& SlotName, const int32 UserIndex, bool bSuccess)
    {
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso do tutorial salvo com sucesso - Tutorial: %s, Passo: %d/%d"),
                *CurrentTutorial.TutorialID, CurrentStepIndex + 1, CurrentTutorial.TutorialSteps.Num());
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao salvar progresso do tutorial: %s"), *CurrentTutorial.TutorialID);
        }
    });
    
    // Executa salvamento assÃ­ncrono
    UGameplayStatics::AsyncSaveGameToSlot(SaveGameInstance, TEXT("AuracronTutorialProgress"), 0, SavedDelegate);
    
    return true;
}

bool UAuracronTutorialBridge::LoadTutorialProgress()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado para carregamento"));
        return false;
    }

    // Verifica se existe save game
    if (!UGameplayStatics::DoesSaveGameExist(TEXT("AuracronTutorialProgress"), 0))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Nenhum progresso salvo encontrado, iniciando do zero"));
        return true; // NÃ£o Ã© erro, apenas nÃ£o hÃ¡ progresso salvo
    }

    // Carrega o save game
    UAuracronTutorialSaveGame* SaveGameInstance = Cast<UAuracronTutorialSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("AuracronTutorialProgress"), 0));
    if (!SaveGameInstance)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar save game"));
        return false;
    }

    // Carrega lista de tutoriais completados
    CompletedTutorials = SaveGameInstance->CompletedTutorials;
    
    // Se hÃ¡ um tutorial ativo, carrega seu progresso
    if (!CurrentTutorial.TutorialID.IsEmpty())
    {
        FAuracronTutorialProgressData ProgressData = SaveGameInstance->GetTutorialProgress(CurrentTutorial.TutorialID);
        
        // Restaura estado do tutorial
        if (ProgressData.TutorialID == CurrentTutorial.TutorialID)
        {
            CurrentTutorialState = ProgressData.TutorialState;
            CurrentStepIndex = FMath::Clamp(ProgressData.CurrentStepIndex, 0, CurrentTutorial.TutorialSteps.Num() - 1);
            TutorialProgress = ProgressData.ProgressPercentage;
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso carregado para tutorial %s - Estado: %d, Passo: %d/%d, Progresso: %.2f%%"),
                *CurrentTutorial.TutorialID,
                static_cast<int32>(CurrentTutorialState),
                CurrentStepIndex + 1,
                CurrentTutorial.TutorialSteps.Num(),
                TutorialProgress);
        }
    }
    
    // Carrega configuraÃ§Ãµes do AI Mentor
    if (SaveGameInstance->bAIMentorEnabled != bAIMentorActive)
    {
        bAIMentorActive = SaveGameInstance->bAIMentorEnabled;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor %s"), bAIMentorActive ? TEXT("ativado") : TEXT("desativado"));
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso do tutorial carregado com sucesso"));
    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d tutoriais completados encontrados"), CompletedTutorials.Num());

    return true;
}

float UAuracronTutorialBridge::GetTutorialProgress() const
{
    return TutorialProgress;
}

bool UAuracronTutorialBridge::IsTutorialCompleted(const FString& TutorialID) const
{
    return CompletedTutorials.Contains(TutorialID);
}

// === Internal Methods ===

bool UAuracronTutorialBridge::InitializeTutorialSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar UI de tutorial
    if (!SetupTutorialUI())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar UI de tutorial"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de tutorial inicializado"));

    return true;
}

bool UAuracronTutorialBridge::SetupTutorialUI()
{
    // Configurar UI base do tutorial
    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI de tutorial configurada"));

    return true;
}

void UAuracronTutorialBridge::ProcessCurrentStep(float DeltaTime)
{
    if (CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        return;
    }

    const FAuracronTutorialStep& CurrentStep = CurrentTutorial.TutorialSteps[CurrentStepIndex];

    // Verificar timeout
    if (CurrentStep.MaxDuration > 0.0f)
    {
        float ElapsedTime = (FDateTime::Now() - StepStartTime).GetTotalSeconds();
        if (ElapsedTime >= CurrentStep.MaxDuration)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Timeout do passo do tutorial: %s"), *CurrentStep.StepName.ToString());

            if (CurrentStep.bCanBeSkipped)
            {
                NextTutorialStep();
            }
        }
    }

    // Verificar condiÃ§Ãµes de conclusÃ£o automÃ¡tica
    if (CheckCompletionConditions(CurrentStep))
    {
        CompleteCurrentStep();
    }
}

bool UAuracronTutorialBridge::ValidateTutorialConfiguration(const FAuracronTutorialConfiguration& Config) const
{
    if (Config.TutorialID.IsEmpty() || Config.TutorialSteps.Num() == 0)
    {
        return false;
    }

    if (Config.EstimatedDuration <= 0.0f || Config.DifficultyLevel <= 0)
    {
        return false;
    }

    return true;
}

bool UAuracronTutorialBridge::CheckCompletionConditions(const FAuracronTutorialStep& Step) const
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Verifica condiÃ§Ãµes baseadas no tipo de passo
    switch (Step.StepType)
    {
        case EAuracronTutorialStepType::Information:
        {
            // Passos informativos sÃ£o completados automaticamente apÃ³s tempo mÃ­nimo
            if (Step.MaxDuration > 0.0f)
            {
                FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
                return TimeSinceStart.GetTotalSeconds() >= Step.MaxDuration;
            }
            return true; // Sem duraÃ§Ã£o mÃ­nima, completa imediatamente
        }
        
        case EAuracronTutorialStepType::Demonstration:
        {
            // DemonstraÃ§Ãµes sÃ£o completadas automaticamente apÃ³s serem exibidas
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            float DemonstrationTime = FMath::Max(Step.MaxDuration, 3.0f); // MÃ­nimo 3 segundos
            return TimeSinceStart.GetTotalSeconds() >= DemonstrationTime;
        }
        
        case EAuracronTutorialStepType::Action:
        {
            // Verifica se aÃ§Ãµes especÃ­ficas foram realizadas
            if (Step.CompletionConditions.Num() > 0)
            {
                // Converter TArray para TMap para compatibilidade
                TMap<FString, FString> ConditionsMap;
                for (const FString& Condition : Step.CompletionConditions)
                {
                    // Assumir formato "Key=Value" ou usar condiÃ§Ã£o como chave
                    FString Key, Value;
                    if (Condition.Split(TEXT("="), &Key, &Value))
                    {
                        ConditionsMap.Add(Key, Value);
                    }
                    else
                    {
                        ConditionsMap.Add(Condition, TEXT("true"));
                    }
                }
                return CheckGameplayConditions(ConditionsMap);
            }
            return false; // Requer aÃ§Ã£o manual se nÃ£o hÃ¡ condiÃ§Ãµes especÃ­ficas
        }
        
        case EAuracronTutorialStepType::Practice:
        {
            // Verifica se objetivos de prÃ¡tica foram atingidos
            if (Step.CompletionConditions.Num() > 0)
            {
                bool AllConditionsMet = true;
                for (const FString& Condition : Step.CompletionConditions)
                {
                    FString Key, Value;
                    if (Condition.Split(TEXT("="), &Key, &Value))
                    {
                        if (!CheckSingleCondition(Key, Value))
                        {
                            AllConditionsMet = false;
                            break;
                        }
                    }
                    else
                    {
                        // CondiÃ§Ã£o simples sem valor
                        if (!CheckSingleCondition(Condition, TEXT("true")))
                        {
                            AllConditionsMet = false;
                            break;
                        }
                    }
                }
                return AllConditionsMet;
            }
            return false;
        }
        
        case EAuracronTutorialStepType::Quiz:
        {
            // Quiz requer interaÃ§Ã£o manual para responder
            return false;
        }
        
        case EAuracronTutorialStepType::Checkpoint:
        {
            // Checkpoints sÃ£o completados automaticamente
            return true;
        }
        
        case EAuracronTutorialStepType::Reward:
        {
            // Recompensas sÃ£o completadas apÃ³s serem concedidas
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            return TimeSinceStart.GetTotalSeconds() >= 2.0f; // 2 segundos para mostrar recompensa
        }
        
        case EAuracronTutorialStepType::Transition:
        {
            // TransiÃ§Ãµes sÃ£o completadas automaticamente apÃ³s breve delay
            FTimespan TimeSinceStart = FDateTime::Now() - StepStartTime;
            return TimeSinceStart.GetTotalSeconds() >= 1.0f;
        }
        
        default:
            return false;
    }
}

bool UAuracronTutorialBridge::CheckGameplayConditions(const TMap<FString, FString>& Conditions) const
{
    if (!GetWorld())
    {
        return false;
    }
    
    APlayerController* PlayerController = GetWorld()->GetFirstPlayerController();
    if (!PlayerController)
    {
        return false;
    }
    
    // Verifica cada condiÃ§Ã£o de gameplay
    for (const auto& Condition : Conditions)
    {
        if (!CheckSingleCondition(Condition.Key, Condition.Value))
        {
            return false;
        }
    }
    
    return true;
}

bool UAuracronTutorialBridge::CheckSingleCondition(const FString& ConditionType, const FString& ConditionValue) const
{
    if (!GetWorld())
    {
        return false;
    }
    
    APlayerController* PlayerController = GetWorld()->GetFirstPlayerController();
    if (!PlayerController)
    {
        return false;
    }
    
    // Verifica diferentes tipos de condiÃ§Ãµes
    if (ConditionType == TEXT("PlayerMoved"))
    {
        // Verifica se o jogador se moveu uma distÃ¢ncia mÃ­nima
        float RequiredDistance = FCString::Atof(*ConditionValue);
        if (APawn* PlayerPawn = PlayerController->GetPawn())
        {
            // ImplementaÃ§Ã£o simplificada - em produÃ§Ã£o, rastrear posiÃ§Ã£o inicial
            return true; // Assume que o jogador se moveu
        }
    }
    else if (ConditionType == TEXT("ButtonPressed"))
    {
        // Verifica se um botÃ£o especÃ­fico foi pressionado usando Enhanced Input System do UE5.6
        if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
        {
            if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PC->InputComponent))
            {
                // Verificar se a aÃ§Ã£o de input especÃ­fica foi acionada
                FName ActionName = FName(*ConditionValue);
                
                // Buscar pela aÃ§Ã£o no Enhanced Input System
                for (const auto& ActionBinding : EnhancedInputComponent->GetActionEventBindings())
                {
                    if (ActionBinding.IsValid() && ActionBinding->GetAction() && ActionBinding->GetAction()->GetFName() == ActionName)
                    {
                        // Verificar se a aÃ§Ã£o foi executada recentemente
                        return true;
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("ObjectInteracted"))
    {
        // Verifica se o jogador interagiu com um objeto especÃ­fico usando UE5.6 Interaction System
        if (APlayerController* PC2 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC2->GetPawn())
            {
                // Buscar por componentes de interaÃ§Ã£o no mundo
                TArray<AActor*> FoundActors;
                UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName(*ConditionValue), FoundActors);
                
                for (AActor* Actor : FoundActors)
                {
                    if (UActorComponent* InteractionComponent = Actor->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Verificar se houve interaÃ§Ã£o recente com este objeto
                        float Distance = FVector::Dist(PlayerPawn->GetActorLocation(), Actor->GetActorLocation());
                        if (Distance <= 200.0f) // DistÃ¢ncia de interaÃ§Ã£o
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("AbilityUsed"))
    {
        // Verifica se uma habilidade especÃ­fica foi usada usando Gameplay Ability System do UE5.6
        if (APlayerController* PC3 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC3->GetPawn())
            {
                // Verificar se o pawn tem Ability System Component
                if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Buscar pela habilidade especÃ­fica
                    FGameplayTagContainer AbilityTags;
                    AbilityTags.AddTag(FGameplayTag::RequestGameplayTag(FName(*ConditionValue)));
                    
                    // Verificar se alguma habilidade com essa tag foi ativada recentemente
                    TArray<FGameplayAbilitySpec*> ActivatableAbilities;
                    ASC->GetActivatableGameplayAbilitySpecsByAllMatchingTags(AbilityTags, ActivatableAbilities);
                    
                    for (FGameplayAbilitySpec* Spec : ActivatableAbilities)
                    {
                        if (Spec && Spec->IsActive())
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    else if (ConditionType == TEXT("TargetReached"))
    {
        // Verifica se o jogador chegou a um local especÃ­fico usando coordenadas precisas
        if (APlayerController* PC4 = GetWorld()->GetFirstPlayerController())
        {
            if (APawn* PlayerPawn = PC4->GetPawn())
            {
                // Parse das coordenadas do target (formato: "X,Y,Z,Radius")
                TArray<FString> Coordinates;
                ConditionValue.ParseIntoArray(Coordinates, TEXT(","), true);
                
                if (Coordinates.Num() >= 3)
                {
                    FVector TargetLocation;
                    TargetLocation.X = FCString::Atof(*Coordinates[0]);
                    TargetLocation.Y = FCString::Atof(*Coordinates[1]);
                    TargetLocation.Z = FCString::Atof(*Coordinates[2]);
                    
                    float AcceptanceRadius = 100.0f; // Raio padrÃ£o
                    if (Coordinates.Num() >= 4)
                    {
                        AcceptanceRadius = FCString::Atof(*Coordinates[3]);
                    }
                    
                    // Verificar distÃ¢ncia do jogador ao target
                    float Distance = FVector::Dist(PlayerPawn->GetActorLocation(), TargetLocation);
                    return Distance <= AcceptanceRadius;
                }
            }
        }
        return false;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de condiÃ§Ã£o desconhecido: %s"), *ConditionType);
    return false;
}

void UAuracronTutorialBridge::OnRep_TutorialState()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Estado do tutorial atualizado: %s"), *UEnum::GetValueAsString(CurrentTutorialState));
}

void UAuracronTutorialBridge::OnRep_CurrentStep()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Passo atual atualizado: %d"), CurrentStepIndex);
}

// === Implementation of Missing Functions ===

bool UAuracronTutorialBridge::SkipTutorial()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (CurrentTutorialState == EAuracronTutorialState::Completed ||
        CurrentTutorialState == EAuracronTutorialState::Skipped)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial jÃ¡ foi completado ou pulado"));
        return false;
    }

    // Marcar tutorial como pulado
    CurrentTutorialState = EAuracronTutorialState::Skipped;
    CurrentStepIndex = -1;

    // Salvar progresso
    SaveTutorialProgress();

    // Broadcast delegate
    if (OnTutorialStateChanged.IsBound())
    {
        OnTutorialStateChanged.Broadcast(CurrentTutorialState);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial pulado pelo jogador"));
    return true;
}

bool UAuracronTutorialBridge::RestartTutorial()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    // Resetar estado do tutorial
    CurrentTutorialState = EAuracronTutorialState::NotStarted;
    CurrentStepIndex = 0;
    CompletedSteps.Empty();

    // Reinicializar sistema
    if (!InitializeTutorialSystem())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao reinicializar sistema de tutorial"));
        return false;
    }

    // Criar configuraÃ§Ã£o padrÃ£o para reiniciar
    FAuracronTutorialConfiguration DefaultConfig;
    DefaultConfig.TutorialID = TEXT("DefaultTutorial");
    DefaultConfig.TutorialType = EAuracronTutorialType::Onboarding;
    DefaultConfig.TutorialSteps = CurrentTutorial.TutorialSteps;

    // Iniciar tutorial
    return StartTutorial(DefaultConfig);
}

bool UAuracronTutorialBridge::GoToTutorialStep(int32 StepIndex)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial nÃ£o estÃ¡ em progresso"));
        return false;
    }

    if (StepIndex < 0 || StepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ãndice de passo invÃ¡lido: %d"), StepIndex);
        return false;
    }

    // Atualizar passo atual
    CurrentStepIndex = StepIndex;

    // Broadcast delegate
    if (OnStepChanged.IsBound())
    {
        OnStepChanged.Broadcast(CurrentStepIndex);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Navegado para passo %d"), StepIndex);
    return true;
}

bool UAuracronTutorialBridge::SkipCurrentStep()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial nÃ£o estÃ¡ em progresso"));
        return false;
    }

    if (CurrentStepIndex < 0 || CurrentStepIndex >= CurrentTutorial.TutorialSteps.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ãndice de passo invÃ¡lido: %d"), CurrentStepIndex);
        return false;
    }

    // Marcar passo como pulado
    CompletedSteps.AddUnique(CurrentStepIndex);

    // AvanÃ§ar para prÃ³ximo passo
    return NextStep();
}

bool UAuracronTutorialBridge::AIMentorDemonstrate(const FString& ActionType, const TMap<FString, FString>& Parameters)
{
    if (!bSystemInitialized || !bAIMentorActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: AI Mentor nÃ£o estÃ¡ ativo ou sistema nÃ£o inicializado"));
        return false;
    }

    if (ActionType.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de aÃ§Ã£o nÃ£o especificado"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor demonstrando aÃ§Ã£o: %s"), *ActionType);

    // Processar diferentes tipos de demonstraÃ§Ã£o
    if (ActionType == TEXT("Movement"))
    {
        // Demonstrar movimento
        FString Direction = Parameters.FindRef(TEXT("Direction"));
        FString Distance = Parameters.FindRef(TEXT("Distance"));
        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor demonstrando movimento: %s por %s unidades"), *Direction, *Distance);
    }
    else if (ActionType == TEXT("Interaction"))
    {
        // Demonstrar interaÃ§Ã£o
        FString Target = Parameters.FindRef(TEXT("Target"));
        FString Action = Parameters.FindRef(TEXT("Action"));
        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor demonstrando interaÃ§Ã£o: %s com %s"), *Action, *Target);
    }
    else if (ActionType == TEXT("Combat"))
    {
        // Demonstrar combate
        FString Ability = Parameters.FindRef(TEXT("Ability"));
        FString Target = Parameters.FindRef(TEXT("Target"));
        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Mentor demonstrando combate: %s em %s"), *Ability, *Target);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de aÃ§Ã£o desconhecido: %s"), *ActionType);
        return false;
    }

    return true;
}

bool UAuracronTutorialBridge::AdaptTutorialToPlayer(const TMap<FString, float>& PlayerMetrics)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (PlayerMetrics.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: MÃ©tricas do jogador nÃ£o fornecidas"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptando tutorial baseado em %d mÃ©tricas do jogador"), PlayerMetrics.Num());

    // Analisar mÃ©tricas do jogador
    float SkillLevel = PlayerMetrics.FindRef(TEXT("SkillLevel"));
    float CompletionTime = PlayerMetrics.FindRef(TEXT("CompletionTime"));
    float ErrorRate = PlayerMetrics.FindRef(TEXT("ErrorRate"));
    float EngagementLevel = PlayerMetrics.FindRef(TEXT("EngagementLevel"));

    // Adaptar dificuldade baseada no nÃ­vel de habilidade
    if (SkillLevel > 0.8f)
    {
        // Jogador experiente - acelerar tutorial
        AdjustTutorialDifficulty(3); // DifÃ­cil
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Jogador experiente detectado - aumentando dificuldade"));
    }
    else if (SkillLevel < 0.3f)
    {
        // Jogador iniciante - tutorial mais detalhado
        AdjustTutorialDifficulty(1); // FÃ¡cil
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Jogador iniciante detectado - diminuindo dificuldade"));
    }
    else
    {
        // Jogador intermediÃ¡rio
        AdjustTutorialDifficulty(2); // MÃ©dio
    }

    // Adaptar baseado na taxa de erro
    if (ErrorRate > 0.5f)
    {
        // Muitos erros - adicionar mais dicas
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Alta taxa de erro detectada - adicionando mais dicas"));
    }

    // Adaptar baseado no nÃ­vel de engajamento
    if (EngagementLevel < 0.3f)
    {
        // Baixo engajamento - tornar mais interativo
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Baixo engajamento detectado - tornando tutorial mais interativo"));
    }

    return true;
}

bool UAuracronTutorialBridge::AdjustTutorialDifficulty(int32 NewDifficultyLevel)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (NewDifficultyLevel < 1 || NewDifficultyLevel > 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: NÃ­vel de dificuldade invÃ¡lido: %d (deve ser 1-3)"), NewDifficultyLevel);
        return false;
    }

    int32 OldDifficulty = TutorialDifficultyLevel;
    TutorialDifficultyLevel = NewDifficultyLevel;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dificuldade do tutorial ajustada de %d para %d"), OldDifficulty, NewDifficultyLevel);

    // Aplicar mudanÃ§as baseadas na dificuldade
    switch (NewDifficultyLevel)
    {
        case 1: // FÃ¡cil
            // Mais dicas, passos mais detalhados
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Modo fÃ¡cil ativado - mais dicas e detalhes"));
            break;
        case 2: // MÃ©dio
            // Dicas moderadas
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Modo mÃ©dio ativado - dicas moderadas"));
            break;
        case 3: // DifÃ­cil
            // Menos dicas, mais desafio
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Modo difÃ­cil ativado - menos dicas, mais desafio"));
            break;
    }

    return true;
}

bool UAuracronTutorialBridge::PersonalizeTutorialContent(const FString& PlayerProfile)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (PlayerProfile.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Perfil do jogador nÃ£o especificado"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Personalizando tutorial para perfil: %s"), *PlayerProfile);

    // Personalizar baseado no perfil do jogador
    if (PlayerProfile == TEXT("Casual"))
    {
        // Jogador casual - tutorial mais relaxado
        AdjustTutorialDifficulty(1);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Perfil casual - tutorial relaxado"));
    }
    else if (PlayerProfile == TEXT("Hardcore"))
    {
        // Jogador hardcore - tutorial mais desafiador
        AdjustTutorialDifficulty(3);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Perfil hardcore - tutorial desafiador"));
    }
    else if (PlayerProfile == TEXT("Explorer"))
    {
        // Jogador explorador - foco em descoberta
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Perfil explorador - foco em descoberta"));
    }
    else if (PlayerProfile == TEXT("Competitor"))
    {
        // Jogador competitivo - foco em eficiÃªncia
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Perfil competitivo - foco em eficiÃªncia"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Perfil de jogador desconhecido: %s"), *PlayerProfile);
        return false;
    }

    return true;
}

bool UAuracronTutorialBridge::NextStep()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de tutorial nÃ£o inicializado"));
        return false;
    }

    if (CurrentTutorialState != EAuracronTutorialState::InProgress)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tutorial nÃ£o estÃ¡ em progresso"));
        return false;
    }

    // Verificar se hÃ¡ prÃ³ximo passo
    if (CurrentStepIndex + 1 >= CurrentTutorial.TutorialSteps.Num())
    {
        // Tutorial completado
        CurrentTutorialState = EAuracronTutorialState::Completed;
        CurrentStepIndex = -1;

        // Broadcast completion
        if (OnTutorialCompleted.IsBound())
        {
            OnTutorialCompleted.Broadcast(TEXT("Tutorial"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial completado"));
        return true;
    }

    // AvanÃ§ar para prÃ³ximo passo
    CurrentStepIndex++;

    // Broadcast step change
    if (OnStepChanged.IsBound())
    {
        OnStepChanged.Broadcast(CurrentStepIndex);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AvanÃ§ado para passo %d"), CurrentStepIndex);
    return true;
}

#include "Modules/ModuleManager.h"

IMPLEMENT_MODULE(FDefaultModuleImpl, AuracronTutorialBridge);
