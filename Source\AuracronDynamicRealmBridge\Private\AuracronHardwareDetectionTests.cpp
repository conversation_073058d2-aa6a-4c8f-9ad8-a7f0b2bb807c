#include "AuracronHardwareDetectionTests.h"
#include "Engine/Engine.h"

UAuracronHardwareDetectionTests::UAuracronHardwareDetectionTests()
{
    TestsRun = 0;
    TestsPassed = 0;
    
    // Criar sistema de teste
    TestHardwareSystem = CreateDefaultSubobject<UAuracronSimpleHardwareDetection>(TEXT("TestHardwareSystem"));
    TestComponent = CreateDefaultSubobject<UAuracronHardwareDetectionComponent>(TEXT("TestComponent"));
}

bool UAuracronHardwareDetectionTests::RunAllTests()
{
    UE_LOG(LogTemp, Warning, TEXT("=== STARTING HARDWARE DETECTION TESTS ==="));
    
    TestsRun = 0;
    TestsPassed = 0;
    
    // Executar todos os testes
    TestBasicHardwareDetection();
    TestQualityCalculation();
    TestSettingsApplication();
    TestHardwareDetectionComponent();
    TestCustomProfiles();
    TestQualityLimits();
    TestPerformance();
    
    // Resultado final
    bool bAllPassed = (TestsPassed == TestsRun);
    UE_LOG(LogTemp, Warning, TEXT("=== TESTS COMPLETED: %d/%d PASSED ==="), TestsPassed, TestsRun);
    
    if (bAllPassed)
    {
        UE_LOG(LogTemp, Log, TEXT("ALL TESTS PASSED! Hardware Detection System is working correctly."));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SOME TESTS FAILED! Please check the logs above."));
    }
    
    return bAllPassed;
}

bool UAuracronHardwareDetectionTests::TestBasicHardwareDetection()
{
    if (!TestHardwareSystem)
    {
        LogTest(TEXT("BasicHardwareDetection"), false, TEXT("TestHardwareSystem is null"));
        return false;
    }
    
    // Detectar hardware real
    FSimpleHardwareInfo HardwareInfo = TestHardwareSystem->DetectHardware();
    
    // Validar resultados
    bool bValid = ValidateHardwareInfo(HardwareInfo);
    
    FString Details = FString::Printf(TEXT("GPU: %s, VRAM: %dMB, RAM: %.1fGB, Cores: %d"), 
                                     *HardwareInfo.GPUName, HardwareInfo.VideoMemoryMB, 
                                     HardwareInfo.TotalRAMGB, HardwareInfo.CPUCores);
    
    LogTest(TEXT("BasicHardwareDetection"), bValid, Details);
    return bValid;
}

bool UAuracronHardwareDetectionTests::TestQualityCalculation()
{
    if (!TestHardwareSystem)
    {
        LogTest(TEXT("QualityCalculation"), false, TEXT("TestHardwareSystem is null"));
        return false;
    }
    
    // Testar com hardware high-end
    FSimpleHardwareInfo HighEndHW = CreateMockHardware(TEXT("RTX 4090"), 24576, 32.0f, 16, true);
    FSimpleQualitySettings HighEndSettings = TestHardwareSystem->CalculateRecommendedSettings(HighEndHW);
    
    bool bHighEndValid = (HighEndSettings.OverallQuality >= 3) && HighEndSettings.bEnableRayTracing;
    
    // Testar com hardware low-end
    FSimpleHardwareInfo LowEndHW = CreateMockHardware(TEXT("GTX 1050"), 2048, 8.0f, 4, false);
    FSimpleQualitySettings LowEndSettings = TestHardwareSystem->CalculateRecommendedSettings(LowEndHW);
    
    bool bLowEndValid = (LowEndSettings.OverallQuality <= 2) && !LowEndSettings.bEnableRayTracing;
    
    bool bOverallValid = bHighEndValid && bLowEndValid && ValidateQualitySettings(HighEndSettings) && ValidateQualitySettings(LowEndSettings);
    
    FString Details = FString::Printf(TEXT("HighEnd Quality: %d, LowEnd Quality: %d"), 
                                     HighEndSettings.OverallQuality, LowEndSettings.OverallQuality);
    
    LogTest(TEXT("QualityCalculation"), bOverallValid, Details);
    return bOverallValid;
}

bool UAuracronHardwareDetectionTests::TestSettingsApplication()
{
    if (!TestHardwareSystem)
    {
        LogTest(TEXT("SettingsApplication"), false, TEXT("TestHardwareSystem is null"));
        return false;
    }
    
    // Criar configurações de teste
    FSimpleQualitySettings TestSettings;
    TestSettings.OverallQuality = 2;
    TestSettings.TextureQuality = 2;
    TestSettings.ResolutionScale = 0.8f;
    TestSettings.TargetFPS = 60;
    
    // Aplicar configurações (não deve crashar)
    TestHardwareSystem->ApplyQualitySettings(TestSettings);
    
    // Verificar se GameUserSettings existe e foi modificado
    UGameUserSettings* GameSettings = UGameUserSettings::GetGameUserSettings();
    bool bValid = (GameSettings != nullptr);
    
    if (bValid)
    {
        // Verificar se algumas configurações foram aplicadas
        bValid = (GameSettings->GetOverallScalabilityLevel() == TestSettings.OverallQuality);
    }
    
    LogTest(TEXT("SettingsApplication"), bValid, bValid ? TEXT("Settings applied successfully") : TEXT("Failed to apply settings"));
    return bValid;
}

bool UAuracronHardwareDetectionTests::TestHardwareDetectionComponent()
{
    if (!TestComponent)
    {
        LogTest(TEXT("HardwareDetectionComponent"), false, TEXT("TestComponent is null"));
        return false;
    }
    
    // Testar detecção via componente
    FSimpleHardwareInfo ComponentHW = TestComponent->DetectHardware();
    bool bValid = ValidateHardwareInfo(ComponentHW);
    
    if (bValid)
    {
        // Testar se o componente mantém o estado
        bValid = TestComponent->IsHardwareDetected();
    }
    
    LogTest(TEXT("HardwareDetectionComponent"), bValid, TEXT("Component detection and state management"));
    return bValid;
}

bool UAuracronHardwareDetectionTests::TestCustomProfiles()
{
    // Este teste seria implementado com um GameMode mock
    // Por simplicidade, vamos apenas validar a lógica básica
    
    FSimpleQualitySettings CustomProfile;
    CustomProfile.OverallQuality = 4;
    CustomProfile.bEnableRayTracing = true;
    
    bool bValid = ValidateQualitySettings(CustomProfile);
    
    LogTest(TEXT("CustomProfiles"), bValid, TEXT("Custom profile validation"));
    return bValid;
}

bool UAuracronHardwareDetectionTests::TestQualityLimits()
{
    if (!TestComponent)
    {
        LogTest(TEXT("QualityLimits"), false, TEXT("TestComponent is null"));
        return false;
    }
    
    // Configurar limites
    TestComponent->MinimumQualityLevel = 1;
    TestComponent->MaximumQualityLevel = 3;
    TestComponent->QualityMultiplier = 0.5f;
    
    // Testar com hardware que normalmente daria qualidade 4
    FSimpleHardwareInfo HighEndHW = CreateMockHardware(TEXT("RTX 4090"), 24576, 32.0f, 16, true);
    FSimpleQualitySettings LimitedSettings = TestComponent->CalculateRecommendedSettings(HighEndHW);
    
    // Deve estar limitado ao máximo (3)
    bool bValid = (LimitedSettings.OverallQuality <= 3) && (LimitedSettings.OverallQuality >= 1);
    
    LogTest(TEXT("QualityLimits"), bValid, FString::Printf(TEXT("Limited quality: %d"), LimitedSettings.OverallQuality));
    return bValid;
}

bool UAuracronHardwareDetectionTests::TestPerformance()
{
    if (!TestHardwareSystem)
    {
        LogTest(TEXT("Performance"), false, TEXT("TestHardwareSystem is null"));
        return false;
    }
    
    // Medir tempo de detecção
    double StartTime = FPlatformTime::Seconds();
    
    for (int32 i = 0; i < 10; ++i)
    {
        FSimpleHardwareInfo HW = TestHardwareSystem->DetectHardware();
        FSimpleQualitySettings Settings = TestHardwareSystem->CalculateRecommendedSettings(HW);
    }
    
    double EndTime = FPlatformTime::Seconds();
    double TotalTime = EndTime - StartTime;
    double AvgTime = TotalTime / 10.0;
    
    // Deve ser rápido (menos de 100ms por detecção)
    bool bValid = (AvgTime < 0.1);
    
    LogTest(TEXT("Performance"), bValid, FString::Printf(TEXT("Avg detection time: %.2fms"), AvgTime * 1000.0));
    return bValid;
}

FSimpleHardwareInfo UAuracronHardwareDetectionTests::CreateMockHardware(const FString& GPUName, int32 VRAM, float RAM, int32 Cores, bool bRayTracing)
{
    FSimpleHardwareInfo MockHW;
    MockHW.GPUName = GPUName;
    MockHW.VideoMemoryMB = VRAM;
    MockHW.TotalRAMGB = RAM;
    MockHW.AvailableRAMGB = RAM * 0.7f; // 70% disponível
    MockHW.CPUCores = Cores;
    MockHW.bSupportsRayTracing = bRayTracing;
    MockHW.bSupportsMeshShaders = bRayTracing; // Assumir que RT implica mesh shaders
    
    return MockHW;
}

bool UAuracronHardwareDetectionTests::ValidateHardwareInfo(const FSimpleHardwareInfo& HardwareInfo)
{
    // Validações básicas
    if (HardwareInfo.GPUName.IsEmpty() || HardwareInfo.GPUName == TEXT("Unknown"))
    {
        return false;
    }
    
    if (!IsInRange(HardwareInfo.VideoMemoryMB, 1024, 65536)) // 1GB - 64GB
    {
        return false;
    }
    
    if (!IsInRange(HardwareInfo.TotalRAMGB, 4.0f, 256.0f)) // 4GB - 256GB
    {
        return false;
    }
    
    if (!IsInRange(HardwareInfo.CPUCores, 1, 128)) // 1 - 128 cores
    {
        return false;
    }
    
    return true;
}

bool UAuracronHardwareDetectionTests::ValidateQualitySettings(const FSimpleQualitySettings& Settings)
{
    // Validar ranges de qualidade
    if (!IsInRange(Settings.OverallQuality, 0, 4)) return false;
    if (!IsInRange(Settings.TextureQuality, 0, 4)) return false;
    if (!IsInRange(Settings.ShadowQuality, 0, 4)) return false;
    if (!IsInRange(Settings.PostProcessQuality, 0, 4)) return false;
    if (!IsInRange(Settings.AntiAliasingQuality, 0, 4)) return false;
    if (!IsInRange(Settings.ViewDistanceQuality, 0, 4)) return false;
    if (!IsInRange(Settings.FoliageQuality, 0, 4)) return false;
    if (!IsInRange(Settings.ShadingQuality, 0, 4)) return false;
    
    if (!IsInRange(Settings.ResolutionScale, 0.1f, 2.0f)) return false;
    if (!IsInRange(Settings.TargetFPS, 15, 240)) return false;
    
    return true;
}

void UAuracronHardwareDetectionTests::LogTest(const FString& TestName, bool bPassed, const FString& Details)
{
    TestsRun++;
    if (bPassed)
    {
        TestsPassed++;
        UE_LOG(LogTemp, Log, TEXT("[PASS] %s: %s"), *TestName, *Details);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("[FAIL] %s: %s"), *TestName, *Details);
    }
}

bool UAuracronHardwareDetectionTests::IsInRange(int32 Value, int32 Min, int32 Max)
{
    return (Value >= Min && Value <= Max);
}

bool UAuracronHardwareDetectionTests::IsInRange(float Value, float Min, float Max)
{
    return (Value >= Min && Value <= Max);
}
