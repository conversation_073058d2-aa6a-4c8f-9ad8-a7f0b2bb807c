// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Combat Bridge Settings Implementation

#include "AuracronCombatBridgeSettings.h"
#include "Engine/Engine.h"

UAuracronCombatBridgeSettings::UAuracronCombatBridgeSettings()
{
    // Initialize default Enhanced Input configuration
    DefaultEnhancedInputConfig.ComboWindowTime = 0.8f;
    DefaultEnhancedInputConfig.MaxComboChain = 5;

    // Initialize default AI Combat configuration
    DefaultAICombatConfig.BehaviorType = EAuracronAICombatBehavior::Tactical;
    DefaultAICombatConfig.AggressionLevel = 0.5f;
    DefaultAICombatConfig.ReactionTime = 0.3f;
    DefaultAICombatConfig.PreferredCombatRange = 800.0f;
    DefaultAICombatConfig.bEnableLearning = true;
    DefaultAICombatConfig.LearningRate = 0.1f;

    // Initialize default Elemental Damage configuration
    DefaultElementalDamageConfig.PrimaryElement = EAuracronElementalType::None;
    DefaultElementalDamageConfig.SecondaryElement = EAuracronElementalType::None;
    DefaultElementalDamageConfig.ElementalMultiplier = 1.0f;
    DefaultElementalDamageConfig.StatusEffectChance = 0.1f;
    DefaultElementalDamageConfig.StatusEffectDuration = 5.0f;

    // Initialize default Advanced Destruction configuration
    DefaultAdvancedDestructionConfig.bEnableChaosDestruction = true;
    DefaultAdvancedDestructionConfig.DestructionThreshold = 1000.0f;
    DefaultAdvancedDestructionConfig.FractureImpulse = 5000.0f;
    DefaultAdvancedDestructionConfig.DebrisLifetime = 30.0f;
    DefaultAdvancedDestructionConfig.bEnableProceduralDamage = true;
    DefaultAdvancedDestructionConfig.DamagePropagationRadius = 500.0f;
}

const UAuracronCombatBridgeSettings* UAuracronCombatBridgeSettings::GetCombatBridgeSettings()
{
    return GetDefault<UAuracronCombatBridgeSettings>();
}

bool UAuracronCombatBridgeSettings::AreAdvancedCombatFeaturesEnabled()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->bEnableAdvancedCombatFeatures : true;
}

float UAuracronCombatBridgeSettings::GetGlobalCombatTickInterval()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->GlobalCombatTickInterval : 0.05f;
}

FAuracronEnhancedInputConfig UAuracronCombatBridgeSettings::GetDefaultEnhancedInputConfig()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->DefaultEnhancedInputConfig : FAuracronEnhancedInputConfig();
}

FAuracronAICombatConfig UAuracronCombatBridgeSettings::GetDefaultAICombatConfig()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->DefaultAICombatConfig : FAuracronAICombatConfig();
}

FAuracronElementalDamageConfig UAuracronCombatBridgeSettings::GetDefaultElementalDamageConfig()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->DefaultElementalDamageConfig : FAuracronElementalDamageConfig();
}

FAuracronAdvancedDestructionConfig UAuracronCombatBridgeSettings::GetDefaultAdvancedDestructionConfig()
{
    const UAuracronCombatBridgeSettings* Settings = GetCombatBridgeSettings();
    return Settings ? Settings->DefaultAdvancedDestructionConfig : FAuracronAdvancedDestructionConfig();
}

FName UAuracronCombatBridgeSettings::GetCategoryName() const
{
    return TEXT("AURACRON");
}

// GetSectionText and GetSectionDescription methods removed - not available in UE 5.6

#if WITH_EDITOR
void UAuracronCombatBridgeSettings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    if (PropertyChangedEvent.Property)
    {
        const FName PropertyName = PropertyChangedEvent.Property->GetFName();
        
        // Validate and clamp values
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, GlobalCombatTickInterval))
        {
            GlobalCombatTickInterval = FMath::Clamp(GlobalCombatTickInterval, 0.01f, 1.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, GlobalComboWindowMultiplier))
        {
            GlobalComboWindowMultiplier = FMath::Clamp(GlobalComboWindowMultiplier, 0.1f, 3.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, GlobalAIReactionTimeMultiplier))
        {
            GlobalAIReactionTimeMultiplier = FMath::Clamp(GlobalAIReactionTimeMultiplier, 0.1f, 5.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, GlobalElementalDamageMultiplier))
        {
            GlobalElementalDamageMultiplier = FMath::Clamp(GlobalElementalDamageMultiplier, 0.1f, 5.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, GlobalDestructionForceMultiplier))
        {
            GlobalDestructionForceMultiplier = FMath::Clamp(GlobalDestructionForceMultiplier, 0.1f, 10.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, MaxCombatEventLogEntries))
        {
            MaxCombatEventLogEntries = FMath::Clamp(MaxCombatEventLogEntries, 100, 10000);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, MaxAILearningDataEntries))
        {
            MaxAILearningDataEntries = FMath::Clamp(MaxAILearningDataEntries, 100, 10000);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, MaxActiveElementalEffectsPerActor))
        {
            MaxActiveElementalEffectsPerActor = FMath::Clamp(MaxActiveElementalEffectsPerActor, 1, 20);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, MaxDebrisObjectsPerEvent))
        {
            MaxDebrisObjectsPerEvent = FMath::Clamp(MaxDebrisObjectsPerEvent, 10, 1000);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, MaxConcurrentCombatEffects))
        {
            MaxConcurrentCombatEffects = FMath::Clamp(MaxConcurrentCombatEffects, 10, 500);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, LODDistance1))
        {
            LODDistance1 = FMath::Clamp(LODDistance1, 500.0f, 5000.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, LODDistance2))
        {
            LODDistance2 = FMath::Clamp(LODDistance2, 1000.0f, 10000.0f);
            // Ensure LOD2 is greater than LOD1
            if (LODDistance2 <= LODDistance1)
            {
                LODDistance2 = LODDistance1 + 500.0f;
            }
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, NetworkUpdateFrequency))
        {
            NetworkUpdateFrequency = FMath::Clamp(NetworkUpdateFrequency, 5.0f, 60.0f);
        }
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, AnalyticsUpdateFrequency))
        {
            AnalyticsUpdateFrequency = FMath::Clamp(AnalyticsUpdateFrequency, 0.1f, 10.0f);
        }

        // Validate analytics export directory
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UAuracronCombatBridgeSettings, AnalyticsExportDirectory))
        {
            if (AnalyticsExportDirectory.IsEmpty())
            {
                AnalyticsExportDirectory = TEXT("Saved/CombatAnalytics/");
            }
            
            // Ensure directory ends with slash
            if (!AnalyticsExportDirectory.EndsWith(TEXT("/")))
            {
                AnalyticsExportDirectory += TEXT("/");
            }
        }

        // Log configuration changes in development builds
        UE_LOG(LogTemp, Log, TEXT("AURACRON Combat Bridge Settings: %s changed"), *PropertyName.ToString());
    }
}
#endif
