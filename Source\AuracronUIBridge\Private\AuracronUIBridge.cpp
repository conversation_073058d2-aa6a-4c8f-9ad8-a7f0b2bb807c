﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Interface do UsuÃ¡rio Bridge Implementation

#include "AuracronUIBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/HUD.h"
#include "Blueprint/UserWidget.h"
#include "UMG.h"
#include "CommonActivatableWidget.h"
#include "CommonButtonBase.h"
#include "CommonTextBlock.h"
#include "CommonInputSubsystem.h"
#include "CommonInputSettings.h"
#include "Components/CanvasPanel.h"
#include "Components/VerticalBox.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ProgressBar.h"
#include "Components/Slider.h"
#include "Animation/WidgetAnimation.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"
UAuracronUIBridge::UAuracronUIBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.033f; // 30 FPS para UI responsiva
    
    // Configurar replicaÃ§Ã£o
    SetIsReplicatedByDefault(true);
    
    // ConfiguraÃ§Ãµes padrÃ£o de UI
    UIConfiguration.UIScale = 1.0f;
    UIConfiguration.UIOpacity = 1.0f;
    UIConfiguration.InputPlatform = EAuracronInputPlatform::Auto;
    UIConfiguration.bUseUIAnimations = true;
    UIConfiguration.AnimationSpeed = 1.0f;
    UIConfiguration.bUseUIParticleEffects = true;
    UIConfiguration.bUseUISounds = true;
    UIConfiguration.UISoundVolume = 0.8f;
    UIConfiguration.bUseHapticFeedback = true;
    UIConfiguration.HapticIntensity = 0.5f;
    UIConfiguration.bHighContrastMode = false;
    UIConfiguration.bColorBlindSupport = false;
    UIConfiguration.ColorBlindnessType = TEXT("None");
    UIConfiguration.FontScale = 1.0f;
    UIConfiguration.bScreenReaderSupport = false;
    UIConfiguration.UILanguage = TEXT("en");
    UIConfiguration.UIRegion = TEXT("US");
    UIConfiguration.bUseResponsiveLayout = true;
    UIConfiguration.ReferenceResolution = FVector2D(1920.0f, 1080.0f);
    UIConfiguration.bUseSafeZones = true;
    UIConfiguration.SafeZoneMargin = 0.05f;
    
    // ConfiguraÃ§Ãµes padrÃ£o do minimapa
    MinimapConfiguration.MinimapSize = FVector2D(200.0f, 200.0f);
    MinimapConfiguration.MinimapPosition = FVector2D(0.85f, 0.15f);
    MinimapConfiguration.MinimapZoom = 1.0f;
    MinimapConfiguration.MinimapRotation = 0.0f;
    MinimapConfiguration.bShowAllRealms = true;
    MinimapConfiguration.bShowAlliedPlayers = true;
    MinimapConfiguration.bShowEnemyPlayers = false;
    MinimapConfiguration.bShowObjectives = true;
    MinimapConfiguration.bShowWards = true;
    MinimapConfiguration.bUseTeamColors = true;
    MinimapConfiguration.AlliedTeamColor = FLinearColor::Blue;
    MinimapConfiguration.EnemyTeamColor = FLinearColor::Red;
    MinimapConfiguration.ObjectiveColor = FLinearColor::Yellow;
    MinimapConfiguration.bRealTimeUpdate = true;
    MinimapConfiguration.UpdateFrequency = 10;
    MinimapConfiguration.bUseFogOfWar = true;
    MinimapConfiguration.VisionRadius = 1500.0f;
    
    // ConfiguraÃ§Ãµes padrÃ£o do HUD de combate
    CombatHUDConfiguration.bShowHealthBar = true;
    CombatHUDConfiguration.bShowManaBar = true;
    CombatHUDConfiguration.bShowAbilityCooldowns = true;
    CombatHUDConfiguration.bShowDamageIndicators = true;
    CombatHUDConfiguration.bShowHealingIndicators = true;
    CombatHUDConfiguration.bShowStatusEffects = true;
    CombatHUDConfiguration.bShowExperienceBar = true;
    CombatHUDConfiguration.bShowGold = true;
    CombatHUDConfiguration.bShowKDA = true;
    CombatHUDConfiguration.bShowMatchTimer = true;
    CombatHUDConfiguration.HealthBarPosition = FVector2D(0.1f, 0.9f);
    CombatHUDConfiguration.HealthBarSize = FVector2D(300.0f, 20.0f);
    CombatHUDConfiguration.ManaBarPosition = FVector2D(0.1f, 0.95f);
    CombatHUDConfiguration.ManaBarSize = FVector2D(300.0f, 15.0f);
    CombatHUDConfiguration.AbilitiesPosition = FVector2D(0.5f, 0.95f);
    CombatHUDConfiguration.AbilityIconSize = 64.0f;
    CombatHUDConfiguration.AbilitySpacing = 8.0f;
    CombatHUDConfiguration.bUseCompactLayout = false;
    CombatHUDConfiguration.bShowTooltips = true;
    CombatHUDConfiguration.TooltipDelay = 0.5f;
    CombatHUDConfiguration.bUseTeamColors = true;
    CombatHUDConfiguration.HealthColor = FLinearColor::Green;
    CombatHUDConfiguration.ManaColor = FLinearColor::Blue;
    CombatHUDConfiguration.ExperienceColor = FLinearColor::Yellow;
    CombatHUDConfiguration.CooldownColor = FLinearColor(0.2f, 0.2f, 0.2f, 0.8f);
    
    // ConfiguraÃ§Ãµes padrÃ£o de input cross-platform
    CrossPlatformInputConfiguration.MouseSensitivity = 1.0f;
    CrossPlatformInputConfiguration.GamepadSensitivity = 1.5f;
    CrossPlatformInputConfiguration.TouchSensitivity = 1.2f;
    CrossPlatformInputConfiguration.bInvertYAxis = false;
    CrossPlatformInputConfiguration.bUseAutoAimOnMobile = true;
    CrossPlatformInputConfiguration.AutoAimIntensity = 0.3f;
    CrossPlatformInputConfiguration.bUseTouchGestures = true;
    CrossPlatformInputConfiguration.TouchButtonSize = 64.0f;
    CrossPlatformInputConfiguration.TouchButtonOpacity = 0.7f;
    CrossPlatformInputConfiguration.bUseVibration = true;
    CrossPlatformInputConfiguration.VibrationIntensity = 0.5f;
}

void UAuracronUIBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Interface do UsuÃ¡rio"));

    // Obter referÃªncias aos subsistemas
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            CommonInputSubsystem = UCommonInputSubsystem::Get(PC->GetLocalPlayer());
            if (!CommonInputSubsystem)
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: CommonInputSubsystem nÃ£o encontrado"));
            }

            if (APawn* ControlledPawn = PC->GetPawn())
            {
                EnhancedInputComponent = Cast<UEnhancedInputComponent>(ControlledPawn->GetComponentByClass(UEnhancedInputComponent::StaticClass()));
                if (!EnhancedInputComponent)
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent nÃ£o encontrado"));
                }
            }
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeUISystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para atualizaÃ§Ãµes
        GetWorld()->GetTimerManager().SetTimer(
            AnimationTimer,
            [this]()
            {
                ProcessUIAnimations(0.033f); // 30 FPS
            },
            0.033f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            MinimapUpdateTimer,
            [this]()
            {
                UpdateMinimap3D();
            },
            1.0f / MinimapConfiguration.UpdateFrequency,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Interface do UsuÃ¡rio inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Interface do UsuÃ¡rio"));
    }
}

void UAuracronUIBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(AnimationTimer);
        GetWorld()->GetTimerManager().ClearTimer(MinimapUpdateTimer);
    }
    
    // Destruir todos os widgets ativos
    for (auto& WidgetPair : ActiveUIWidgets)
    {
        if (IsValid(WidgetPair.Value))
        {
            UCommonUserWidget* Widget = WidgetPair.Value;
            
            // Se for um CommonActivatableWidget, desativa primeiro
            if (UCommonActivatableWidget* ActivatableWidget = Cast<UCommonActivatableWidget>(Widget))
            {
                ActivatableWidget->DeactivateWidget();
            }
            
            // Remove do viewport se estiver adicionado diretamente
            if (Widget->IsInViewport())
            {
                Widget->RemoveFromParent();
            }
            
            // Remove do parent se tiver um
            if (Widget->GetParent())
            {
                Widget->RemoveFromParent();
            }
            
            // Para animaÃ§Ãµes ativas
            Widget->StopAllAnimations();
            
            // Marca para garbage collection
            Widget->MarkAsGarbage();
            
            UE_LOG(LogTemp, Log, TEXT("Widget %s destruÃ­do com sucesso"), *UEnum::GetValueAsString(WidgetPair.Key));
        }
    }
    ActiveUIWidgets.Empty();
    
    // Force garbage collection para limpar widgets imediatamente
    if (GEngine)
    {
        GEngine->ForceGarbageCollection(true);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronUIBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronUIBridge, UIConfiguration);
    DOREPLIFETIME(UAuracronUIBridge, UIStates);
    DOREPLIFETIME(UAuracronUIBridge, CurrentInputPlatform);
}

void UAuracronUIBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar animaÃ§Ãµes de UI
    ProcessUIAnimations(DeltaTime);
    
    // Atualizar layout responsivo se necessÃ¡rio
    if (UIConfiguration.bUseResponsiveLayout)
    {
        UpdateResponsiveLayout();
    }
}

// === Core UI Management ===

bool UAuracronUIBridge::ShowUI(EAuracronUIType UIType, bool bAnimate)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de UI nÃ£o inicializado"));
        return false;
    }

    if (UIType == EAuracronUIType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de UI invÃ¡lido"));
        return false;
    }

    // Verificar se jÃ¡ estÃ¡ visÃ­vel
    if (IsUIVisible(UIType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: UI jÃ¡ estÃ¡ visÃ­vel: %d"), (int32)UIType);
        return false;
    }

    // Obter ou criar widget
    UCommonUserWidget* Widget = GetUIWidget(UIType);
    if (!Widget)
    {
        // Tentar criar widget se tiver classe registrada
        if (RegisteredWidgetClasses.Contains(UIType))
        {
            Widget = CreateUIWidget(UIType, RegisteredWidgetClasses[UIType]);
        }
        
        if (!Widget)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar widget para UI: %d"), (int32)UIType);
            return false;
        }
    }

    // Mostrar widget
    Widget->SetVisibility(ESlateVisibility::Visible);
    Widget->AddToViewport();

    // Atualizar estado
    // Procurar entrada existente ou criar nova
    FAuracronUIStateEntry* ExistingEntry = UIStates.FindByPredicate([UIType](const FAuracronUIStateEntry& Entry) {
        return Entry.UIType == UIType;
    });

    if (ExistingEntry)
    {
        ExistingEntry->UIState = EAuracronUIState::Visible;
    }
    else
    {
        UIStates.Add(FAuracronUIStateEntry(UIType, EAuracronUIState::Visible));
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI mostrada: %d"), (int32)UIType);

    // Broadcast evento
    OnUIShown.Broadcast(UIType);

    return true;
}

bool UAuracronUIBridge::HideUI(EAuracronUIType UIType, bool bAnimate)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema de UI nÃ£o inicializado"));
        return false;
    }

    if (!IsUIVisible(UIType))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: UI jÃ¡ estÃ¡ escondida: %d"), (int32)UIType);
        return false;
    }

    UCommonUserWidget* Widget = GetUIWidget(UIType);
    if (!Widget)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Widget nÃ£o encontrado para UI: %d"), (int32)UIType);
        return false;
    }

    // Esconder widget
    Widget->SetVisibility(ESlateVisibility::Hidden);
    Widget->RemoveFromParent();

    // Atualizar estado
    // Procurar entrada existente ou criar nova
    FAuracronUIStateEntry* ExistingEntry = UIStates.FindByPredicate([UIType](const FAuracronUIStateEntry& Entry) {
        return Entry.UIType == UIType;
    });

    if (ExistingEntry)
    {
        ExistingEntry->UIState = EAuracronUIState::Hidden;
    }
    else
    {
        UIStates.Add(FAuracronUIStateEntry(UIType, EAuracronUIState::Hidden));
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: UI escondida: %d"), (int32)UIType);

    // Broadcast evento
    OnUIHidden.Broadcast(UIType);

    return true;
}

bool UAuracronUIBridge::ToggleUI(EAuracronUIType UIType, bool bAnimate)
{
    if (IsUIVisible(UIType))
    {
        return HideUI(UIType, bAnimate);
    }
    else
    {
        return ShowUI(UIType, bAnimate);
    }
}

bool UAuracronUIBridge::IsUIVisible(EAuracronUIType UIType) const
{
    const FAuracronUIStateEntry* Entry = UIStates.FindByPredicate([UIType](const FAuracronUIStateEntry& StateEntry) {
        return StateEntry.UIType == UIType;
    });
    return Entry && Entry->UIState == EAuracronUIState::Visible;
}

EAuracronUIState UAuracronUIBridge::GetUIState(EAuracronUIType UIType) const
{
    const FAuracronUIStateEntry* Entry = UIStates.FindByPredicate([UIType](const FAuracronUIStateEntry& StateEntry) {
        return StateEntry.UIType == UIType;
    });
    return Entry ? Entry->UIState : EAuracronUIState::Hidden;
}

bool UAuracronUIBridge::SetUIWidget(EAuracronUIType UIType, TSubclassOf<UCommonUserWidget> WidgetClass)
{
    if (!WidgetClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Classe de widget invÃ¡lida"));
        return false;
    }

    RegisteredWidgetClasses.Add(UIType, WidgetClass);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Widget registrado para UI: %d"), (int32)UIType);

    return true;
}

UCommonUserWidget* UAuracronUIBridge::GetUIWidget(EAuracronUIType UIType) const
{
    const TObjectPtr<UCommonUserWidget>* Widget = ActiveUIWidgets.Find(UIType);
    return Widget ? Widget->Get() : nullptr;
}

// === HUD Management ===

bool UAuracronUIBridge::UpdateHealthBar(float CurrentHealth, float MaxHealth)
{
    if (!bSystemInitialized || MaxHealth <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    // Encontrar barra de HP no widget
    UProgressBar* HealthBar = Cast<UProgressBar>(HUDWidget->GetWidgetFromName(TEXT("HealthBar")));
    if (HealthBar)
    {
        float HealthPercentage = FMath::Clamp(CurrentHealth / MaxHealth, 0.0f, 1.0f);
        HealthBar->SetPercent(HealthPercentage);

        // Atualizar cor baseada na porcentagem
        FLinearColor HealthColor = FMath::Lerp(FLinearColor::Red, CombatHUDConfiguration.HealthColor, HealthPercentage);
        HealthBar->SetFillColorAndOpacity(HealthColor);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Barra de HP atualizada: %.2f/%.2f (%.1f%%)"), CurrentHealth, MaxHealth, HealthPercentage * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateManaBar(float CurrentMana, float MaxMana)
{
    if (!bSystemInitialized || MaxMana <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    UProgressBar* ManaBar = Cast<UProgressBar>(HUDWidget->GetWidgetFromName(TEXT("ManaBar")));
    if (ManaBar)
    {
        float ManaPercentage = FMath::Clamp(CurrentMana / MaxMana, 0.0f, 1.0f);
        ManaBar->SetPercent(ManaPercentage);
        ManaBar->SetFillColorAndOpacity(CombatHUDConfiguration.ManaColor);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Barra de mana atualizada: %.2f/%.2f (%.1f%%)"), CurrentMana, MaxMana, ManaPercentage * 100.0f);
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateAbilityCooldown(const FString& AbilitySlot, float CooldownRemaining, float MaxCooldown)
{
    if (!bSystemInitialized || MaxCooldown <= 0.0f)
    {
        return false;
    }

    UCommonUserWidget* HUDWidget = GetUIWidget(EAuracronUIType::InGameHUD);
    if (!HUDWidget)
    {
        return false;
    }

    FString WidgetName = FString::Printf(TEXT("%sAbilityButton"), *AbilitySlot);
    UWidget* AbilityWidget = HUDWidget->GetWidgetFromName(*WidgetName);

    if (UButton* AbilityButton = Cast<UButton>(AbilityWidget))
    {
        // Atualizar visual do cooldown
        bool bOnCooldown = CooldownRemaining > 0.0f;
        AbilityButton->SetIsEnabled(!bOnCooldown);

        if (bOnCooldown)
        {
            float CooldownPercentage = CooldownRemaining / MaxCooldown;
            
            // Implementar overlay de cooldown visual
            // Procurar por um overlay de cooldown existente ou criar um novo
            FString OverlayName = FString::Printf(TEXT("%sCooldownOverlay"), *AbilitySlot);
            UWidget* OverlayWidget = HUDWidget->GetWidgetFromName(*OverlayName);
            
            if (!OverlayWidget)
            {
                // Criar um novo overlay de cooldown se nÃ£o existir
                if (UOverlay* ParentOverlay = Cast<UOverlay>(AbilityButton->GetParent()))
                {
                    // Criar uma imagem circular para o overlay de cooldown
                    UImage* CooldownOverlay = NewObject<UImage>(this);
                    if (CooldownOverlay)
                    {
                        CooldownOverlay->SetBrushFromTexture(LoadObject<UTexture2D>(nullptr, TEXT("/Engine/EngineResources/WhiteSquareTexture.WhiteSquareTexture")));
                        CooldownOverlay->SetColorAndOpacity(FLinearColor(0.0f, 0.0f, 0.0f, 0.7f)); // Overlay escuro semi-transparente
                        CooldownOverlay->SetRenderTransformPivot(FVector2D(0.5f, 0.5f));
                        
                        // Configurar material para criar efeito circular de cooldown
                        if (UMaterialInterface* CooldownMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/Widget_CircularThrobber.Widget_CircularThrobber")))
                        {
                            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(CooldownMaterial, CooldownOverlay);
                            if (DynamicMaterial)
                            {
                                DynamicMaterial->SetScalarParameterValue(TEXT("Percent"), 1.0f - CooldownPercentage);
                                CooldownOverlay->SetBrushFromMaterial(DynamicMaterial);
                            }
                        }
                        
                        // Adicionar ao overlay parent com o mesmo slot do botÃ£o
                        UOverlaySlot* OverlaySlot = ParentOverlay->AddChildToOverlay(CooldownOverlay);
                        if (OverlaySlot)
                        {
                            OverlaySlot->SetHorizontalAlignment(HAlign_Fill);
                            OverlaySlot->SetVerticalAlignment(VAlign_Fill);
                        }
                        
                        // Widget criado com nome automÃ¡tico pelo ConstructWidget
                        OverlayWidget = CooldownOverlay;
                    }
                }
            }
            
            // Atualizar o overlay existente
            if (UImage* CooldownImage = Cast<UImage>(OverlayWidget))
            {
                CooldownImage->SetVisibility(ESlateVisibility::HitTestInvisible);
                
                // Atualizar o material dinÃ¢mico com a nova porcentagem
                if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(CooldownImage->GetBrush().GetResourceObject()))
                {
                    DynamicMaterial->SetScalarParameterValue(TEXT("Percent"), 1.0f - CooldownPercentage);
                }
                
                // Adicionar texto de tempo restante
                FString CooldownText = FString::Printf(TEXT("%.1fs"), CooldownRemaining);
                if (UTextBlock* CooldownTextWidget = Cast<UTextBlock>(HUDWidget->GetWidgetFromName(*FString::Printf(TEXT("%sCooldownText"), *AbilitySlot))))
                {
                    CooldownTextWidget->SetText(FText::FromString(CooldownText));
                    CooldownTextWidget->SetVisibility(ESlateVisibility::HitTestInvisible);
                }
            }
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Cooldown %s: %.2f/%.2f (%.1f%%) - Visual overlay updated"), *AbilitySlot, CooldownRemaining, MaxCooldown, CooldownPercentage * 100.0f);
        }
        else
        {
            // Esconder o overlay quando nÃ£o hÃ¡ cooldown
            FString OverlayName = FString::Printf(TEXT("%sCooldownOverlay"), *AbilitySlot);
            if (UWidget* OverlayWidget = HUDWidget->GetWidgetFromName(*OverlayName))
            {
                OverlayWidget->SetVisibility(ESlateVisibility::Collapsed);
            }
            
            // Esconder texto de cooldown
            FString TextName = FString::Printf(TEXT("%sCooldownText"), *AbilitySlot);
            if (UWidget* TextWidget = HUDWidget->GetWidgetFromName(*TextName))
            {
                TextWidget->SetVisibility(ESlateVisibility::Collapsed);
            }
        }

        return true;
    }

    return false;
}

// === ImplementaÃ§Ãµes dos mÃ©todos faltantes ===

bool UAuracronUIBridge::InitializeUISystem()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Inicializando sistema de UI"));

    // Configurar plataforma de input
    CurrentInputPlatform = DetectInputPlatform();
    SetupPlatformInput(CurrentInputPlatform);

    // Carregar configuraÃ§Ãµes de UI
    LoadUISettings();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de UI inicializado com sucesso"));
    return true;
}

bool UAuracronUIBridge::UpdateResponsiveLayout()
{
    // Atualizar layout responsivo baseado no tamanho da tela
    if (UGameViewportClient* ViewportClient = GetWorld()->GetGameViewport())
    {
        FVector2D ViewportSize;
        ViewportClient->GetViewportSize(ViewportSize);

        // Aplicar configuraÃ§Ãµes responsivas baseadas no tamanho
        if (ViewportSize.X < 1280.0f)
        {
            // Layout para telas pequenas
            UIConfiguration.UIScale = 0.8f;
        }
        else if (ViewportSize.X > 1920.0f)
        {
            // Layout para telas grandes
            UIConfiguration.UIScale = 1.2f;
        }
        else
        {
            // Layout padrÃ£o
            UIConfiguration.UIScale = 1.0f;
        }

        return true;
    }

    return false;
}

void UAuracronUIBridge::ProcessUIAnimations(float DeltaTime)
{
    // Processar animaÃ§Ãµes de UI
    for (auto& WidgetPair : ActiveUIWidgets)
    {
        if (UCommonUserWidget* Widget = WidgetPair.Value)
        {
            // Atualizar animaÃ§Ãµes do widget se necessÃ¡rio
            // ImplementaÃ§Ã£o especÃ­fica de animaÃ§Ãµes pode ser adicionada aqui
        }
    }
}

UCommonUserWidget* UAuracronUIBridge::CreateUIWidget(EAuracronUIType UIType, TSubclassOf<UCommonUserWidget> WidgetClass)
{
    if (!WidgetClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: WidgetClass Ã© nulo para UIType %d"), (int32)UIType);
        return nullptr;
    }

    if (UCommonUserWidget* ExistingWidget = ActiveUIWidgets.FindRef(UIType))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Widget jÃ¡ existe para UIType %d, retornando existente"), (int32)UIType);
        return ExistingWidget;
    }

    // Criar novo widget
    UCommonUserWidget* NewWidget = CreateWidget<UCommonUserWidget>(GetWorld(), WidgetClass);
    if (NewWidget)
    {
        ActiveUIWidgets.Add(UIType, NewWidget);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Widget criado com sucesso para UIType %d"), (int32)UIType);
        return NewWidget;
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar widget para UIType %d"), (int32)UIType);
    return nullptr;
}

EAuracronInputPlatform UAuracronUIBridge::DetectInputPlatform() const
{
    // Detectar plataforma baseada no sistema
    FString PlatformName = UGameplayStatics::GetPlatformName();

    if (PlatformName.Contains(TEXT("Windows")) || PlatformName.Contains(TEXT("Mac")) || PlatformName.Contains(TEXT("Linux")))
    {
        return EAuracronInputPlatform::PC;
    }
    else if (PlatformName.Contains(TEXT("Android")) || PlatformName.Contains(TEXT("IOS")))
    {
        return EAuracronInputPlatform::Mobile;
    }
    else if (PlatformName.Contains(TEXT("XboxOne")) || PlatformName.Contains(TEXT("PS4")) || PlatformName.Contains(TEXT("Switch")))
    {
        return EAuracronInputPlatform::Console;
    }

    // PadrÃ£o para PC
    return EAuracronInputPlatform::PC;
}

bool UAuracronUIBridge::SetupPlatformInput(EAuracronInputPlatform Platform)
{
    CurrentInputPlatform = Platform;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configurando input para plataforma %d"), (int32)Platform);

    // Configurar input baseado na plataforma
    switch (Platform)
    {
        case EAuracronInputPlatform::PC:
            // ConfiguraÃ§Ãµes para PC (mouse + teclado)
            UIConfiguration.InputPlatform = EAuracronInputPlatform::PC;
            bTouchControlsEnabled = false;
            break;

        case EAuracronInputPlatform::Mobile:
            // ConfiguraÃ§Ãµes para mobile (touch)
            UIConfiguration.InputPlatform = EAuracronInputPlatform::Mobile;
            bTouchControlsEnabled = true;
            SetupTouchControls();
            break;

        case EAuracronInputPlatform::Console:
            // ConfiguraÃ§Ãµes para console (gamepad)
            UIConfiguration.InputPlatform = EAuracronInputPlatform::Console;
            bTouchControlsEnabled = false;
            break;
    }

    return true;
}

bool UAuracronUIBridge::SetupTouchControls()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configurando controles touch"));

    // Configurar controles touch para mobile
    UIConfiguration.InputPlatform = EAuracronInputPlatform::Mobile;
    bTouchControlsEnabled = true;

    // Configurar widgets especÃ­ficos para touch
    // Mostrar barra de habilidades adaptada para touch
    ShowUI(EAuracronUIType::AbilityBar, false);

    // Configurar tamanho e posiÃ§Ã£o dos controles para touch
    if (UCommonUserWidget* AbilityWidget = ActiveUIWidgets.FindRef(EAuracronUIType::AbilityBar))
    {
        // Aumentar escala para touch
        AbilityWidget->SetRenderScale(FVector2D(1.2f, 1.2f));
    }

    return true;
}

bool UAuracronUIBridge::ToggleTouchControls(bool bEnable)
{
    bTouchControlsEnabled = bEnable;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Controles touch %s"), bEnable ? TEXT("habilitados") : TEXT("desabilitados"));

    // Mostrar/ocultar widgets de controles touch
    if (bEnable)
    {
        ShowUI(EAuracronUIType::AbilityBar, true);

        // Configurar para touch
        if (UCommonUserWidget* AbilityWidget = ActiveUIWidgets.FindRef(EAuracronUIType::AbilityBar))
        {
            AbilityWidget->SetRenderScale(FVector2D(1.2f, 1.2f));
        }
    }
    else
    {
        // Restaurar escala normal
        if (UCommonUserWidget* AbilityWidget = ActiveUIWidgets.FindRef(EAuracronUIType::AbilityBar))
        {
            AbilityWidget->SetRenderScale(FVector2D(1.0f, 1.0f));
        }
    }

    return true;
}

bool UAuracronUIBridge::ProcessUIInput(const FString& InputAction, const FVector2D& InputLocation)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Processando input UI: %s na posiÃ§Ã£o (%f, %f)"),
           *InputAction, InputLocation.X, InputLocation.Y);

    // Processar input baseado na aÃ§Ã£o
    if (InputAction == TEXT("Click") || InputAction == TEXT("Touch"))
    {
        // Processar clique/toque
        return true;
    }
    else if (InputAction == TEXT("Drag"))
    {
        // Processar arraste
        return true;
    }

    return false;
}

bool UAuracronUIBridge::ApplyUIConfiguration(const FAuracronUIConfiguration& Configuration)
{
    UIConfiguration = Configuration;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Aplicando configuraÃ§Ã£o de UI - Escala: %f"), Configuration.UIScale);

    // Aplicar configuraÃ§Ãµes aos widgets ativos
    for (auto& WidgetPair : ActiveUIWidgets)
    {
        if (UCommonUserWidget* Widget = WidgetPair.Value)
        {
            // Aplicar escala
            Widget->SetRenderScale(FVector2D(Configuration.UIScale, Configuration.UIScale));
        }
    }

    return true;
}

bool UAuracronUIBridge::SaveUISettings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Salvando configuraÃ§Ãµes de UI"));

    // Salvar configuraÃ§Ãµes usando UGameUserSettings ou sistema de save personalizado
    if (UGameUserSettings* UserSettings = UGameUserSettings::GetGameUserSettings())
    {
        // Aqui vocÃª pode salvar configuraÃ§Ãµes customizadas
        UserSettings->SaveSettings();
        return true;
    }

    return false;
}

bool UAuracronUIBridge::LoadUISettings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Carregando configuraÃ§Ãµes de UI"));

    // Carregar configuraÃ§Ãµes padrÃ£o
    UIConfiguration.UIScale = 1.0f;
    UIConfiguration.UIOpacity = 1.0f;
    UIConfiguration.bUseUIAnimations = true;
    UIConfiguration.AnimationSpeed = 1.0f;

    return true;
}

bool UAuracronUIBridge::ResetToDefaultSettings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Resetando configuraÃ§Ãµes para padrÃ£o"));

    // Resetar para configuraÃ§Ãµes padrÃ£o
    UIConfiguration = FAuracronUIConfiguration();
    ApplyUIConfiguration(UIConfiguration);
    SaveUISettings();

    return true;
}

void UAuracronUIBridge::OnRep_UIStates()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Estados de UI replicados"));

    // Atualizar UI baseado nos estados replicados
    for (const FAuracronUIStateEntry& StateEntry : UIStates)
    {
        if (StateEntry.UIState == EAuracronUIState::Visible)
        {
            ShowUI(StateEntry.UIType, false); // false para nÃ£o replicar novamente
        }
        else
        {
            HideUI(StateEntry.UIType, false); // false para nÃ£o replicar novamente
        }
    }
}

void UAuracronUIBridge::OnRep_InputPlatform()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Plataforma de input replicada: %d"), (int32)CurrentInputPlatform);

    // Configurar input baseado na plataforma replicada
    SetupPlatformInput(CurrentInputPlatform);
}

bool UAuracronUIBridge::ShowDamageIndicator(const FVector& WorldLocation, float Damage, bool bIsCritical)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mostrando indicador de dano: %f na posiÃ§Ã£o (%f, %f, %f)"),
           Damage, WorldLocation.X, WorldLocation.Y, WorldLocation.Z);

    // Converter posiÃ§Ã£o do mundo para tela
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        FVector2D ScreenLocation;
        if (PC->ProjectWorldLocationToScreen(WorldLocation, ScreenLocation))
        {
            // Criar widget de dano temporÃ¡rio
            // ImplementaÃ§Ã£o especÃ­fica do widget de dano pode ser adicionada aqui
            return true;
        }
    }

    return false;
}

bool UAuracronUIBridge::UpdateExperienceBar(int32 CurrentXP, int32 MaxXP, int32 Level)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Atualizando barra de experiÃªncia - XP: %d/%d, NÃ­vel: %d"),
           CurrentXP, MaxXP, Level);

    // Calcular porcentagem de XP
    float XPPercentage = MaxXP > 0 ? (float)CurrentXP / (float)MaxXP : 0.0f;

    // Atualizar widget de experiÃªncia se existir (usando InGameHUD como substituto)
    if (UCommonUserWidget* XPWidget = ActiveUIWidgets.FindRef(EAuracronUIType::InGameHUD))
    {
        // ImplementaÃ§Ã£o especÃ­fica para atualizar a barra de XP
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateGold(int32 NewGoldAmount)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Atualizando ouro: %d"), NewGoldAmount);

    // Atualizar widget de ouro se existir (usando InGameHUD como substituto)
    if (UCommonUserWidget* GoldWidget = ActiveUIWidgets.FindRef(EAuracronUIType::InGameHUD))
    {
        // ImplementaÃ§Ã£o especÃ­fica para atualizar o display de ouro
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateKDA(int32 Kills, int32 Deaths, int32 Assists)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Atualizando KDA - K:%d D:%d A:%d"), Kills, Deaths, Assists);

    // Atualizar widget de KDA se existir (usando ScoreBoard como substituto)
    if (UCommonUserWidget* KDAWidget = ActiveUIWidgets.FindRef(EAuracronUIType::ScoreBoard))
    {
        // ImplementaÃ§Ã£o especÃ­fica para atualizar o display de KDA
        return true;
    }

    return false;
}

bool UAuracronUIBridge::UpdateMinimap3D()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Atualizando minimapa 3D"));

    // Atualizar minimapa 3D se existir
    if (UCommonUserWidget* MinimapWidget = ActiveUIWidgets.FindRef(EAuracronUIType::Minimap))
    {
        // ImplementaÃ§Ã£o especÃ­fica para atualizar o minimapa 3D
        // Aqui vocÃª pode atualizar posiÃ§Ãµes de jogadores, objetivos, etc.
        return true;
    }

    return false;
}

bool UAuracronUIBridge::AddMinimapMarker(const FVector& WorldLocation, const FString& MarkerName, const FLinearColor& MarkerColor)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adicionando marcador no minimapa: %s na posiÃ§Ã£o (%f, %f, %f)"),
           *MarkerName, WorldLocation.X, WorldLocation.Y, WorldLocation.Z);

    // Criar e configurar marcador
    FAuracronMinimapMarker NewMarker;
    NewMarker.MarkerName = MarkerName;
    NewMarker.WorldLocation = WorldLocation;
    NewMarker.MarkerColor = MarkerColor;
    NewMarker.MarkerType = MarkerName; // Usar nome como tipo por padrÃ£o
    NewMarker.MarkerSize = 10.0f;
    NewMarker.bIsVisible = true;
    NewMarker.CreationTime = GetWorld()->GetTimeSeconds();

    // Adicionar ao mapa de marcadores
    MinimapMarkers.Add(MarkerName, NewMarker);

    // Atualizar minimapa se estiver ativo
    if (ActiveUIWidgets.Contains(EAuracronUIType::Minimap))
    {
        UpdateMinimap3D();
    }

    return true;
}

bool UAuracronUIBridge::RemoveMinimapMarker(const FString& MarkerName)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo marcador do minimapa: %s"), *MarkerName);

    // Remover marcador do mapa
    bool bRemoved = MinimapMarkers.Remove(MarkerName) > 0;

    // Atualizar minimapa se estiver ativo e marcador foi removido
    if (bRemoved && ActiveUIWidgets.Contains(EAuracronUIType::Minimap))
    {
        UpdateMinimap3D();
    }

    return bRemoved;
}

bool UAuracronUIBridge::SetMinimapZoom(float ZoomLevel)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Definindo zoom do minimapa: %f"), ZoomLevel);

    // Validar e aplicar zoom
    float ClampedZoom = FMath::Clamp(ZoomLevel, 0.1f, 5.0f);
    MinimapZoom = ClampedZoom;

    // Atualizar configuraÃ§Ã£o do minimapa
    MinimapConfiguration.MinimapZoom = ClampedZoom;

    // Atualizar zoom do minimapa se existir
    if (UCommonUserWidget* MinimapWidget = ActiveUIWidgets.FindRef(EAuracronUIType::Minimap))
    {
        // Aplicar escala baseada no zoom
        MinimapWidget->SetRenderScale(FVector2D(ClampedZoom, ClampedZoom));

        // Recalcular posiÃ§Ãµes dos marcadores baseado no novo zoom
        for (auto& MarkerPair : MinimapMarkers)
        {
            FAuracronMinimapMarker& Marker = MarkerPair.Value;
            if (Marker.bIsVisible)
            {
                // Recalcular posiÃ§Ã£o do marcador na tela baseado no zoom
                FVector2D ScreenPos = WorldToMinimapPosition(Marker.WorldLocation, ClampedZoom);
                // A posiÃ§Ã£o serÃ¡ usada pelo widget do minimapa para renderizar o marcador
            }
        }

        // Atualizar minimapa
        UpdateMinimap3D();
        OnMinimapUpdated.Broadcast();
        return true;
    }

    return false;
}

bool UAuracronUIBridge::ToggleMinimapRealm(int32 RealmIndex)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Alternando realm do minimapa: %d"), RealmIndex);

    // Validar Ã­ndice do realm
    if (RealmIndex < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ãndice de realm invÃ¡lido: %d"), RealmIndex);
        return false;
    }

    // Alternar visualizaÃ§Ã£o de realm no minimapa
    CurrentMinimapRealm = RealmIndex;

    // Filtrar marcadores por realm
    int32 VisibleMarkers = 0;
    for (auto& MarkerPair : MinimapMarkers)
    {
        FAuracronMinimapMarker& Marker = MarkerPair.Value;

        // Determinar se o marcador deve ser visÃ­vel neste realm
        bool bShouldBeVisible = ShouldMarkerBeVisibleInRealm(Marker, RealmIndex);

        if (Marker.bIsVisible != bShouldBeVisible)
        {
            Marker.bIsVisible = bShouldBeVisible;
            if (bShouldBeVisible) VisibleMarkers++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d marcadores visÃ­veis no realm %d"), VisibleMarkers, RealmIndex);

    // Atualizar minimapa para mostrar o realm selecionado
    bool bSuccess = UpdateMinimap3D();

    if (bSuccess)
    {
        OnMinimapUpdated.Broadcast();
    }

    return bSuccess;
}

// === MÃ©todos auxiliares ===

FVector2D UAuracronUIBridge::WorldToMinimapPosition(const FVector& WorldLocation, float ZoomLevel) const
{
    // Converter posiÃ§Ã£o do mundo para coordenadas do minimapa
    FVector2D MinimapCenter = MinimapConfiguration.MinimapPosition;
    FVector2D MinimapSize = MinimapConfiguration.MinimapSize;

    // Obter posiÃ§Ã£o do jogador como referÃªncia
    FVector PlayerLocation = FVector::ZeroVector;
    if (APawn* PlayerPawn = GetWorld()->GetFirstPlayerController()->GetPawn())
    {
        PlayerLocation = PlayerPawn->GetActorLocation();
    }

    // Calcular offset relativo ao jogador
    FVector RelativeLocation = WorldLocation - PlayerLocation;

    // Aplicar zoom e escala
    float Scale = ZoomLevel * 0.001f; // Escala para converter unidades do mundo para pixels
    FVector2D MinimapPos;
    MinimapPos.X = MinimapCenter.X + (RelativeLocation.X * Scale);
    MinimapPos.Y = MinimapCenter.Y + (RelativeLocation.Y * Scale);

    // Clampar dentro dos limites do minimapa
    MinimapPos.X = FMath::Clamp(MinimapPos.X, MinimapCenter.X - MinimapSize.X * 0.5f, MinimapCenter.X + MinimapSize.X * 0.5f);
    MinimapPos.Y = FMath::Clamp(MinimapPos.Y, MinimapCenter.Y - MinimapSize.Y * 0.5f, MinimapCenter.Y + MinimapSize.Y * 0.5f);

    return MinimapPos;
}

bool UAuracronUIBridge::ShouldMarkerBeVisibleInRealm(const FAuracronMinimapMarker& Marker, int32 RealmIndex) const
{
    // LÃ³gica para determinar se um marcador deve ser visÃ­vel em um realm especÃ­fico

    // Marcadores especiais sempre visÃ­veis
    if (Marker.MarkerType == TEXT("Player") || Marker.MarkerType == TEXT("Objective"))
    {
        return true;
    }

    // Marcadores de realm especÃ­fico
    if (Marker.MarkerType.Contains(TEXT("Realm")))
    {
        // Extrair nÃºmero do realm do tipo do marcador
        FString RealmNumberStr = Marker.MarkerType.RightChop(5); // Remove "Realm"
        int32 MarkerRealm = FCString::Atoi(*RealmNumberStr);
        return MarkerRealm == RealmIndex;
    }

    // Marcadores temporÃ¡rios (baseado no tempo)
    if (Marker.MarkerType == TEXT("Temporary"))
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        float MarkerAge = CurrentTime - Marker.CreationTime;
        return MarkerAge < 30.0f; // VisÃ­vel por 30 segundos
    }

    // Por padrÃ£o, mostrar todos os marcadores
    return true;
}

#include "Modules/ModuleManager.h"

IMPLEMENT_MODULE(FDefaultModuleImpl, AuracronUIBridge);
