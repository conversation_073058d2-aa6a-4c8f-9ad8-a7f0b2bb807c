// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Realms DinÃ¢micos Bridge
// IntegraÃ§Ã£o C++ para gerenciamento de 3 realms com transiÃ§Ãµes dinÃ¢micas usando UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
// Forward declaration for ModularGameplay compatibility
#include "Components/GameFrameworkComponent.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"
#include "PCG/Public/PCGComponent.h"
#include "PCG/Public/PCGSubsystem.h"
#include "PCG/Public/PCGGraph.h"
#include "PCG/Public/Elements/PCGSplineSampler.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "Materials/MaterialParameterCollection.h"

#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "MetasoundSource.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "InstancedFoliageActor.h"
#include "FoliageType.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Net/Core/PushModel/PushModel.h"
#include "AuracronRealmsBridge.generated.h"

// Forward Declarations
class UWorldPartition;
class UDataLayerManager;
class UDataLayerInstance;
class UPCGComponent;
class UPCGSubsystem;
class UPCGGraph;
class UNiagaraSystem;
class UNiagaraComponent;
class ALandscape;
class AInstancedFoliageActor;
class UHierarchicalInstancedStaticMeshComponent;

/**
 * EnumeraÃ§Ã£o para tipos de Realms do AURACRON
 */
UENUM(BlueprintType)
enum class EAuracronRealmType : uint8
{
    None                UMETA(DisplayName = "None"),
    PlanicieRadiante    UMETA(DisplayName = "PlanÃ­cie Radiante (Surface)"),
    FirmamentoZephyr    UMETA(DisplayName = "Firmamento Zephyr (Sky)"),
    AbismoUmbrio        UMETA(DisplayName = "Abismo Umbrio (Underground)")
};

/**
 * EnumeraÃ§Ã£o para estados de transiÃ§Ã£o entre Realms
 */
UENUM(BlueprintType)
enum class EAuracronRealmTransitionState : uint8
{
    Stable          UMETA(DisplayName = "Stable"),
    Transitioning   UMETA(DisplayName = "Transitioning"),
    Evolving        UMETA(DisplayName = "Evolving"),
    Merging         UMETA(DisplayName = "Merging"),
    Splitting       UMETA(DisplayName = "Splitting")
};

/**
 * Estrutura para configuraÃ§Ã£o de um Realm
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronRealmConfiguration
{
    GENERATED_BODY()

    /** Tipo do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    EAuracronRealmType RealmType = EAuracronRealmType::None;

    /** Nome do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FText RealmName;

    /** DescriÃ§Ã£o do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FText RealmDescription;

    /** Altura mÃ­nima do Realm (em unidades) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "-10000.0", ClampMax = "10000.0"))
    float MinHeight = 0.0f;

    /** Altura mÃ¡xima do Realm (em unidades) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "-10000.0", ClampMax = "10000.0"))
    float MaxHeight = 1000.0f;

    /** Data Layers associadas ao Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    TArray<TSoftObjectPtr<UDataLayerInstance>> DataLayers;

    /** GrÃ¡ficos PCG para geraÃ§Ã£o procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    TArray<TSoftObjectPtr<UPCGGraph>> PCGGraphs;

    /** Landscape principal do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    TSoftObjectPtr<ALandscape> PrimaryLandscape;

    /** Atores de foliage instanciada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    TArray<TSoftObjectPtr<AInstancedFoliageActor>> FoliageActors;

    /** Cor ambiente do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FLinearColor AmbientColor = FLinearColor::White;

    /** Intensidade da iluminaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float LightingIntensity = 1.0f;

    /** Densidade de nÃ©voa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float FogDensity = 0.1f;

    /** Gravidade especÃ­fica do Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "-2000.0", ClampMax = "2000.0"))
    float GravityScale = 1.0f;

    /** Velocidade de movimento modificada no Realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "0.1", ClampMax = "3.0"))
    float MovementSpeedModifier = 1.0f;

    /** Tempo para evoluÃ§Ã£o do Realm (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration", meta = (ClampMin = "60.0", ClampMax = "1800.0"))
    float EvolutionTime = 600.0f; // 10 minutos

    /** Ativo por padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    bool bActiveByDefault = true;

    /** Permite transiÃ§Ãµes dinÃ¢micas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    bool bAllowDynamicTransitions = true;

    /** Suporta geraÃ§Ã£o procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    bool bSupportsProceduralGeneration = true;
};

/**
 * Estrutura para portal dimensional entre Realms
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronDimensionalPortal
{
    GENERATED_BODY()

    /** Realm de origem */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    EAuracronRealmType SourceRealm = EAuracronRealmType::None;

    /** Realm de destino */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    EAuracronRealmType DestinationRealm = EAuracronRealmType::None;

    /** LocalizaÃ§Ã£o do portal no mundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    FVector PortalLocation = FVector::ZeroVector;

    /** RotaÃ§Ã£o do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    FRotator PortalRotation = FRotator::ZeroRotator;

    /** Escala do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    FVector PortalScale = FVector::OneVector;

    /** Raio de ativaÃ§Ã£o do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration", meta = (ClampMin = "50.0", ClampMax = "1000.0"))
    float ActivationRadius = 200.0f;

    /** Tempo de transiÃ§Ã£o (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration", meta = (ClampMin = "0.5", ClampMax = "10.0"))
    float TransitionTime = 2.0f;

    /** Cooldown do portal (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration", meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float PortalCooldown = 5.0f;

    /** Portal ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    bool bPortalActive = true;

    /** Permite transiÃ§Ã£o bidirecional */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    bool bBidirectional = true;

    /** Requer condiÃ§Ãµes especiais para ativaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Configuration")
    bool bRequiresSpecialConditions = false;

    /** Sistema de partÃ­culas do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Effects")
    TSoftObjectPtr<UNiagaraSystem> PortalParticleSystem;

    /** Som do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Effects")
    TSoftObjectPtr<UMetaSoundSource> PortalSound;

    /** Material do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Portal Effects")
    TSoftObjectPtr<UMaterialInterface> PortalMaterial;
};

/**
 * Estrutura para evoluÃ§Ã£o temporal do mapa
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronMapEvolution
{
    GENERATED_BODY()

    /** Tempo de inÃ­cio da evoluÃ§Ã£o (em segundos de jogo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution", meta = (ClampMin = "0.0", ClampMax = "3600.0"))
    float StartTime = 0.0f;

    /** DuraÃ§Ã£o da evoluÃ§Ã£o (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution", meta = (ClampMin = "10.0", ClampMax = "300.0"))
    float Duration = 60.0f;

    /** Realms afetados pela evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TArray<EAuracronRealmType> AffectedRealms;

    /** GrÃ¡ficos PCG para evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TArray<TSoftObjectPtr<UPCGGraph>> EvolutionPCGGraphs;

    /** Novos portais criados durante evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TArray<FAuracronDimensionalPortal> NewPortals;

    /** Portais removidos durante evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TArray<int32> RemovedPortalIndices;

    /** MudanÃ§as na iluminaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    FLinearColor NewAmbientColor = FLinearColor::White;

    /** Nova intensidade de iluminaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float NewLightingIntensity = 1.0f;

    /** Efeitos visuais da evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TSoftObjectPtr<UNiagaraSystem> EvolutionParticleSystem;

    /** Som da evoluÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    TSoftObjectPtr<UMetaSoundSource> EvolutionSound;

    /** EvoluÃ§Ã£o ativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    bool bEvolutionActive = true;

    /** Afeta navegaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    bool bAffectsNavigation = true;

    /** Requer regeneraÃ§Ã£o de PCG */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Evolution")
    bool bRequiresPCGRegeneration = true;
};

/**
 * Estrutura para conector vertical entre Realms
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronVerticalConnector
{
    GENERATED_BODY()

    /** Realm inferior */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    EAuracronRealmType LowerRealm = EAuracronRealmType::None;

    /** Realm superior */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    EAuracronRealmType UpperRealm = EAuracronRealmType::None;

    /** LocalizaÃ§Ã£o do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    FVector ConnectorLocation = FVector::ZeroVector;

    /** Raio do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector", meta = (ClampMin = "100.0", ClampMax = "2000.0"))
    float ConnectorRadius = 500.0f;

    /** Altura do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector", meta = (ClampMin = "200.0", ClampMax = "5000.0"))
    float ConnectorHeight = 1000.0f;

    /** Tipo de conector (escada, elevador, portal, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    FString ConnectorType = TEXT("Portal");

    /** Velocidade de transiÃ§Ã£o vertical */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector", meta = (ClampMin = "100.0", ClampMax = "2000.0"))
    float TransitionSpeed = 800.0f;

    /** Permite movimento bidirecional */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    bool bBidirectional = true;

    /** Conector ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    bool bConnectorActive = true;

    /** Requer habilidade especial para usar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    bool bRequiresSpecialAbility = false;

    /** Mesh do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    TSoftObjectPtr<UStaticMesh> ConnectorMesh;

    /** Material do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    TSoftObjectPtr<UMaterialInterface> ConnectorMaterial;

    /** Efeitos visuais do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    TSoftObjectPtr<UNiagaraSystem> ConnectorParticleSystem;

    /** Som do conector */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector")
    TSoftObjectPtr<UMetaSoundSource> ConnectorSound;
};

/**
 * Estrutura para configuraÃ§Ã£o de geraÃ§Ã£o procedural
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronProceduralGenerationConfig
{
    GENERATED_BODY()

    /** Realm alvo para geraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    EAuracronRealmType TargetRealm = EAuracronRealmType::None;

    /** GrÃ¡fico PCG principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    TSoftObjectPtr<UPCGGraph> MainPCGGraph;

    /** GrÃ¡ficos PCG secundÃ¡rios */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    TArray<TSoftObjectPtr<UPCGGraph>> SecondaryPCGGraphs;

    /** Seed para geraÃ§Ã£o aleatÃ³ria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation", meta = (ClampMin = "1", ClampMax = "999999"))
    int32 GenerationSeed = 12345;

    /** Densidade de geraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float GenerationDensity = 1.0f;

    /** Ãrea de geraÃ§Ã£o (em unidades quadradas) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation", meta = (ClampMin = "1000.0", ClampMax = "10000000.0"))
    float GenerationArea = 1000000.0f; // 1kmÂ²

    /** Intervalo de regeneraÃ§Ã£o (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation", meta = (ClampMin = "30.0", ClampMax = "600.0"))
    float RegenerationInterval = 120.0f; // 2 minutos

    /** GeraÃ§Ã£o ativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    bool bGenerationActive = true;

    /** Permite regeneraÃ§Ã£o dinÃ¢mica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    bool bAllowDynamicRegeneration = true;

    /** Usa streaming assÃ­ncrono */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    bool bUseAsyncStreaming = true;

    /** Otimizado para mobile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    bool bMobileOptimized = false;
};

/**
 * Estrutura para entrada de configuração de Realm (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronRealmConfigurationEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    EAuracronRealmType RealmType = EAuracronRealmType::PlanicieRadiante;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FAuracronRealmConfiguration Configuration;

    FAuracronRealmConfigurationEntry()
    {
        RealmType = EAuracronRealmType::PlanicieRadiante;
    }

    FAuracronRealmConfigurationEntry(EAuracronRealmType InRealmType, const FAuracronRealmConfiguration& InConfiguration)
        : RealmType(InRealmType), Configuration(InConfiguration)
    {
    }
};

/**
 * Estrutura para entrada de configuração de geração procedural (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONREALMSBRIDGE_API FAuracronProceduralGenerationConfigEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Configuration")
    EAuracronRealmType RealmType = EAuracronRealmType::PlanicieRadiante;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Configuration")
    FAuracronProceduralGenerationConfig Configuration;

    FAuracronProceduralGenerationConfigEntry()
    {
        RealmType = EAuracronRealmType::PlanicieRadiante;
    }

    FAuracronProceduralGenerationConfigEntry(EAuracronRealmType InRealmType, const FAuracronProceduralGenerationConfig& InConfiguration)
        : RealmType(InRealmType), Configuration(InConfiguration)
    {
    }
};

/**
 * Classe principal do Bridge para Sistema de Realms DinÃ¢micos
 * ResponsÃ¡vel pelo gerenciamento de 3 realms com transiÃ§Ãµes dinÃ¢micas
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Realms", meta = (DisplayName = "AURACRON Realms Bridge", BlueprintSpawnableComponent))
class AURACRONREALMSBRIDGE_API UAuracronRealmsBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronRealmsBridge(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Realm Management ===

    /**
     * Ativar um Realm especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    bool ActivateRealm(EAuracronRealmType RealmType);

    /**
     * Desativar um Realm especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    bool DeactivateRealm(EAuracronRealmType RealmType);

    /**
     * Alternar visibilidade de um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    bool ToggleRealmVisibility(EAuracronRealmType RealmType);

    /**
     * Obter Realm ativo no momento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    EAuracronRealmType GetActiveRealm() const { return CurrentActiveRealm; }

    /**
     * Obter todos os Realms ativos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    TArray<EAuracronRealmType> GetActiveRealms() const;

    /**
     * Verificar se um Realm estÃ¡ ativo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Management", CallInEditor)
    bool IsRealmActive(EAuracronRealmType RealmType) const;

    // === Transition Management ===

    /**
     * Iniciar transiÃ§Ã£o entre Realms
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Transitions", CallInEditor)
    bool StartRealmTransition(EAuracronRealmType FromRealm, EAuracronRealmType ToRealm);

    /**
     * Completar transiÃ§Ã£o em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Transitions", CallInEditor)
    bool CompleteRealmTransition();

    /**
     * Cancelar transiÃ§Ã£o em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Transitions", CallInEditor)
    bool CancelRealmTransition();

    /**
     * Obter estado atual da transiÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Transitions", CallInEditor)
    EAuracronRealmTransitionState GetTransitionState() const { return CurrentTransitionState; }

    /**
     * Obter progresso da transiÃ§Ã£o (0.0 - 1.0)
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Transitions", CallInEditor)
    float GetTransitionProgress() const;

    // === Portal Management ===

    /**
     * Criar portal dimensional
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Portals", CallInEditor)
    int32 CreateDimensionalPortal(const FAuracronDimensionalPortal& PortalConfig);

    /**
     * Remover portal dimensional
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Portals", CallInEditor)
    bool RemoveDimensionalPortal(int32 PortalIndex);

    /**
     * Ativar/Desativar portal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Portals", CallInEditor)
    bool SetPortalActive(int32 PortalIndex, bool bActive);

    /**
     * Obter todos os portais ativos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Portals", CallInEditor)
    TArray<FAuracronDimensionalPortal> GetActivePortals() const;

    /**
     * Encontrar portal mais prÃ³ximo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Portals", CallInEditor)
    int32 FindNearestPortal(const FVector& Location, float MaxDistance = 1000.0f) const;

    // === Procedural Generation ===

    /**
     * Executar geraÃ§Ã£o procedural para um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|PCG", CallInEditor)
    bool ExecuteProceduralGeneration(EAuracronRealmType RealmType);

    /**
     * Regenerar conteÃºdo procedural
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|PCG", CallInEditor)
    bool RegenerateProceduralContent(EAuracronRealmType RealmType, bool bForceRegeneration = false);

    /**
     * Limpar conteÃºdo procedural
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|PCG", CallInEditor)
    bool ClearProceduralContent(EAuracronRealmType RealmType);

    /**
     * Obter progresso da geraÃ§Ã£o procedural
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|PCG", CallInEditor)
    float GetProceduralGenerationProgress(EAuracronRealmType RealmType) const;

    // === Map Evolution ===

    /**
     * Iniciar evoluÃ§Ã£o do mapa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Evolution", CallInEditor)
    bool StartMapEvolution(const FAuracronMapEvolution& EvolutionConfig);

    /**
     * Parar evoluÃ§Ã£o em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Evolution", CallInEditor)
    bool StopMapEvolution();

    /**
     * Obter progresso da evoluÃ§Ã£o atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Evolution", CallInEditor)
    float GetEvolutionProgress() const;

    /**
     * Verificar se evoluÃ§Ã£o estÃ¡ ativa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Evolution", CallInEditor)
    bool IsEvolutionActive() const;

    // === World Partition Integration ===

    /**
     * Carregar Data Layers de um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|WorldPartition", CallInEditor)
    bool LoadRealmDataLayers(EAuracronRealmType RealmType);

    /**
     * Descarregar Data Layers de um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|WorldPartition", CallInEditor)
    bool UnloadRealmDataLayers(EAuracronRealmType RealmType);

    /**
     * Otimizar streaming de World Partition
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|WorldPartition", CallInEditor)
    bool OptimizeWorldPartitionStreaming();

    // === Configuration Management ===

    /**
     * Obter configuraÃ§Ã£o de um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Configuration", CallInEditor)
    FAuracronRealmConfiguration GetRealmConfiguration(EAuracronRealmType RealmType) const;

    /**
     * Definir configuraÃ§Ã£o de um Realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Configuration", CallInEditor)
    void SetRealmConfiguration(EAuracronRealmType RealmType, const FAuracronRealmConfiguration& Configuration);

    /**
     * Carregar configuraÃ§Ãµes padrÃ£o dos Realms
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Realms|Configuration", CallInEditor)
    bool LoadDefaultRealmConfigurations();

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de Realms */
    bool InitializeRealmSystem();

    /** Configurar World Partition */
    bool SetupWorldPartition();

    /** Configurar PCG Subsystem */
    bool SetupPCGSubsystem();

    /** Processar transiÃ§Ã£o em andamento */
    void ProcessRealmTransition(float DeltaTime);

    /** Processar evoluÃ§Ã£o do mapa */
    void ProcessMapEvolution(float DeltaTime);

    /** Atualizar Data Layers */
    bool UpdateDataLayers(EAuracronRealmType RealmType, bool bLoad);

    /** Executar PCG para Realm especÃ­fico */
    bool ExecutePCGForRealm(EAuracronRealmType RealmType, const FAuracronProceduralGenerationConfig& Config);

    /** Validar configuraÃ§Ã£o de Realm */
    bool ValidateRealmConfiguration(const FAuracronRealmConfiguration& Configuration) const;

    /** Atualizar efeitos ambientais */
    bool UpdateAmbientEffects(EAuracronRealmType RealmType);

    /** Sincronizar com outros sistemas */
    bool SynchronizeWithOtherSystems();

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes dos Realms disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronRealmConfigurationEntry> RealmConfigurations;

    /** Portais dimensionais ativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronDimensionalPortal> DimensionalPortals;

    /** Conectores verticais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronVerticalConnector> VerticalConnectors;

    /** ConfiguraÃ§Ãµes de geraÃ§Ã£o procedural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronProceduralGenerationConfigEntry> ProceduralConfigs;

    /** EvoluÃ§Ãµes programadas do mapa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronMapEvolution> ScheduledEvolutions;

    /** Realm atualmente ativo */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_ActiveRealm)
    EAuracronRealmType CurrentActiveRealm = EAuracronRealmType::PlanicieRadiante;

    /** Estado atual da transiÃ§Ã£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_TransitionState)
    EAuracronRealmTransitionState CurrentTransitionState = EAuracronRealmTransitionState::Stable;

    /** Tempo de inÃ­cio da transiÃ§Ã£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float TransitionStartTime = 0.0f;

    /** Realm de origem da transiÃ§Ã£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    EAuracronRealmType TransitionFromRealm = EAuracronRealmType::None;

    /** Realm de destino da transiÃ§Ã£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    EAuracronRealmType TransitionToRealm = EAuracronRealmType::None;

    /** EvoluÃ§Ã£o atualmente ativa */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    int32 CurrentEvolutionIndex = -1;

    /** Tempo de inÃ­cio da evoluÃ§Ã£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float EvolutionStartTime = 0.0f;

    /** Componente de timeline para transiÃ§Ãµes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UTimelineComponent> TransitionTimeline;

    /** Componente de timeline para evoluÃ§Ãµes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UTimelineComponent> EvolutionTimeline;

private:
    // === Internal State ===

    /** ReferÃªncia ao World Partition Subsystem */
    UPROPERTY()
    TObjectPtr<UWorldPartitionSubsystem> WorldPartitionSubsystem;

    /** ReferÃªncia ao Data Layer Manager */
    UPROPERTY()
    TObjectPtr<UDataLayerManager> DataLayerManager;

    /** ReferÃªncia ao PCG Subsystem */
    UPROPERTY()
    TObjectPtr<UPCGSubsystem> PCGSubsystem;

    /** Componentes PCG ativos por Realm */
    TMap<EAuracronRealmType, TArray<TObjectPtr<UPCGComponent>>> ActivePCGComponents;

    /** Componentes de efeitos visuais ativos */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveRealmEffects;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** Realms carregados */
    TSet<EAuracronRealmType> LoadedRealms;

    /** Timer para verificaÃ§Ãµes periÃ³dicas */
    FTimerHandle SystemUpdateTimer;

    /** Timer para evoluÃ§Ãµes programadas */
    FTimerHandle EvolutionTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection RealmMutex;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_ActiveRealm();

    UFUNCTION()
    void OnRep_TransitionState();

public:
    // === Delegates ===

    /** Delegate chamado quando Realm Ã© ativado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRealmActivated, EAuracronRealmType, RealmType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnRealmActivated OnRealmActivated;

    /** Delegate chamado quando Realm Ã© desativado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRealmDeactivated, EAuracronRealmType, RealmType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnRealmDeactivated OnRealmDeactivated;

    /** Delegate chamado quando transiÃ§Ã£o Ã© iniciada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTransitionStarted, EAuracronRealmType, FromRealm, EAuracronRealmType, ToRealm);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnTransitionStarted OnTransitionStarted;

    /** Delegate chamado quando transiÃ§Ã£o Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTransitionCompleted, EAuracronRealmType, FromRealm, EAuracronRealmType, ToRealm);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnTransitionCompleted OnTransitionCompleted;

    /** Delegate chamado quando evoluÃ§Ã£o Ã© iniciada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEvolutionStarted, int32, EvolutionIndex);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnEvolutionStarted OnEvolutionStarted;

    /** Delegate chamado quando evoluÃ§Ã£o Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEvolutionCompleted, int32, EvolutionIndex);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnEvolutionCompleted OnEvolutionCompleted;

    /** Delegate chamado quando portal Ã© criado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPortalCreated, int32, PortalIndex);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnPortalCreated OnPortalCreated;

    /** Delegate chamado quando portal Ã© removido */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPortalRemoved, int32, PortalIndex);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnPortalRemoved OnPortalRemoved;

    /** Delegate chamado quando geraÃ§Ã£o procedural Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnProceduralGenerationCompleted, EAuracronRealmType, RealmType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Realms|Events")
    FOnProceduralGenerationCompleted OnProceduralGenerationCompleted;
};

