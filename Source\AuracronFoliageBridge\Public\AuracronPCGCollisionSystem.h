// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - PCG Collision Integration System
// Advanced collision system for procedural content generation with foliage

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Chaos/ChaosEngineInterface.h"

// PCG Framework includes
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"

#include "AuracronPCGCollisionSystem.generated.h"

// Forward declarations
class UPCGComponent;
class UPCGGraph;
class UStaticMesh;
class UMaterialInterface;
struct FPCGPoint;

UENUM(BlueprintType)
enum class EAuracronPCGCollisionType : uint8
{
    None            UMETA(DisplayName = "No Collision"),
    Simple          UMETA(DisplayName = "Simple Collision"),
    Complex         UMETA(DisplayName = "Complex Collision"),
    Custom          UMETA(DisplayName = "Custom Collision"),
    Procedural      UMETA(DisplayName = "Procedural Collision"),
    Adaptive        UMETA(DisplayName = "Adaptive Collision")
};

UENUM(BlueprintType)
enum class EAuracronPCGCollisionQuality : uint8
{
    Low             UMETA(DisplayName = "Low Quality"),
    Medium          UMETA(DisplayName = "Medium Quality"),
    High            UMETA(DisplayName = "High Quality"),
    Ultra           UMETA(DisplayName = "Ultra Quality"),
    Custom          UMETA(DisplayName = "Custom Quality")
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronPCGCollisionSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Type")
    EAuracronPCGCollisionType CollisionType = EAuracronPCGCollisionType::Simple;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Quality")
    EAuracronPCGCollisionQuality CollisionQuality = EAuracronPCGCollisionQuality::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float ComplexityReduction = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 MaxCollisionVertices = 256;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCollisionCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseAsyncGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bGenerateOverlapEvents = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bCanCharacterStepUpOn = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float CollisionResponseScale = 1.0f;

    FAuracronPCGCollisionSettings()
    {
        CollisionType = EAuracronPCGCollisionType::Simple;
        CollisionQuality = EAuracronPCGCollisionQuality::Medium;
        ComplexityReduction = 1.0f;
        MaxCollisionVertices = 256;
        bEnableCollisionCaching = true;
        bUseAsyncGeneration = true;
        bGenerateOverlapEvents = false;
        bCanCharacterStepUpOn = true;
        CollisionResponseScale = 1.0f;
    }
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronPCGCollisionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Mesh")
    TObjectPtr<UStaticMesh> CollisionMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Material")
    TObjectPtr<UMaterialInterface> CollisionMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FTransform CollisionTransform = FTransform::Identity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Mass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Friction = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Restitution = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float LinearDamping = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float AngularDamping = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    TEnumAsByte<ECollisionEnabled::Type> CollisionEnabled = ECollisionEnabled::QueryAndPhysics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    TEnumAsByte<ECollisionResponse> CollisionResponse = ECR_Block;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    FAuracronPCGCollisionSettings CollisionSettings;

    FAuracronPCGCollisionData()
    {
        CollisionMesh = nullptr;
        CollisionMaterial = nullptr;
        CollisionTransform = FTransform::Identity;
        Mass = 1.0f;
        Friction = 0.7f;
        Restitution = 0.3f;
        LinearDamping = 0.01f;
        AngularDamping = 0.0f;
        CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
        CollisionResponse = ECR_Block;
    }
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronPCGCollisionCache
{
    GENERATED_BODY()

    UPROPERTY()
    TMap<FString, FAuracronPCGCollisionData> CachedCollisionData;

    UPROPERTY()
    TMap<FString, TObjectPtr<UStaticMesh>> GeneratedCollisionMeshes;

    UPROPERTY()
    float LastUpdateTime = 0.0f;

    UPROPERTY()
    int32 CacheVersion = 1;

    FAuracronPCGCollisionCache()
    {
        LastUpdateTime = 0.0f;
        CacheVersion = 1;
    }
};

/**
 * Advanced PCG Collision System for Auracron Framework
 * Provides sophisticated collision generation and management for procedural content
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG")
class AURACRONFOLIAGEBRIDGE_API UAuracronPCGCollisionSystem : public UObject
{
    GENERATED_BODY()

public:
    UAuracronPCGCollisionSystem();

    // Core PCG Collision Functions
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Collision")
    bool GenerateCollisionForPCGData(UPCGComponent* PCGComponent, const FAuracronPCGCollisionSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Collision")
    bool GenerateCollisionForPointData(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Collision")
    UStaticMesh* GenerateCollisionMeshFromPoints(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Collision")
    bool ApplyCollisionToComponent(UStaticMeshComponent* Component, const FAuracronPCGCollisionData& CollisionData);

    // Advanced Collision Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Advanced")
    bool OptimizeCollisionMesh(UStaticMesh* SourceMesh, const FAuracronPCGCollisionSettings& Settings, UStaticMesh*& OutOptimizedMesh);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Advanced")
    bool GenerateAdaptiveCollision(const TArray<FVector>& SamplePoints, const FAuracronPCGCollisionSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Advanced")
    bool UpdateCollisionLOD(UStaticMeshComponent* Component, float Distance, const FAuracronPCGCollisionSettings& Settings);

    // Cache Management
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Cache")
    void ClearCollisionCache();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Cache")
    bool SaveCollisionCache(const FString& CachePath);

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Cache")
    bool LoadCollisionCache(const FString& CachePath);

    // Performance Monitoring
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Performance")
    float GetCollisionGenerationTime() const { return LastGenerationTime; }

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG|Performance")
    int32 GetCachedCollisionCount() const { return CollisionCache.CachedCollisionData.Num(); }

protected:
    // Internal collision generation
    UStaticMesh* GenerateSimpleCollisionMesh(const TArray<FVector>& Vertices, const FAuracronPCGCollisionSettings& Settings);
    UStaticMesh* GenerateComplexCollisionMesh(const TArray<FVector>& Vertices, const TArray<int32>& Indices, const FAuracronPCGCollisionSettings& Settings);
    UStaticMesh* GenerateProceduralCollisionMesh(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings);

    // Collision optimization
    void OptimizeCollisionVertices(TArray<FVector>& Vertices, const FAuracronPCGCollisionSettings& Settings);
    void SimplifyCollisionMesh(UStaticMesh* Mesh, float ReductionPercentage);

    // Cache management
    FString GenerateCacheKey(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings);
    bool IsCacheValid(const FString& CacheKey) const;

private:
    UPROPERTY()
    FAuracronPCGCollisionCache CollisionCache;

    UPROPERTY()
    float LastGenerationTime = 0.0f;

    UPROPERTY()
    TMap<FString, TWeakObjectPtr<UStaticMesh>> ActiveCollisionMeshes;

    // Performance tracking
    UPROPERTY()
    int32 TotalCollisionsGenerated = 0;

    UPROPERTY()
    float AverageGenerationTime = 0.0f;
};
