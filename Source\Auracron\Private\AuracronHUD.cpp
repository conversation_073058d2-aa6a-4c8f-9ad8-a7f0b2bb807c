#include "AuracronHUD.h"
#include "AuracronCharacter.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "Blueprint/UserWidget.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameModeBase.h"

AAuracronHUD::AAuracronHUD()
{
    PrimaryActorTick.bCanEverTick = true;
}

void AAuracronHUD::BeginPlay()
{
    Super::BeginPlay();

    // Create main HUD if widget class is set
    if (MainHUDWidgetClass)
    {
        CreateMainHUD();
    }

    UE_LOG(LogTemp, Log, TEXT("AuracronHUD: BeginPlay completed"));
}

void AAuracronHUD::DrawHUD()
{
    Super::DrawHUD();

    // Draw crosshair
    DrawCrosshair();

    // Draw debug information if enabled
    if (bShowAuracronDebugInfo)
    {
        DrawDebugInfo();
    }

    // Draw player stats if enabled and no widget is being used
    if (bShowPlayerStats && !MainHUDWidget)
    {
        DrawPlayerStats();
    }
}

void AAuracronHUD::CreateMainHUD()
{
    if (MainHUDWidgetClass && !MainHUDWidget)
    {
        if (APlayerController* PC = GetOwningPlayerController())
        {
            MainHUDWidget = CreateWidget<UUserWidget>(PC, MainHUDWidgetClass);
            if (MainHUDWidget)
            {
                MainHUDWidget->AddToViewport();
                UE_LOG(LogTemp, Log, TEXT("AuracronHUD: Main HUD widget created and added to viewport"));
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("AuracronHUD: Failed to create main HUD widget"));
            }
        }
    }
}

void AAuracronHUD::RemoveMainHUD()
{
    if (MainHUDWidget)
    {
        MainHUDWidget->RemoveFromParent();
        MainHUDWidget = nullptr;
        UE_LOG(LogTemp, Log, TEXT("AuracronHUD: Main HUD widget removed"));
    }
}

void AAuracronHUD::ToggleDebugInfo()
{
    bShowAuracronDebugInfo = !bShowAuracronDebugInfo;
    UE_LOG(LogTemp, Log, TEXT("AuracronHUD: Debug info toggled to %s"), bShowAuracronDebugInfo ? TEXT("ON") : TEXT("OFF"));
}

void AAuracronHUD::UpdatePlayerStats()
{
    // This can be called from Blueprint to update widget-based stats
    if (MainHUDWidget)
    {
        // Widget will handle its own updates
        UE_LOG(LogTemp, VeryVerbose, TEXT("AuracronHUD: Player stats update requested"));
    }
}

void AAuracronHUD::DrawDebugInfo()
{
    if (!Canvas)
    {
        return;
    }

    // Set up text properties
    FCanvasTextItem TextItem(FVector2D::ZeroVector, FText::GetEmpty(), GEngine->GetSmallFont(), FLinearColor::White);
    TextItem.EnableShadow(FLinearColor::Black);

    float YPos = 50.0f;
    const float LineHeight = 20.0f;

    // Game Mode Info
    if (GetWorld() && GetWorld()->GetAuthGameMode())
    {
        FString GameModeInfo = FString::Printf(TEXT("GameMode: %s"), *GetWorld()->GetAuthGameMode()->GetClass()->GetName());
        TextItem.Text = FText::FromString(GameModeInfo);
        TextItem.Position = FVector2D(50.0f, YPos);
        Canvas->DrawItem(TextItem);
        YPos += LineHeight;
    }

    // Player Controller Info
    if (APlayerController* PC = GetOwningPlayerController())
    {
        FString PCInfo = FString::Printf(TEXT("PlayerController: %s"), *PC->GetClass()->GetName());
        TextItem.Text = FText::FromString(PCInfo);
        TextItem.Position = FVector2D(50.0f, YPos);
        Canvas->DrawItem(TextItem);
        YPos += LineHeight;
    }

    // Character Info
    if (AAuracronCharacter* Character = GetPlayerCharacter())
    {
        FString CharacterInfo = FString::Printf(TEXT("Character: %s"), *Character->GetClass()->GetName());
        TextItem.Text = FText::FromString(CharacterInfo);
        TextItem.Position = FVector2D(50.0f, YPos);
        Canvas->DrawItem(TextItem);
        YPos += LineHeight;

        // Character position
        FVector Location = Character->GetActorLocation();
        FString LocationInfo = FString::Printf(TEXT("Location: X=%.1f Y=%.1f Z=%.1f"), Location.X, Location.Y, Location.Z);
        TextItem.Text = FText::FromString(LocationInfo);
        TextItem.Position = FVector2D(50.0f, YPos);
        Canvas->DrawItem(TextItem);
        YPos += LineHeight;

        // Character velocity
        FVector Velocity = Character->GetVelocity();
        float Speed = Velocity.Size();
        FString VelocityInfo = FString::Printf(TEXT("Speed: %.1f"), Speed);
        TextItem.Text = FText::FromString(VelocityInfo);
        TextItem.Position = FVector2D(50.0f, YPos);
        Canvas->DrawItem(TextItem);
        YPos += LineHeight;
    }

    // FPS
    float FPS = 1.0f / GetWorld()->GetDeltaSeconds();
    FString FPSInfo = FString::Printf(TEXT("FPS: %.1f"), FPS);
    TextItem.Text = FText::FromString(FPSInfo);
    TextItem.Position = FVector2D(50.0f, YPos);
    Canvas->DrawItem(TextItem);
}

void AAuracronHUD::DrawPlayerStats()
{
    if (!Canvas)
    {
        return;
    }

    AAuracronCharacter* Character = GetPlayerCharacter();
    if (!Character)
    {
        return;
    }

    // Only update stats at specified intervals
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastStatsUpdateTime < StatsUpdateInterval)
    {
        return;
    }
    LastStatsUpdateTime = CurrentTime;

    // Set up text properties
    FCanvasTextItem TextItem(FVector2D::ZeroVector, FText::GetEmpty(), GEngine->GetMediumFont(), FLinearColor::White);
    TextItem.EnableShadow(FLinearColor::Black);

    // Health Bar
    float HealthPercentage = Character->GetHealthPercentage();
    FString HealthText = FString::Printf(TEXT("Health: %.0f/%.0f"), Character->Health, Character->MaxHealth);
    
    // Position health in bottom left
    float HealthX = 50.0f;
    float HealthY = Canvas->SizeY - 150.0f;
    
    TextItem.Text = FText::FromString(HealthText);
    TextItem.Position = FVector2D(HealthX, HealthY);
    TextItem.SetColor(FLinearColor::Red);
    Canvas->DrawItem(TextItem);

    // Draw health bar background
    FCanvasTileItem HealthBG(FVector2D(HealthX, HealthY + 25.0f), FVector2D(200.0f, 20.0f), FLinearColor::Black);
    Canvas->DrawItem(HealthBG);

    // Draw health bar fill
    FCanvasTileItem HealthFill(FVector2D(HealthX, HealthY + 25.0f), FVector2D(200.0f * HealthPercentage, 20.0f), FLinearColor::Red);
    Canvas->DrawItem(HealthFill);

    // Mana Bar
    float ManaPercentage = Character->GetManaPercentage();
    FString ManaText = FString::Printf(TEXT("Mana: %.0f/%.0f"), Character->Mana, Character->MaxMana);
    
    float ManaY = HealthY + 50.0f;
    
    TextItem.Text = FText::FromString(ManaText);
    TextItem.Position = FVector2D(HealthX, ManaY);
    TextItem.SetColor(FLinearColor::Blue);
    Canvas->DrawItem(TextItem);

    // Draw mana bar background
    FCanvasTileItem ManaBG(FVector2D(HealthX, ManaY + 25.0f), FVector2D(200.0f, 20.0f), FLinearColor::Black);
    Canvas->DrawItem(ManaBG);

    // Draw mana bar fill
    FCanvasTileItem ManaFill(FVector2D(HealthX, ManaY + 25.0f), FVector2D(200.0f * ManaPercentage, 20.0f), FLinearColor::Blue);
    Canvas->DrawItem(ManaFill);
}

void AAuracronHUD::DrawCrosshair()
{
    if (!Canvas)
    {
        return;
    }

    // Get center of screen
    float CenterX = Canvas->SizeX * 0.5f;
    float CenterY = Canvas->SizeY * 0.5f;

    // Draw simple crosshair
    float CrosshairSize = 10.0f;
    FLinearColor CrosshairColor = FLinearColor::White;

    // Horizontal line
    FCanvasLineItem HorizontalLine(
        FVector2D(CenterX - CrosshairSize, CenterY),
        FVector2D(CenterX + CrosshairSize, CenterY)
    );
    HorizontalLine.SetColor(CrosshairColor);
    Canvas->DrawItem(HorizontalLine);

    // Vertical line
    FCanvasLineItem VerticalLine(
        FVector2D(CenterX, CenterY - CrosshairSize),
        FVector2D(CenterX, CenterY + CrosshairSize)
    );
    VerticalLine.SetColor(CrosshairColor);
    Canvas->DrawItem(VerticalLine);
}

AAuracronCharacter* AAuracronHUD::GetPlayerCharacter() const
{
    if (!CachedPlayerCharacter)
    {
        if (APlayerController* PC = GetOwningPlayerController())
        {
            // Remove const to allow assignment
            const_cast<AAuracronHUD*>(this)->CachedPlayerCharacter = Cast<AAuracronCharacter>(PC->GetPawn());
        }
    }
    return CachedPlayerCharacter;
}
