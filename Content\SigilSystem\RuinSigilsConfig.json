{"ruin_sigils": {"flamejante": {"id": 1, "name": "<PERSON><PERSON>", "description": "Dano de fogo contínuo que se espalha para inimigos próximos", "type": "Damage", "element": "Fire", "rarity": "Common", "stats": {"base_damage": 80.0, "damage_per_second": 25.0, "spread_radius": 300.0, "duration": 6.0, "cooldown": 25.0, "energy_cost": 80.0, "scaling_per_level": 0.12}, "effects": {"primary_effect": "Fire Damage Over Time", "secondary_effect": "Damage Spread", "passive_bonus": "+10% Fire Damage", "active_bonus": "Burning effect spreads to nearby enemies"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Ruin/RuinFlamejante_Activation", "active_vfx": "/Game/VFX/Sigils/Ruin/RuinFlamejante_Active", "damage_vfx": "/Game/VFX/Sigils/Ruin/RuinFlamejante_Damage", "primary_color": "#FF4500", "secondary_color": "#FF6347", "particle_count": 600, "effect_scale": 1.2}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Ruin/RuinFlamejante_Activation", "active_sound": "/Game/Audio/Sigils/Ruin/RuinFlamejante_Active", "damage_sound": "/Game/Audio/Sigils/Ruin/RuinFlamejante_Damage", "volume": 0.9, "pitch": 1.1}, "gameplay_tags": ["Sigil.Type.Ruin.<PERSON>", "Sigil.Effect.Damage", "Sigil.Effect.DOT", "Sigil.Element.Fire"]}, "gelido": {"id": 2, "name": "<PERSON><PERSON>", "description": "Dano de gelo que reduz velocidade de movimento e ataque", "type": "Damage", "element": "Ice", "rarity": "Common", "stats": {"base_damage": 70.0, "slow_percentage": 0.5, "freeze_chance": 0.15, "duration": 8.0, "cooldown": 28.0, "energy_cost": 75.0, "scaling_per_level": 0.1}, "effects": {"primary_effect": "Ice Damage + Slow", "secondary_effect": "Freeze Chance", "passive_bonus": "+8% Slow Resistance", "active_bonus": "50% Movement Speed Reduction"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Ruin/RuinGelido_Activation", "active_vfx": "/Game/VFX/Sigils/Ruin/RuinGelido_Active", "freeze_vfx": "/Game/VFX/Sigils/Ruin/Ruin<PERSON><PERSON><PERSON>_Freeze", "primary_color": "#00BFFF", "secondary_color": "#87CEFA", "particle_count": 550, "effect_scale": 1.1}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Ruin/RuinGelido_Activation", "active_sound": "/Game/Audio/Sigils/Ruin/RuinGelido_Active", "freeze_sound": "/Game/Audio/Sigils/Ruin/Ruin<PERSON><PERSON>do_Freeze", "volume": 0.8, "pitch": 0.9}, "gameplay_tags": ["Sigil.Type.Ruin.G<PERSON>do", "Sigil.Effect.Damage", "Sigil.Effect.Slow", "Sigil.Element.Ice"]}, "sombrio": {"id": 3, "name": "<PERSON><PERSON>", "description": "Dano sombrio que reduz visão e precisão do inimigo", "type": "Damage", "element": "Shadow", "rarity": "Uncommon", "stats": {"base_damage": 90.0, "vision_reduction": 0.6, "accuracy_reduction": 0.3, "duration": 7.0, "cooldown": 30.0, "energy_cost": 85.0, "scaling_per_level": 0.11}, "effects": {"primary_effect": "Shadow Damage + Blind", "secondary_effect": "Accuracy Reduction", "passive_bonus": "+12% Stealth Effectiveness", "active_bonus": "60% Vision Range Reduction"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Ruin/RuinSombrio_Activation", "active_vfx": "/Game/VFX/Sigils/Ruin/RuinSombrio_Active", "blind_vfx": "/Game/VFX/Sigils/Ruin/RuinSombrio_Blind", "primary_color": "#2F2F2F", "secondary_color": "#800080", "particle_count": 700, "effect_scale": 1.4}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Ruin/RuinSombrio_Activation", "active_sound": "/Game/Audio/Sigils/Ruin/RuinSombrio_Active", "blind_sound": "/Game/Audio/Sigils/Ruin/Ruin<PERSON>_Blind", "volume": 0.7, "pitch": 0.7}, "gameplay_tags": ["Sigil.Type.Ruin.Sombrio", "Sigil.Effect.Damage", "Sigil.Effect.Blind", "Sigil.Element.Shadow"]}, "corrosivo": {"id": 4, "name": "<PERSON><PERSON>", "description": "Dano corrosivo que reduz armadura e resistências do inimigo", "type": "Damage", "element": "Acid", "rarity": "Rare", "stats": {"base_damage": 60.0, "armor_reduction": 0.4, "resistance_reduction": 0.3, "duration": 10.0, "cooldown": 35.0, "energy_cost": 90.0, "scaling_per_level": 0.13}, "effects": {"primary_effect": "Armor Corrosion", "secondary_effect": "Resistance Reduction", "passive_bonus": "+15% Armor Penetration", "active_bonus": "40% Armor and Resistance Reduction"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Ruin/RuinCorrosivo_Activation", "active_vfx": "/Game/VFX/Sigils/Ruin/RuinCorrosivo_Active", "corrosion_vfx": "/Game/VFX/Sigils/Ruin/RuinCorrosivo_Corrosion", "primary_color": "#32CD32", "secondary_color": "#228B22", "particle_count": 650, "effect_scale": 1.3}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Ruin/RuinCorrosivo_Activation", "active_sound": "/Game/Audio/Sigils/Ruin/RuinCorrosivo_Active", "corrosion_sound": "/Game/Audio/Sigils/Ruin/RuinCorrosivo_Corrosion", "volume": 0.8, "pitch": 1.2}, "gameplay_tags": ["Sigil.Type.Ruin.Corrosivo", "Sigil.Effect.Damage", "Sigil.Effect.ArmorReduction", "Sigil.Element.Acid"]}, "aniquilador": {"id": 5, "name": "<PERSON><PERSON>", "description": "Dano massivo instantâneo com chance de execução", "type": "Damage", "element": "Void", "rarity": "Legendary", "stats": {"base_damage": 300.0, "execution_threshold": 0.2, "critical_multiplier": 2.5, "duration": 1.0, "cooldown": 50.0, "energy_cost": 150.0, "scaling_per_level": 0.2}, "effects": {"primary_effect": "Massive Instant Damage", "secondary_effect": "Execution Below 20% HP", "passive_bonus": "+20% Critical Damage", "active_bonus": "Execute enemies below 20% HP"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Ruin/RuinAniquilador_Activation", "active_vfx": "/Game/VFX/Sigils/Ruin/RuinAniquilador_Active", "execution_vfx": "/Game/VFX/Sigils/Ruin/RuinAniquilador_Execution", "primary_color": "#8B0000", "secondary_color": "#000000", "particle_count": 1000, "effect_scale": 2.5}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Ruin/RuinAniquilador_Activation", "active_sound": "/Game/Audio/Sigils/Ruin/RuinAniquilador_Active", "execution_sound": "/Game/Audio/Sigils/Ruin/RuinAniquilador_Execution", "volume": 1.0, "pitch": 0.6}, "gameplay_tags": ["Sigil.Type.Ruin.<PERSON>", "Sigil.Effect.Damage", "Sigil.Effect.Execution", "Sigil.Element.Void"]}}, "elemental_interactions": {"fire_vs_ice": {"damage_bonus": 1.25, "description": "Fire deals extra damage to ice effects"}, "ice_vs_fire": {"damage_bonus": 0.75, "description": "Ice deals reduced damage to fire effects"}, "shadow_vs_divine": {"damage_bonus": 0.5, "description": "Shadow is weakened by divine effects"}, "void_vs_all": {"damage_bonus": 1.1, "description": "Void damage bypasses most resistances"}}}