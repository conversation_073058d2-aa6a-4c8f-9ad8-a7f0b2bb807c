#include "HarmonyEngineSettings.h"
#include "AuracronHarmonyEngineBridge.h"

UHarmonyEngineSettings::UHarmonyEngineSettings()
{
    // Set category and section for Project Settings
    CategoryName = TEXT("Game");
    SectionName = TEXT("Harmony Engine");
    
    // Initialize default values
    InitializeDefaultValues();
}

void UHarmonyEngineSettings::PostInitProperties()
{
    Super::PostInitProperties();
    
    // Validate and clamp values after initialization
    ValidateAndClampValues();
    
    // Log configuration status
    LogConfigurationStatus();
}

#if WITH_EDITOR
void UHarmonyEngineSettings::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    // Validate settings when changed in editor
    ValidateAndClampValues();

    // Apply settings if the game is running
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        ApplySettings();
    }
}
#endif

bool UHarmonyEngineSettings::ValidateSettings()
{
    bool bIsValid = true;
    
    // Validate core settings
    if (ToxicityDetectionThreshold < 0.0f || ToxicityDetectionThreshold > 1.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid ToxicityDetectionThreshold: %f"), ToxicityDetectionThreshold);
        bIsValid = false;
    }
    
    if (BehaviorAnalysisInterval < 1.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid BehaviorAnalysisInterval: %f"), BehaviorAnalysisInterval);
        bIsValid = false;
    }
    
    if (MaxConcurrentInterventions < 0)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid MaxConcurrentInterventions: %d"), MaxConcurrentInterventions);
        bIsValid = false;
    }
    
    if (InterventionCooldownTime < 30.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid InterventionCooldownTime: %f"), InterventionCooldownTime);
        bIsValid = false;
    }
    
    // Validate tier requirements
    if (BronzeToSilverRequirement <= 0 || SilverToGoldRequirement <= BronzeToSilverRequirement ||
        GoldToPlatinumRequirement <= SilverToGoldRequirement || PlatinumToDiamondRequirement <= GoldToPlatinumRequirement ||
        DiamondToLegendaryRequirement <= PlatinumToDiamondRequirement)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid tier requirements progression"));
        bIsValid = false;
    }
    
    // Validate performance settings
    if (TickInterval < 0.1f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid TickInterval: %f"), TickInterval);
        bIsValid = false;
    }
    
    if (MaxPlayersPerAnalysisBatch < 1)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid MaxPlayersPerAnalysisBatch: %d"), MaxPlayersPerAnalysisBatch);
        bIsValid = false;
    }
    
    return bIsValid;
}

void UHarmonyEngineSettings::ResetToDefaults()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Resetting Harmony Engine settings to defaults"));
    
    InitializeDefaultValues();
    ValidateAndClampValues();
    
    // Save configuration
    SaveConfig();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine settings reset to defaults"));
}

void UHarmonyEngineSettings::ApplySettings()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applying Harmony Engine settings"));
    
    // Apply settings to running systems
    // This would update all active Harmony Engine components with new settings
    
    // In a full implementation, this would:
    // 1. Update HarmonyEngineSubsystem configuration
    // 2. Update all active components
    // 3. Restart systems if necessary
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine settings applied successfully"));
}

UHarmonyEngineSettings* UHarmonyEngineSettings::GetHarmonyEngineSettings()
{
    return GetMutableDefault<UHarmonyEngineSettings>();
}

void UHarmonyEngineSettings::InitializeDefaultValues()
{
    // Core System Settings
    bEnableHarmonyEngine = true;
    bEnableRealTimeMonitoring = true;
    bEnablePredictiveIntervention = true;
    bEnableCommunityHealing = true;
    bEnableMachineLearning = true;
    
    // Behavior Detection Settings
    ToxicityDetectionThreshold = 0.6f;
    PositivityDetectionThreshold = 0.7f;
    FrustrationDetectionThreshold = 0.6f;
    BehaviorAnalysisInterval = 30.0f;
    bEnableAutomaticBehaviorDetection = true;
    
    // Intervention Settings
    MaxConcurrentInterventions = 5;
    InterventionCooldownTime = 300.0f; // 5 minutes
    InterventionTimeout = 300.0f; // 5 minutes
    bEnableAutomaticEscalation = true;
    EscalationThreshold = 0.8f;
    
    // Community Healing Settings
    MaxConcurrentHealingSessions = 10;
    MaxHealingSessionDuration = 1800.0f; // 30 minutes
    MinHealerSkillLevel = 0.6f;
    MaxHealersPerSession = 3;
    bEnableAutomaticHealerMatching = true;
    
    // Rewards System Settings
    MaxRewardsPerSession = 10;
    RewardCooldownTime = 60.0f; // 1 minute
    bEnableProgressiveRewards = true;
    bEnableSpecialEvents = true;
    RewardAmplificationFactor = 1.5f;
    
    // Emotional Intelligence Settings
    EmotionalMonitoringInterval = 5.0f;
    EscalationDetectionWindow = 120.0f; // 2 minutes
    MaxSupportMessagesPerSession = 3;
    bEnablePredictiveEmotionalAnalysis = true;
    
    // Machine Learning Settings
    MinTrainingDataPoints = 50;
    MLModelUpdateInterval = 300.0f; // 5 minutes
    MLModelAccuracyThreshold = 0.75f;
    bEnableMLModelTraining = true;
    MaxTrainingDatasetSize = 5000;
    
    // Tier Requirements
    BronzeToSilverRequirement = 100;
    SilverToGoldRequirement = 500;
    GoldToPlatinumRequirement = 1500;
    PlatinumToDiamondRequirement = 5000;
    DiamondToLegendaryRequirement = 15000;
    
    // Logging and Debug Settings
    bEnableVerboseLogging = false;
    bEnableBehaviorDataLogging = true;
    bEnableInterventionLogging = true;
    bSaveDebugDataToDisk = false;
    DebugDataSavePath = TEXT("Saved/HarmonyEngine/Debug/");
    
    // Performance Settings
    TickInterval = 1.0f;
    MaxPlayersPerAnalysisBatch = 10;
    bEnablePerformanceOptimizations = true;
    MaxBehaviorHistoryEntries = 1000;
    
    // Network Settings
    bReplicateBehaviorData = true;
    bReplicateInterventions = true;
    bReplicateRewards = true;
    NetworkUpdateFrequency = 1.0f;
    
    // Integration Settings
    bIntegrateWithGameplayAbilitySystem = true;
    bIntegrateWithChatSystem = true;
    bIntegrateWithUISystem = true;
    bIntegrateWithAudioSystem = true;
}

void UHarmonyEngineSettings::ValidateAndClampValues()
{
    // Clamp values to valid ranges
    ToxicityDetectionThreshold = FMath::Clamp(ToxicityDetectionThreshold, 0.0f, 1.0f);
    PositivityDetectionThreshold = FMath::Clamp(PositivityDetectionThreshold, 0.0f, 1.0f);
    FrustrationDetectionThreshold = FMath::Clamp(FrustrationDetectionThreshold, 0.0f, 1.0f);
    BehaviorAnalysisInterval = FMath::Max(BehaviorAnalysisInterval, 1.0f);
    
    MaxConcurrentInterventions = FMath::Max(MaxConcurrentInterventions, 0);
    InterventionCooldownTime = FMath::Max(InterventionCooldownTime, 30.0f);
    InterventionTimeout = FMath::Max(InterventionTimeout, 60.0f);
    EscalationThreshold = FMath::Clamp(EscalationThreshold, 0.0f, 1.0f);
    
    MaxConcurrentHealingSessions = FMath::Max(MaxConcurrentHealingSessions, 0);
    MaxHealingSessionDuration = FMath::Max(MaxHealingSessionDuration, 300.0f);
    MinHealerSkillLevel = FMath::Clamp(MinHealerSkillLevel, 0.0f, 1.0f);
    MaxHealersPerSession = FMath::Max(MaxHealersPerSession, 1);
    
    MaxRewardsPerSession = FMath::Max(MaxRewardsPerSession, 0);
    RewardCooldownTime = FMath::Max(RewardCooldownTime, 0.0f);
    RewardAmplificationFactor = FMath::Max(RewardAmplificationFactor, 1.0f);
    
    EmotionalMonitoringInterval = FMath::Max(EmotionalMonitoringInterval, 1.0f);
    EscalationDetectionWindow = FMath::Max(EscalationDetectionWindow, 60.0f);
    MaxSupportMessagesPerSession = FMath::Max(MaxSupportMessagesPerSession, 0);
    
    MinTrainingDataPoints = FMath::Max(MinTrainingDataPoints, 10);
    MLModelUpdateInterval = FMath::Max(MLModelUpdateInterval, 60.0f);
    MLModelAccuracyThreshold = FMath::Clamp(MLModelAccuracyThreshold, 0.0f, 1.0f);
    MaxTrainingDatasetSize = FMath::Max(MaxTrainingDatasetSize, 100);
    
    // Validate tier progression
    BronzeToSilverRequirement = FMath::Max(BronzeToSilverRequirement, 1);
    SilverToGoldRequirement = FMath::Max(SilverToGoldRequirement, BronzeToSilverRequirement + 1);
    GoldToPlatinumRequirement = FMath::Max(GoldToPlatinumRequirement, SilverToGoldRequirement + 1);
    PlatinumToDiamondRequirement = FMath::Max(PlatinumToDiamondRequirement, GoldToPlatinumRequirement + 1);
    DiamondToLegendaryRequirement = FMath::Max(DiamondToLegendaryRequirement, PlatinumToDiamondRequirement + 1);
    
    TickInterval = FMath::Max(TickInterval, 0.1f);
    MaxPlayersPerAnalysisBatch = FMath::Max(MaxPlayersPerAnalysisBatch, 1);
    MaxBehaviorHistoryEntries = FMath::Max(MaxBehaviorHistoryEntries, 100);
    
    NetworkUpdateFrequency = FMath::Max(NetworkUpdateFrequency, 0.1f);
    
    // Validate debug path
    if (DebugDataSavePath.IsEmpty())
    {
        DebugDataSavePath = TEXT("Saved/HarmonyEngine/Debug/");
    }
}

void UHarmonyEngineSettings::LogConfigurationStatus()
{
    if (!bEnableVerboseLogging)
    {
        return;
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("=== Harmony Engine Configuration ==="));
    UE_LOG(LogHarmonyEngine, Log, TEXT("Core System Enabled: %s"), bEnableHarmonyEngine ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogHarmonyEngine, Log, TEXT("Real-Time Monitoring: %s"), bEnableRealTimeMonitoring ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogHarmonyEngine, Log, TEXT("Predictive Intervention: %s"), bEnablePredictiveIntervention ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogHarmonyEngine, Log, TEXT("Community Healing: %s"), bEnableCommunityHealing ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogHarmonyEngine, Log, TEXT("Machine Learning: %s"), bEnableMachineLearning ? TEXT("Yes") : TEXT("No"));
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Toxicity Threshold: %.3f"), ToxicityDetectionThreshold);
    UE_LOG(LogHarmonyEngine, Log, TEXT("Positivity Threshold: %.3f"), PositivityDetectionThreshold);
    UE_LOG(LogHarmonyEngine, Log, TEXT("Frustration Threshold: %.3f"), FrustrationDetectionThreshold);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Max Concurrent Interventions: %d"), MaxConcurrentInterventions);
    UE_LOG(LogHarmonyEngine, Log, TEXT("Intervention Cooldown: %.1f seconds"), InterventionCooldownTime);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Tier Requirements - Bronze->Silver: %d, Silver->Gold: %d, Gold->Platinum: %d"),
        BronzeToSilverRequirement, SilverToGoldRequirement, GoldToPlatinumRequirement);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("====================================="));
}
