# Vertical Connectors Curve Assets

This directory contains curve assets for the Vertical Connectors system.

## Missing Assets (Placeholders Created)

The following curve assets were referenced in the code but missing from the project:

### Movement Curves
- `Curve_PortalAnima_Movement.uasset` - Portal Anima movement curve
- `Curve_FendaFluxo_Movement.uasset` - Fenda Fluxo movement curve  
- `Curve_CipoAstria_Movement.uasset` - Cipo Astria movement curve
- `Curve_ElevadorVortice_Movement.uasset` - Elevador Vortice movement curve
- `Curve_RespiradoroGeotermal_Movement.uasset` - Respiradoro Geotermal movement curve

## Status
These curve assets need to be created by the design team or replaced with proper references.

## Notes
- All references have been updated to use placeholder curves
- The connector system will function with basic linear interpolation
- Replace with final curve assets when available
- Each curve should define the movement pattern for its respective connector type
