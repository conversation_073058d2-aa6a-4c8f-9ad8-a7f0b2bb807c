/**
 * AuracronLayerComponent.cpp
 * 
 * Implementação completa do componente de camada usando UE 5.6 APIs modernas.
 * Gerencia a relação do ator com o sistema de realm dinâmico.
 */

#include "AuracronLayerComponent.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronRealmTransitionComponent.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/Engine.h"

UAuracronLayerComponent::UAuracronLayerComponent()
{
    // Set this component to be ticked every frame
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.bStartWithTickEnabled = true;
    
    // Initialize default values
    CurrentLayer = EAuracronRealmLayer::Terrestrial;
    PreviousLayer = EAuracronRealmLayer::None;
    bIsTransitioning = false;
    TransitionTargetLayer = EAuracronRealmLayer::None;
    CurrentTransitionType = ERealmTransitionType::Gradual;
    TransitionProgress = 0.0f;
    TransitionDuration = 2.0f;
    TransitionStartTime = 0.0f;
    
    // Rendering state
    bIsLayerVisible = true;
    bRenderingEnabled = true;
    CurrentLODLevel = 0;
    
    // Initialize interaction settings
    InteractionSettings = FAuracronLayerInteraction();
    MovementSettings = FAuracronLayerMovement();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer Component created"));
}

void UAuracronLayerComponent::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer Component BeginPlay"));
    
    // Cache subsystem references
    if (UWorld* World = GetWorld())
    {
        CachedRealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    }
    
    // Initialize layer modifiers
    InitializeLayerModifiers();
    
    // Apply initial layer modifiers
    ApplyLayerModifiers();
    
    // Update initial visibility
    UpdateLayerVisibility();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer Component initialized in layer %s"), 
        *UEnum::GetValueAsString(CurrentLayer));
}

void UAuracronLayerComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer Component EndPlay"));
    
    // Remove layer modifiers
    RemoveLayerModifiers();
    
    // Cancel any active transitions
    if (bIsTransitioning)
    {
        CancelTransition();
    }
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronLayerComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Update transition if active
    if (bIsTransitioning)
    {
        UpdateTransition(DeltaTime);
    }
    
    // Update layer-specific behavior
    UpdateLayerSpecificBehavior(DeltaTime);
    
    // Update visibility based on current layer
    UpdateLayerVisibility();
}

// === Layer Management ===

void UAuracronLayerComponent::SetCurrentLayer(EAuracronRealmLayer NewLayer)
{
    if (NewLayer == CurrentLayer)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting layer from %s to %s"), 
        *UEnum::GetValueAsString(CurrentLayer), *UEnum::GetValueAsString(NewLayer));
    
    EAuracronRealmLayer OldLayer = CurrentLayer;
    PreviousLayer = CurrentLayer;
    CurrentLayer = NewLayer;
    
    // Remove old layer modifiers
    RemoveLayerModifiers();
    
    // Apply new layer modifiers
    ApplyLayerModifiers();
    
    // Update visibility
    UpdateLayerVisibility();
    
    // Trigger layer changed event
    OnLayerChanged(OldLayer, NewLayer);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer changed successfully"));
}

bool UAuracronLayerComponent::CanTransitionToLayer(EAuracronRealmLayer TargetLayer) const
{
    // Check if already in target layer
    if (TargetLayer == CurrentLayer)
    {
        return false;
    }
    
    // Check if currently transitioning
    if (bIsTransitioning)
    {
        return false;
    }
    
    // Check movement settings
    if (!MovementSettings.bCanTransitionLayers)
    {
        return false;
    }
    
    // Check if target layer is allowed
    if (MovementSettings.AllowedLayers.Num() > 0 && !MovementSettings.AllowedLayers.Contains(TargetLayer))
    {
        return false;
    }
    
    // Check cooldown
    if (UWorld* World = GetWorld())
    {
        float CurrentTime = World->GetTimeSeconds();
        if (CurrentTime - MovementSettings.LastTransitionTime < MovementSettings.TransitionCooldown)
        {
            return false;
        }
    }
    
    return ValidateTransition(TargetLayer);
}

// === Transition Management ===

bool UAuracronLayerComponent::RequestLayerTransition(EAuracronRealmLayer TargetLayer, ERealmTransitionType TransitionType)
{
    if (!CanTransitionToLayer(TargetLayer))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot transition to layer %s"), *UEnum::GetValueAsString(TargetLayer));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting transition from %s to %s"), 
        *UEnum::GetValueAsString(CurrentLayer), *UEnum::GetValueAsString(TargetLayer));
    
    // Setup transition
    bIsTransitioning = true;
    TransitionTargetLayer = TargetLayer;
    CurrentTransitionType = TransitionType;
    TransitionProgress = 0.0f;
    
    // Set transition duration based on type
    switch (TransitionType)
    {
        case ERealmTransitionType::Instant:
            TransitionDuration = 0.1f;
            break;
        case ERealmTransitionType::Gradual:
            TransitionDuration = 2.0f;
            break;
        case ERealmTransitionType::Cinematic:
            TransitionDuration = 5.0f;
            break;
        case ERealmTransitionType::Combat:
            TransitionDuration = 0.5f;
            break;
        case ERealmTransitionType::Stealth:
            TransitionDuration = 3.0f;
            break;
        default:
            TransitionDuration = 2.0f;
            break;
    }
    
    // Apply transition speed multiplier
    TransitionDuration *= MovementSettings.TransitionSpeedMultiplier;
    
    if (UWorld* World = GetWorld())
    {
        TransitionStartTime = World->GetTimeSeconds();
        MovementSettings.LastTransitionTime = TransitionStartTime;
    }
    
    // Apply transition effects
    ApplyTransitionEffects();
    
    // Trigger transition started event
    OnTransitionStarted(TargetLayer);
    
    return true;
}

void UAuracronLayerComponent::CancelTransition()
{
    if (!bIsTransitioning)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Cancelling transition to %s"), *UEnum::GetValueAsString(TransitionTargetLayer));
    
    // Reset transition state
    bIsTransitioning = false;
    TransitionTargetLayer = EAuracronRealmLayer::None;
    TransitionProgress = 0.0f;
    
    // Restore original layer state
    ApplyLayerModifiers();
}

// === Layer Modifiers ===

FAuracronLayerModifiers UAuracronLayerComponent::GetCurrentLayerModifiers() const
{
    const FAuracronLayerModifiers* Modifiers = LayerModifiers.Find(CurrentLayer);
    return Modifiers ? *Modifiers : FAuracronLayerModifiers();
}

void UAuracronLayerComponent::ApplyLayerModifiers()
{
    FAuracronLayerModifiers Modifiers = GetCurrentLayerModifiers();
    
    // Apply modifiers to owner actor
    if (AActor* Owner = GetOwner())
    {
        // Apply movement speed modifier
        if (APawn* Pawn = Cast<APawn>(Owner))
        {
            // Implementation would depend on movement component
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applied movement speed modifier %.2f"), Modifiers.MovementSpeedMultiplier);
        }
        
        // Apply other modifiers as needed
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applied layer modifiers for %s"), *UEnum::GetValueAsString(CurrentLayer));
    }
    
    // Trigger modifiers applied event
    OnLayerModifiersApplied(Modifiers);
}

void UAuracronLayerComponent::RemoveLayerModifiers()
{
    // Reset modifiers to default values
    FAuracronLayerModifiers DefaultModifiers;
    
    if (AActor* Owner = GetOwner())
    {
        // Remove movement speed modifier
        if (APawn* Pawn = Cast<APawn>(Owner))
        {
            // Implementation would depend on movement component
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Removed movement speed modifier"));
        }
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Removed layer modifiers"));
    }
}

// === Cross-Layer Interactions ===

bool UAuracronLayerComponent::CanAffectLayer(EAuracronRealmLayer TargetLayer) const
{
    if (!InteractionSettings.bCanInteractCrossLayer)
    {
        return TargetLayer == CurrentLayer;
    }
    
    return InteractionSettings.AffectableLayers.Contains(TargetLayer) || TargetLayer == CurrentLayer;
}

float UAuracronLayerComponent::CalculateCrossLayerDamage(float BaseDamage, EAuracronRealmLayer TargetLayer) const
{
    if (TargetLayer == CurrentLayer)
    {
        return BaseDamage; // Same layer, no modifier
    }
    
    if (!CanAffectLayer(TargetLayer))
    {
        return 0.0f; // Cannot affect this layer
    }
    
    return BaseDamage * InteractionSettings.CrossLayerDamageMultiplier;
}

float UAuracronLayerComponent::CalculateCrossLayerRange(float BaseRange, EAuracronRealmLayer TargetLayer) const
{
    if (TargetLayer == CurrentLayer)
    {
        return BaseRange; // Same layer, no modifier
    }
    
    if (!CanAffectLayer(TargetLayer))
    {
        return 0.0f; // Cannot affect this layer
    }
    
    return BaseRange * InteractionSettings.CrossLayerRangeMultiplier;
}

// === Visibility and Rendering ===

void UAuracronLayerComponent::UpdateLayerVisibility()
{
    // Determine if this layer should be visible based on current realm state
    bool bShouldBeVisible = true;
    
    if (CachedRealmSubsystem)
    {
        // Check with realm subsystem for layer visibility
        // Implementation would depend on realm subsystem API
        bShouldBeVisible = true; // Default to visible
    }
    
    if (bIsLayerVisible != bShouldBeVisible)
    {
        bIsLayerVisible = bShouldBeVisible;
        SetLayerRenderingEnabled(bShouldBeVisible && bRenderingEnabled);
    }
}

void UAuracronLayerComponent::SetLayerRenderingEnabled(bool bEnabled)
{
    if (AActor* Owner = GetOwner())
    {
        // Update rendering for all primitive components
        TArray<UPrimitiveComponent*> PrimitiveComponents;
        Owner->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
        
        for (UPrimitiveComponent* Component : PrimitiveComponents)
        {
            if (Component)
            {
                Component->SetVisibility(bEnabled);
                Component->SetHiddenInGame(!bEnabled);
            }
        }
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Set layer rendering enabled: %s"), bEnabled ? TEXT("true") : TEXT("false"));
    }
}

// === Private Implementation Methods ===

void UAuracronLayerComponent::InitializeLayerModifiers()
{
    // Initialize default modifiers for each layer
    LayerModifiers.Empty();

    // Terrestrial Layer (Planície Radiante)
    FAuracronLayerModifiers TerrestrialModifiers;
    TerrestrialModifiers.MovementSpeedMultiplier = 1.0f;
    TerrestrialModifiers.DamageMultiplier = 1.0f;
    TerrestrialModifiers.DefenseMultiplier = 1.0f;
    TerrestrialModifiers.AbilityRangeMultiplier = 1.0f;
    TerrestrialModifiers.AbilityCooldownMultiplier = 1.0f;
    TerrestrialModifiers.VisibilityMultiplier = 1.0f;
    TerrestrialModifiers.StealthBonus = 0.0f;
    LayerModifiers.Add(EAuracronRealmLayer::Terrestrial, TerrestrialModifiers);

    // Celestial Layer (Firmamento Zephyr)
    FAuracronLayerModifiers CelestialModifiers;
    CelestialModifiers.MovementSpeedMultiplier = 1.2f;
    CelestialModifiers.DamageMultiplier = 0.9f;
    CelestialModifiers.DefenseMultiplier = 0.8f;
    CelestialModifiers.AbilityRangeMultiplier = 1.3f;
    CelestialModifiers.AbilityCooldownMultiplier = 0.9f;
    CelestialModifiers.VisibilityMultiplier = 1.2f;
    CelestialModifiers.StealthBonus = -0.2f;
    LayerModifiers.Add(EAuracronRealmLayer::Celestial, CelestialModifiers);

    // Abyssal Layer (Abismo Umbrio)
    FAuracronLayerModifiers AbyssalModifiers;
    AbyssalModifiers.MovementSpeedMultiplier = 0.8f;
    AbyssalModifiers.DamageMultiplier = 1.3f;
    AbyssalModifiers.DefenseMultiplier = 1.2f;
    AbyssalModifiers.AbilityRangeMultiplier = 0.8f;
    AbyssalModifiers.AbilityCooldownMultiplier = 1.1f;
    AbyssalModifiers.VisibilityMultiplier = 0.7f;
    AbyssalModifiers.StealthBonus = 0.3f;
    LayerModifiers.Add(EAuracronRealmLayer::Abyssal, AbyssalModifiers);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer modifiers initialized"));
}

void UAuracronLayerComponent::UpdateTransition(float DeltaTime)
{
    if (!bIsTransitioning)
    {
        return;
    }

    // Calculate transition progress
    if (UWorld* World = GetWorld())
    {
        float CurrentTime = World->GetTimeSeconds();
        float ElapsedTime = CurrentTime - TransitionStartTime;
        TransitionProgress = FMath::Clamp(ElapsedTime / TransitionDuration, 0.0f, 1.0f);
    }

    // Apply transition effects
    ApplyTransitionEffects();

    // Check if transition is complete
    if (TransitionProgress >= 1.0f)
    {
        CompleteTransition();
    }
}

void UAuracronLayerComponent::CompleteTransition()
{
    if (!bIsTransitioning)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Completing transition to %s"), *UEnum::GetValueAsString(TransitionTargetLayer));

    // Set new layer
    EAuracronRealmLayer OldLayer = CurrentLayer;
    SetCurrentLayer(TransitionTargetLayer);

    // Reset transition state
    bIsTransitioning = false;
    TransitionTargetLayer = EAuracronRealmLayer::None;
    TransitionProgress = 0.0f;

    // Trigger transition completed event
    OnTransitionCompleted(CurrentLayer);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition completed successfully"));
}

void UAuracronLayerComponent::ApplyTransitionEffects()
{
    if (!bIsTransitioning)
    {
        return;
    }

    // Apply visual transition effects based on progress
    float Progress = TransitionProgress;

    // Interpolate between current and target layer modifiers
    FAuracronLayerModifiers CurrentModifiers = GetCurrentLayerModifiers();
    const FAuracronLayerModifiers* TargetModifiers = LayerModifiers.Find(TransitionTargetLayer);

    if (TargetModifiers)
    {
        // Interpolate modifiers during transition
        FAuracronLayerModifiers InterpolatedModifiers;
        InterpolatedModifiers.MovementSpeedMultiplier = FMath::Lerp(CurrentModifiers.MovementSpeedMultiplier, TargetModifiers->MovementSpeedMultiplier, Progress);
        InterpolatedModifiers.DamageMultiplier = FMath::Lerp(CurrentModifiers.DamageMultiplier, TargetModifiers->DamageMultiplier, Progress);
        InterpolatedModifiers.DefenseMultiplier = FMath::Lerp(CurrentModifiers.DefenseMultiplier, TargetModifiers->DefenseMultiplier, Progress);
        InterpolatedModifiers.AbilityRangeMultiplier = FMath::Lerp(CurrentModifiers.AbilityRangeMultiplier, TargetModifiers->AbilityRangeMultiplier, Progress);
        InterpolatedModifiers.AbilityCooldownMultiplier = FMath::Lerp(CurrentModifiers.AbilityCooldownMultiplier, TargetModifiers->AbilityCooldownMultiplier, Progress);
        InterpolatedModifiers.VisibilityMultiplier = FMath::Lerp(CurrentModifiers.VisibilityMultiplier, TargetModifiers->VisibilityMultiplier, Progress);
        InterpolatedModifiers.StealthBonus = FMath::Lerp(CurrentModifiers.StealthBonus, TargetModifiers->StealthBonus, Progress);

        // Apply interpolated modifiers
        // Implementation would apply these to the actor
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applied transition effects - Progress: %.2f"), Progress);
    }
}

void UAuracronLayerComponent::UpdateLayerSpecificBehavior(float DeltaTime)
{
    switch (CurrentLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            UpdateTerrestrialBehavior(DeltaTime);
            break;
        case EAuracronRealmLayer::Celestial:
            UpdateCelestialBehavior(DeltaTime);
            break;
        case EAuracronRealmLayer::Abyssal:
            UpdateAbyssalBehavior(DeltaTime);
            break;
        default:
            break;
    }
}

void UAuracronLayerComponent::UpdateTerrestrialBehavior(float DeltaTime)
{
    // Terrestrial layer specific behavior
    // - Standard movement and abilities
    // - Normal visibility
    // - Balanced stats
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating terrestrial behavior"));
}

void UAuracronLayerComponent::UpdateCelestialBehavior(float DeltaTime)
{
    // Celestial layer specific behavior
    // - Enhanced movement speed
    // - Increased ability range
    // - Reduced stealth
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating celestial behavior"));
}

void UAuracronLayerComponent::UpdateAbyssalBehavior(float DeltaTime)
{
    // Abyssal layer specific behavior
    // - Reduced movement speed
    // - Enhanced damage and defense
    // - Increased stealth
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating abyssal behavior"));
}

UAuracronDynamicRealmSubsystem* UAuracronLayerComponent::GetRealmSubsystem() const
{
    if (CachedRealmSubsystem)
    {
        return CachedRealmSubsystem;
    }

    if (UWorld* World = GetWorld())
    {
        return World->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    }

    return nullptr;
}

bool UAuracronLayerComponent::ValidateTransition(EAuracronRealmLayer TargetLayer) const
{
    // Validate transition based on game rules
    // This could include checks for:
    // - Player level requirements
    // - Quest completion
    // - Item requirements
    // - Environmental conditions

    // For now, allow all transitions
    return true;
}

void UAuracronLayerComponent::LogComponentStatus() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Layer Component Status:"));
    UE_LOG(LogTemp, Log, TEXT("  Current Layer: %s"), *UEnum::GetValueAsString(CurrentLayer));
    UE_LOG(LogTemp, Log, TEXT("  Is Transitioning: %s"), bIsTransitioning ? TEXT("true") : TEXT("false"));
    UE_LOG(LogTemp, Log, TEXT("  Transition Progress: %.2f"), TransitionProgress);
    UE_LOG(LogTemp, Log, TEXT("  Is Visible: %s"), bIsLayerVisible ? TEXT("true") : TEXT("false"));
}
