#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "GameplayTagContainer.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineGameMode.generated.h"

class UHarmonyEngineSubsystem;
class APlayerController;

/**
 * Example Game Mode that demonstrates Harmony Engine integration
 * Shows how to integrate anti-toxicity AI with your game's core systems
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API AHarmonyEngineGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    AHarmonyEngineGameMode();

    // GameModeBase overrides
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void PostLogin(APlayerController* NewPlayer) override;
    virtual void Logout(AController* Exiting) override;

    // Harmony Engine Integration
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnPlayerChatMessage(APlayerController* Player, const FString& Message);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnPlayerAction(APlayerController* Player, const FString& ActionType, bool bIsPositive);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnPlayerKill(APlayerController* Killer, APlayerController* Victim);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnPlayerDeath(APlayerController* Player, APlayerController* Killer);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnPlayerDisconnect(APlayerController* Player, bool bRageQuit);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void OnTeamworkAction(APlayerController* Player, const FString& TeamworkType);

    // Behavior Analysis Events
    UFUNCTION(BlueprintImplementableEvent, Category = "Harmony Engine Events")
    void OnToxicBehaviorDetected(APlayerController* Player, float ToxicityLevel);

    UFUNCTION(BlueprintImplementableEvent, Category = "Harmony Engine Events")
    void OnPositiveBehaviorDetected(APlayerController* Player, float PositivityLevel);

    UFUNCTION(BlueprintImplementableEvent, Category = "Harmony Engine Events")
    void OnInterventionTriggered(APlayerController* Player, const FString& InterventionMessage);

    UFUNCTION(BlueprintImplementableEvent, Category = "Harmony Engine Events")
    void OnCommunityHealingStarted(APlayerController* Victim, APlayerController* Healer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Harmony Engine Events")
    void OnKindnessRewardAwarded(APlayerController* Player, int32 Points, const FString& Reason);

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableHarmonyEngine;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    float BehaviorAnalysisInterval;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    float ToxicityDetectionThreshold;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableAutomaticInterventions;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableCommunityHealing;

    // Harmony Engine Reference
    UPROPERTY()
    TObjectPtr<UHarmonyEngineSubsystem> HarmonyEngineSubsystem;

    // Player Tracking
    UPROPERTY()
    TMap<FString, FDateTime> PlayerJoinTimes;

    UPROPERTY()
    TMap<FString, int32> PlayerActionCounts;

    UPROPERTY()
    TMap<FString, float> PlayerSessionDurations;

private:
    // Event handlers for Harmony Engine
    UFUNCTION()
    void OnHarmonyBehaviorDetected(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData);

    UFUNCTION()
    void OnHarmonyInterventionTriggered(const FString& PlayerID, const FHarmonyInterventionData& InterventionData);

    UFUNCTION()
    void OnHarmonyCommunityHealing(const FString& HealerID, const FString& VictimID);

    UFUNCTION()
    void OnHarmonyKindnessReward(const FString& PlayerID, const FKindnessReward& Reward);

    UFUNCTION()
    void OnHarmonyLevelChanged(int32 NewHarmonyLevel);

    // Behavior analysis helpers
    void AnalyzeChatMessage(const FString& PlayerID, const FString& Message);
    void UpdatePlayerBehaviorMetrics(const FString& PlayerID);
    void CheckForInterventionNeeds(const FString& PlayerID);

    // Action analysis methods
    int32 CalculateActionKindnessPoints(const FString& ActionType);
    int32 CalculateTeamworkKindnessPoints(const FString& TeamworkType);
    bool IsKillPotentiallyToxic(APlayerController* Killer, APlayerController* Victim);
    void CheckVictimForSupport(const FString& VictimID);

    // Community events
    void TriggerPositiveCommunityEvent();
    void TriggerCommunityHealingEvent();
    void BroadcastMessage(const FString& Message);
    void ActivateHealingBonus(const FString& PlayerID);
    
    // Player action tracking
    void TrackPlayerAction(const FString& PlayerID, const FString& ActionType, bool bIsPositive);
    void UpdateSessionDuration(const FString& PlayerID);
    FPlayerBehaviorSnapshot CreateBehaviorSnapshot(const FString& PlayerID);
    
    // Toxicity detection
    float AnalyzeMessageToxicity(const FString& Message);
    bool ContainsToxicKeywords(const FString& Message);
    float CalculateMessageSentiment(const FString& Message);
    
    // Positivity detection
    float AnalyzeMessagePositivity(const FString& Message);
    bool ContainsPositiveKeywords(const FString& Message);
    bool ContainsEncouragingPhrases(const FString& Message);
    
    // Utility functions
    APlayerController* GetPlayerControllerByID(const FString& PlayerID);
    FString GetPlayerIDFromController(APlayerController* PlayerController);
    void InitializeHarmonyEngineCallbacks();
    void CleanupHarmonyEngineCallbacks();
};
