// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Tutorial Save Game Implementation
// Sistema de salvamento de progresso do tutorial usando UE5.6 Save Game API

#include "AuracronTutorialSaveGame.h"
#include "Engine/Engine.h"

UAuracronTutorialSaveGame::UAuracronTutorialSaveGame()
{
    SaveSlotName = TEXT("AuracronTutorialProgress");
    UserIndex = 0;
    SaveGameVersion = 1;
    CreationTime = FDateTime::Now();
    LastModified = FDateTime::Now();
    bAIMentorEnabled = true;
    AIMentorVolume = 1.0f;
    AIMentorSpeechRate = 1.0f;
}

void UAuracronTutorialSaveGame::UpdateTutorialProgress(const FString& TutorialID, const FAuracronTutorialProgressData& ProgressData)
{
    if (TutorialID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tentativa de atualizar progresso com TutorialID vazio"));
        return;
    }

    // Atualiza o progresso no mapa
    TutorialProgressMap.Add(TutorialID, ProgressData);
    
    // Atualiza timestamp
    LastModified = FDateTime::Now();
    
    // Se o tutorial foi completado, adiciona à lista de completados
    if (ProgressData.TutorialState == EAuracronTutorialState::Completed)
    {
        CompletedTutorials.AddUnique(TutorialID);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso atualizado para tutorial %s - Estado: %d, Passo: %d, Progresso: %.2f%%"),
        *TutorialID, 
        static_cast<int32>(ProgressData.TutorialState),
        ProgressData.CurrentStepIndex,
        ProgressData.ProgressPercentage);
}

FAuracronTutorialProgressData UAuracronTutorialSaveGame::GetTutorialProgress(const FString& TutorialID) const
{
    if (const FAuracronTutorialProgressData* FoundProgress = TutorialProgressMap.Find(TutorialID))
    {
        return *FoundProgress;
    }
    
    // Retorna dados padrão se não encontrado
    FAuracronTutorialProgressData DefaultProgress;
    DefaultProgress.TutorialID = TutorialID;
    return DefaultProgress;
}

bool UAuracronTutorialSaveGame::IsTutorialCompleted(const FString& TutorialID) const
{
    return CompletedTutorials.Contains(TutorialID);
}

void UAuracronTutorialSaveGame::MarkTutorialCompleted(const FString& TutorialID)
{
    if (TutorialID.IsEmpty())
    {
        return;
    }
    
    CompletedTutorials.AddUnique(TutorialID);
    
    // Atualiza o progresso no mapa também
    if (FAuracronTutorialProgressData* FoundProgress = TutorialProgressMap.Find(TutorialID))
    {
        FoundProgress->TutorialState = EAuracronTutorialState::Completed;
        FoundProgress->ProgressPercentage = 100.0f;
        FoundProgress->LastUpdated = FDateTime::Now();
    }
    else
    {
        // Cria novo registro de progresso
        FAuracronTutorialProgressData NewProgress;
        NewProgress.TutorialID = TutorialID;
        NewProgress.TutorialState = EAuracronTutorialState::Completed;
        NewProgress.ProgressPercentage = 100.0f;
        TutorialProgressMap.Add(TutorialID, NewProgress);
    }
    
    LastModified = FDateTime::Now();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tutorial %s marcado como completado"), *TutorialID);
}

void UAuracronTutorialSaveGame::ResetTutorialProgress(const FString& TutorialID)
{
    if (TutorialID.IsEmpty())
    {
        return;
    }
    
    // Remove do mapa de progresso
    TutorialProgressMap.Remove(TutorialID);
    
    // Remove da lista de completados
    CompletedTutorials.Remove(TutorialID);
    
    LastModified = FDateTime::Now();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progresso do tutorial %s foi resetado"), *TutorialID);
}

void UAuracronTutorialSaveGame::ResetAllProgress()
{
    // Armazenar estatísticas antes do reset para logs
    int32 TotalTutorialsInProgress = TutorialProgressMap.Num();
    int32 TotalCompletedTutorials = CompletedTutorials.Num();
    
    // Limpar todos os dados de progresso
    TutorialProgressMap.Empty();
    CompletedTutorials.Empty();
    
    // Atualizar timestamp de modificação
    LastModified = FDateTime::Now();
    
    // Log detalhado do reset
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Todo o progresso dos tutoriais foi resetado"));
    UE_LOG(LogTemp, Log, TEXT("AURACRON: - Tutoriais em progresso removidos: %d"), TotalTutorialsInProgress);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: - Tutoriais completados removidos: %d"), TotalCompletedTutorials);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: - Reset realizado em: %s"), *LastModified.ToString());
    
    // Marcar o save game como modificado para garantir que seja salvo
    MarkPackageDirty();
    
    // Broadcast evento de reset se houver delegates configurados
    OnAllProgressReset.Broadcast(TotalTutorialsInProgress, TotalCompletedTutorials);
}