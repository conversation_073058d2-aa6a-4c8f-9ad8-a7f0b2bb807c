/**
 * AuracronDynamicRailTest.cpp
 * 
 * Testes automatizados para verificar se os trilhos dinâmicos estão funcionando
 * corretamente conforme especificado no documento de design.
 * 
 * Testa:
 * - Solar Trilhos: boost de velocidade e regeneração durante o dia
 * - Axis Trilhos: movimento vertical instantâneo entre camadas
 * - Lunar Trilhos: stealth bonus e velocidade aumentada à noite
 */

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AuracronDynamicRail.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronDynamicRealmBridge.h"

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronSolarRailTest, "Auracron.DynamicRealm.SolarRail",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronAxisRailTest, "Auracron.DynamicRealm.AxisRail",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronLunarRailTest, "Auracron.DynamicRealm.LunarRail",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronRailIntegrationTest, "Auracron.DynamicRealm.RailIntegration",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

/**
 * Teste do Solar Rail - deve fornecer boost de velocidade e regeneração
 */
bool FAuracronSolarRailTest::RunTest(const FString& Parameters)
{
    // Criar mundo de teste
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // Inicializar subsistema
    UAuracronDynamicRealmSubsystem* RealmSubsystem = TestWorld->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    if (!RealmSubsystem)
    {
        AddError(TEXT("Failed to get AuracronDynamicRealmSubsystem"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Criar Solar Rail
    AAuracronDynamicRail* SolarRail = TestWorld->SpawnActor<AAuracronDynamicRail>();
    if (!SolarRail)
    {
        AddError(TEXT("Failed to spawn Solar Rail"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Configurar como Solar Rail
    SolarRail->RailType = EAuracronRailType::Solar;
    SolarRail->bIsActive = true;

    // Verificar configuração
    TestTrue(TEXT("Solar Rail type is correct"), SolarRail->RailType == EAuracronRailType::Solar);
    TestTrue(TEXT("Solar Rail is active"), SolarRail->bIsActive);

    // Verificar se o rail tem velocidade configurada
    TestTrue(TEXT("Solar Rail has movement speed"), SolarRail->MovementData.MovementSpeed > 0.0f);
    
    // Verificar se a cor é dourada (Solar)
    TestTrue(TEXT("Solar Rail has golden color"), 
        SolarRail->VisualConfig.RailColor.R > 0.8f && 
        SolarRail->VisualConfig.RailColor.G > 0.6f);

    AddInfo(FString::Printf(TEXT("Solar Rail test completed - Speed: %.2f, Color: R=%.2f G=%.2f B=%.2f"), 
        SolarRail->MovementData.MovementSpeed,
        SolarRail->VisualConfig.RailColor.R,
        SolarRail->VisualConfig.RailColor.G,
        SolarRail->VisualConfig.RailColor.B));

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Teste do Axis Rail - deve permitir movimento vertical instantâneo
 */
bool FAuracronAxisRailTest::RunTest(const FString& Parameters)
{
    // Criar mundo de teste
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // Criar Axis Rail
    AAuracronDynamicRail* AxisRail = TestWorld->SpawnActor<AAuracronDynamicRail>();
    if (!AxisRail)
    {
        AddError(TEXT("Failed to spawn Axis Rail"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Configurar como Axis Rail
    AxisRail->RailType = EAuracronRailType::Axis;
    AxisRail->bIsActive = true;

    // Verificar configuração
    TestTrue(TEXT("Axis Rail type is correct"), AxisRail->RailType == EAuracronRailType::Axis);
    TestTrue(TEXT("Axis Rail is active"), AxisRail->bIsActive);

    // Verificar se permite movimento vertical (alta velocidade)
    TestTrue(TEXT("Axis Rail has high speed for vertical movement"), 
        AxisRail->MovementData.MovementSpeed >= 1500.0f);

    // Verificar se a cor é neutra (prata/cinza)
    TestTrue(TEXT("Axis Rail has neutral color"), 
        FMath::Abs(AxisRail->VisualConfig.RailColor.R - AxisRail->VisualConfig.RailColor.G) < 0.2f &&
        FMath::Abs(AxisRail->VisualConfig.RailColor.G - AxisRail->VisualConfig.RailColor.B) < 0.2f);

    AddInfo(FString::Printf(TEXT("Axis Rail test completed - Speed: %.2f, Color: R=%.2f G=%.2f B=%.2f"), 
        AxisRail->MovementData.MovementSpeed,
        AxisRail->VisualConfig.RailColor.R,
        AxisRail->VisualConfig.RailColor.G,
        AxisRail->VisualConfig.RailColor.B));

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Teste do Lunar Rail - deve fornecer stealth bonus à noite
 */
bool FAuracronLunarRailTest::RunTest(const FString& Parameters)
{
    // Criar mundo de teste
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // Criar Lunar Rail
    AAuracronDynamicRail* LunarRail = TestWorld->SpawnActor<AAuracronDynamicRail>();
    if (!LunarRail)
    {
        AddError(TEXT("Failed to spawn Lunar Rail"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Configurar como Lunar Rail
    LunarRail->RailType = EAuracronRailType::Lunar;
    LunarRail->bIsActive = true;

    // Verificar configuração
    TestTrue(TEXT("Lunar Rail type is correct"), LunarRail->RailType == EAuracronRailType::Lunar);
    TestTrue(TEXT("Lunar Rail is active"), LunarRail->bIsActive);

    // Verificar se tem velocidade configurada
    TestTrue(TEXT("Lunar Rail has movement speed"), LunarRail->MovementData.MovementSpeed > 0.0f);

    // Verificar se a cor é azul etéreo
    TestTrue(TEXT("Lunar Rail has ethereal blue color"), 
        LunarRail->VisualConfig.RailColor.B > 0.7f &&
        LunarRail->VisualConfig.RailColor.B > LunarRail->VisualConfig.RailColor.R);

    // Verificar visibilidade noturna
    TestTrue(TEXT("Lunar Rail is visible at night"), LunarRail->VisualConfig.bVisibleDuringNight);

    AddInfo(FString::Printf(TEXT("Lunar Rail test completed - Speed: %.2f, Color: R=%.2f G=%.2f B=%.2f"), 
        LunarRail->MovementData.MovementSpeed,
        LunarRail->VisualConfig.RailColor.R,
        LunarRail->VisualConfig.RailColor.G,
        LunarRail->VisualConfig.RailColor.B));

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Teste de integração - verificar se todos os trilhos funcionam juntos
 */
bool FAuracronRailIntegrationTest::RunTest(const FString& Parameters)
{
    // Criar mundo de teste
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // Inicializar subsistema
    UAuracronDynamicRealmSubsystem* RealmSubsystem = TestWorld->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    if (!RealmSubsystem)
    {
        AddError(TEXT("Failed to get AuracronDynamicRealmSubsystem"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Criar todos os tipos de trilhos
    AAuracronDynamicRail* SolarRail = TestWorld->SpawnActor<AAuracronDynamicRail>();
    AAuracronDynamicRail* AxisRail = TestWorld->SpawnActor<AAuracronDynamicRail>();
    AAuracronDynamicRail* LunarRail = TestWorld->SpawnActor<AAuracronDynamicRail>();

    if (!SolarRail || !AxisRail || !LunarRail)
    {
        AddError(TEXT("Failed to spawn all rail types"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Configurar trilhos
    SolarRail->RailType = EAuracronRailType::Solar;
    AxisRail->RailType = EAuracronRailType::Axis;
    LunarRail->RailType = EAuracronRailType::Lunar;

    SolarRail->bIsActive = true;
    AxisRail->bIsActive = true;
    LunarRail->bIsActive = true;

    // Verificar se todos estão funcionando
    TestTrue(TEXT("All rails are active"), 
        SolarRail->bIsActive && AxisRail->bIsActive && LunarRail->bIsActive);

    // Verificar se têm tipos diferentes
    TestTrue(TEXT("Rails have different types"), 
        SolarRail->RailType != AxisRail->RailType && 
        AxisRail->RailType != LunarRail->RailType &&
        LunarRail->RailType != SolarRail->RailType);

    // Verificar se todos têm velocidades configuradas
    TestTrue(TEXT("All rails have movement speed"), 
        SolarRail->MovementData.MovementSpeed > 0.0f &&
        AxisRail->MovementData.MovementSpeed > 0.0f &&
        LunarRail->MovementData.MovementSpeed > 0.0f);

    AddInfo(FString::Printf(TEXT("Integration test completed - Solar: %.2f, Axis: %.2f, Lunar: %.2f"), 
        SolarRail->MovementData.MovementSpeed,
        AxisRail->MovementData.MovementSpeed,
        LunarRail->MovementData.MovementSpeed));

    TestWorld->DestroyWorld(false);
    return true;
}
