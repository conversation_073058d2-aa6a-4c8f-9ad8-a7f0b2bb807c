#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/BoxComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "ActiveGameplayEffectHandle.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronPrismalIsland.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronPrismalFlow;

/**
 * Island activation requirements
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronIslandActivationRequirements
{
    GENERATED_BODY()

    /** Minimum players required for activation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    int32 MinPlayersRequired;

    /** Maximum players that can benefit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    int32 MaxPlayersSupported;

    /** Activation duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    float ActivationDuration;

    /** Cooldown after deactivation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    float CooldownDuration;

    /** Team requirement (0 = any, 1 = same team, 2 = mixed teams) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    int32 TeamRequirement;

    /** Required evolution phase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Activation")
    ERealmEvolutionPhase RequiredPhase;

    FAuracronIslandActivationRequirements()
    {
        MinPlayersRequired = 1;
        MaxPlayersSupported = 5;
        ActivationDuration = 30.0f;
        CooldownDuration = 60.0f;
        TeamRequirement = 0;
        RequiredPhase = ERealmEvolutionPhase::Despertar;
    }
};

/**
 * Island benefits and effects
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronIslandBenefits
{
    GENERATED_BODY()

    /** Health regeneration rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float HealthRegenRate;

    /** Mana regeneration rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float ManaRegenRate;

    /** Damage boost percentage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float DamageBoost;

    /** Defense boost percentage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float DefenseBoost;

    /** Energy regeneration rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float EnergyRegenRate;

    /** Shield strength */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float ShieldStrength;

    /** Damage multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float DamageMultiplier;

    /** Speed multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float SpeedMultiplier;

    /** Vision range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float VisionRange;

    /** Movement speed boost */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float MovementSpeedBoost;

    /** Ability cooldown reduction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float CooldownReduction;

    /** Vision range increase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    float VisionRangeIncrease;

    /** Special abilities granted */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Benefits")
    TArray<FString> SpecialAbilities;

    FAuracronIslandBenefits()
    {
        HealthRegenRate = 10.0f;
        ManaRegenRate = 0.0f;
        DamageBoost = 0.0f;
        DefenseBoost = 0.0f;
        MovementSpeedBoost = 0.0f;
        CooldownReduction = 0.0f;
        VisionRangeIncrease = 0.0f;
    }
};

/**
 * Auracron Prismal Island
 * 
 * Strategic islands within the Prismal Flow:
 * 
 * Nexus Islands (5 total):
 * - Control towers with defensive positions
 * - Resource generators
 * - Flow manipulation abilities
 * 
 * Santuário Islands (8 total):
 * - Safe zones for healing
 * - Temporary shields
 * - Vision amplifiers
 * 
 * Arsenal Islands (6 total):
 * - Weapon upgrades
 * - Ability enhancers
 * - Temporary buffs
 * 
 * Caos Islands (4 total):
 * - High risk, high reward
 * - Environmental hazards
 * - Unstable terrain
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronPrismalIsland : public AActor
{
    GENERATED_BODY()

public:
    AAuracronPrismalIsland();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Island configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Configuration")
    EPrismalIslandType IslandType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Configuration")
    FAuracronIslandActivationRequirements ActivationRequirements;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Configuration")
    FAuracronIslandBenefits IslandBenefits;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Configuration")
    float IslandRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Configuration")
    bool bAutoActivate;

    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> IslandMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> ActivationTrigger;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UBoxComponent> BenefitArea;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UNiagaraComponent>> IslandEffects;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> IslandAudio;

    // Island management
    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void ActivateIsland();

    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void DeactivateIsland();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Management")
    bool IsIslandActive() const { return bIsActive; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Management")
    bool CanActivate() const;

    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void UpdateIslandState(float DeltaTime);

    // Player interaction
    UFUNCTION(BlueprintCallable, Category = "Player Interaction")
    void AddPlayerToIsland(APawn* Player);

    UFUNCTION(BlueprintCallable, Category = "Player Interaction")
    void RemovePlayerFromIsland(APawn* Player);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Interaction")
    TArray<APawn*> GetPlayersOnIsland() const { return PlayersOnIsland; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Interaction")
    int32 GetPlayerCount() const { return PlayersOnIsland.Num(); }

    // Island properties
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Properties")
    EPrismalIslandType GetIslandType() const { return IslandType; }

    UFUNCTION(BlueprintCallable, Category = "Island Properties")
    void SetIslandType(EPrismalIslandType NewType) { IslandType = NewType; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Properties")
    float GetIslandRadius() const { return IslandRadius; }

    UFUNCTION(BlueprintCallable, Category = "Island Properties")
    void SetIslandRadius(float NewRadius) { IslandRadius = NewRadius; }

    UFUNCTION(BlueprintCallable, Category = "Team Control")
    void SetControllingTeam(int32 TeamID) { ControllingTeamID = TeamID; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Team Control")
    int32 GetControllingTeam() const { return ControllingTeamID; }

    // Island initialization and flow methods
    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void InitializeIsland();

    UFUNCTION(BlueprintCallable, Category = "Flow Management")
    void SetFlowSegmentIndex(int32 SegmentIndex);

    UFUNCTION(BlueprintCallable, Category = "Flow Management")
    void SetFlowInfluence(float Influence);

    UFUNCTION(BlueprintCallable, Category = "Flow Management")
    void SetFlowDirection(const FVector& Direction);

    UFUNCTION(BlueprintCallable, Category = "Flow Management")
    void AddEnergyBonus(float Bonus);

    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void PlayRepositioningEffect();

    UFUNCTION(BlueprintCallable, Category = "Utility")
    void UpdateEffectIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Team Management")
    void ApplyTeamInfluence(int32 TeamID, float InfluenceStrength);

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void OptimizeIslandRendering(const FVector& ViewerLocation);

    // Island state management
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island State")
    bool IsIslandOnCooldown() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island State")
    TArray<APawn*> GetPlayersInBenefitArea() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Configuration")
    FAuracronIslandActivationRequirements GetActivationRequirements() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Configuration")
    FAuracronIslandBenefits GetIslandBenefits() const;

    UFUNCTION(BlueprintCallable, Category = "Island Configuration")
    void SetActivationRequirements(const FAuracronIslandActivationRequirements& NewRequirements);

    UFUNCTION(BlueprintCallable, Category = "Island Configuration")
    void SetIslandBenefits(const FAuracronIslandBenefits& NewBenefits);

    UFUNCTION(BlueprintCallable, Category = "Island Configuration")
    void SetAutoActivate(bool bNewAutoActivate);

    // Benefit application
    UFUNCTION(BlueprintCallable, Category = "Benefits")
    void ApplyBenefitsToPlayer(APawn* Player);

    UFUNCTION(BlueprintCallable, Category = "Benefits")
    void RemoveBenefitsFromPlayer(APawn* Player);

    UFUNCTION(BlueprintCallable, Category = "Benefits")
    void UpdatePlayerBenefits(float DeltaTime);

    // Visual and audio
    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void UpdateIslandVisuals();

    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void PlayActivationEffects();

    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void PlayDeactivationEffects();

    // Events
    UFUNCTION(BlueprintImplementableEvent, Category = "Island Events")
    void OnIslandActivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Island Events")
    void OnIslandDeactivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Island Events")
    void OnPlayerEntered(APawn* Player);

    UFUNCTION(BlueprintImplementableEvent, Category = "Island Events")
    void OnPlayerLeft(APawn* Player);

    // Collision events
    UFUNCTION()
    void OnActivationTriggerBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnActivationTriggerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    UFUNCTION()
    void OnBenefitAreaBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnBenefitAreaEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

protected:
    // Island state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island State")
    bool bIsActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island State")
    bool bIsOnCooldown;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island State")
    float ActivationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island State")
    float CooldownStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island State")
    int32 ControllingTeam;

    // Player tracking
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TArray<TObjectPtr<APawn>> PlayersOnIsland;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TArray<TObjectPtr<APawn>> PlayersInBenefitArea;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TMap<TObjectPtr<APawn>, float> PlayerBenefitTimers;

    // Visual assets
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UStaticMesh> InactiveMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UStaticMesh> ActiveMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UMaterialInterface> InactiveMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UMaterialInterface> ActiveMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UNiagaraSystem> ActivationEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UNiagaraSystem> ActiveEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TObjectPtr<UNiagaraSystem> DeactivationEffect;

    // Audio assets
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> ActivationSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> ActiveLoopSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> DeactivationSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> BenefitAppliedSound;

    // Additional island properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Properties")
    int32 ControllingTeamID;

    // Flow-related properties (moved to private section to avoid duplication)

    // State properties (moved to protected section to avoid duplication)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsInitialized;

private:
    // Island type specific initialization
    void InitializeNexusIsland();
    void InitializeSantuarioIsland();
    void InitializeArsenalIsland();
    void InitializeCaosIsland();
    
    // Activation logic
    bool CheckActivationRequirements() const;
    void ProcessActivation();
    void ProcessDeactivation();
    void UpdateActivationState(float DeltaTime);
    
    // Player management
    void ProcessPlayerEntry(APawn* Player);
    void ProcessPlayerExit(APawn* Player);
    bool IsPlayerEligible(APawn* Player) const;

    // Activation processing
    void ProcessNexusActivation();
    void ProcessSantuarioActivation();
    void ProcessArsenalActivation();
    void ProcessCaosActivation();
    void ProcessNexusDeactivation();
    void ProcessSantuarioDeactivation();
    void ProcessArsenalDeactivation();
    void ProcessCaosDeactivation();
    
    // Benefit application
    void ApplyIslandBenefits(APawn* Player);
    void RemoveIslandBenefits(APawn* Player);

    // Additional methods used in implementation
    void GetCurrentEvolutionPhase();
    void UpdateIslandBenefits();
    void ApplySantuarioProtection(APawn* Player);
    void RemoveSantuarioProtection(APawn* Player);
    void ApplyArsenalUpgrades(APawn* Player);
    void ApplyCaosEffects(APawn* Player);
    void RemoveCaosEffects(APawn* Player);
    void ApplyContinuousBenefits(APawn* Player, float DeltaTime);

    // Team influence
    int32 GetDominantTeamOnIsland() const;
    int32 GetPlayerTeamID(APawn* Player) const;
    
    // Visual and audio management
    void SetupIslandEffects();
    void ConfigureIslandAudio();
    void CreateDynamicMaterial();
    void UpdateIslandAppearance();
    void PlayIslandEffects();
    void StopIslandEffects();
    void PlayPlayerEntryEffect(APawn* Player);
    void UpdateFlowMaterials();
    void UpdateFlowEffects();

    // Configuration and reconfiguration
    void ReconfigureForType();
    FString GetBenefitEffectPath() const;
    FLinearColor GetIslandTypeColor() const;
    FLinearColor GetTeamColor() const;
    
    // Team control
    void UpdateTeamControl();
    int32 GetDominantTeam() const;
    void ApplyTeamControlEffects(int32 TeamID);
    
    // Performance optimization
    void UpdateIslandLOD(float DistanceToViewer);
    void CullDistantEffects(float DistanceToViewer);
    
    // Utility functions
    bool ValidateIslandConfiguration() const;
    void LogIslandStatus() const;
    float GetDistanceToPlayer(APawn* Player) const;
    bool IsPlayerInRange(APawn* Player, float Range) const;
    FVector GetNearestPlayerLocation() const;
    void ShowIslandInteractionPrompt(APawn* Player);
    void HideIslandInteractionPrompt(APawn* Player);
    void SpawnChaosEvents();
    
    // Cached references
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    UPROPERTY()
    TObjectPtr<AAuracronPrismalFlow> CachedPrismalFlow;

    // Dynamic materials
    UPROPERTY()
    TObjectPtr<UMaterialInstanceDynamic> DynamicIslandMaterial;

    // Timers
    FTimerHandle ActivationTimer;
    FTimerHandle CooldownTimer;
    FTimerHandle BenefitUpdateTimer;
    FTimerHandle EffectUpdateTimer;
    
    // State tracking
    float LastUpdateTime;
    float LastPlayerCheckTime;
    int32 LastPlayerCount;

    // Flow integration (moved from public section to avoid duplication)
    int32 FlowSegmentIndex;
    float FlowInfluence;
    FVector FlowDirection;
    float EnergyBonus;

    // Team influence tracking
    TMap<int32, float> TeamInfluenceMap;

    // Player effect tracking
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerBenefitEffects;
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerProtectionEffects;
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerChaosEffects;

    // UI tracking
    TMap<TObjectPtr<APawn>, TObjectPtr<UUserWidget>> PlayerInteractionWidgets;

    // Asset references
    TObjectPtr<UMaterialInterface> IslandMaterial;
    TObjectPtr<UNiagaraSystem> IslandEffect;
    TObjectPtr<USoundBase> IslandAudioAsset;
};
