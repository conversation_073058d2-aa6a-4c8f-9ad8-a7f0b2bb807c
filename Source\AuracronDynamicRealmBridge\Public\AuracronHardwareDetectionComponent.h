#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "AuracronSimpleHardwareDetection.h"
#include "AuracronHardwareDetectionComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHardwareDetected, const FSimpleHardwareInfo&, HardwareInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnQualitySettingsApplied, const FSimpleQualitySettings&, AppliedSettings);

/**
 * Component para detecção automática de hardware e aplicação de configurações
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronHardwareDetectionComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronHardwareDetectionComponent();

protected:
    virtual void BeginPlay() override;

public:
    /** Executar detecção automática no início do jogo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    bool bAutoDetectOnBeginPlay;

    /** Aplicar configurações automaticamente após detecção */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    bool bAutoApplySettings;

    /** Mostrar logs detalhados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    bool bVerboseLogging;

    /** Configurações mínimas de qualidade (não vai abaixo deste nível) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    int32 MinimumQualityLevel;

    /** Configurações máximas de qualidade (não vai acima deste nível) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    int32 MaximumQualityLevel;

    /** Multiplicador de qualidade (para ajuste fino) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float QualityMultiplier;

    /** Evento disparado quando hardware é detectado */
    UPROPERTY(BlueprintAssignable, Category = "Hardware Detection")
    FOnHardwareDetected OnHardwareDetected;

    /** Evento disparado quando configurações são aplicadas */
    UPROPERTY(BlueprintAssignable, Category = "Hardware Detection")
    FOnQualitySettingsApplied OnQualitySettingsApplied;

    /** Detecta hardware manualmente */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    FSimpleHardwareInfo DetectHardware();

    /** Calcula configurações recomendadas */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    FSimpleQualitySettings CalculateRecommendedSettings(const FSimpleHardwareInfo& HardwareInfo);

    /** Aplica configurações de qualidade */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void ApplyQualitySettings(const FSimpleQualitySettings& Settings);

    /** Executa detecção completa e aplica configurações */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void RunFullHardwareDetection();

    /** Obtém informações de hardware detectadas anteriormente */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    FSimpleHardwareInfo GetLastDetectedHardware() const { return LastDetectedHardware; }

    /** Obtém configurações aplicadas anteriormente */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    FSimpleQualitySettings GetLastAppliedSettings() const { return LastAppliedSettings; }

    /** Verifica se o hardware foi detectado */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Hardware Detection")
    bool IsHardwareDetected() const { return bHardwareDetected; }

    /** Salva configurações personalizadas */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void SaveCustomSettings(const FSimpleQualitySettings& CustomSettings);

    /** Carrega configurações personalizadas */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    FSimpleQualitySettings LoadCustomSettings();

    /** Reseta para configurações padrão */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void ResetToDefaultSettings();

private:
    /** Sistema de detecção de hardware */
    UPROPERTY()
    TObjectPtr<UAuracronSimpleHardwareDetection> HardwareDetectionSystem;

    /** Última informação de hardware detectada */
    UPROPERTY()
    FSimpleHardwareInfo LastDetectedHardware;

    /** Últimas configurações aplicadas */
    UPROPERTY()
    FSimpleQualitySettings LastAppliedSettings;

    /** Flag indicando se hardware foi detectado */
    UPROPERTY()
    bool bHardwareDetected;

    /** Aplica limitações de qualidade */
    FSimpleQualitySettings ApplyQualityLimits(const FSimpleQualitySettings& Settings);

    /** Log detalhado de hardware */
    void LogHardwareInfo(const FSimpleHardwareInfo& HardwareInfo);

    /** Log detalhado de configurações */
    void LogQualitySettings(const FSimpleQualitySettings& Settings);
};
