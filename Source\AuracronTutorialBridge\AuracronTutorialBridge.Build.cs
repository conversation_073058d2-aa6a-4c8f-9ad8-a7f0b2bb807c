﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Tutorial Bridge Build Configuration
using UnrealBuildTool;
public class AuracronTutorialBridge : ModuleRules
{
    public AuracronTutorialBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","CommonInput","DeveloperSettings",
                "EngineSettings","NavigationSystem","NiagaraCore"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",
                "MovieScene",
                "MovieSceneTracks",
                "CinematicCamera",
                "MediaAssets",
                "MediaUtils",
                "Localization",
                "ICU"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus",
                    "SequencerWidgets",
                    "UMGEditor",
                    "CommonUIEditor",
                    "AIGraph",
                    "AITestSuite",
                    "GameplayTasksEditor"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_COMMON_UI=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_AI_MODULE=1");
        PublicDefinitions.Add("WITH_NAVIGATION_SYSTEM=1");
        PublicDefinitions.Add("WITH_LEVEL_SEQUENCE=1");
        PublicDefinitions.Add("WITH_CINEMATIC_CAMERA=1");
        PublicDefinitions.Add("WITH_LOCALIZATION=1");
        PublicDefinitions.Add("WITH_INTERNATIONALIZATION=1");
        // Tutorial features
        PublicDefinitions.Add("AURACRON_INTERACTIVE_TUTORIAL=1");
        PublicDefinitions.Add("AURACRON_AI_MENTOR=1");
        PublicDefinitions.Add("AURACRON_PROGRESSIVE_TUTORIAL=1");
        PublicDefinitions.Add("AURACRON_ADAPTIVE_TUTORIAL=1");
        PublicDefinitions.Add("AURACRON_TUTORIAL_ANALYTICS=1");
        PublicDefinitions.Add("AURACRON_TUTORIAL_LOCALIZATION=1");
        PublicDefinitions.Add("AURACRON_TUTORIAL_ACCESSIBILITY=1");
        PublicDefinitions.Add("AURACRON_TUTORIAL_CUSTOMIZATION=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_TUTORIAL=1");
            PublicDefinitions.Add("AURACRON_TOUCH_TUTORIAL=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_TUTORIAL=0");
            PublicDefinitions.Add("AURACRON_TOUCH_TUTORIAL=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_TUTORIAL_DEBUG=1");
            PublicDefinitions.Add("AURACRON_TUTORIAL_EDITOR=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_TUTORIAL_DEBUG=0");
            PublicDefinitions.Add("AURACRON_TUTORIAL_EDITOR=0");
        }
    }
}


