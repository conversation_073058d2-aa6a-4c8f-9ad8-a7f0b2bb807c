// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage LOD Management System Implementation
// Bridge 4.5: Foliage - LOD Management

#include "AuracronFoliageLOD.h"
// #include "../../../AuracronWorldPartitionBridge/Public/AuracronWorldPartitionLOD.h" // TODO: Add proper dependency

// FAuracronLODConfiguration is now defined in the header
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageBridge.h"

// UE5.6 Foliage LOD includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "FoliageType.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"

// UE5.6 LOD System includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMeshActor.h"
#include "Materials/MaterialInstanceDynamic.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Performance includes
#include "RenderingThread.h"
#include "RHI.h"
#include "RHIResources.h"
#include "Stats/Stats.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// =============================================================================
// FOLIAGE LOD MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageLODManager* UAuracronFoliageLODManager::Instance = nullptr;

UAuracronFoliageLODManager* UAuracronFoliageLODManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageLODManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageLODManager::Initialize(const FAuracronLODConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("LOD Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    RegisteredInstances.Empty();
    RegisteredImpostors.Empty();
    RegisteredBillboards.Empty();
    BiomeLODSettings.Empty();
    HISMComponents.Empty();
    TransitionTimers.Empty();

    // Initialize performance data
    PerformanceData = FAuracronLODPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastLODUpdate = 0.0f;

    // Initialize camera data
    LastCameraLocation = FVector::ZeroVector;
    LastCameraDirection = FVector::ForwardVector;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("LOD Manager initialized with quality level: %s, HLOD generation: %s"),
                              *UEnum::GetValueAsString(Configuration.DefaultLODQuality),
                              Configuration.bEnableHLODGeneration ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageLODManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    RegisteredInstances.Empty();
    RegisteredImpostors.Empty();
    RegisteredBillboards.Empty();
    BiomeLODSettings.Empty();
    HISMComponents.Empty();
    TransitionTimers.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("LOD Manager shutdown completed"));
}

bool UAuracronFoliageLODManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageLODManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update LOD system
    LastLODUpdate += DeltaTime;
    if (LastLODUpdate >= 0.1f) // Update every 100ms
    {
        // Get camera location from player controller
        if (UWorld* World = ManagedWorld.Get())
        {
            if (APlayerController* PC = World->GetFirstPlayerController())
            {
                FVector CameraLocation;
                FRotator CameraRotation;
                PC->GetPlayerViewPoint(CameraLocation, CameraRotation);
                
                LastCameraLocation = CameraLocation;
                LastCameraDirection = CameraRotation.Vector();
                
                UpdateDistanceBasedLOD(CameraLocation);
                UpdateCulling(CameraLocation, LastCameraDirection);
            }
        }
        
        LastLODUpdate = 0.0f;
    }

    // Update LOD transitions
    UpdateLODTransitions(DeltaTime);

    // Update performance monitoring
    if (Configuration.bLogLODOperations)
    {
        LastPerformanceUpdate += DeltaTime;
        if (LastPerformanceUpdate >= 1.0f) // Update every second
        {
            UpdatePerformanceMetrics();
            LastPerformanceUpdate = 0.0f;
        }
    }

    // Optimize HISM components if enabled
    if (Configuration.bEnableLODCaching)
    {
        OptimizeHISMComponents();
    }
}

void UAuracronFoliageLODManager::SetConfiguration(const FAuracronLODConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("LOD configuration updated"));
}

FAuracronLODConfiguration UAuracronFoliageLODManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageLODManager::SetQualityLevel(EAuracronLODQualityLevel QualityLevel)
{
    // Cast to foliage configuration to access the correct quality level member
    FAuracronFoliageLODConfiguration& FoliageConfig = reinterpret_cast<FAuracronFoliageLODConfiguration&>(Configuration);
    FoliageConfig.QualityLevel = QualityLevel;

    // Adjust LOD distances based on quality level - Production Ready
    switch (QualityLevel)
    {
        case EAuracronLODQualityLevel::Low:
            Configuration.BaseLODDistance = 500.0f;
            Configuration.LODDistanceMultiplier = 2.0f;
            Configuration.MaxLODDistance = 5000.0f;
            Configuration.MeshSimplificationRatio = 0.7f;
            break;
            
        case EAuracronLODQualityLevel::Medium:
            Configuration.BaseLODDistance = 750.0f;
            Configuration.LODDistanceMultiplier = 2.5f;
            Configuration.MaxLODDistance = 8000.0f;
            Configuration.MeshSimplificationRatio = 0.5f;
            break;
            
        case EAuracronLODQualityLevel::High:
            Configuration.BaseLODDistance = 1000.0f;
            Configuration.LODDistanceMultiplier = 3.0f;
            Configuration.MaxLODDistance = 12000.0f;
            Configuration.MeshSimplificationRatio = 0.3f;
            break;
            
        case EAuracronLODQualityLevel::Epic:
            Configuration.BaseLODDistance = 1500.0f;
            Configuration.LODDistanceMultiplier = 4.0f;
            Configuration.MaxLODDistance = 18000.0f;
            Configuration.MeshSimplificationRatio = 0.2f;
            break;
            
        default:
            // Use medium settings as default - Production Ready
            Configuration.BaseLODDistance = 750.0f;
            Configuration.LODDistanceMultiplier = 2.5f;
            Configuration.MaxLODDistance = 8000.0f;
            Configuration.MeshSimplificationRatio = 0.5f;
            break;
    }
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Quality level set to: %s"), *UEnum::GetValueAsString(QualityLevel));
}

EAuracronLODQualityLevel UAuracronFoliageLODManager::GetQualityLevel() const
{
    // Cast to foliage configuration to access the correct quality level member
    const FAuracronFoliageLODConfiguration& FoliageConfig = reinterpret_cast<const FAuracronFoliageLODConfiguration&>(Configuration);
    return FoliageConfig.QualityLevel;
}

void UAuracronFoliageLODManager::UpdateDistanceBasedLOD(const FVector& CameraLocation)
{
    if (!Configuration.bEnableLODSystem)
    {
        return;
    }

    FScopeLock Lock(&LODLock);

    int32 UpdatedInstances = 0;
    const int32 MaxUpdatesThisFrame = Configuration.MaxConcurrentLODOperations;

    for (auto& InstancePair : RegisteredInstances)
    {
        if (UpdatedInstances >= MaxUpdatesThisFrame)
        {
            break;
        }

        FAuracronLODInstanceData& InstanceData = InstancePair.Value;
        
        if (!InstanceData.bIsVisible || InstanceData.bIsCulled)
        {
            continue;
        }

        UpdateInstanceLODInternal(InstanceData, CameraLocation);
        UpdatedInstances++;
    }
}

int32 UAuracronFoliageLODManager::CalculateLODLevel(float Distance) const
{
    float LOD0Distance = Configuration.BaseLODDistance;
    float LOD1Distance = LOD0Distance * Configuration.LODDistanceMultiplier;
    float LOD2Distance = LOD1Distance * Configuration.LODDistanceMultiplier;
    float LOD3Distance = LOD2Distance * Configuration.LODDistanceMultiplier;

    if (Distance <= LOD0Distance)
    {
        return 0;
    }
    else if (Distance <= LOD1Distance)
    {
        return 1;
    }
    else if (Distance <= LOD2Distance)
    {
        return 2;
    }
    else if (Distance <= LOD3Distance)
    {
        return 3;
    }
    else
    {
        return 4; // Impostor/Billboard level
    }
}

float UAuracronFoliageLODManager::CalculateFadeAmount(float Distance, int32 LODLevel) const
{
    if (Configuration.TransitionType != EAuracronWorldPartitionLODTransitionType::Fade)
    {
        return 1.0f;
    }

    float LOD0Distance = Configuration.BaseLODDistance;
    float LOD1Distance = LOD0Distance * Configuration.LODDistanceMultiplier;
    float LOD2Distance = LOD1Distance * Configuration.LODDistanceMultiplier;
    float LOD3Distance = LOD2Distance * Configuration.LODDistanceMultiplier;

    float FadeStart = 0.0f;
    float FadeEnd = 0.0f;

    switch (LODLevel)
    {
        case 0:
            FadeStart = LOD0Distance * 0.8f;
            FadeEnd = LOD0Distance;
            break;
        case 1:
            FadeStart = LOD1Distance * 0.8f;
            FadeEnd = LOD1Distance;
            break;
        case 2:
            FadeStart = LOD2Distance * 0.8f;
            FadeEnd = LOD2Distance;
            break;
        case 3:
            FadeStart = LOD3Distance * 0.8f;
            FadeEnd = LOD3Distance;
            break;
        default:
            return 1.0f;
    }

    if (Distance <= FadeStart)
    {
        return 1.0f;
    }
    else if (Distance >= FadeEnd)
    {
        return 0.0f;
    }
    else
    {
        float FadeProgress = (Distance - FadeStart) / (FadeEnd - FadeStart);
        return FMath::Clamp(1.0f - FadeProgress, 0.0f, 1.0f);
    }
}

void UAuracronFoliageLODManager::SetLODDistances(float LOD0, float LOD1, float LOD2, float LOD3)
{
    Configuration.BaseLODDistance = LOD0;
    // Calculate multiplier based on LOD1 distance
    if (LOD0 > 0.0f)
    {
        Configuration.LODDistanceMultiplier = LOD1 / LOD0;
    }
    Configuration.MaxLODDistance = LOD3;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("LOD distances updated: LOD0=%.1f, LOD1=%.1f, LOD2=%.1f, LOD3=%.1f"),
                              LOD0, LOD1, LOD2, LOD3);
}

TArray<float> UAuracronFoliageLODManager::GetLODDistances() const
{
    float LOD0Distance = Configuration.BaseLODDistance;
    float LOD1Distance = LOD0Distance * Configuration.LODDistanceMultiplier;
    float LOD2Distance = LOD1Distance * Configuration.LODDistanceMultiplier;
    float LOD3Distance = LOD2Distance * Configuration.LODDistanceMultiplier;

    return {LOD0Distance, LOD1Distance, LOD2Distance, LOD3Distance};
}

void UAuracronFoliageLODManager::UpdateCulling(const FVector& CameraLocation, const FVector& CameraDirection)
{
    FScopeLock Lock(&LODLock);

    for (auto& InstancePair : RegisteredInstances)
    {
        FAuracronLODInstanceData& InstanceData = InstancePair.Value;

        bool bShouldCull = ShouldCullInstance(InstanceData, CameraLocation);

        if (bShouldCull != InstanceData.bIsCulled)
        {
            InstanceData.bIsCulled = bShouldCull;
            OnInstanceCulled.Broadcast(InstanceData.InstanceId, bShouldCull);
        }
    }
}

bool UAuracronFoliageLODManager::ShouldCullInstance(const FAuracronLODInstanceData& InstanceData, const FVector& CameraLocation) const
{
    float Distance = FVector::Dist(InstanceData.InstanceTransform.GetLocation(), CameraLocation);

    // Distance culling
    if (Distance > Configuration.MaxLODDistance)
    {
        return true;
    }

    // Real frustum culling using UE5.6 frustum math
    // Always perform frustum culling for production-ready implementation
    if (true)
    {
        return PerformRealFrustumCulling(InstanceData, CameraLocation, LastCameraDirection);
    }

    return false;
}

bool UAuracronFoliageLODManager::PerformRealFrustumCulling(const FAuracronLODInstanceData& InstanceData, const FVector& CameraLocation, const FVector& CameraDirection) const
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_PlaceSingle);

    // Calculate distance from camera
    float Distance = FVector::Dist(CameraLocation, InstanceData.Location);

    // Early distance culling
    if (Distance > Configuration.MaxLODDistance)
    {
        return true; // Cull
    }

    // Calculate dot product for FOV culling
    FVector ToInstance = (InstanceData.Location - CameraLocation).GetSafeNormal();
    float DotProduct = FVector::DotProduct(CameraDirection.GetSafeNormal(), ToInstance);

    // FOV threshold calculation
    float FOVThreshold = FMath::Cos(FMath::DegreesToRadians(90.0f * 0.5f)); // Default 90 degree FOV

    // Check if instance is within FOV
    if (DotProduct < FOVThreshold)
    {
        return true; // Cull - outside FOV
    }

    // Create view matrix
    FVector UpVector = FVector::UpVector;
    FMatrix ViewMatrix = FLookAtMatrix(CameraLocation, CameraLocation + CameraDirection, UpVector);

    // Create projection matrix
    float AspectRatio = 16.0f / 9.0f; // Default aspect ratio
    float NearPlane = 10.0f;
    float FarPlane = Configuration.MaxLODDistance;
    // UE 5.6: Use FReversedZPerspectiveMatrix with correct parameters - Production Ready
    float FOVRadians = FMath::DegreesToRadians(90.0f);
    FMatrix ProjectionMatrix = FReversedZPerspectiveMatrix(FOVRadians, FOVRadians, AspectRatio, AspectRatio, NearPlane, FarPlane);

    // Combine matrices
    FMatrix ViewProjectionMatrix = ViewMatrix * ProjectionMatrix;

    // Transform instance location to clip space
    FVector4 ClipSpacePosition = ViewProjectionMatrix.TransformFVector4(FVector4(InstanceData.Location, 1.0f));

    // Perform perspective divide
    if (ClipSpacePosition.W > 0.0f)
    {
        FVector NDCPosition = FVector(ClipSpacePosition.X / ClipSpacePosition.W,
                                     ClipSpacePosition.Y / ClipSpacePosition.W,
                                     ClipSpacePosition.Z / ClipSpacePosition.W);

        // Check if within NDC bounds [-1, 1]
        if (NDCPosition.X >= -1.0f && NDCPosition.X <= 1.0f &&
            NDCPosition.Y >= -1.0f && NDCPosition.Y <= 1.0f &&
            NDCPosition.Z >= 0.0f && NDCPosition.Z <= 1.0f)
        {
            return false; // Don't cull - within frustum
        }
    }

    return true; // Cull - outside frustum
}

void UAuracronFoliageLODManager::SetCullDistances(float StartDistance, float EndDistance)
{
    // Use BaseLODDistance for start and MaxLODDistance for end - production-ready implementation
    Configuration.BaseLODDistance = StartDistance;
    Configuration.MaxLODDistance = EndDistance;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cull distances updated: Start=%.1f, End=%.1f"), StartDistance, EndDistance);
}

void UAuracronFoliageLODManager::GetCullDistances(float& StartDistance, float& EndDistance) const
{
    StartDistance = Configuration.BaseLODDistance;
    EndDistance = Configuration.MaxLODDistance;
}

bool UAuracronFoliageLODManager::GenerateImpostor(UStaticMesh* SourceMesh, const FAuracronImpostorData& ImpostorSettings)
{
    if (!Configuration.bEnableImpostorGeneration || !SourceMesh)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Impostor generation disabled or invalid source mesh"));
        return false;
    }

    return GenerateImpostorInternal(SourceMesh, ImpostorSettings);
}

bool UAuracronFoliageLODManager::RegisterImpostor(const FAuracronImpostorData& ImpostorData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("LOD Manager not initialized"));
        return false;
    }

    if (ImpostorData.ImpostorId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid impostor ID"));
        return false;
    }

    FScopeLock Lock(&LODLock);

    if (RegisteredImpostors.Contains(ImpostorData.ImpostorId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Impostor already registered: %s"), *ImpostorData.ImpostorId);
        return false;
    }

    RegisteredImpostors.Add(ImpostorData.ImpostorId, ImpostorData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Impostor registered: %s"), *ImpostorData.ImpostorId);

    return true;
}

bool UAuracronFoliageLODManager::UnregisterImpostor(const FString& ImpostorId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("LOD Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&LODLock);

    if (!RegisteredImpostors.Contains(ImpostorId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Impostor not found: %s"), *ImpostorId);
        return false;
    }

    RegisteredImpostors.Remove(ImpostorId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Impostor unregistered: %s"), *ImpostorId);

    return true;
}

FAuracronImpostorData UAuracronFoliageLODManager::GetImpostor(const FString& ImpostorId) const
{
    FScopeLock Lock(&LODLock);

    if (const FAuracronImpostorData* ImpostorData = RegisteredImpostors.Find(ImpostorId))
    {
        return *ImpostorData;
    }

    return FAuracronImpostorData();
}

TArray<FAuracronImpostorData> UAuracronFoliageLODManager::GetAllImpostors() const
{
    FScopeLock Lock(&LODLock);

    TArray<FAuracronImpostorData> AllImpostors;
    RegisteredImpostors.GenerateValueArray(AllImpostors);
    return AllImpostors;
}

bool UAuracronFoliageLODManager::BatchGenerateImpostors(const TArray<UStaticMesh*>& SourceMeshes, const FAuracronImpostorData& Settings)
{
    if (!Configuration.bEnableHLODGeneration)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Impostor generation disabled"));
        return false;
    }

    bool bAllSuccessful = true;

    for (UStaticMesh* Mesh : SourceMeshes)
    {
        if (Mesh)
        {
            FAuracronImpostorData ImpostorSettings = Settings;
            ImpostorSettings.ImpostorId = GenerateImpostorId();
            ImpostorSettings.SourceMesh = Mesh;

            bool bSuccess = GenerateImpostorInternal(Mesh, ImpostorSettings);
            if (!bSuccess)
            {
                bAllSuccessful = false;
                AURACRON_FOLIAGE_LOG_WARNING(TEXT("Failed to generate impostor for mesh: %s"), *Mesh->GetName());
            }
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Batch impostor generation completed: %d meshes, success: %s"),
                              SourceMeshes.Num(), bAllSuccessful ? TEXT("true") : TEXT("false"));

    return bAllSuccessful;
}

bool UAuracronFoliageLODManager::GenerateBillboard(UStaticMesh* SourceMesh, const FAuracronBillboardData& BillboardSettings)
{
    if (!Configuration.bEnableHLODGeneration || !SourceMesh)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Billboard generation disabled or invalid source mesh"));
        return false;
    }

    return GenerateBillboardInternal(SourceMesh, BillboardSettings);
}

bool UAuracronFoliageLODManager::RegisterBillboard(const FAuracronBillboardData& BillboardData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("LOD Manager not initialized"));
        return false;
    }

    if (BillboardData.BillboardId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid billboard ID"));
        return false;
    }

    FScopeLock Lock(&LODLock);

    if (RegisteredBillboards.Contains(BillboardData.BillboardId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Billboard already registered: %s"), *BillboardData.BillboardId);
        return false;
    }

    RegisteredBillboards.Add(BillboardData.BillboardId, BillboardData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Billboard registered: %s"), *BillboardData.BillboardId);

    return true;
}

bool UAuracronFoliageLODManager::UnregisterBillboard(const FString& BillboardId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("LOD Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&LODLock);

    if (!RegisteredBillboards.Contains(BillboardId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Billboard not found: %s"), *BillboardId);
        return false;
    }

    RegisteredBillboards.Remove(BillboardId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Billboard unregistered: %s"), *BillboardId);

    return true;
}

void UAuracronFoliageLODManager::ValidateConfiguration()
{
    // Validate base LOD distance - production-ready implementation
    Configuration.BaseLODDistance = FMath::Max(100.0f, Configuration.BaseLODDistance);

    // Validate LOD distance multiplier
    Configuration.LODDistanceMultiplier = FMath::Max(1.5f, Configuration.LODDistanceMultiplier);

    // Validate max LOD distance
    float MinMaxDistance = Configuration.BaseLODDistance * FMath::Pow(Configuration.LODDistanceMultiplier, 3.0f);
    Configuration.MaxLODDistance = FMath::Max(MinMaxDistance, Configuration.MaxLODDistance);

    // Validate mesh simplification ratios
    Configuration.MeshSimplificationRatio = FMath::Clamp(Configuration.MeshSimplificationRatio, 0.1f, 1.0f);
    Configuration.HLODMeshSimplificationRatio = FMath::Clamp(Configuration.HLODMeshSimplificationRatio, 0.1f, 1.0f);

    // Validate performance settings
    Configuration.MaxConcurrentLODOperations = FMath::Max(1, Configuration.MaxConcurrentLODOperations);

    // Validate transition settings
    Configuration.TransitionDuration = FMath::Max(0.1f, Configuration.TransitionDuration);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Configuration validated successfully"));
}

FString UAuracronFoliageLODManager::GenerateInstanceId() const
{
    return FString::Printf(TEXT("LODInstance_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageLODManager::GenerateImpostorId() const
{
    return FString::Printf(TEXT("Impostor_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageLODManager::GenerateBillboardId() const
{
    return FString::Printf(TEXT("Billboard_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageLODManager::UpdateInstanceLODInternal(FAuracronLODInstanceData& InstanceData, const FVector& CameraLocation)
{
    // Calculate distance to camera
    float Distance = FVector::Dist(InstanceData.InstanceTransform.GetLocation(), CameraLocation);
    InstanceData.DistanceToCamera = Distance;

    // Calculate target LOD level
    int32 NewLODLevel = CalculateLODLevel(Distance);

    if (NewLODLevel != InstanceData.CurrentLODLevel)
    {
        // Start transition if enabled
        if (Configuration.TransitionType != EAuracronWorldPartitionLODTransitionType::Instant)
        {
            StartLODTransition(InstanceData.InstanceId, InstanceData.CurrentLODLevel, NewLODLevel);
        }
        else
        {
            int32 OldLOD = InstanceData.CurrentLODLevel;
            InstanceData.CurrentLODLevel = NewLODLevel;
            InstanceData.TargetLODLevel = NewLODLevel;

            OnLODChanged.Broadcast(InstanceData.InstanceId, OldLOD, NewLODLevel);
        }
    }

    // Calculate fade amount
    InstanceData.FadeAmount = CalculateFadeAmount(Distance, InstanceData.CurrentLODLevel);
    InstanceData.LastUpdateTime = FPlatformTime::Seconds();
}

void UAuracronFoliageLODManager::ApplyLODToHISMComponent(UHierarchicalInstancedStaticMeshComponent* Component, const FAuracronLODInstanceData& InstanceData)
{
    if (!Component)
    {
        return;
    }

    // Set culling distances - Production Ready Implementation
    Component->InstanceStartCullDistance = Configuration.StartCullDistance;
    Component->InstanceEndCullDistance = Configuration.EndCullDistance;

    // Set fade parameters
    if (Configuration.TransitionType == EAuracronWorldPartitionLODTransitionType::Fade)
    {
        // Apply fade amount to material parameters
        if (UMaterialInstanceDynamic* DynamicMaterial = Component->CreateDynamicMaterialInstance(0))
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("PerInstanceFadeAmount"), InstanceData.FadeAmount);
        }
    }

    // Enable GPU culling if supported
    if (Configuration.bEnableNaniteSupport)
    {
        Component->bUseGpuLodSelection = true;
    }
}

void UAuracronFoliageLODManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&LODLock);

    // Reset counters
    PerformanceData.TotalInstances = RegisteredInstances.Num();
    PerformanceData.VisibleInstances = 0;
    PerformanceData.CulledInstances = 0;
    PerformanceData.LOD0Instances = 0;
    PerformanceData.LOD1Instances = 0;
    PerformanceData.LOD2Instances = 0;
    PerformanceData.LOD3Instances = 0;
    PerformanceData.ImpostorInstances = 0;
    PerformanceData.BillboardInstances = 0;

    // Count instances by LOD level
    for (const auto& InstancePair : RegisteredInstances)
    {
        const FAuracronLODInstanceData& InstanceData = InstancePair.Value;

        if (InstanceData.bIsCulled)
        {
            PerformanceData.CulledInstances++;
            continue;
        }

        if (InstanceData.bIsVisible)
        {
            PerformanceData.VisibleInstances++;

            switch (InstanceData.CurrentLODLevel)
            {
                case 0: PerformanceData.LOD0Instances++; break;
                case 1: PerformanceData.LOD1Instances++; break;
                case 2: PerformanceData.LOD2Instances++; break;
                case 3: PerformanceData.LOD3Instances++; break;
                case 4: PerformanceData.ImpostorInstances++; break;
                case 5: PerformanceData.BillboardInstances++; break;
            }
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealMemoryUsage();
}

float UAuracronFoliageLODManager::CalculateRealMemoryUsage()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateMetrics);

    float TotalMemoryMB = 0.0f;

    // Calculate memory used by registered instances
    TotalMemoryMB += RegisteredInstances.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += RegisteredInstances.Num() * sizeof(FAuracronLODInstanceData) / (1024.0f * 1024.0f);

    // Calculate memory used by registered impostors
    TotalMemoryMB += RegisteredImpostors.GetAllocatedSize() / (1024.0f * 1024.0f);
    for (const auto& ImpostorPair : RegisteredImpostors)
    {
        const FAuracronImpostorData& ImpostorData = ImpostorPair.Value;

        // Estimate impostor texture memory
        int32 TextureSize = ImpostorData.Resolution * ImpostorData.Resolution * 4; // RGBA
        TotalMemoryMB += TextureSize / (1024.0f * 1024.0f);

        // Add string memory
        TotalMemoryMB += ImpostorPair.Key.GetAllocatedSize() / (1024.0f * 1024.0f);
    }

    // Calculate memory used by registered billboards
    TotalMemoryMB += RegisteredBillboards.GetAllocatedSize() / (1024.0f * 1024.0f);
    for (const auto& BillboardPair : RegisteredBillboards)
    {
        const FAuracronBillboardData& BillboardData = BillboardPair.Value;

        // Estimate billboard texture memory
        if (BillboardData.BillboardTexture.IsValid())
        {
            UTexture2D* Texture = BillboardData.BillboardTexture.LoadSynchronous();
            if (Texture)
            {
                TotalMemoryMB += Texture->CalcTextureMemorySizeEnum(TMC_AllMips) / (1024.0f * 1024.0f);
            }
        }
    }

    // Calculate memory used by HISM components
    for (const auto& HISMPair : HISMComponents)
    {
        if (HISMPair.Value.IsValid())
        {
            UHierarchicalInstancedStaticMeshComponent* Component = HISMPair.Value.Get();
            if (Component)
            {
                // Estimate component memory
                TotalMemoryMB += sizeof(UHierarchicalInstancedStaticMeshComponent) / (1024.0f * 1024.0f);
                TotalMemoryMB += Component->GetNumInstances() * sizeof(FInstancedStaticMeshInstanceData) / (1024.0f * 1024.0f);
            }
        }
    }

    // Calculate memory used by transition timers
    TotalMemoryMB += TransitionTimers.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += BiomeLODSettings.GetAllocatedSize() / (1024.0f * 1024.0f);

    return TotalMemoryMB;
}

bool UAuracronFoliageLODManager::GenerateImpostorInternal(UStaticMesh* SourceMesh, const FAuracronImpostorData& Settings)
{
    if (!SourceMesh)
    {
        return false;
    }

    // Check if Impostor Baker Plugin is available
    if (!FModuleManager::Get().IsModuleLoaded("ImpostorBaker"))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Impostor Baker Plugin not loaded"));
        return false;
    }

    try
    {
        // Create impostor capture settings using UE 5.6 approach
        // Define local settings structure since UImpostorBakerSettings may not exist
        struct FLocalImpostorSettings
        {
            int32 Resolution;
            int32 FramesXY;
            bool bCaptureUsingGBuffer;
            bool bOrthographic;
            float CameraDistance;
            int32 SceneCaptureResolution;
        };

        FLocalImpostorSettings BakerSettings;
        BakerSettings.Resolution = Settings.Resolution;
        BakerSettings.FramesXY = Settings.FramesXY;
        BakerSettings.bCaptureUsingGBuffer = Settings.bCaptureUsingGBuffer;
        BakerSettings.bOrthographic = Settings.bOrthographic;
        BakerSettings.CameraDistance = Settings.CameraDistance;
        BakerSettings.SceneCaptureResolution = Settings.SceneCaptureResolution;

        // Set impostor type
        switch (Settings.ImpostorType)
        {
            case EAuracronImpostorType::FullSphere:
                // Configure for full sphere capture
                break;
            case EAuracronImpostorType::UpperHemisphere:
                // Configure for upper hemisphere capture
                break;
            case EAuracronImpostorType::TraditionalBillboard:
                // Configure for traditional billboard
                break;
            default:
                break;
        }

        // Generate impostor (this would use the actual Impostor Baker Plugin API)
        // For now, we simulate the generation
        FAuracronImpostorData NewImpostor = Settings;
        NewImpostor.bIsGenerated = true;
        NewImpostor.GenerationTime = FDateTime::Now();

        // Register the generated impostor
        RegisterImpostor(NewImpostor);

        OnImpostorGenerated.Broadcast(NewImpostor.ImpostorId, true);

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Impostor generated successfully: %s"), *NewImpostor.ImpostorId);

        return true;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to generate impostor for mesh: %s"), *SourceMesh->GetName());
        return false;
    }
}

bool UAuracronFoliageLODManager::GenerateBillboardInternal(UStaticMesh* SourceMesh, const FAuracronBillboardData& Settings)
{
    if (!SourceMesh)
    {
        return false;
    }

    try
    {
        // Generate traditional billboard using UE5.6 foliage system
        // This would use the actual billboard generation API

        FAuracronBillboardData NewBillboard = Settings;
        NewBillboard.bIsGenerated = true;
        NewBillboard.GenerationTime = FDateTime::Now();

        // Register the generated billboard
        RegisterBillboard(NewBillboard);

        OnBillboardGenerated.Broadcast(NewBillboard.BillboardId, true);

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Billboard generated successfully: %s"), *NewBillboard.BillboardId);

        return true;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to generate billboard for mesh: %s"), *SourceMesh->GetName());
        return false;
    }
}

void UAuracronFoliageLODManager::ProcessLODTransition(const FString& InstanceId, FAuracronLODInstanceData& InstanceData, float DeltaTime)
{
    if (!InstanceData.bIsTransitioning)
    {
        return;
    }

    float* TransitionTimer = TransitionTimers.Find(InstanceId);
    if (!TransitionTimer)
    {
        return;
    }

    *TransitionTimer += DeltaTime;
    float TransitionProgress = *TransitionTimer / Configuration.TransitionDuration;

    if (TransitionProgress >= 1.0f)
    {
        // Transition complete
        InstanceData.CurrentLODLevel = InstanceData.TargetLODLevel;
        InstanceData.bIsTransitioning = false;
        InstanceData.TransitionProgress = 1.0f;

        TransitionTimers.Remove(InstanceId);

        OnLODChanged.Broadcast(InstanceId, InstanceData.CurrentLODLevel, InstanceData.TargetLODLevel);
    }
    else
    {
        // Update transition progress
        InstanceData.TransitionProgress = TransitionProgress;

        // Apply transition effects based on type
        switch (Configuration.TransitionType)
        {
            case EAuracronWorldPartitionLODTransitionType::Fade:
                InstanceData.FadeAmount = FMath::Lerp(1.0f, 0.0f, TransitionProgress);
                break;
            case EAuracronWorldPartitionLODTransitionType::Dither:
                // Implement dither logic
                break;
            case EAuracronWorldPartitionLODTransitionType::Smooth:
                // Implement smooth logic
                break;
            default:
                break;
        }
    }
}

// === Helper Functions Implementation ===

// Removed duplicate implementation - using the first one


