// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Advanced Physics System Example Implementation

#include "AuracronAdvancedPhysicsExample.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"

AAuracronAdvancedPhysicsExample::AAuracronAdvancedPhysicsExample()
{
    PrimaryActorTick.bCanEverTick = true;

    // Create Physics Bridge Component
    PhysicsBridge = CreateDefaultSubobject<UAuracronPhysicsBridge>(TEXT("PhysicsBridge"));

    // Initialize example configurations
    InitializeExampleConfigurations();

    CurrentExamplePhase = 0;
}

void AAuracronAdvancedPhysicsExample::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Advanced Physics Example starting..."));

    // Create example actors
    CreateExampleActors();

    // Bind to physics bridge events
    if (PhysicsBridge)
    {
        PhysicsBridge->OnFluidSimulationCreated.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnFluidSimulationCreatedExample);
        PhysicsBridge->OnSoftBodyDeformed.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnSoftBodyDeformedExample);
        PhysicsBridge->OnClothTorn.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnClothTornExample);
        PhysicsBridge->OnConstraintBroken.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnConstraintBrokenExample);
        PhysicsBridge->OnVehicleCreated.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnVehicleCreatedExample);
        PhysicsBridge->OnPhysicsPerformanceUpdated.AddDynamic(this, &AAuracronAdvancedPhysicsExample::OnPhysicsPerformanceUpdatedExample);
    }

    // Start example timer
    GetWorld()->GetTimerManager().SetTimer(ExampleTimer, this, &AAuracronAdvancedPhysicsExample::RunAdvancedPhysicsScenario, 3.0f, true);
}

void AAuracronAdvancedPhysicsExample::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Monitor physics performance
    if (PhysicsBridge)
    {
        FString PerformanceMetrics = PhysicsBridge->GetPhysicsPerformanceMetrics();
        if (!PerformanceMetrics.IsEmpty())
        {
            // Display performance on screen occasionally
            static float LastDisplayTime = 0.0f;
            float CurrentTime = GetWorld()->GetTimeSeconds();
            if (CurrentTime - LastDisplayTime >= 5.0f)
            {
                if (GEngine)
                {
                    GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Cyan, 
                        FString::Printf(TEXT("AURACRON Physics: %s"), *PerformanceMetrics));
                }
                LastDisplayTime = CurrentTime;
            }
        }
    }
}

void AAuracronAdvancedPhysicsExample::InitializeExampleConfigurations()
{
    // Initialize Fluid Simulation Configuration
    ExampleFluidConfig.FluidType = EAuracronFluidType::Water;
    ExampleFluidConfig.Density = 1000.0f;
    ExampleFluidConfig.Viscosity = 1.0f;
    ExampleFluidConfig.SurfaceTension = 0.5f;
    ExampleFluidConfig.ParticleCount = 2000;
    ExampleFluidConfig.ParticleRadius = 1.0f;
    ExampleFluidConfig.SimulationBounds = FBox(FVector(-1000.0f), FVector(1000.0f));
    ExampleFluidConfig.bEnableStaticCollision = true;
    ExampleFluidConfig.bEnableDynamicCollision = true;
    ExampleFluidConfig.bEnableTemperatureSimulation = true;
    ExampleFluidConfig.InitialTemperature = 20.0f;

    // Initialize Soft Body Configuration
    ExampleSoftBodyConfig.SoftBodyType = EAuracronSoftBodyType::Rubber;
    ExampleSoftBodyConfig.Stiffness = 150.0f;
    ExampleSoftBodyConfig.Damping = 2.0f;
    ExampleSoftBodyConfig.PoissonRatio = 0.3f;
    ExampleSoftBodyConfig.YoungsModulus = 2000.0f;
    ExampleSoftBodyConfig.Density = 1200.0f;
    ExampleSoftBodyConfig.bEnableSelfCollision = true;
    ExampleSoftBodyConfig.bEnablePlasticity = true;
    ExampleSoftBodyConfig.PlasticThreshold = 15.0f;
    ExampleSoftBodyConfig.bEnableFracture = true;
    ExampleSoftBodyConfig.FractureThreshold = 200.0f;
    ExampleSoftBodyConfig.SimulationResolution = 64;

    // Initialize Cloth Configuration
    ExampleClothConfig.bEnableClothSimulation = true;
    ExampleClothConfig.ClothMass = 1.5f;
    ExampleClothConfig.EdgeStiffness = 0.9f;
    ExampleClothConfig.BendingStiffness = 0.2f;
    ExampleClothConfig.AreaStiffness = 0.8f;
    ExampleClothConfig.VolumeStiffness = 0.1f;
    ExampleClothConfig.DampingCoefficient = 0.02f;
    ExampleClothConfig.CollisionThickness = 1.5f;
    ExampleClothConfig.bEnableSelfCollision = true;
    ExampleClothConfig.SelfCollisionThickness = 3.0f;
    ExampleClothConfig.bEnableWind = true;
    ExampleClothConfig.WindVelocity = FVector(200.0f, 50.0f, 0.0f);
    ExampleClothConfig.AirDragCoefficient = 0.15f;
    ExampleClothConfig.bEnableMachineLearning = true;
    ExampleClothConfig.MLTrainingIterations = 150;

    // Initialize Constraint Configuration
    ExampleConstraintConfig.ConstraintType = EAuracronConstraintType::Spring;
    ExampleConstraintConfig.ConstraintLocation = FVector::ZeroVector;
    ExampleConstraintConfig.bEnableLinearLimits = true;
    ExampleConstraintConfig.LinearLimit = 200.0f;
    ExampleConstraintConfig.bEnableAngularLimits = true;
    ExampleConstraintConfig.AngularLimit = 90.0f;
    ExampleConstraintConfig.bEnableMotor = false;
    ExampleConstraintConfig.MotorForce = 2000.0f;
    ExampleConstraintConfig.MotorVelocity = 150.0f;
    ExampleConstraintConfig.bEnableSpring = true;
    ExampleConstraintConfig.SpringStiffness = 1500.0f;
    ExampleConstraintConfig.SpringDamping = 20.0f;
    ExampleConstraintConfig.BreakForce = 15000.0f;
    ExampleConstraintConfig.BreakTorque = 15000.0f;

    // Set physics quality
    PhysicsQuality = EAuracronPhysicsQuality::Ultra;
}

void AAuracronAdvancedPhysicsExample::CreateExampleActors()
{
    if (!GetWorld())
    {
        return;
    }

    // Create example actors for demonstrations
    for (int32 i = 0; i < 5; i++)
    {
        AActor* ExampleActor = GetWorld()->SpawnActor<AActor>();
        if (ExampleActor)
        {
            // Add a static mesh component
            UStaticMeshComponent* MeshComp = NewObject<UStaticMeshComponent>(ExampleActor);
            ExampleActor->SetRootComponent(MeshComp);
            MeshComp->RegisterComponent();

            // Position the actors
            FVector Location = GetActorLocation() + FVector(i * 200.0f, 0.0f, 100.0f);
            ExampleActor->SetActorLocation(Location);

            ExampleActors.Add(ExampleActor);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Created %d example actors"), ExampleActors.Num());
}

void AAuracronAdvancedPhysicsExample::InitializeFluidSimulationExample()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Initializing Fluid Simulation System..."));

    // Initialize fluid simulation
    bool bSuccess = PhysicsBridge->InitializeFluidSimulation();

    LogExampleResults(TEXT("Fluid Simulation"), 
        bSuccess ? TEXT("System initialized successfully") : TEXT("System initialization failed"));
}

void AAuracronAdvancedPhysicsExample::CreateWaterSimulationExample(const FVector& Location)
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Water Simulation..."));

    // Configure for water
    FAuracronFluidSimulationConfig WaterConfig = ExampleFluidConfig;
    WaterConfig.FluidType = EAuracronFluidType::Water;
    WaterConfig.Density = 1000.0f;
    WaterConfig.Viscosity = 1.0f;

    // Create water simulation
    bool bSuccess = PhysicsBridge->CreateFluidSimulation(Location, WaterConfig);

    LogExampleResults(TEXT("Water Simulation"), 
        bSuccess ? TEXT("Water simulation created") : TEXT("Water simulation failed"));
}

void AAuracronAdvancedPhysicsExample::CreateLavaSimulationExample(const FVector& Location)
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Lava Simulation..."));

    // Configure for lava
    FAuracronFluidSimulationConfig LavaConfig = ExampleFluidConfig;
    LavaConfig.FluidType = EAuracronFluidType::Lava;
    LavaConfig.Density = 2500.0f;
    LavaConfig.Viscosity = 10.0f;
    LavaConfig.bEnableTemperatureSimulation = true;
    LavaConfig.InitialTemperature = 1200.0f;

    // Create lava simulation
    bool bSuccess = PhysicsBridge->CreateFluidSimulation(Location, LavaConfig);

    LogExampleResults(TEXT("Lava Simulation"), 
        bSuccess ? TEXT("Lava simulation created") : TEXT("Lava simulation failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateFluidInteractions()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Fluid Interactions..."));

    FVector BaseLocation = GetActorLocation() + FVector(0.0f, 500.0f, 200.0f);

    // Add fluid particles
    bool bParticlesAdded = PhysicsBridge->AddFluidParticles(BaseLocation, 500, ExampleFluidConfig);

    // Apply fluid force
    FVector Force(1000.0f, 0.0f, 500.0f);
    bool bForceApplied = PhysicsBridge->ApplyFluidForce(BaseLocation, Force, 300.0f);

    // Set fluid temperature
    bool bTemperatureSet = PhysicsBridge->SetFluidTemperature(BaseLocation, 200.0f, 50.0f);

    LogExampleResults(TEXT("Fluid Interactions"), 
        FString::Printf(TEXT("Particles: %s, Force: %s, Temperature: %s"), 
            bParticlesAdded ? TEXT("Added") : TEXT("Failed"),
            bForceApplied ? TEXT("Applied") : TEXT("Failed"),
            bTemperatureSet ? TEXT("Set") : TEXT("Failed")));
}

void AAuracronAdvancedPhysicsExample::InitializeSoftBodyExample()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Initializing Soft Body System..."));

    // Initialize soft body system
    bool bSuccess = PhysicsBridge->InitializeSoftBodySystem();

    LogExampleResults(TEXT("Soft Body System"), 
        bSuccess ? TEXT("System initialized successfully") : TEXT("System initialization failed"));
}

void AAuracronAdvancedPhysicsExample::ConvertToRubberSoftBody(AActor* TargetActor)
{
    if (!PhysicsBridge || !TargetActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Converting to Rubber Soft Body..."));

    // Configure for rubber
    FAuracronSoftBodyConfig RubberConfig = ExampleSoftBodyConfig;
    RubberConfig.SoftBodyType = EAuracronSoftBodyType::Rubber;
    RubberConfig.Stiffness = 100.0f;
    RubberConfig.YoungsModulus = 1500.0f;

    // Convert to soft body
    bool bSuccess = PhysicsBridge->ConvertActorToSoftBody(TargetActor, RubberConfig);

    LogExampleResults(TEXT("Rubber Soft Body"),
        bSuccess ? FString::Printf(TEXT("Actor %s converted"), *TargetActor->GetName()) : TEXT("Conversion failed"));
}

void AAuracronAdvancedPhysicsExample::ConvertToJellySoftBody(AActor* TargetActor)
{
    if (!PhysicsBridge || !TargetActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Converting to Jelly Soft Body..."));

    // Configure for jelly
    FAuracronSoftBodyConfig JellyConfig = ExampleSoftBodyConfig;
    JellyConfig.SoftBodyType = EAuracronSoftBodyType::Jelly;
    JellyConfig.Stiffness = 50.0f;
    JellyConfig.YoungsModulus = 800.0f;
    JellyConfig.Damping = 5.0f;

    // Convert to soft body
    bool bSuccess = PhysicsBridge->ConvertActorToSoftBody(TargetActor, JellyConfig);

    LogExampleResults(TEXT("Jelly Soft Body"),
        bSuccess ? FString::Printf(TEXT("Actor %s converted"), *TargetActor->GetName()) : TEXT("Conversion failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateSoftBodyDeformation(AActor* TargetActor)
{
    if (!PhysicsBridge || !TargetActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Soft Body Deformation..."));

    FVector DeformationLocation = TargetActor->GetActorLocation() + FVector(50.0f, 0.0f, 0.0f);
    float DeformationForce = 500.0f;
    float DeformationRadius = 100.0f;

    // Apply deformation
    bool bSuccess = PhysicsBridge->ApplySoftBodyDeformation(TargetActor, DeformationLocation, DeformationForce, DeformationRadius);

    LogExampleResults(TEXT("Soft Body Deformation"),
        bSuccess ? TEXT("Deformation applied successfully") : TEXT("Deformation failed"));
}

void AAuracronAdvancedPhysicsExample::InitializeClothExample()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Initializing Chaos Cloth System..."));

    // Initialize cloth system
    bool bSuccess = PhysicsBridge->InitializeChaosClothSystem();

    LogExampleResults(TEXT("Chaos Cloth System"),
        bSuccess ? TEXT("System initialized successfully") : TEXT("System initialization failed"));
}

void AAuracronAdvancedPhysicsExample::CreateClothSimulationExample(AActor* TargetActor)
{
    if (!PhysicsBridge || !TargetActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Cloth Simulation..."));

    // Create cloth simulation
    bool bSuccess = PhysicsBridge->CreateClothSimulation(TargetActor, ExampleClothConfig);

    LogExampleResults(TEXT("Cloth Simulation"),
        bSuccess ? FString::Printf(TEXT("Cloth created on %s"), *TargetActor->GetName()) : TEXT("Cloth creation failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateClothWindEffects(AActor* ClothActor)
{
    if (!PhysicsBridge || !ClothActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Cloth Wind Effects..."));

    FVector WindVelocity(300.0f, 100.0f, 50.0f);
    float AirDragCoefficient = 0.2f;

    // Set wind properties
    bool bSuccess = PhysicsBridge->SetClothWindProperties(ClothActor, WindVelocity, AirDragCoefficient);

    LogExampleResults(TEXT("Cloth Wind Effects"),
        bSuccess ? TEXT("Wind effects applied") : TEXT("Wind effects failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateClothTearing(AActor* ClothActor, const FVector& TearLocation)
{
    if (!PhysicsBridge || !ClothActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Cloth Tearing..."));

    float TearRadius = 50.0f;

    // Tear cloth
    bool bSuccess = PhysicsBridge->TearClothAtLocation(ClothActor, TearLocation, TearRadius);

    LogExampleResults(TEXT("Cloth Tearing"),
        bSuccess ? TEXT("Cloth torn successfully") : TEXT("Cloth tearing failed"));
}

void AAuracronAdvancedPhysicsExample::CreateSpringConstraintExample(AActor* FirstActor, AActor* SecondActor)
{
    if (!PhysicsBridge || !FirstActor || !SecondActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Spring Constraint..."));

    // Configure spring constraint
    FAuracronAdvancedConstraintConfig SpringConfig = ExampleConstraintConfig;
    SpringConfig.ConstraintType = EAuracronConstraintType::Spring;
    SpringConfig.FirstActor = FirstActor;
    SpringConfig.SecondActor = SecondActor;
    SpringConfig.ConstraintLocation = (FirstActor->GetActorLocation() + SecondActor->GetActorLocation()) * 0.5f;
    SpringConfig.bEnableSpring = true;
    SpringConfig.SpringStiffness = 2000.0f;
    SpringConfig.SpringDamping = 30.0f;

    // Create constraint
    bool bSuccess = PhysicsBridge->CreateAdvancedConstraint(SpringConfig);

    LogExampleResults(TEXT("Spring Constraint"),
        bSuccess ? TEXT("Spring constraint created") : TEXT("Spring constraint failed"));
}

void AAuracronAdvancedPhysicsExample::CreateMotorConstraintExample(AActor* FirstActor, AActor* SecondActor)
{
    if (!PhysicsBridge || !FirstActor || !SecondActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Motor Constraint..."));

    // Configure motor constraint
    FAuracronAdvancedConstraintConfig MotorConfig = ExampleConstraintConfig;
    MotorConfig.ConstraintType = EAuracronConstraintType::Motor;
    MotorConfig.FirstActor = FirstActor;
    MotorConfig.SecondActor = SecondActor;
    MotorConfig.ConstraintLocation = (FirstActor->GetActorLocation() + SecondActor->GetActorLocation()) * 0.5f;
    MotorConfig.bEnableMotor = true;
    MotorConfig.MotorForce = 3000.0f;
    MotorConfig.MotorVelocity = 200.0f;

    // Create constraint
    bool bSuccess = PhysicsBridge->CreateAdvancedConstraint(MotorConfig);

    LogExampleResults(TEXT("Motor Constraint"),
        bSuccess ? TEXT("Motor constraint created") : TEXT("Motor constraint failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateConstraintBreaking()
{
    if (!PhysicsBridge || ExampleConstraintIDs.Num() == 0)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Constraint Breaking..."));

    // Break the first constraint
    int32 ConstraintID = ExampleConstraintIDs[0];
    bool bSuccess = PhysicsBridge->BreakConstraint(ConstraintID);

    if (bSuccess)
    {
        ExampleConstraintIDs.RemoveAt(0);
    }

    LogExampleResults(TEXT("Constraint Breaking"),
        bSuccess ? FString::Printf(TEXT("Constraint %d broken"), ConstraintID) : TEXT("Constraint breaking failed"));
}

void AAuracronAdvancedPhysicsExample::InitializeVehiclePhysicsExample()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Initializing Vehicle Physics System..."));

    // Initialize vehicle system
    bool bSuccess = PhysicsBridge->InitializeChaosVehicleSystem();

    LogExampleResults(TEXT("Vehicle Physics System"),
        bSuccess ? TEXT("System initialized successfully") : TEXT("System initialization failed"));
}

void AAuracronAdvancedPhysicsExample::CreateAdvancedVehicleExample(AActor* VehicleActor)
{
    if (!PhysicsBridge || !VehicleActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Creating Advanced Vehicle..."));

    // Create advanced vehicle
    bool bSuccess = PhysicsBridge->CreateAdvancedVehicle(VehicleActor, TEXT("SportsCar"));

    LogExampleResults(TEXT("Advanced Vehicle"),
        bSuccess ? FString::Printf(TEXT("Vehicle created on %s"), *VehicleActor->GetName()) : TEXT("Vehicle creation failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstrateVehicleSuspension(AActor* VehicleActor)
{
    if (!PhysicsBridge || !VehicleActor)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Vehicle Suspension..."));

    float SpringStiffness = 50000.0f;
    float DampingRatio = 0.8f;

    // Set suspension properties
    bool bSuccess = PhysicsBridge->SetVehicleSuspensionProperties(VehicleActor, SpringStiffness, DampingRatio);

    LogExampleResults(TEXT("Vehicle Suspension"),
        bSuccess ? TEXT("Suspension properties set") : TEXT("Suspension setup failed"));
}

void AAuracronAdvancedPhysicsExample::DemonstratePerformanceMonitoring()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Demonstrating Performance Monitoring..."));

    // Get performance metrics
    FString PerformanceMetrics = PhysicsBridge->GetPhysicsPerformanceMetrics();
    int32 ActiveObjects = PhysicsBridge->GetActivePhysicsObjectCount();
    float MemoryUsage = PhysicsBridge->GetPhysicsMemoryUsage();

    LogExampleResults(TEXT("Performance Monitoring"),
        FString::Printf(TEXT("Objects: %d, Memory: %.2fMB"), ActiveObjects, MemoryUsage / (1024.0f * 1024.0f)));

    // Display detailed metrics
    if (GEngine && !PerformanceMetrics.IsEmpty())
    {
        GEngine->AddOnScreenDebugMessage(-1, 10.0f, FColor::Yellow,
            FString::Printf(TEXT("AURACRON Performance: %s"), *PerformanceMetrics));
    }
}

void AAuracronAdvancedPhysicsExample::ExportPhysicsAnalyticsExample()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Exporting Physics Analytics..."));

    // Export analytics
    FString ExportPath = FPaths::ProjectSavedDir() + TEXT("AuracronPhysicsAnalytics_Example.txt");
    bool bSuccess = PhysicsBridge->ExportPhysicsAnalytics(ExportPath);

    LogExampleResults(TEXT("Physics Analytics Export"),
        bSuccess ? FString::Printf(TEXT("Exported to: %s"), *ExportPath) : TEXT("Export failed"));
}

void AAuracronAdvancedPhysicsExample::SetPhysicsQualityExample(EAuracronPhysicsQuality QualityLevel)
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Setting Physics Quality..."));

    // Set physics quality
    bool bSuccess = PhysicsBridge->SetPhysicsQuality(QualityLevel);
    PhysicsQuality = QualityLevel;

    LogExampleResults(TEXT("Physics Quality"),
        bSuccess ? FString::Printf(TEXT("Quality set to: %s"), *UEnum::GetValueAsString(QualityLevel)) : TEXT("Quality setting failed"));
}

void AAuracronAdvancedPhysicsExample::RunAdvancedPhysicsScenario()
{
    if (!PhysicsBridge)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Running Advanced Physics Scenario - Phase %d"), CurrentExamplePhase);

    switch (CurrentExamplePhase)
    {
        case 0:
            SetPhysicsQualityExample(EAuracronPhysicsQuality::Ultra);
            break;
        case 1:
            InitializeFluidSimulationExample();
            break;
        case 2:
            InitializeSoftBodyExample();
            break;
        case 3:
            InitializeClothExample();
            break;
        case 4:
            InitializeVehiclePhysicsExample();
            break;
        case 5:
            CreateWaterSimulationExample(GetActorLocation() + FVector(300.0f, 0.0f, 200.0f));
            break;
        case 6:
            CreateLavaSimulationExample(GetActorLocation() + FVector(600.0f, 0.0f, 200.0f));
            break;
        case 7:
            if (ExampleActors.Num() > 0)
            {
                ConvertToRubberSoftBody(ExampleActors[0]);
            }
            break;
        case 8:
            if (ExampleActors.Num() > 1)
            {
                ConvertToJellySoftBody(ExampleActors[1]);
            }
            break;
        case 9:
            if (ExampleActors.Num() > 2)
            {
                CreateClothSimulationExample(ExampleActors[2]);
            }
            break;
        case 10:
            if (ExampleActors.Num() > 3)
            {
                CreateAdvancedVehicleExample(ExampleActors[3]);
            }
            break;
        case 11:
            if (ExampleActors.Num() > 1)
            {
                CreateSpringConstraintExample(ExampleActors[0], ExampleActors[1]);
            }
            break;
        case 12:
            if (ExampleActors.Num() > 3)
            {
                CreateMotorConstraintExample(ExampleActors[2], ExampleActors[3]);
            }
            break;
        case 13:
            DemonstrateFluidInteractions();
            break;
        case 14:
            if (ExampleActors.Num() > 0)
            {
                DemonstrateSoftBodyDeformation(ExampleActors[0]);
            }
            break;
        case 15:
            if (ExampleActors.Num() > 2)
            {
                DemonstrateClothWindEffects(ExampleActors[2]);
            }
            break;
        case 16:
            if (ExampleActors.Num() > 3)
            {
                DemonstrateVehicleSuspension(ExampleActors[3]);
            }
            break;
        case 17:
            DemonstratePerformanceMonitoring();
            break;
        case 18:
            ExportPhysicsAnalyticsExample();
            break;
        default:
            UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Advanced Physics Scenario completed!"));
            GetWorld()->GetTimerManager().ClearTimer(ExampleTimer);
            CleanupExampleResources();
            return;
    }

    CurrentExamplePhase++;
}

// === Event Handlers ===

void AAuracronAdvancedPhysicsExample::OnFluidSimulationCreatedExample(FVector Location, EAuracronFluidType FluidType, int32 ParticleCount)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE EVENT: Fluid Simulation Created - Type: %s, Particles: %d, Location: %s"),
        *UEnum::GetValueAsString(FluidType), ParticleCount, *Location.ToString());
}

void AAuracronAdvancedPhysicsExample::OnSoftBodyDeformedExample(AActor* TargetActor, FVector DeformationLocation, float DeformationForce, float DeformationRadius)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE EVENT: Soft Body Deformed - Actor: %s, Force: %.2f, Radius: %.2f"),
        TargetActor ? *TargetActor->GetName() : TEXT("None"), DeformationForce, DeformationRadius);
}

void AAuracronAdvancedPhysicsExample::OnClothTornExample(AActor* ClothActor, FVector TearLocation, float TearRadius)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE EVENT: Cloth Torn - Actor: %s, Location: %s, Radius: %.2f"),
        ClothActor ? *ClothActor->GetName() : TEXT("None"), *TearLocation.ToString(), TearRadius);
}

void AAuracronAdvancedPhysicsExample::OnConstraintBrokenExample(int32 ConstraintID, AActor* FirstActor, AActor* SecondActor)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE EVENT: Constraint Broken - ID: %d, Actors: %s <-> %s"),
        ConstraintID,
        FirstActor ? *FirstActor->GetName() : TEXT("None"),
        SecondActor ? *SecondActor->GetName() : TEXT("None"));
}

void AAuracronAdvancedPhysicsExample::OnVehicleCreatedExample(AActor* VehicleActor, FString VehicleType)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE EVENT: Vehicle Created - Actor: %s, Type: %s"),
        VehicleActor ? *VehicleActor->GetName() : TEXT("None"), *VehicleType);
}

void AAuracronAdvancedPhysicsExample::OnPhysicsPerformanceUpdatedExample(float FrameTime, int32 ActiveObjects, float MemoryUsage)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON EXAMPLE EVENT: Performance Updated - Frame: %.3fms, Objects: %d, Memory: %.2fMB"),
        FrameTime * 1000.0f, ActiveObjects, MemoryUsage / (1024.0f * 1024.0f));
}

void AAuracronAdvancedPhysicsExample::LogExampleResults(const FString& ExampleName, const FString& Results)
{
    UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE RESULT [%s]: %s"), *ExampleName, *Results);

    // Also display on screen for easier debugging
    if (GEngine)
    {
        GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Magenta,
            FString::Printf(TEXT("AURACRON Physics [%s]: %s"), *ExampleName, *Results));
    }
}

void AAuracronAdvancedPhysicsExample::CleanupExampleResources()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Cleaning up example resources..."));

    // Clean up example actors
    for (AActor* Actor : ExampleActors)
    {
        if (Actor && IsValid(Actor))
        {
            Actor->Destroy();
        }
    }
    ExampleActors.Empty();

    // Clear constraint IDs
    ExampleConstraintIDs.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Cleanup completed"));
}
