LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
Cmd: py "../../../../../../Aura/projeto/Auracron/Content/Python/create_planicie_radiante_base.py"
LogPython: Warning: ⚠️ AuracronDynamicRealmSubsystem not available: module 'unreal' has no attribute 'get_subsystem'
LogPython: ✅ PlanicieRadianteCreator initialized successfully
LogPython: 🌟 Starting Planície Radiante creation...
LogPython: 🔍 Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogPython: ✅ LandscapeProxy API available
LogPython: ✅ StaticMeshActor API available
LogPython: ✅ WorldPartitionSubsystem API available
LogPython: ✅ EditorLevelLibrary API available
LogPython: ✅ EditorAssetLibrary API available
LogPython: 🎯 Checking and creating missing curve assets...
LogPython: ✅ Curve asset /Game/Curves/VerticalConnectors/Curve_PortalAnima_Movement already exists
LogPython: ✅ Curve asset /Game/Curves/VerticalConnectors/Curve_FendaFluxo_Movement already exists
LogPython: ✅ Curve asset /Game/Curves/VerticalConnectors/Curve_CipoAstria_Movement already exists
LogPython: ✅ Curve asset /Game/Curves/VerticalConnectors/Curve_ElevadorVortice_Movement already exists
LogPython: ✅ Curve asset /Game/Curves/VerticalConnectors/Curve_RespiradoroGeotermal_Movement already exists
LogPython: ✅ Created/verified 5/5 curve assets
LogPython: ⚠️ Level /Game/Levels/Realms/PlanicieRadiante already exists, loading...
Cmd: MAP LOAD FILE="../../../../../../Aura/projeto/Auracron/Content/Levels/Realms/PlanicieRadiante.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
LogAuracronVerticalConnectors: Deinitializing Auracron Vertical Connector System
LogAuracronVerticalConnectors: Shutting down connector system...
LogAuracronVerticalConnectors: Connector system shutdown complete
LogTemp: AURACRON: Deinitializing Advanced Performance Analyzer
LogTemp: AURACRON: Deinitializing Advanced Rendering System
LogTemp: AURACRON: Deinitializing Dynamic Weather System
LogTemp: AURACRON: Deinitializing Procedural Objective System
LogHarmonyEngine: AURACRON: Advanced ML System EndPlay - Cleaning up...
LogHarmonyEngine: AURACRON: Saving ML models...
LogHarmonyEngine: AURACRON: ML models saved to ../../../../../../Aura/projeto/Auracron/Saved/HarmonyEngine/MLModels.json
LogTemp: AURACRON: Deinitializing Nexus Community Bridge
LogTemp: AURACRON: Deinitializing Living World Bridge
LogTemp: AURACRON: Deinitializing Adaptive Engagement Bridge
LogTemp: AURACRON: Deinitializing Quantum Consciousness Bridge
LogTemp: AURACRON: Deinitializing Intelligent Documentation Bridge
LogTemp: AURACRON: Deinitializing Advanced Networking Coordinator
LogTemp: AURACRON: Deinitializing Automated QA Bridge
LogAudio: Display: Audio Device unregistered from world 'None'.
LogUObjectHash: Compacting FUObjectHashTables data took   1.75ms
LogStreaming: Display: FlushAsyncLoading(411): 1 QueuedPackages, 247 AsyncPackages
LogAudio: Display: Audio Device (ID: 1) registered with world 'PlanicieRadiante'.
LogChaosDD: Creating Chaos Debug Draw Scene for world PlanicieRadiante
LogAuracronVerticalConnectors: Initializing Auracron Vertical Connector System
LogAuracronVerticalConnectors: Initializing connector system...
LogAuracronVerticalConnectors: Connector system initialized successfully
LogTemp: AURACRON: Initializing Advanced Performance Analyzer
LogTemp: AURACRON: Advanced Performance Analyzer initialized
LogTemp: AURACRON: Initializing Advanced Rendering System
LogTemp: AURACRON: Advanced Rendering System initialized
LogTemp: AURACRON: Initializing Dynamic Weather System
LogTemp: AURACRON: Dynamic Weather System initialized
LogTemp: AURACRON: Initializing Procedural Objective System
LogTemp: AURACRON: Procedural Objective System initialized
LogHarmonyEngine: AURACRON: Advanced ML System BeginPlay
LogHarmonyEngine: Initialized tier requirements
LogHarmonyEngine: Initialized 6 default rewards
LogHarmonyEngine: Initialized default intervention strategies
LogTemp: AURACRON: Initializing Nexus Community Bridge
LogTemp: AURACRON: Nexus Community Bridge initialized
LogTemp: AURACRON: Initializing Living World Bridge
LogTemp: AURACRON: Living World Bridge initialized
LogTemp: AURACRON: Initializing Adaptive Engagement Bridge
LogTemp: AURACRON: Adaptive Engagement Bridge initialized
LogTemp: AURACRON: Initializing Quantum Consciousness Bridge
LogTemp: AURACRON: Quantum Consciousness Bridge initialized
LogTemp: AURACRON: Initializing Intelligent Documentation Bridge
LogTemp: AURACRON: Intelligent Documentation Bridge initialized
LogTemp: AURACRON: Initializing Advanced Networking Coordinator
LogTemp: AURACRON: Advanced Networking Coordinator initialized
LogTemp: AURACRON: Initializing Automated QA Bridge
LogTemp: AURACRON: Automated QA Bridge initialized
LogInstancedActors: Using Player's camera location for instanced actors LOD calculations - this can skew the LOD calculations in non-FPP games.
LogEditorServer: Finished looking for orphan Actors (0.000 secs)
LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
Cmd: MAP CHECKDEP NOCLEARLOG
MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,068ms para ser concluída.
LogPython: Warning: ⚠️ Failed to get WorldPartitionSubsystem: module 'unreal' has no attribute 'get_subsystem'
LogPython: Warning: ⚠️ World Partition subsystem not available, continuing without streaming
LogPython: 🌱 Creating base landscape terrain...
LogPython: 🌱 Creating landscape layer info objects...
LogPython: ✅ AuracronWorldPartitionLandscapeManager connected successfully
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogLinker: Conditionally flushing loading for linker(s) (/Game/Landscapes/LayerInfos/LayerInfo_Grass)
OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Landscapes/LayerInfos/LayerInfo_Grass] ([1] browsable assets)...
OBJ SavePackage: Finished generating thumbnails for package [/Game/Landscapes/LayerInfos/LayerInfo_Grass]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Landscapes/LayerInfos/LayerInfo_Grass" FILE="../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Grass.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Landscapes/LayerInfos/LayerInfo_Grass
LogSavePackage: Moving '../../../../../../Aura/projeto/Auracron/Saved/LayerInfo_Grass8B28444C47AE13DA4A8F9289F298ADBD.tmp' to '../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Grass.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 101.916 ms
LogPython: ✅ Created layer info object: Grass
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogLinker: Conditionally flushing loading for linker(s) (/Game/Landscapes/LayerInfos/LayerInfo_Rock)
OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Landscapes/LayerInfos/LayerInfo_Rock] ([1] browsable assets)...
OBJ SavePackage: Finished generating thumbnails for package [/Game/Landscapes/LayerInfos/LayerInfo_Rock]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Landscapes/LayerInfos/LayerInfo_Rock" FILE="../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Rock.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Landscapes/LayerInfos/LayerInfo_Rock
LogSavePackage: Moving '../../../../../../Aura/projeto/Auracron/Saved/LayerInfo_Rock82CC526848B3D94194524D961AC7C8EB.tmp' to '../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Rock.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 86.680 ms (total: 188.596 ms)
LogPython: ✅ Created layer info object: Rock
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogLinker: Conditionally flushing loading for linker(s) (/Game/Landscapes/LayerInfos/LayerInfo_Dirt)
OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Landscapes/LayerInfos/LayerInfo_Dirt] ([1] browsable assets)...
OBJ SavePackage: Finished generating thumbnails for package [/Game/Landscapes/LayerInfos/LayerInfo_Dirt]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Landscapes/LayerInfos/LayerInfo_Dirt" FILE="../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Dirt.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Landscapes/LayerInfos/LayerInfo_Dirt
LogSavePackage: Moving '../../../../../../Aura/projeto/Auracron/Saved/LayerInfo_Dirt256D883B42599ABA56D0C7A0EC4792BE.tmp' to '../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Dirt.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 65.505 ms (total: 254.102 ms)
LogPython: ✅ Created layer info object: Dirt
LogFileHelpers: InternalPromptForCheckoutAndSave started...
LogLinker: Conditionally flushing loading for linker(s) (/Game/Landscapes/LayerInfos/LayerInfo_Sand)
OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Landscapes/LayerInfos/LayerInfo_Sand] ([1] browsable assets)...
OBJ SavePackage: Finished generating thumbnails for package [/Game/Landscapes/LayerInfos/LayerInfo_Sand]
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Landscapes/LayerInfos/LayerInfo_Sand" FILE="../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Sand.uasset" SILENT=true
LogSavePackage: Moving output files for package: /Game/Landscapes/LayerInfos/LayerInfo_Sand
LogSavePackage: Moving '../../../../../../Aura/projeto/Auracron/Saved/LayerInfo_SandCB228B0F44D7149F56835EB34D68DDBC.tmp' to '../../../../../../Aura/projeto/Auracron/Content/Landscapes/LayerInfos/LayerInfo_Sand.uasset'
LogFileHelpers: InternalPromptForCheckoutAndSave took 64.710 ms (total: 318.813 ms)
LogPython: ✅ Created layer info object: Sand
LogTemp: Warning: [23:42:44][WARN][WorldPartition] Cannot create landscape: Manager not initialized
LogPython: Warning: ⚠️ Failed to create landscape through Auracron bridge, falling back to standard creation
LogPython: 🔄 Using standard UE5 landscape creation
LogActorFactory: Actor Factory attempting to spawn Class /Script/Landscape.LandscapeProxy
LogActorFactory: Actor Factory attempting to spawn Class /Script/Landscape.LandscapeProxy
LogActorFactory: Actor Factory spawned Class /Script/Landscape.LandscapeProxy as actor: LandscapePlaceholder /Game/Levels/Realms/PlanicieRadiante.PlanicieRadiante:PersistentLevel.LandscapePlaceholder_0
LogActorComponent: RegisterComponentWithWorld: (/Game/Levels/Realms/PlanicieRadiante.PlanicieRadiante:PersistentLevel.LandscapePlaceholder_0.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogActorFactory: Actor Factory spawned Class /Script/Landscape.LandscapeProxy as actor: LandscapePlaceholder /Game/Levels/Realms/PlanicieRadiante.PlanicieRadiante:PersistentLevel.LandscapePlaceholder_0
LogActorComponent: RegisterComponentWithWorld: (/Game/Levels/Realms/PlanicieRadiante.PlanicieRadiante:PersistentLevel.LandscapePlaceholder_0.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogActorComponent: RegisterComponentWithWorld: (/Game/Levels/Realms/PlanicieRadiante.PlanicieRadiante:PersistentLevel.LandscapePlaceholder_0.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogPython: Error: ❌ Base landscape creation failed: 'LandscapePlaceholder' object has no attribute 'set_landscape_material_scalar_parameter_value'
LogPython: Error: ❌ Base landscape creation failed
LogPython: Error: ❌ AURACRON Planície Radiante creation failed!
LogPython: Error: Traceback (most recent call last):
LogPython: Error:   File "C:/Aura/projeto/Auracron/Content/Python/create_planicie_radiante_base.py", line 740, in <module>
LogPython: Error:     sys.exit(exit_code)
LogPython: Error: SystemExit: 1