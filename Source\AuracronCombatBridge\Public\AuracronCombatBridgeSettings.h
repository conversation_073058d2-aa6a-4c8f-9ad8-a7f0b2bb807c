// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Combat Bridge Settings for Project Configuration

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AuracronCombatBridge.h"
#include "AuracronCombatBridgeSettings.generated.h"

/**
 * Project settings for AURACRON Combat Bridge
 * Allows configuration of default values and global settings
 */
UCLASS(Config=Game, DefaultConfig, meta=(DisplayName="AURACRON Combat Bridge"))
class AURACRONCOMBABRIDGE_API UAuracronCombatBridgeSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UAuracronCombatBridgeSettings();

    // === Global Combat Settings ===

    /** Enable advanced combat features globally */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Global Settings")
    bool bEnableAdvancedCombatFeatures = true;

    /** Enable debug logging for combat systems */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Global Settings")
    bool bEnableDebugLogging = true;

    /** Enable performance profiling */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Global Settings")
    bool bEnablePerformanceProfiling = true;

    /** Global combat tick interval */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Global Settings", meta = (ClampMin = "0.01", ClampMax = "1.0"))
    float GlobalCombatTickInterval = 0.05f;

    // === Enhanced Input Settings ===

    /** Default Enhanced Input configuration */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
    FAuracronEnhancedInputConfig DefaultEnhancedInputConfig;

    /** Enable Enhanced Input system by default */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
    bool bEnableEnhancedInputByDefault = true;

    /** Global combo window multiplier */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input", meta = (ClampMin = "0.1", ClampMax = "3.0"))
    float GlobalComboWindowMultiplier = 1.0f;

    // === AI Combat Settings ===

    /** Default AI Combat configuration */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI Combat")
    FAuracronAICombatConfig DefaultAICombatConfig;

    /** Enable AI learning by default */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI Combat")
    bool bEnableAILearningByDefault = true;

    /** Global AI reaction time multiplier */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI Combat", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float GlobalAIReactionTimeMultiplier = 1.0f;

    /** Maximum AI learning data entries */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "AI Combat", meta = (ClampMin = "100", ClampMax = "10000"))
    int32 MaxAILearningDataEntries = 1000;

    // === Elemental System Settings ===

    /** Default Elemental Damage configuration */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Elemental System")
    FAuracronElementalDamageConfig DefaultElementalDamageConfig;

    /** Enable elemental interactions by default */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Elemental System")
    bool bEnableElementalInteractionsByDefault = true;

    /** Global elemental damage multiplier */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Elemental System", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float GlobalElementalDamageMultiplier = 1.0f;

    /** Maximum active elemental effects per actor */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Elemental System", meta = (ClampMin = "1", ClampMax = "20"))
    int32 MaxActiveElementalEffectsPerActor = 5;

    // === Analytics Settings ===

    /** Enable combat analytics by default */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Analytics")
    bool bEnableCombatAnalyticsByDefault = true;

    /** Maximum combat event log entries */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Analytics", meta = (ClampMin = "100", ClampMax = "10000"))
    int32 MaxCombatEventLogEntries = 1000;

    /** Analytics update frequency (seconds) */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Analytics", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float AnalyticsUpdateFrequency = 1.0f;

    /** Auto-export analytics on combat end */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Analytics")
    bool bAutoExportAnalyticsOnCombatEnd = false;

    /** Analytics export directory */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Analytics")
    FString AnalyticsExportDirectory = TEXT("Saved/CombatAnalytics/");

    // === Advanced Destruction Settings ===

    /** Default Advanced Destruction configuration */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Advanced Destruction")
    FAuracronAdvancedDestructionConfig DefaultAdvancedDestructionConfig;

    /** Enable Chaos destruction by default */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Advanced Destruction")
    bool bEnableChaosDestructionByDefault = true;

    /** Global destruction force multiplier */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Advanced Destruction", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float GlobalDestructionForceMultiplier = 1.0f;

    /** Maximum debris objects per destruction event */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Advanced Destruction", meta = (ClampMin = "10", ClampMax = "1000"))
    int32 MaxDebrisObjectsPerEvent = 100;

    // === Performance Settings ===

    /** Enable LOD system for combat features */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance")
    bool bEnableCombatLODSystem = true;

    /** Distance threshold for LOD level 1 */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float LODDistance1 = 1500.0f;

    /** Distance threshold for LOD level 2 */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "10000.0"))
    float LODDistance2 = 3000.0f;

    /** Maximum concurrent combat effects */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta = (ClampMin = "10", ClampMax = "500"))
    int32 MaxConcurrentCombatEffects = 50;

    /** Enable async asset loading */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance")
    bool bEnableAsyncAssetLoading = true;

    // === Platform-Specific Settings ===

    /** Mobile platform optimizations */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Platform Settings")
    bool bEnableMobileOptimizations = true;

    /** Console platform optimizations */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Platform Settings")
    bool bEnableConsoleOptimizations = true;

    /** VR platform optimizations */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Platform Settings")
    bool bEnableVROptimizations = false;

    // === Networking Settings ===

    /** Enable network replication for combat data */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Networking")
    bool bEnableNetworkReplication = true;

    /** Network update frequency for combat data */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Networking", meta = (ClampMin = "5.0", ClampMax = "60.0"))
    float NetworkUpdateFrequency = 20.0f;

    /** Enable client-side prediction */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Networking")
    bool bEnableClientSidePrediction = true;

    // === Developer Settings ===

    /** Enable developer debug features */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Developer")
    bool bEnableDeveloperDebugFeatures = false;

    /** Show debug information on screen */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Developer")
    bool bShowDebugInfoOnScreen = false;

    /** Enable combat system unit tests */
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Developer")
    bool bEnableCombatSystemUnitTests = false;

public:
    // === Static Access Functions ===

    /** Get the combat bridge settings */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static const UAuracronCombatBridgeSettings* GetCombatBridgeSettings();

    /** Check if advanced combat features are enabled */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static bool AreAdvancedCombatFeaturesEnabled();

    /** Get global combat tick interval */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static float GetGlobalCombatTickInterval();

    /** Get default enhanced input configuration */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static FAuracronEnhancedInputConfig GetDefaultEnhancedInputConfig();

    /** Get default AI combat configuration */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static FAuracronAICombatConfig GetDefaultAICombatConfig();

    /** Get default elemental damage configuration */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static FAuracronElementalDamageConfig GetDefaultElementalDamageConfig();

    /** Get default advanced destruction configuration */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Settings")
    static FAuracronAdvancedDestructionConfig GetDefaultAdvancedDestructionConfig();

    // === UDeveloperSettings Interface ===
    virtual FName GetCategoryName() const override;
    // GetSectionText and GetSectionDescription removed - not available in UE 5.6

#if WITH_EDITOR
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};
