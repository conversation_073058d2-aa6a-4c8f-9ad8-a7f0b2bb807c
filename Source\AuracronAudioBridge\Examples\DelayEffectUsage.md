# Uso do Sistema de Delay do AuracronAudioBridge

Este documento demonstra como usar o sistema de efeitos de delay implementado no AuracronAudioBridge para Unreal Engine 5.6.

## Funcionalidades Implementadas

### 1. Aplicar Efeito de Delay

```cpp
// C++ - Aplicar delay de 500ms com feedback de 30%
if (UAuracronAudioBridge* AudioBridge = GetAudioBridge())
{
    bool bSuccess = AudioBridge->ApplyDelayEffect(500.0f, 0.3f);
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("Delay aplicado com sucesso!"));
    }
}
```

### 2. Remover Efeito de Delay

```cpp
// C++ - Remover delay ativo
if (UAuracronAudioBridge* AudioBridge = GetAudioBridge())
{
    bool bSuccess = AudioBridge->RemoveDelayEffect();
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("Delay removido com sucesso!"));
    }
}
```

## Parâmetros do MetaSound

O sistema utiliza os seguintes parâmetros do MetaSound Delay node:

- **DelayTime**: Tempo de delay em segundos (convertido automaticamente de ms)
- **Feedback**: Quantidade de feedback (0.0 a 0.95)
- **WetLevel**: Nível do sinal processado (0.0 a 1.0)
- **DryLevel**: Nível do sinal original (0.0 a 1.0)
- **DelayWetLevel**: Parâmetro adicional para controle fino
- **DelayDryLevel**: Parâmetro adicional para controle fino

## Configuração Padrão

- **Delay Time**: 0.0 segundos (desabilitado)
- **Feedback**: 0.0 (sem feedback)
- **Wet Level**: 0.3 (30% do sinal processado)
- **Dry Level**: 0.7 (70% do sinal original)

## Validação de Parâmetros

- **DelayTime**: Mínimo 1ms, Máximo 2000ms
- **Feedback**: Mínimo 0.0, Máximo 0.95 (para evitar feedback infinito)

## Logs do Sistema

O sistema gera logs detalhados para debug:

```
AURACRON: Configurando delay - Tempo: 500.000000 ms, Feedback: 0.300000
AURACRON: Efeito de delay MetaSound aplicado a 3 componentes - Tempo: 500.000000 ms, Feedback: 0.300000, Wet: 0.300000, Dry: 0.700000
```

## Integração com MetaSound

Para usar este sistema, certifique-se de que seus assets de áudio:

1. Usam MetaSound como fonte de áudio
2. Têm os parâmetros de delay expostos no MetaSound Graph
3. Incluem um nó "Delay" ou "Stereo Delay" no graph

## Exemplo de Uso em Blueprint

As funções estão expostas como BlueprintCallable e podem ser usadas diretamente em Blueprints:

1. **Apply Delay Effect** - Categoria: "AURACRON Audio|Effects"
2. **Remove Delay Effect** - Categoria: "AURACRON Audio|Effects"

## Compatibilidade

- **Unreal Engine**: 5.6+
- **Módulos Requeridos**: Synthesis, MetasoundEngine
- **Plataformas**: Windows, outras plataformas suportadas pelo UE5.6