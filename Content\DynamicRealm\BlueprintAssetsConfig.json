{"realm_managers": {"BP_TerrestrialRealmManager": {"parent_class": "AAuracronRealmManager", "managed_layer": "Terrestrial", "auto_generate_content": true, "enable_evolution": true}, "BP_CelestialRealmManager": {"parent_class": "AAuracronRealmManager", "managed_layer": "Celestial", "auto_generate_content": true, "enable_evolution": true}, "BP_AbyssalRealmManager": {"parent_class": "AAuracronRealmManager", "managed_layer": "Abyssal", "auto_generate_content": true, "enable_evolution": true}}, "prismal_flow": {"BP_PrismalFlow": {"parent_class": "AAuracronPrismalFlow", "auto_initialize": true, "serpentine_pattern": true}}, "islands": {"BP_NexusIsland": {"parent_class": "AAuracronPrismalIsland", "island_type": "Nexus", "auto_activate": false}, "BP_SantuarioIsland": {"parent_class": "AAuracronPrismalIsland", "island_type": "Santuario", "auto_activate": true}, "BP_ArsenalIsland": {"parent_class": "AAuracronPrismalIsland", "island_type": "Arsenal", "auto_activate": false}, "BP_CaosIsland": {"parent_class": "AAuracronPrismalIsland", "island_type": "<PERSON><PERSON>", "auto_activate": false}}, "rails": {"BP_SolarRail": {"parent_class": "AAuracronDynamicRail", "rail_type": "Solar", "auto_activate": true}, "BP_AxisRail": {"parent_class": "AAuracronDynamicRail", "rail_type": "Axis", "auto_activate": true}, "BP_LunarRail": {"parent_class": "AAuracronDynamicRail", "rail_type": "Lunar", "auto_activate": false}}}