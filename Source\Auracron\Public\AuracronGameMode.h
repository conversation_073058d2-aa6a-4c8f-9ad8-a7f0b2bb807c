/**
 * AuracronGameMode.h
 * 
 * GameMode principal do Auracron que coordena todos os bridges e sistemas
 * para criar uma experiência de jogo completa e integrada.
 * 
 * Responsabilidades:
 * - <PERSON><PERSON><PERSON>zar todos os bridges na ordem correta
 * - Gerenciar fases do jogo (<PERSON><PERSON>, Champion Select, Game, Post-Game)
 * - Coordenar sistemas de matchmaking e progressão
 * - Integrar Harmony Engine para experiência positiva
 * - Gerenciar objetivos procedurais e eventos dinâmicos
 * 
 * Usa UE 5.6 GameMode moderno com integração completa dos bridges.
 */

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "Engine/DataTable.h"
#include "AuracronGameMode.generated.h"

// Forward declarations para todos os bridges
class UAuracronMasterOrchestrator;
class UAuracronDynamicRealmSubsystem;
class UHarmonyEngineSubsystem;
class UAuracronSigilosBridge;
class UAuracronVerticalTransitionsBridge;
class UAuracronAdaptiveCreaturesBridge;
class UAuracronProceduralObjectiveSystem;
class UAuracronHardwareDetectionSystem;

/**
 * Estados do jogo
 */
UENUM(BlueprintType)
enum class EAuracronGameState : uint8
{
    WaitingToStart      UMETA(DisplayName = "Waiting To Start"),
    Lobby               UMETA(DisplayName = "Lobby"),
    ChampionSelect      UMETA(DisplayName = "Champion Select"),
    LoadingGame         UMETA(DisplayName = "Loading Game"),
    InGame              UMETA(DisplayName = "In Game"),
    PostGame            UMETA(DisplayName = "Post Game"),
    Ended               UMETA(DisplayName = "Ended")
};

/**
 * Fases da partida
 */
UENUM(BlueprintType)
enum class EAuracronMatchPhase : uint8
{
    EarlyGame           UMETA(DisplayName = "Early Game"),
    MidGame             UMETA(DisplayName = "Mid Game"),
    LateGame            UMETA(DisplayName = "Late Game"),
    Overtime            UMETA(DisplayName = "Overtime")
};

/**
 * Dados de configuração da partida
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronMatchConfig
{
    GENERATED_BODY()

    /** Duração máxima da partida em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float MaxMatchDuration = 1800.0f; // 30 minutos

    /** Tempo de seleção de campeões */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float ChampionSelectTime = 120.0f; // 2 minutos

    /** Número máximo de jogadores */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    int32 MaxPlayers = 10;

    /** Habilitar objetivos procedurais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    bool bEnableProceduralObjectives = true;

    /** Habilitar IA adaptativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    bool bEnableAdaptiveAI = true;

    /** Habilitar Harmony Engine */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    bool bEnableHarmonyEngine = true;

    /** Habilitar detecção de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    bool bEnableHardwareDetection = true;

    FAuracronMatchConfig()
    {
        MaxMatchDuration = 1800.0f;
        ChampionSelectTime = 120.0f;
        MaxPlayers = 10;
        bEnableProceduralObjectives = true;
        bEnableAdaptiveAI = true;
        bEnableHarmonyEngine = true;
        bEnableHardwareDetection = true;
    }
};

/**
 * Dados de estatísticas da partida
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronMatchStats
{
    GENERATED_BODY()

    /** Tempo de partida */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    float MatchDuration = 0.0f;

    /** Objetivos completados */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    int32 ObjectivesCompleted = 0;

    /** Transições verticais usadas */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    int32 VerticalTransitionsUsed = 0;

    /** Sígilos ativados */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    int32 SigilsActivated = 0;

    /** Intervenções do Harmony Engine */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    int32 HarmonyInterventions = 0;

    /** Score de performance médio */
    UPROPERTY(BlueprintReadOnly, Category = "Match Stats")
    float AveragePerformanceScore = 1.0f;

    FAuracronMatchStats()
    {
        MatchDuration = 0.0f;
        ObjectivesCompleted = 0;
        VerticalTransitionsUsed = 0;
        SigilsActivated = 0;
        HarmonyInterventions = 0;
        AveragePerformanceScore = 1.0f;
    }
};

/**
 * GameMode principal do Auracron
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API AAuracronGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    AAuracronGameMode();

    // GameModeBase interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;
    virtual void PostLogin(APlayerController* NewPlayer) override;
    virtual void Logout(AController* Exiting) override;

    // === Game State Management ===
    
    /** Obter estado atual do jogo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Game Mode")
    EAuracronGameState GetCurrentGameState() const { return CurrentGameState; }

    /** Obter fase atual da partida */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Game Mode")
    EAuracronMatchPhase GetCurrentMatchPhase() const { return CurrentMatchPhase; }

    /** Iniciar seleção de campeões */
    UFUNCTION(BlueprintCallable, Category = "Auracron Game Mode")
    void StartChampionSelect();

    /** Iniciar partida */
    UFUNCTION(BlueprintCallable, Category = "Auracron Game Mode")
    void StartMatch();

    /** Finalizar partida */
    UFUNCTION(BlueprintCallable, Category = "Auracron Game Mode")
    void EndMatch(int32 WinningTeam);

    // === Bridge Integration ===
    
    /** Inicializar todos os bridges */
    UFUNCTION(BlueprintCallable, Category = "Auracron Game Mode")
    void InitializeAllBridges();

    /** Obter Master Orchestrator */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Game Mode")
    UObject* GetMasterOrchestrator() const { return MasterOrchestrator; }

    /** Obter configuração da partida */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Game Mode")
    FAuracronMatchConfig GetMatchConfig() const { return MatchConfig; }

    /** Obter estatísticas da partida */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Game Mode")
    FAuracronMatchStats GetMatchStats() const { return MatchStats; }

    // === Events ===
    
    /** Evento quando estado do jogo muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, EAuracronGameState, OldState, EAuracronGameState, NewState);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Game Mode")
    FOnGameStateChanged OnGameStateChanged;

    /** Evento quando fase da partida muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMatchPhaseChanged, EAuracronMatchPhase, OldPhase, EAuracronMatchPhase, NewPhase);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Game Mode")
    FOnMatchPhaseChanged OnMatchPhaseChanged;

    /** Evento quando partida termina */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMatchEnded, int32, WinningTeam, FAuracronMatchStats, FinalStats);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Game Mode")
    FOnMatchEnded OnMatchEnded;

protected:
    // === Configuration ===
    
    /** Configuração da partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronMatchConfig MatchConfig;

    /** Habilitar logs detalhados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bVerboseLogging = true;

    // === State ===
    
    /** Estado atual do jogo */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    EAuracronGameState CurrentGameState;

    /** Fase atual da partida */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    EAuracronMatchPhase CurrentMatchPhase;

    /** Estatísticas da partida */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    FAuracronMatchStats MatchStats;

    /** Tempo de início da partida */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    float MatchStartTime;

    /** Jogadores conectados */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    TArray<APlayerController*> ConnectedPlayers;

    // === Bridge References ===
    // Usando UObject* para evitar problemas de linkagem até os bridges estarem prontos

    /** Master Orchestrator */
    UPROPERTY()
    TObjectPtr<UObject> MasterOrchestrator;

    /** Dynamic Realm Subsystem */
    UPROPERTY()
    TObjectPtr<UObject> DynamicRealmSubsystem;

    /** Harmony Engine Subsystem */
    UPROPERTY()
    TObjectPtr<UObject> HarmonyEngineSubsystem;

    // === Timers ===
    
    /** Timer para atualização do jogo */
    FTimerHandle GameUpdateTimer;

    /** Timer para seleção de campeões */
    FTimerHandle ChampionSelectTimer;

    /** Timer para duração da partida */
    FTimerHandle MatchDurationTimer;

private:
    // === Implementation ===
    void ChangeGameState(EAuracronGameState NewState);
    void ChangeMatchPhase(EAuracronMatchPhase NewPhase);
    void UpdateGameLogic(float DeltaTime);
    void UpdateMatchPhase();
    void InitializeBridgeReferences();
    void ValidateGameConfiguration();
    void StartGameUpdateTimer();
    void HandleChampionSelectTimeout();
    void HandleMatchTimeout();
    void CollectMatchStatistics();
    void CleanupMatch();

    /** Flag de inicialização */
    bool bGameInitialized = false;

    /** Tempo da última atualização */
    float LastUpdateTime = 0.0f;
};
