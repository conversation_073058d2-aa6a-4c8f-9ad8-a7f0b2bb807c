#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste final para verificar as correções implementadas
Este script deve ser executado dentro do Unreal Engine Editor
"""

import unreal

def test_curve_float_api():
    """Testa a API corrigida do CurveFloat"""
    try:
        # Teste da API de CurveFloat
        curve_factory = unreal.CurveFloatFactory()
        editor_asset_lib = unreal.EditorAssetLibrary()
        
        # Criar um asset de curva de teste
        test_curve_path = "/Game/Test/TestCurve"
        curve_asset = editor_asset_lib.create_asset(
            asset_path=test_curve_path,
            asset_class=unreal.CurveFloat,
            factory=curve_factory
        )
        
        if curve_asset:
            # Testar o método corrigido
            if hasattr(curve_asset, 'float_curve'):
                if hasattr(curve_asset.float_curve, 'reset'):
                    curve_asset.float_curve.reset()
                    unreal.log("✅ CurveFloat.float_curve.reset() funciona corretamente")
                    return True
                else:
                    unreal.log_error("❌ Método reset() não encontrado em float_curve")
            else:
                unreal.log_error("❌ Atributo float_curve não encontrado em CurveFloat")
        else:
            unreal.log_error("❌ Falha ao criar asset de curva")
            
    except Exception as e:
        unreal.log_error(f"❌ Erro no teste CurveFloat: {str(e)}")
    
    return False

def test_world_partition_subsystem():
    """Testa a API corrigida do WorldPartitionSubsystem"""
    try:
        # Obter o mundo do editor
        editor_world = unreal.EditorLevelLibrary.get_editor_world()
        
        if editor_world:
            # Testar o método corrigido
            world_partition_subsystem = editor_world.get_subsystem(unreal.WorldPartitionSubsystem)
            
            if world_partition_subsystem:
                unreal.log("✅ WorldPartitionSubsystem via world.get_subsystem() funciona corretamente")
                return True
            else:
                unreal.log_error("❌ WorldPartitionSubsystem não encontrado via world.get_subsystem()")
        else:
            unreal.log_error("❌ Falha ao obter editor world")
            
    except Exception as e:
        unreal.log_error(f"❌ Erro no teste WorldPartitionSubsystem: {str(e)}")
    
    return False

def test_landscape_layer_info_object():
    """Testa a API corrigida do LandscapeLayerInfoObject"""
    try:
        editor_asset_lib = unreal.EditorAssetLibrary()
        
        # Testar criação direta via EditorAssetLibrary
        test_layer_path = "/Game/Test/TestLayerInfo"
        layer_info_asset = editor_asset_lib.create_asset(
            asset_path=test_layer_path,
            asset_class=unreal.LandscapeLayerInfoObject
        )
        
        if layer_info_asset:
            # Configurar propriedades
            layer_info_asset.set_editor_property('layer_name', 'TestLayer')
            
            if editor_asset_lib.save_asset(test_layer_path):
                unreal.log("✅ LandscapeLayerInfoObject via EditorAssetLibrary.create_asset() funciona corretamente")
                return True
            else:
                unreal.log_error("❌ Falha ao salvar LandscapeLayerInfoObject")
        else:
            unreal.log_error("❌ Falha ao criar LandscapeLayerInfoObject")
            
    except Exception as e:
        unreal.log_error(f"❌ Erro no teste LandscapeLayerInfoObject: {str(e)}")
    
    return False

def run_all_tests():
    """Executa todos os testes de correção"""
    unreal.log("🧪 Iniciando testes das correções implementadas...")
    
    results = {
        'CurveFloat': test_curve_float_api(),
        'WorldPartitionSubsystem': test_world_partition_subsystem(),
        'LandscapeLayerInfoObject': test_landscape_layer_info_object()
    }
    
    # Resumo dos resultados
    unreal.log("\n📊 Resumo dos testes:")
    all_passed = True
    
    for test_name, passed in results.items():
        status = "✅ PASSOU" if passed else "❌ FALHOU"
        unreal.log(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        unreal.log("\n🎉 Todos os testes passaram! As correções estão funcionando.")
    else:
        unreal.log("\n⚠️ Alguns testes falharam. Verificar implementações.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()