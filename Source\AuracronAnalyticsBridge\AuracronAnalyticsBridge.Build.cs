﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Telemetria e Analytics Bridge Build Configuration
using UnrealBuildTool;
public class AuracronAnalyticsBridge : ModuleRules
{
    public AuracronAnalyticsBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine","AnalyticsMulticast","GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph","OnlineSubsystemUtils","EOSShared",
                "Json","EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "AudioMixer",
                "SignalProcessing",
                "SessionServices",
                "PlatformCrypto",
                "RSA",
                "Localization",
                "ICU",
                "GameplayDebugger",

                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "ScreenShotComparisonTools",
                "TraceLog"
            }
        );

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_ANALYTICS=1");
        PublicDefinitions.Add("WITH_TELEMETRY=1");
        PublicDefinitions.Add("WITH_FIREBASE_ANALYTICS=1");
        PublicDefinitions.Add("WITH_EOS_ANALYTICS=1");
        PublicDefinitions.Add("WITH_UNREAL_INSIGHTS=1");
        PublicDefinitions.Add("WITH_TRACE_LOG=1");
        PublicDefinitions.Add("WITH_A_B_TESTING=1");
        PublicDefinitions.Add("WITH_BEHAVIORAL_ANALYTICS=1");
        PublicDefinitions.Add("WITH_PERFORMANCE_METRICS=1");
        PublicDefinitions.Add("WITH_BALANCE_ANALYTICS=1");
        // Development vs Shipping configurations
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_ANALYTICS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_DETAILED_LOGGING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_ANALYTICS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_DETAILED_LOGGING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_ANALYTICS=1");
            PublicDefinitions.Add("WITH_MOBILE_TELEMETRY=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_ANALYTICS=0");
            PublicDefinitions.Add("WITH_MOBILE_TELEMETRY=0");
        }
        // Privacy and GDPR compliance
        PublicDefinitions.Add("AURACRON_GDPR_COMPLIANT=1");
        PublicDefinitions.Add("AURACRON_PRIVACY_CONTROLS=1");
        PublicDefinitions.Add("AURACRON_DATA_ANONYMIZATION=1");
        PublicDefinitions.Add("AURACRON_CONSENT_MANAGEMENT=1");
        // Security
        PublicDefinitions.Add("AURACRON_SECURE_ANALYTICS=1");
        PublicDefinitions.Add("AURACRON_ENCRYPTED_TELEMETRY=1");
        // Note: AURACRON_TAMPER_DETECTION is defined in AuracronAntiCheatBridge based on configuration

        // C++ standard - Using Cpp20 for UE 5.6 compatibility
        CppStandard = CppStandardVersion.Cpp20;
    }
}

