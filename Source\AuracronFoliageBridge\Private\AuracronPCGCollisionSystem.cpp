// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - PCG Collision Integration System Implementation

#include "AuracronPCGCollisionSystem.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Materials/MaterialInterface.h"
#include "UObject/ConstructorHelpers.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Policies/PrettyJsonPrintPolicy.h"

// PCG Framework includes
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronPCGCollision, Log, All);

UAuracronPCGCollisionSystem::UAuracronPCGCollisionSystem()
{
    LastGenerationTime = 0.0f;
    TotalCollisionsGenerated = 0;
    AverageGenerationTime = 0.0f;
}

bool UAuracronPCGCollisionSystem::GenerateCollisionForPCGData(UPCGComponent* PCGComponent, const FAuracronPCGCollisionSettings& Settings)
{
    if (!PCGComponent)
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("PCGComponent is null"));
        return false;
    }

    float StartTime = FPlatformTime::Seconds();

    // Get PCG data from component
    const UPCGData* PCGData = PCGComponent->GetPCGData();
    if (!PCGData)
    {
        UE_LOG(LogAuracronPCGCollision, Warning, TEXT("No PCG data found in component"));
        return false;
    }

    // Extract point data from PCG
    TArray<FPCGPoint> Points;
    if (const UPCGPointData* PointData = Cast<UPCGPointData>(PCGData))
    {
        Points = PointData->GetPoints();
    }
    else
    {
        UE_LOG(LogAuracronPCGCollision, Warning, TEXT("PCG data is not point data"));
        return false;
    }

    bool bSuccess = GenerateCollisionForPointData(Points, Settings);

    LastGenerationTime = FPlatformTime::Seconds() - StartTime;
    TotalCollisionsGenerated++;
    AverageGenerationTime = ((AverageGenerationTime * (TotalCollisionsGenerated - 1)) + LastGenerationTime) / TotalCollisionsGenerated;

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Generated collision for %d PCG points in %.3f seconds"), Points.Num(), LastGenerationTime);

    return bSuccess;
}

bool UAuracronPCGCollisionSystem::GenerateCollisionForPointData(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings)
{
    if (Points.Num() == 0)
    {
        UE_LOG(LogAuracronPCGCollision, Warning, TEXT("No points provided for collision generation"));
        return false;
    }

    float StartTime = FPlatformTime::Seconds();

    // Generate cache key
    FString CacheKey = GenerateCacheKey(Points, Settings);

    // Check cache if enabled
    if (Settings.bEnableCollisionCaching && IsCacheValid(CacheKey))
    {
        UE_LOG(LogAuracronPCGCollision, Log, TEXT("Using cached collision data for key: %s"), *CacheKey);
        LastGenerationTime = FPlatformTime::Seconds() - StartTime;
        return true;
    }

    // Generate collision mesh based on type
    UStaticMesh* CollisionMesh = nullptr;
    switch (Settings.CollisionType)
    {
        case EAuracronPCGCollisionType::Simple:
        {
            TArray<FVector> Vertices;
            for (const FPCGPoint& Point : Points)
            {
                Vertices.Add(Point.Transform.GetLocation());
            }
            CollisionMesh = GenerateSimpleCollisionMesh(Vertices, Settings);
            break;
        }
        case EAuracronPCGCollisionType::Complex:
        {
            TArray<FVector> Vertices;
            TArray<int32> Indices;
            for (int32 i = 0; i < Points.Num(); ++i)
            {
                Vertices.Add(Points[i].Transform.GetLocation());
                if (i >= 2)
                {
                    // Create triangles
                    Indices.Add(0);
                    Indices.Add(i - 1);
                    Indices.Add(i);
                }
            }
            CollisionMesh = GenerateComplexCollisionMesh(Vertices, Indices, Settings);
            break;
        }
        case EAuracronPCGCollisionType::Procedural:
        {
            CollisionMesh = GenerateProceduralCollisionMesh(Points, Settings);
            break;
        }
        case EAuracronPCGCollisionType::Adaptive:
        {
            TArray<FVector> SamplePoints;
            for (const FPCGPoint& Point : Points)
            {
                SamplePoints.Add(Point.Transform.GetLocation());
            }
            return GenerateAdaptiveCollision(SamplePoints, Settings);
        }
        default:
            UE_LOG(LogAuracronPCGCollision, Warning, TEXT("Unsupported collision type"));
            return false;
    }

    if (!CollisionMesh)
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Failed to generate collision mesh"));
        return false;
    }

    // Cache the result if enabled
    if (Settings.bEnableCollisionCaching)
    {
        FAuracronPCGCollisionData CacheData;
        CacheData.CollisionMesh = CollisionMesh;
        CacheData.CollisionSettings = Settings;
        CollisionCache.CachedCollisionData.Add(CacheKey, CacheData);
        CollisionCache.GeneratedCollisionMeshes.Add(CacheKey, CollisionMesh);
        CollisionCache.LastUpdateTime = FPlatformTime::Seconds();
    }

    LastGenerationTime = FPlatformTime::Seconds() - StartTime;
    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Generated collision for %d points in %.3f seconds"), Points.Num(), LastGenerationTime);

    return true;
}

UStaticMesh* UAuracronPCGCollisionSystem::GenerateCollisionMeshFromPoints(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings)
{
    if (Points.Num() == 0)
    {
        return nullptr;
    }

    // Extract vertices from points
    TArray<FVector> Vertices;
    for (const FPCGPoint& Point : Points)
    {
        Vertices.Add(Point.Transform.GetLocation());
    }

    // Generate mesh based on collision type
    switch (Settings.CollisionType)
    {
        case EAuracronPCGCollisionType::Simple:
            return GenerateSimpleCollisionMesh(Vertices, Settings);
        case EAuracronPCGCollisionType::Complex:
        {
            TArray<int32> Indices;
            // Generate indices for triangulation
            for (int32 i = 2; i < Vertices.Num(); ++i)
            {
                Indices.Add(0);
                Indices.Add(i - 1);
                Indices.Add(i);
            }
            return GenerateComplexCollisionMesh(Vertices, Indices, Settings);
        }
        case EAuracronPCGCollisionType::Procedural:
            return GenerateProceduralCollisionMesh(Points, Settings);
        default:
            return GenerateSimpleCollisionMesh(Vertices, Settings);
    }
}

bool UAuracronPCGCollisionSystem::ApplyCollisionToComponent(UStaticMeshComponent* Component, const FAuracronPCGCollisionData& CollisionData)
{
    if (!Component)
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Component is null"));
        return false;
    }

    // Set collision mesh if available
    if (CollisionData.CollisionMesh)
    {
        Component->SetStaticMesh(CollisionData.CollisionMesh);
    }

    // Apply collision settings
    Component->SetCollisionEnabled(CollisionData.CollisionEnabled);
    Component->SetCollisionResponseToAllChannels(CollisionData.CollisionResponse);

    // Apply physics properties
    if (FBodyInstance* BodyInstance = Component->GetBodyInstance())
    {
        BodyInstance->SetMassOverride(CollisionData.Mass, true);
        BodyInstance->SetPhysMaterialOverride(nullptr); // Could be extended to support physics materials
        BodyInstance->LinearDamping = CollisionData.LinearDamping;
        BodyInstance->AngularDamping = CollisionData.AngularDamping;
        BodyInstance->bNotifyRigidBodyCollision = CollisionData.CollisionSettings.bGenerateOverlapEvents;
        // bCanModify is not available in UE 5.6 FBodyInstance
    }

    // Apply transform
    Component->SetWorldTransform(CollisionData.CollisionTransform);

    // Apply material if available
    if (CollisionData.CollisionMaterial)
    {
        Component->SetMaterial(0, CollisionData.CollisionMaterial);
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Applied collision data to component"));
    return true;
}

bool UAuracronPCGCollisionSystem::OptimizeCollisionMesh(UStaticMesh* SourceMesh, const FAuracronPCGCollisionSettings& Settings, UStaticMesh*& OutOptimizedMesh)
{
    if (!SourceMesh)
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Source mesh is null"));
        return false;
    }

    // Create a copy of the source mesh for optimization
    OutOptimizedMesh = DuplicateObject<UStaticMesh>(SourceMesh, GetTransientPackage());
    if (!OutOptimizedMesh)
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Failed to duplicate mesh for optimization"));
        return false;
    }

    // Apply optimization based on settings
    float ReductionPercentage = 1.0f - (1.0f / Settings.ComplexityReduction);
    SimplifyCollisionMesh(OutOptimizedMesh, ReductionPercentage);

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Optimized collision mesh with %.1f%% reduction"), ReductionPercentage * 100.0f);
    return true;
}

bool UAuracronPCGCollisionSystem::GenerateAdaptiveCollision(const TArray<FVector>& SamplePoints, const FAuracronPCGCollisionSettings& Settings)
{
    if (SamplePoints.Num() == 0)
    {
        return false;
    }

    // Adaptive collision generation based on point density and distribution
    float AverageDistance = 0.0f;
    int32 DistanceCount = 0;

    // Calculate average distance between points
    for (int32 i = 0; i < SamplePoints.Num() - 1; ++i)
    {
        for (int32 j = i + 1; j < FMath::Min(i + 10, SamplePoints.Num()); ++j) // Sample nearby points only
        {
            float Distance = FVector::Dist(SamplePoints[i], SamplePoints[j]);
            AverageDistance += Distance;
            DistanceCount++;
        }
    }

    if (DistanceCount > 0)
    {
        AverageDistance /= DistanceCount;
    }

    // Determine collision complexity based on point density
    EAuracronPCGCollisionType AdaptiveType = EAuracronPCGCollisionType::Simple;
    if (AverageDistance < 100.0f) // High density
    {
        AdaptiveType = EAuracronPCGCollisionType::Complex;
    }
    else if (AverageDistance < 500.0f) // Medium density
    {
        AdaptiveType = EAuracronPCGCollisionType::Procedural;
    }

    // Create adaptive settings
    FAuracronPCGCollisionSettings AdaptiveSettings = Settings;
    AdaptiveSettings.CollisionType = AdaptiveType;

    // Generate collision with adaptive settings
    TArray<FPCGPoint> AdaptivePoints;
    for (const FVector& Point : SamplePoints)
    {
        FPCGPoint PCGPoint;
        PCGPoint.Transform.SetLocation(Point);
        AdaptivePoints.Add(PCGPoint);
    }

    return GenerateCollisionForPointData(AdaptivePoints, AdaptiveSettings);
}

bool UAuracronPCGCollisionSystem::UpdateCollisionLOD(UStaticMeshComponent* Component, float Distance, const FAuracronPCGCollisionSettings& Settings)
{
    if (!Component)
    {
        return false;
    }

    // Determine LOD level based on distance
    EAuracronPCGCollisionType LODType = Settings.CollisionType;
    if (Distance > 2000.0f)
    {
        LODType = EAuracronPCGCollisionType::None;
        Component->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    }
    else if (Distance > 1000.0f)
    {
        LODType = EAuracronPCGCollisionType::Simple;
        Component->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    }
    else
    {
        Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }

    UE_LOG(LogAuracronPCGCollision, VeryVerbose, TEXT("Updated collision LOD for distance %.1f"), Distance);
    return true;
}

void UAuracronPCGCollisionSystem::ClearCollisionCache()
{
    CollisionCache.CachedCollisionData.Empty();
    CollisionCache.GeneratedCollisionMeshes.Empty();
    CollisionCache.LastUpdateTime = FPlatformTime::Seconds();
    CollisionCache.CacheVersion++;

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Cleared collision cache"));
}

bool UAuracronPCGCollisionSystem::SaveCollisionCache(const FString& CachePath)
{
    // Create JSON object for cache data
    TSharedPtr<FJsonObject> CacheJson = MakeShareable(new FJsonObject);
    CacheJson->SetNumberField(TEXT("CacheVersion"), CollisionCache.CacheVersion);
    CacheJson->SetNumberField(TEXT("LastUpdateTime"), CollisionCache.LastUpdateTime);
    CacheJson->SetNumberField(TEXT("CachedDataCount"), CollisionCache.CachedCollisionData.Num());

    // Serialize cache data
    TArray<TSharedPtr<FJsonValue>> CacheDataArray;
    for (const auto& CacheEntry : CollisionCache.CachedCollisionData)
    {
        TSharedPtr<FJsonObject> EntryJson = MakeShareable(new FJsonObject);
        EntryJson->SetStringField(TEXT("Key"), CacheEntry.Key);
        // Add more serialization as needed
        CacheDataArray.Add(MakeShareable(new FJsonValueObject(EntryJson)));
    }
    CacheJson->SetArrayField(TEXT("CacheData"), CacheDataArray);

    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(CacheJson.ToSharedRef(), Writer);

    bool bSuccess = FFileHelper::SaveStringToFile(OutputString, *CachePath);
    if (bSuccess)
    {
        UE_LOG(LogAuracronPCGCollision, Log, TEXT("Saved collision cache to: %s"), *CachePath);
    }
    else
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Failed to save collision cache to: %s"), *CachePath);
    }

    return bSuccess;
}

bool UAuracronPCGCollisionSystem::LoadCollisionCache(const FString& CachePath)
{
    FString FileContent;
    if (!FFileHelper::LoadFileToString(FileContent, *CachePath))
    {
        UE_LOG(LogAuracronPCGCollision, Warning, TEXT("Failed to load collision cache from: %s"), *CachePath);
        return false;
    }

    TSharedPtr<FJsonObject> CacheJson;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);

    if (!FJsonSerializer::Deserialize(Reader, CacheJson) || !CacheJson.IsValid())
    {
        UE_LOG(LogAuracronPCGCollision, Error, TEXT("Failed to parse collision cache JSON"));
        return false;
    }

    // Load cache metadata
    CollisionCache.CacheVersion = CacheJson->GetIntegerField(TEXT("CacheVersion"));
    CollisionCache.LastUpdateTime = CacheJson->GetNumberField(TEXT("LastUpdateTime"));

    // Load cache data
    const TArray<TSharedPtr<FJsonValue>>* CacheDataArray;
    if (CacheJson->TryGetArrayField(TEXT("CacheData"), CacheDataArray))
    {
        for (const auto& CacheValue : *CacheDataArray)
        {
            const TSharedPtr<FJsonObject>* EntryJson;
            if (CacheValue->TryGetObject(EntryJson))
            {
                FString Key = (*EntryJson)->GetStringField(TEXT("Key"));
                // Load more data as needed
                // For now, just create empty cache entries
                FAuracronPCGCollisionData CacheData;
                CollisionCache.CachedCollisionData.Add(Key, CacheData);
            }
        }
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Loaded collision cache from: %s"), *CachePath);
    return true;
}

// Internal collision generation functions
UStaticMesh* UAuracronPCGCollisionSystem::GenerateSimpleCollisionMesh(const TArray<FVector>& Vertices, const FAuracronPCGCollisionSettings& Settings)
{
    if (Vertices.Num() < 3)
    {
        return nullptr;
    }

    // Create a new static mesh
    UStaticMesh* NewMesh = NewObject<UStaticMesh>(GetTransientPackage(), NAME_None, RF_Transient);
    if (!NewMesh)
    {
        return nullptr;
    }

    // Calculate bounding box for simple collision
    FBox BoundingBox(ForceInit);
    for (const FVector& Vertex : Vertices)
    {
        BoundingBox += Vertex;
    }

    // Create simple box collision
    if (UBodySetup* BodySetup = NewMesh->GetBodySetup())
    {
        BodySetup->AggGeom.BoxElems.Empty();
        FKBoxElem BoxElem;
        BoxElem.Center = BoundingBox.GetCenter();
        BoxElem.X = BoundingBox.GetSize().X;
        BoxElem.Y = BoundingBox.GetSize().Y;
        BoxElem.Z = BoundingBox.GetSize().Z;
        BodySetup->AggGeom.BoxElems.Add(BoxElem);
        BodySetup->bGenerateMirroredCollision = false;
        BodySetup->bDoubleSidedGeometry = true;
        BodySetup->CollisionTraceFlag = CTF_UseSimpleAsComplex;
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Generated simple collision mesh with %d vertices"), Vertices.Num());
    return NewMesh;
}

UStaticMesh* UAuracronPCGCollisionSystem::GenerateComplexCollisionMesh(const TArray<FVector>& Vertices, const TArray<int32>& Indices, const FAuracronPCGCollisionSettings& Settings)
{
    if (Vertices.Num() < 3 || Indices.Num() < 3)
    {
        return nullptr;
    }

    // Create a new static mesh
    UStaticMesh* NewMesh = NewObject<UStaticMesh>(GetTransientPackage(), NAME_None, RF_Transient);
    if (!NewMesh)
    {
        return nullptr;
    }

    // Create complex collision using convex hulls
    if (UBodySetup* BodySetup = NewMesh->GetBodySetup())
    {
        BodySetup->AggGeom.ConvexElems.Empty();

        // Optimize vertices if needed
        TArray<FVector> OptimizedVertices = Vertices;
        OptimizeCollisionVertices(OptimizedVertices, Settings);

        // Create convex hull from optimized vertices
        FKConvexElem ConvexElem;
        ConvexElem.VertexData = OptimizedVertices;
        ConvexElem.UpdateElemBox();
        BodySetup->AggGeom.ConvexElems.Add(ConvexElem);

        BodySetup->bGenerateMirroredCollision = false;
        BodySetup->bDoubleSidedGeometry = true;
        BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Generated complex collision mesh with %d vertices, %d indices"), Vertices.Num(), Indices.Num());
    return NewMesh;
}

UStaticMesh* UAuracronPCGCollisionSystem::GenerateProceduralCollisionMesh(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings)
{
    if (Points.Num() == 0)
    {
        return nullptr;
    }

    // Create a new static mesh
    UStaticMesh* NewMesh = NewObject<UStaticMesh>(GetTransientPackage(), NAME_None, RF_Transient);
    if (!NewMesh)
    {
        return nullptr;
    }

    // Generate procedural collision based on point properties
    TArray<FVector> CollisionVertices;
    for (const FPCGPoint& Point : Points)
    {
        FVector Location = Point.Transform.GetLocation();
        FVector Scale = Point.Transform.GetScale3D();

        // Create collision vertices around each point based on its scale
        float Radius = FMath::Max(Scale.X, FMath::Max(Scale.Y, Scale.Z)) * 50.0f; // Base radius

        // Add vertices in a circle around the point
        int32 NumSegments = FMath::Clamp(static_cast<int32>(Radius / 10.0f), 6, 16);
        for (int32 i = 0; i < NumSegments; ++i)
        {
            float Angle = (2.0f * PI * i) / NumSegments;
            FVector Offset = FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
            CollisionVertices.Add(Location + Offset);
        }
    }

    // Create collision using the generated vertices
    if (UBodySetup* BodySetup = NewMesh->GetBodySetup())
    {
        BodySetup->AggGeom.ConvexElems.Empty();

        // Optimize vertices
        OptimizeCollisionVertices(CollisionVertices, Settings);

        // Create convex hull
        FKConvexElem ConvexElem;
        ConvexElem.VertexData = CollisionVertices;
        ConvexElem.UpdateElemBox();
        BodySetup->AggGeom.ConvexElems.Add(ConvexElem);

        BodySetup->bGenerateMirroredCollision = false;
        BodySetup->bDoubleSidedGeometry = true;
        BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Generated procedural collision mesh for %d points"), Points.Num());
    return NewMesh;
}

void UAuracronPCGCollisionSystem::OptimizeCollisionVertices(TArray<FVector>& Vertices, const FAuracronPCGCollisionSettings& Settings)
{
    if (Vertices.Num() <= Settings.MaxCollisionVertices)
    {
        return; // No optimization needed
    }

    // Simple vertex reduction by distance threshold
    TArray<FVector> OptimizedVertices;
    float MinDistance = 10.0f * Settings.ComplexityReduction; // Minimum distance between vertices

    OptimizedVertices.Add(Vertices[0]); // Always keep first vertex

    for (int32 i = 1; i < Vertices.Num(); ++i)
    {
        bool bTooClose = false;
        for (const FVector& ExistingVertex : OptimizedVertices)
        {
            if (FVector::Dist(Vertices[i], ExistingVertex) < MinDistance)
            {
                bTooClose = true;
                break;
            }
        }

        if (!bTooClose)
        {
            OptimizedVertices.Add(Vertices[i]);

            // Stop if we've reached the maximum
            if (OptimizedVertices.Num() >= Settings.MaxCollisionVertices)
            {
                break;
            }
        }
    }

    Vertices = OptimizedVertices;
    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Optimized collision vertices from %d to %d"), Vertices.Num(), OptimizedVertices.Num());
}

void UAuracronPCGCollisionSystem::SimplifyCollisionMesh(UStaticMesh* Mesh, float ReductionPercentage)
{
    if (!Mesh || ReductionPercentage <= 0.0f)
    {
        return;
    }

    // Simplified mesh reduction - in a full implementation, this would use more sophisticated algorithms
    if (UBodySetup* BodySetup = Mesh->GetBodySetup())
    {
        // Reduce convex hull complexity
        for (FKConvexElem& ConvexElem : BodySetup->AggGeom.ConvexElems)
        {
            int32 TargetVertexCount = FMath::Max(4, static_cast<int32>(ConvexElem.VertexData.Num() * (1.0f - ReductionPercentage)));

            if (ConvexElem.VertexData.Num() > TargetVertexCount)
            {
                // Simple reduction by removing every nth vertex
                TArray<FVector> ReducedVertices;
                int32 Step = FMath::Max(1, ConvexElem.VertexData.Num() / TargetVertexCount);

                for (int32 i = 0; i < ConvexElem.VertexData.Num(); i += Step)
                {
                    ReducedVertices.Add(ConvexElem.VertexData[i]);
                    if (ReducedVertices.Num() >= TargetVertexCount)
                    {
                        break;
                    }
                }

                ConvexElem.VertexData = ReducedVertices;
                ConvexElem.UpdateElemBox();
            }
        }
    }

    UE_LOG(LogAuracronPCGCollision, Log, TEXT("Simplified collision mesh by %.1f%%"), ReductionPercentage * 100.0f);
}

FString UAuracronPCGCollisionSystem::GenerateCacheKey(const TArray<FPCGPoint>& Points, const FAuracronPCGCollisionSettings& Settings)
{
    // Generate a hash-based cache key
    FString KeyString = FString::Printf(TEXT("Points_%d_Type_%d_Quality_%d_Complexity_%.2f"),
        Points.Num(),
        static_cast<int32>(Settings.CollisionType),
        static_cast<int32>(Settings.CollisionQuality),
        Settings.ComplexityReduction);

    // Add point positions to the hash (sample first few points to avoid performance issues)
    int32 SampleCount = FMath::Min(10, Points.Num());
    for (int32 i = 0; i < SampleCount; ++i)
    {
        FVector Loc = Points[i].Transform.GetLocation();
        KeyString += FString::Printf(TEXT("_%.1f_%.1f_%.1f"), Loc.X, Loc.Y, Loc.Z);
    }

    return FString::Printf(TEXT("%u"), GetTypeHash(KeyString));
}

bool UAuracronPCGCollisionSystem::IsCacheValid(const FString& CacheKey) const
{
    return CollisionCache.CachedCollisionData.Contains(CacheKey) &&
           CollisionCache.GeneratedCollisionMeshes.Contains(CacheKey);
}
