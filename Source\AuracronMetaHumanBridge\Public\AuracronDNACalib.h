#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// RigLogic DNA includes for calibration
#include "dna/BinaryStreamReader.h"
#include "dna/BinaryStreamWriter.h"
#include "dna/DataLayer.h"
#include "dna/Reader.h"
#include "dna/Writer.h"
#include "dna/layers/GeometryWriter.h"
#include "dna/layers/DefinitionWriter.h"
#include "dna/types/Aliases.h"
#include "dna/types/Vector3.h"
#include "dna/layers/Geometry.h"
#include "riglogic/RigLogic.h"
#include "UObject/ObjectMacros.h"

#include "AuracronDNACalib.generated.h"

// Forward declarations
class FAuracronDNAReader;
enum class EDNACalibrationOperation : uint8;

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronDNACalib, Log, All);

/**
 * Quality metrics for DNA calibration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNAQualityMetrics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Metrics")
    float Accuracy = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Metrics")
    float Precision = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Metrics")
    float Recall = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Metrics")
    float FScore = 0.0f;
};

/**
 * Performance metrics for DNA calibration operations
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNAPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    double CalibrationTime = 0.0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    double ValidationTime = 0.0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int32 VerticesProcessed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int32 MemoryUsage = 0;
};

/**
 * Validation results for DNA calibration
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNAValidationResults
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Results")
    bool bIsValid = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Results")
    TArray<FString> ErrorMessages;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Results")
    TArray<FString> WarningMessages;
};

/**
 * Comprehensive DNA calibration parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FDNACalibrationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    TMap<FName, float> BoneWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    TMap<FName, float> BlendShapeWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    TMap<FName, FMatrix> TransformationMatrices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    FDNAQualityMetrics QualityMetrics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    float CalibrationTolerance = 0.001f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    int32 MaxIterations = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Calibration Parameters")
    bool bUseAdvancedCalibration = true;
};

/**
 * Thread-safe wrapper for MetaHuman DNA calibration operations
 * Provides type-safe abstraction over DNACalibDNAReader for editing operations
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronDNACalib
{
public:
    FAuracronDNACalib();
    ~FAuracronDNACalib();

    // Core calibration operations
    bool InitializeFromReader(const FAuracronDNAReader& Reader);
    bool IsValid() const;
    void Reset();

    // Vertex manipulation
    bool SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions);
    bool SetVertexPositions(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector>& Positions);
    bool TransformVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FTransform& Transform);
    bool ScaleVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FVector& Scale);

    // Blend shape manipulation
    bool SetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, const TArray<FVector>& Deltas);
    bool SetBlendShapeTargetVertexIndices(int32 MeshIndex, int32 TargetIndex, const TArray<int32>& VertexIndices);
    bool AddBlendShapeTarget(int32 MeshIndex, const FString& BlendShapeName, const TArray<int32>& VertexIndices, const TArray<FVector>& Deltas);
    bool RemoveBlendShapeTarget(int32 MeshIndex, int32 TargetIndex);
    bool ScaleBlendShapeDeltas(int32 MeshIndex, int32 TargetIndex, float ScaleFactor);

    // Joint manipulation
    bool SetNeutralJointTranslations(const TArray<FVector>& Translations);
    bool SetNeutralJointRotations(const TArray<FRotator>& Rotations);
    bool SetJointHierarchy(const TArray<int32>& ParentIndices);
    bool SetJointTransform(int32 JointIndex, const FTransform& Transform);
    bool ScaleJoint(int32 JointIndex, const FVector& Scale);

    // Skin weights manipulation
    bool SetSkinWeights(int32 MeshIndex, int32 VertexIndex, const TArray<float>& Weights, const TArray<int32>& JointIndices);
    bool NormalizeSkinWeights(int32 MeshIndex);
    bool PruneSkinWeights(int32 MeshIndex, float WeightThreshold = 0.001f);

    // Animation data manipulation
    bool SetAnimatedMapValues(int32 MapIndex, const TArray<float>& Values);
    bool AddAnimatedMap(const FString& MapName, const TArray<float>& Values);
    bool RemoveAnimatedMap(int32 MapIndex);
    bool ScaleAnimatedMapValues(int32 MapIndex, float ScaleFactor);

    // Control rig manipulation
    bool SetControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveControl(int32 ControlIndex);
    bool SetControlLimits(int32 ControlIndex, const TArray<float>& MinValues, const TArray<float>& MaxValues);

    // Machine learning data manipulation
    bool SetMLControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddMLControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveMLControl(int32 ControlIndex);
    bool TrainMLControl(int32 ControlIndex, const TArray<TArray<float>>& TrainingData);

    // Neural network manipulation
    bool SetNeuralNetworkWeights(int32 NetworkIndex, const TArray<float>& Weights);
    bool SetNeuralNetworkBiases(int32 NetworkIndex, const TArray<float>& Biases);
    bool AddNeuralNetwork(const FString& NetworkName, const TArray<int32>& Topology, const TArray<float>& Weights, const TArray<float>& Biases);
    bool RemoveNeuralNetwork(int32 NetworkIndex);
    bool TrainNeuralNetwork(int32 NetworkIndex, const TArray<TArray<float>>& InputData, const TArray<TArray<float>>& OutputData);

    // Batch operations
    bool BeginBatchOperation();
    bool EndBatchOperation();
    bool ExecuteBatchOperation();
    bool CancelBatchOperation();
    bool AddToBatch(EDNACalibrationOperation Operation, const TArray<FString>& Parameters);

    // Validation and optimization
    bool ValidateCalibration() const;
    bool OptimizeForPerformance();
    bool OptimizeForSize();
    bool RepairCorruption();

    // Undo/Redo system
    bool CanUndo() const;
    bool CanRedo() const;
    bool Undo();
    bool Redo();
    void ClearUndoHistory();

    // Export operations
    bool ExportToReader(FAuracronDNAReader& OutReader) const;
    bool SaveToFile(const FString& FilePath, bool bBinaryFormat = true) const;

    // Error handling
    FString GetLastError() const;
    TArray<FString> GetValidationErrors() const;
    TArray<FString> GetValidationWarnings() const;

    // Thread safety
    mutable FCriticalSection AccessMutex;

private:
    // Internal helper functions
    bool RefreshReaderFromWriter();
    bool ValidateMeshIndex(int32 MeshIndex) const;
    void SaveUndoState(const FString& Description);
    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;
    // Native DNA calibration instances - using RigLogic DNA API
    TUniquePtr<dna::BinaryStreamReader> NativeReader;
    TUniquePtr<dna::BinaryStreamWriter> NativeWriter;
    TUniquePtr<dna::MemoryStream> MemoryStream;

    // Command batching for performance
    TArray<TFunction<void()>> PendingCommands;
    bool bBatchMode;

    // State tracking
    FThreadSafeBool bIsValid;
    FThreadSafeBool bInBatchOperation;
    mutable FString LastError;
    TArray<FString> ValidationErrors;
    TArray<FString> ValidationWarnings;

    // Undo/Redo system
    struct FCalibrationState
    {
        TArray<uint8> SerializedData;
        FString Description;
        FDateTime Timestamp;
    };
    TArray<FCalibrationState> UndoStack;
    TArray<FCalibrationState> RedoStack;
    int32 MaxUndoStates;

    // Calibration data structures
    FDNACalibrationParameters CalibrationParameters;
    FDNAValidationResults ValidationResults;
    FDNAPerformanceMetrics PerformanceMetrics;

    // Additional validation methods
    bool ValidateJointIndex(int32 JointIndex) const;
    bool ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const;
    bool ValidateAnimatedMapIndex(int32 MapIndex) const;
    bool ValidateControlIndex(int32 ControlIndex) const;
    bool ValidateMLControlIndex(int32 ControlIndex) const;
    bool ValidateNeuralNetworkIndex(int32 NetworkIndex) const;
    bool RestoreState(const FCalibrationState& State);

    // Prevent copying
    FAuracronDNACalib(const FAuracronDNACalib&) = delete;
    FAuracronDNACalib& operator=(const FAuracronDNACalib&) = delete;
};
