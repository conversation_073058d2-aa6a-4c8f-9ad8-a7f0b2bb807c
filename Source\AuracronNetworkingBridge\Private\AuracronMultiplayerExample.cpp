/**
 * AuracronMultiplayerExample.cpp
 * 
 * Implementation of the advanced multiplayer networking example
 * demonstrating production-ready usage of UE 5.6 networking APIs.
 */

#include "AuracronMultiplayerExample.h"
#include "Engine/World.h"
#include "GameFramework/PlayerState.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"

// === AAuracronMultiplayerExample Implementation ===

AAuracronMultiplayerExample::AAuracronMultiplayerExample()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Initialize default configurations
    SessionConfig = FAuracronMultiplayerSessionConfig();
    SessionConfig.SessionName = TEXT("AuracronExampleSession");
    SessionConfig.SessionType = EAuracronSessionType::Cooperative;
    SessionConfig.MaxPlayers = 8;
    SessionConfig.bUseDedicatedServer = true;
    SessionConfig.bEnableAntiCheat = true;
    SessionConfig.AntiCheatLevel = EAuracronAntiCheatLevel::Advanced;
    
    IrisConfig = FAuracronIrisReplicationConfig();
    IrisConfig.bEnableIrisReplication = true;
    IrisConfig.ReplicationMode = EAuracronIrisReplicationMode::Optimized;
    IrisConfig.MaxReplicationFrequency = 60.0f;
    
    AntiCheatLevel = EAuracronAntiCheatLevel::Advanced;
}

void AAuracronMultiplayerExample::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Initializing multiplayer example..."));
    
    InitializeNetworkingSystem();
    SetupNetworkingCallbacks();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Multiplayer example initialized"));
}

void AAuracronMultiplayerExample::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    if (NetworkingCoordinator)
    {
        NetworkingCoordinator->LeaveMultiplayerSession();
    }
    
    Super::EndPlay(EndPlayReason);
}

void AAuracronMultiplayerExample::CreateCooperativeSession()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Creating cooperative session..."));
    
    if (!NetworkingCoordinator)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: NetworkingCoordinator not initialized"));
        return;
    }
    
    // Configure for cooperative gameplay
    SessionConfig.SessionType = EAuracronSessionType::Cooperative;
    SessionConfig.MaxPlayers = 8;
    SessionConfig.bEnableAntiCheat = true;
    SessionConfig.AntiCheatLevel = EAuracronAntiCheatLevel::Standard;
    
    // Create the session
    bool bSuccess = NetworkingCoordinator->CreateMultiplayerSession(SessionConfig);
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Cooperative session created successfully"));
        OnSessionCreated(TEXT("CooperativeSession"));
        
        // Setup Iris replication for cooperative gameplay
        SetupIrisReplication();
        
        // Initialize anti-cheat
        InitializeAntiCheat();
        
        // Setup server authority
        SetupServerAuthority();
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to create cooperative session"));
    }
}

void AAuracronMultiplayerExample::CreateCompetitiveSession()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Creating competitive session..."));
    
    if (!NetworkingCoordinator)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: NetworkingCoordinator not initialized"));
        return;
    }
    
    // Configure for competitive gameplay
    SessionConfig.SessionType = EAuracronSessionType::Competitive;
    SessionConfig.MaxPlayers = 10; // 5v5
    SessionConfig.bEnableAntiCheat = true;
    SessionConfig.AntiCheatLevel = EAuracronAntiCheatLevel::Strict;
    SessionConfig.bUseDedicatedServer = true;
    
    // Create the session
    bool bSuccess = NetworkingCoordinator->CreateMultiplayerSession(SessionConfig);
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Competitive session created successfully"));
        OnSessionCreated(TEXT("CompetitiveSession"));
        
        // Setup Iris replication for competitive gameplay
        IrisConfig.ReplicationMode = EAuracronIrisReplicationMode::LowLatency;
        IrisConfig.MaxReplicationFrequency = 120.0f; // Higher frequency for competitive
        SetupIrisReplication();
        
        // Initialize strict anti-cheat
        InitializeAntiCheat();
        
        // Setup server authority
        SetupServerAuthority();
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to create competitive session"));
    }
}

void AAuracronMultiplayerExample::JoinSession(const FString& SessionID)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Joining session: %s"), *SessionID);
    
    if (!NetworkingCoordinator)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: NetworkingCoordinator not initialized"));
        return;
    }
    
    bool bSuccess = NetworkingCoordinator->JoinMultiplayerSession(SessionID);
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Successfully joined session"));
        
        // Setup client-side systems
        SetupIrisReplication();
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to join session"));
    }
}

void AAuracronMultiplayerExample::SetupIrisReplication()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Setting up Iris replication..."));
    
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    // Initialize Iris replication system
    bool bIrisInitialized = NetworkingCoordinator->InitializeIrisReplicationSystem();
    if (!bIrisInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to initialize Iris replication"));
        return;
    }
    
    // Configure Iris replication
    NetworkingCoordinator->ConfigureIrisReplication(IrisConfig);
    
    // Register game objects for replication
    RegisterGameObjectsForReplication();
    
    // Optimize Iris replication graph
    NetworkingCoordinator->OptimizeIrisReplicationGraph();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Iris replication setup completed"));
}

void AAuracronMultiplayerExample::RegisterGameObjectsForReplication()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Registering game objects for Iris replication..."));
    
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    // Register this game mode for replication
    NetworkingCoordinator->RegisterObjectForIrisReplication(this, TEXT("GameMode"));
    
    // Register all player controllers
    UWorld* World = GetWorld();
    if (World)
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC)
            {
                FString PlayerName = FString::Printf(TEXT("PlayerController_%d"), PC->GetPlayerState<APlayerState>() ? PC->GetPlayerState<APlayerState>()->GetPlayerId() : 0);
                NetworkingCoordinator->RegisterObjectForIrisReplication(PC, PlayerName);
                
                // Register player pawn if exists
                if (PC->GetPawn())
                {
                    FString PawnName = FString::Printf(TEXT("PlayerPawn_%d"), PC->GetPlayerState<APlayerState>() ? PC->GetPlayerState<APlayerState>()->GetPlayerId() : 0);
                    NetworkingCoordinator->RegisterObjectForIrisReplication(PC->GetPawn(), PawnName);
                }
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Game objects registered for Iris replication"));
}

void AAuracronMultiplayerExample::InitializeAntiCheat()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Initializing anti-cheat system..."));
    
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    bool bAntiCheatInitialized = NetworkingCoordinator->InitializeAdvancedAntiCheat(AntiCheatLevel);
    if (bAntiCheatInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Anti-cheat system initialized successfully"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to initialize anti-cheat system"));
    }
}

bool AAuracronMultiplayerExample::ValidatePlayerAction(const FString& PlayerID, const FString& ActionType, const TMap<FString, FString>& ActionData)
{
    if (!NetworkingCoordinator)
    {
        return false;
    }
    
    // Validate the action server-side
    FAuracronAntiCheatValidation Validation = NetworkingCoordinator->ValidatePlayerActionServerSide(PlayerID, ActionType, ActionData);
    
    if (!Validation.bValidationPassed)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Player action validation failed - Player: %s, Action: %s"), *PlayerID, *ActionType);
        OnCheatDetected(PlayerID, ActionType);
    }
    
    return Validation.bValidationPassed;
}

void AAuracronMultiplayerExample::SetupServerAuthority()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Setting up server authority..."));
    
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    // Enable authoritative mode for critical systems
    NetworkingCoordinator->EnableAuthoritativeModeForSystem(TEXT("PlayerMovement"));
    NetworkingCoordinator->EnableAuthoritativeModeForSystem(TEXT("Combat"));
    NetworkingCoordinator->EnableAuthoritativeModeForSystem(TEXT("GameState"));
    NetworkingCoordinator->EnableAuthoritativeModeForSystem(TEXT("PlayerStats"));
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Server authority setup completed"));
}

void AAuracronMultiplayerExample::SyncGameStateAcrossNetwork()
{
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    // Create game state data
    TMap<FString, FString> GameStateData;
    GameStateData.Add(TEXT("GameTime"), FString::Printf(TEXT("%.2f"), GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f));
    GameStateData.Add(TEXT("PlayerCount"), FString::Printf(TEXT("%d"), GetNumPlayers()));
    GameStateData.Add(TEXT("GamePhase"), TEXT("InProgress"));
    
    // Sync across network
    NetworkingCoordinator->SyncBridgeStateAcrossNetwork(TEXT("GameState"), GameStateData);
}

// === Private Helper Methods ===

void AAuracronMultiplayerExample::InitializeNetworkingSystem()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Initializing networking system..."));
    
    // Get the networking coordinator
    UWorld* World = GetWorld();
    if (World)
    {
        NetworkingCoordinator = World->GetSubsystem<UAuracronAdvancedNetworkingCoordinator>();
        if (NetworkingCoordinator)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: NetworkingCoordinator obtained"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON EXAMPLE: Failed to get NetworkingCoordinator"));
        }
    }
}

void AAuracronMultiplayerExample::ConfigureSessionSettings()
{
    // Add custom session settings
    SessionConfig.SessionSettings.Add(TEXT("GameMode"), TEXT("AuracronExample"));
    SessionConfig.SessionSettings.Add(TEXT("MapName"), GetWorld() ? GetWorld()->GetMapName() : TEXT("Unknown"));
    SessionConfig.SessionSettings.Add(TEXT("Difficulty"), TEXT("Normal"));
}

void AAuracronMultiplayerExample::SetupNetworkingCallbacks()
{
    // Setup networking event callbacks
    // This would bind to the networking coordinator's events
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Networking callbacks setup"));
}

void AAuracronMultiplayerExample::HandleNetworkingEvents()
{
    // Handle networking events
    // This would process networking events and update game state accordingly
}

// === AAuracronMultiplayerPlayerController Implementation ===

AAuracronMultiplayerPlayerController::AAuracronMultiplayerPlayerController()
{
    bClientPredictionEnabled = false;
}

void AAuracronMultiplayerPlayerController::BeginPlay()
{
    Super::BeginPlay();
    
    // Get networking coordinator
    UWorld* World = GetWorld();
    if (World)
    {
        NetworkingCoordinator = World->GetSubsystem<UAuracronAdvancedNetworkingCoordinator>();
    }
    
    // Enable client prediction
    EnableClientPrediction();
}

void AAuracronMultiplayerPlayerController::SendActionToServer(const FString& ActionType, const TMap<FString, FString>& ActionData)
{
    if (!NetworkingCoordinator)
    {
        return;
    }
    
    FString PlayerID = GetPlayerState<APlayerState>() ?
        (GetPlayerState<APlayerState>()->GetUniqueId().GetUniqueNetId().IsValid() ?
            GetPlayerState<APlayerState>()->GetUniqueId().GetUniqueNetId()->ToString() :
            GetName()) : TEXT("Unknown");
    
    // Add timestamp to action data
    TMap<FString, FString> TimestampedActionData = ActionData;
    TimestampedActionData.Add(TEXT("Timestamp"), FString::Printf(TEXT("%.3f"), GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f));
    
    // Validate action server-side
    FAuracronAntiCheatValidation Validation = NetworkingCoordinator->ValidatePlayerActionServerSide(PlayerID, ActionType, TimestampedActionData);
    
    ReceiveValidatedAction(ActionType, Validation.bValidationPassed);
}

void AAuracronMultiplayerPlayerController::ReceiveValidatedAction(const FString& ActionType, bool bWasValid)
{
    if (bWasValid)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON EXAMPLE: Action validated - %s"), *ActionType);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON EXAMPLE: Action rejected - %s"), *ActionType);
    }
}

void AAuracronMultiplayerPlayerController::EnableClientPrediction()
{
    bClientPredictionEnabled = true;
    UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Client prediction enabled"));
}

void AAuracronMultiplayerPlayerController::HandleServerReconciliation(const FVector& ServerPosition, float Timestamp)
{
    if (!bClientPredictionEnabled)
    {
        return;
    }
    
    // Handle server reconciliation for client prediction
    if (GetPawn())
    {
        FVector CurrentPosition = GetPawn()->GetActorLocation();
        float PositionError = FVector::Dist(CurrentPosition, ServerPosition);
        
        // If error is significant, reconcile with server
        if (PositionError > 100.0f) // 1 meter tolerance
        {
            GetPawn()->SetActorLocation(ServerPosition);
            UE_LOG(LogTemp, Log, TEXT("AURACRON EXAMPLE: Server reconciliation applied - Error: %.1f"), PositionError);
        }
    }
}
