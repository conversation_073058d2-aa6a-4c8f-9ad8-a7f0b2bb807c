#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronSimpleHardwareDetection.h"
#include "AuracronHardwareDetectionComponent.h"
#include "AuracronHardwareDetectionTests.generated.h"

/**
 * Classe para testes do sistema de detecção de hardware
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronHardwareDetectionTests : public UObject
{
    GENERATED_BODY()

public:
    UAuracronHardwareDetectionTests();

    /** Executa todos os testes */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool RunAllTests();

    /** Testa detecção básica de hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestBasicHardwareDetection();

    /** Testa cálculo de configurações de qualidade */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestQualityCalculation();

    /** Testa aplicação de configurações */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestSettingsApplication();

    /** Testa componente de detecção */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestHardwareDetectionComponent();

    /** Testa perfis personalizados */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestCustomProfiles();

    /** Testa limitações de qualidade */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestQualityLimits();

    /** Simula diferentes tipos de hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    FSimpleHardwareInfo CreateMockHardware(const FString& GPUName, int32 VRAM, float RAM, int32 Cores, bool bRayTracing = false);

    /** Valida informações de hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool ValidateHardwareInfo(const FSimpleHardwareInfo& HardwareInfo);

    /** Valida configurações de qualidade */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool ValidateQualitySettings(const FSimpleQualitySettings& Settings);

    /** Executa teste de performance */
    UFUNCTION(BlueprintCallable, Category = "Hardware Tests")
    bool TestPerformance();

private:
    /** Sistema de detecção para testes */
    UPROPERTY()
    TObjectPtr<UAuracronSimpleHardwareDetection> TestHardwareSystem;

    /** Componente para testes */
    UPROPERTY()
    TObjectPtr<UAuracronHardwareDetectionComponent> TestComponent;

    /** Contador de testes executados */
    int32 TestsRun;

    /** Contador de testes que passaram */
    int32 TestsPassed;

    /** Log de teste */
    void LogTest(const FString& TestName, bool bPassed, const FString& Details = TEXT(""));

    /** Verifica se valor está dentro do range esperado */
    bool IsInRange(int32 Value, int32 Min, int32 Max);
    bool IsInRange(float Value, float Min, float Max);
};
