﻿using UnrealBuildTool;
public class AuracronAbismoUmbrioBridge : ModuleRules
{
    public AuracronAbismoUmbrioBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GeometryScriptingCore",
                "GeometryFramework",
                "DynamicMesh",
                "MeshDescription",
                "StaticMeshDescription",
                "MeshConversion",
                "ModelingComponents",
                "ModelingOperators",
                "Landscape",
                "RenderCore",
                "RHI",
                "NavigationSystem",
                "GameplayTasks",
                "UMG",
                "Slate",
                "SlateCore",

                "PCG"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "ChaosCore",
                "PhysicsCore",
                "Niagara",
                "NiagaraCore",
                "NiagaraShader",
                "CinematicCamera",
                "MovieScene",
                "MovieSceneTracks"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "LevelEditor",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers"
                }
            );
        }
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronAbismoUmbrioBridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronAbismoUmbrioBridge/Private"
        });
    }
}


