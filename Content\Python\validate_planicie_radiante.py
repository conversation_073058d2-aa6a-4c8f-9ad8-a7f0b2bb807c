#!/usr/bin/env python3
"""
AURACRON - Planície Radiante Production Validation Script
Validates that the created terrain meets all production requirements
"""

import unreal
import sys

def validate_planicie_radiante_production():
    """Validate Planície Radiante meets production standards"""
    try:
        unreal.log("🔍 Starting Planície Radiante production validation...")
        
        validation_results = {
            'level_exists': False,
            'landscape_created': False,
            'lighting_configured': False,
            'materials_applied': False,
            'physics_configured': False,
            'world_partition_enabled': False
        }
        
        # Check if level exists
        level_path = "/Game/Levels/Realms/PlanicieRadiante"
        if unreal.EditorAssetLibrary.does_asset_exist(level_path):
            validation_results['level_exists'] = True
            unreal.log("✅ Level exists")
        else:
            unreal.log_error("❌ Level does not exist")
        
        # Load level for validation
        if validation_results['level_exists']:
            if unreal.EditorLevelLibrary.load_level(level_path):
                world = unreal.EditorLevelLibrary.get_editor_world()
                
                if world:
                    # Check for landscape actors
                    landscape_actors = unreal.EditorFilterLibrary.by_class(
                        unreal.EditorLevelLibrary.get_all_level_actors(),
                        unreal.LandscapeProxy
                    )
                    
                    if landscape_actors:
                        validation_results['landscape_created'] = True
                        unreal.log("✅ Landscape actors found")
                    else:
                        unreal.log_error("❌ No landscape actors found")
                    
                    # Check for lighting actors
                    light_actors = unreal.EditorFilterLibrary.by_class(
                        unreal.EditorLevelLibrary.get_all_level_actors(),
                        unreal.Light
                    )
                    
                    if light_actors:
                        validation_results['lighting_configured'] = True
                        unreal.log("✅ Lighting actors found")
                    else:
                        unreal.log_error("❌ No lighting actors found")
                    
                    # Check world partition
                    world_partition = world.get_world_partition()
                    if world_partition and world_partition.is_streaming_enabled():
                        validation_results['world_partition_enabled'] = True
                        unreal.log("✅ World Partition enabled")
                    else:
                        unreal.log_warning("⚠️ World Partition not enabled")
        
        # Calculate validation score
        passed_checks = sum(validation_results.values())
        total_checks = len(validation_results)
        success_rate = (passed_checks / total_checks) * 100
        
        unreal.log(f"📊 Validation Results: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            unreal.log("✅ Planície Radiante meets production standards!")
            return True
        else:
            unreal.log_error("❌ Planície Radiante does not meet production standards")
            return False
            
    except Exception as e:
        unreal.log_error(f"❌ Validation failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = validate_planicie_radiante_production()
    sys.exit(0 if success else 1)