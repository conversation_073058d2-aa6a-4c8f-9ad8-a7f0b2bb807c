#include "AuracronVerticalConnector.h"

#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "GameFramework/Character.h"

AAuracronVerticalConnectorActor::AAuracronVerticalConnectorActor()
{
    PrimaryActorTick.bCanEverTick = true;

    Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
    SetRootComponent(Root);

    VisualMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("VisualMesh"));
    VisualMesh->SetupAttachment(Root);

    ActivationSphere = CreateDefaultSubobject<USphereComponent>(TEXT("ActivationSphere"));
    ActivationSphere->SetupAttachment(Root);
    ActivationSphere->SetSphereRadius(300.f);
    ActivationSphere->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    ActivationSphere->SetCollisionResponseToAllChannels(ECR_Ignore);
    ActivationSphere->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    NiagaraFX = CreateDefaultSubobject<UNiagaraComponent>(TEXT("NiagaraFX"));
    NiagaraFX->SetupAttachment(Root);
    NiagaraFX->bAutoActivate = false;

    AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("Audio"));
    AudioComponent->SetupAttachment(Root);
    AudioComponent->bAutoActivate = false;
}

void AAuracronVerticalConnectorActor::OnConstruction(const FTransform& Transform)
{
    Super::OnConstruction(Transform);
    UpdateComponentsFromConfig();
}

void AAuracronVerticalConnectorActor::BeginPlay()
{
    Super::BeginPlay();
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::Tick(float DeltaSeconds)
{
    Super::Tick(DeltaSeconds);
}

void AAuracronVerticalConnectorActor::InitializeFromConfig(const FVerticalConnectorConfig& InConfig)
{
    ConnectorConfig = InConfig;
    SetActorLocation(ConnectorConfig.ConnectorLocation);
    ActivationSphere->SetSphereRadius(FMath::Max(50.f, ConnectorConfig.ActivationRadius));
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::SetConnectorState(EVerticalConnectorState NewState)
{
    if (ConnectorState == NewState)
    {
        return;
    }
    ConnectorState = NewState;
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::ApplyTransitionEffects(ACharacter* Character, bool bStarting)
{
    if (!IsValid(Character))
    {
        return;
    }

    // Basic production-safe hooks: activate/deactivate FX and audio
    if (bStarting)
    {
        if (NiagaraFX && ActivationFX)
        {
            NiagaraFX->SetAsset(ActivationFX);
            NiagaraFX->Activate(true);
        }
        if (AudioComponent && ActivationSound)
        {
            AudioComponent->SetSound(ActivationSound);
            AudioComponent->Play();
        }
    }
    else
    {
        if (NiagaraFX)
        {
            NiagaraFX->Deactivate();
        }
        if (AudioComponent)
        {
            AudioComponent->Stop();
        }
    }
}

void AAuracronVerticalConnectorActor::UpdateComponentsFromConfig()
{
    ActivationSphere->SetSphereRadius(FMath::Max(50.f, ConnectorConfig.ActivationRadius));
}

void AAuracronVerticalConnectorActor::UpdateFXForState()
{
    switch (ConnectorState)
    {
        case EVerticalConnectorState::Active:
            if (NiagaraFX)
            {
                NiagaraFX->Activate(true);
            }
            break;
        case EVerticalConnectorState::InUse:
            if (NiagaraFX)
            {
                NiagaraFX->Activate(true);
            }
            break;
        case EVerticalConnectorState::Inactive:
        case EVerticalConnectorState::Cooldown:
        default:
            if (NiagaraFX)
            {
                NiagaraFX->Deactivate();
            }
            break;
    }
}

void AAuracronVerticalConnectorActor::UpdateConnectorState(float DeltaTime)
{
    // Handle cooldown countdown
    if (ConnectorState == EVerticalConnectorState::Cooldown)
    {
        CooldownRemaining = FMath::Max(0.f, CooldownRemaining - DeltaTime);
        if (CooldownRemaining <= KINDA_SMALL_NUMBER)
        {
            SetConnectorState(EVerticalConnectorState::Active);
        }
    }
}

void AAuracronVerticalConnectorActor::HandleAutoActivation(float DeltaTime)
{
    const FVerticalConnectorConfig& Cfg = ConnectorConfig;
    if (!Cfg.bAutoActivation)
    {
        return;
    }

    AutoActivationTimer += DeltaTime;

    if (!bAutoActive)
    {
        // Wait for next auto activation window
        if (AutoActivationTimer >= FMath::Max(0.1f, Cfg.AutoActivationInterval))
        {
            bAutoActive = true;
            AutoActivationTimer = 0.f;
            AutoActiveRemaining = FMath::Max(0.1f, Cfg.AutoActivationDuration);
            SetConnectorState(EVerticalConnectorState::Active);
        }
    }
    else
    {
        // Active window running
        AutoActiveRemaining -= DeltaTime;
        if (AutoActiveRemaining <= 0.f)
        {
            bAutoActive = false;
            SetConnectorState(EVerticalConnectorState::Inactive);
        }
    }
}

void AAuracronVerticalConnectorActor::SetConnectorConfiguration(const FVerticalConnectorConfig& NewConfig)
{
    ConnectorConfig = NewConfig;

    // Update visual representation based on new configuration
    UpdateVisualRepresentation();

    // Update collision based on new configuration
    if (ActivationSphere)
    {
        ActivationSphere->SetSphereRadius(ConnectorConfig.ActivationRadius);
    }

    // Update effects based on configuration
    UpdateEffectsForConfiguration();

    UE_LOG(LogTemp, Log, TEXT("Connector configuration updated for type %d"), (int32)ConnectorConfig.ConnectorType);
}

void AAuracronVerticalConnectorActor::UpdateVisualRepresentation()
{
    if (!VisualMesh)
    {
        return;
    }

    // Update mesh and materials based on connector type
    switch (ConnectorConfig.ConnectorType)
    {
        case EVerticalConnectorType::PortalAnima:
            // Set portal de ânima-specific visual properties
            if (VisualMesh && VisualMesh->GetStaticMesh())
            {
                VisualMesh->SetWorldScale3D(FVector(1.5f, 1.5f, 0.1f)); // Flat portal
            }
            break;

        case EVerticalConnectorType::ElevadorVortice:
            // Set elevador de vórtice-specific visual properties
            if (VisualMesh && VisualMesh->GetStaticMesh())
            {
                VisualMesh->SetWorldScale3D(FVector(2.0f, 2.0f, 0.5f)); // Platform-like
            }
            break;

        case EVerticalConnectorType::FendaFluxo:
            // Set fenda fluxo-specific visual properties
            if (VisualMesh && VisualMesh->GetStaticMesh())
            {
                VisualMesh->SetWorldScale3D(FVector(1.0f, 1.0f, 0.3f)); // Low platform
            }
            break;

        case EVerticalConnectorType::CipoAstria:
            // Set cipó astria-specific visual properties
            if (VisualMesh && VisualMesh->GetStaticMesh())
            {
                VisualMesh->SetWorldScale3D(FVector(0.5f, 0.5f, 3.0f)); // Tall column
            }
            break;

        case EVerticalConnectorType::RespiradoroGeotermal:
            // Set respiradouro geotermal-specific visual properties
            if (VisualMesh && VisualMesh->GetStaticMesh())
            {
                VisualMesh->SetWorldScale3D(FVector(0.8f, 0.8f, 0.2f)); // Ground vent
            }
            break;

        default:
            break;
    }
}

void AAuracronVerticalConnectorActor::UpdateEffectsForConfiguration()
{
    // Update particle effects based on connector type
    if (NiagaraFX && NiagaraFX->GetAsset())
    {
        // Configure particle system parameters based on connector type
        switch (ConnectorConfig.ConnectorType)
        {
            case EVerticalConnectorType::PortalAnima:
                NiagaraFX->SetFloatParameter(TEXT("IntensityMultiplier"), 1.5f);
                NiagaraFX->SetColorParameter(TEXT("PortalColor"), FLinearColor::Blue);
                break;

            case EVerticalConnectorType::ElevadorVortice:
                NiagaraFX->SetFloatParameter(TEXT("IntensityMultiplier"), 0.8f);
                NiagaraFX->SetColorParameter(TEXT("PortalColor"), FLinearColor::Yellow);
                break;

            case EVerticalConnectorType::FendaFluxo:
                NiagaraFX->SetFloatParameter(TEXT("IntensityMultiplier"), 1.2f);
                NiagaraFX->SetColorParameter(TEXT("PortalColor"), FLinearColor::Green);
                break;

            case EVerticalConnectorType::CipoAstria:
                NiagaraFX->SetFloatParameter(TEXT("IntensityMultiplier"), 2.0f);
                NiagaraFX->SetColorParameter(TEXT("PortalColor"), FLinearColor::White);
                break;

            case EVerticalConnectorType::RespiradoroGeotermal:
                NiagaraFX->SetFloatParameter(TEXT("IntensityMultiplier"), 1.8f);
                NiagaraFX->SetColorParameter(TEXT("PortalColor"), FLinearColor::Red);
                break;

            default:
                break;
        }
    }

    // Update audio effects
    if (AudioComponent && AudioComponent->GetSound())
    {
        // Adjust volume and pitch based on connector type
        float VolumeMultiplier = 1.0f;
        float PitchMultiplier = 1.0f;

        switch (ConnectorConfig.ConnectorType)
        {
            case EVerticalConnectorType::PortalAnima:
                VolumeMultiplier = 0.8f;
                PitchMultiplier = 1.2f;
                break;

            case EVerticalConnectorType::CipoAstria:
                VolumeMultiplier = 1.5f;
                PitchMultiplier = 0.8f;
                break;

            case EVerticalConnectorType::RespiradoroGeotermal:
                VolumeMultiplier = 1.2f;
                PitchMultiplier = 0.6f;
                break;

            default:
                break;
        }

        AudioComponent->SetVolumeMultiplier(VolumeMultiplier);
        AudioComponent->SetPitchMultiplier(PitchMultiplier);
    }
}

