#include "AuracronCharacter.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/InputComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/Controller.h"
#include "GameFramework/SpringArmComponent.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "Engine/LocalPlayer.h"

AAuracronCharacter::AAuracronCharacter()
{
    // Set size for collision capsule
    GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);

    // Don't rotate when the controller rotates. Let that just affect the camera.
    bUseControllerRotationPitch = false;
    bUseControllerRotationYaw = false;
    bUseControllerRotationRoll = false;

    // Configure character movement
    GetCharacterMovement()->bOrientRotationToMovement = true; // Character moves in the direction of input...
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f); // ...at this rotation rate

    // Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
    // instead of recompiling to adjust them
    GetCharacterMovement()->JumpZVelocity = 700.f;
    GetCharacterMovement()->AirControl = 0.35f;
    GetCharacterMovement()->MaxWalkSpeed = MovementSpeed;
    GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
    GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
    GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

    // Create a camera boom (pulls in towards the player if there is a collision)
    CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
    CameraBoom->SetupAttachment(RootComponent);
    CameraBoom->TargetArmLength = 400.0f; // The camera follows at this distance behind the character
    CameraBoom->bUsePawnControlRotation = true; // Rotate the arm based on the controller

    // Create a follow camera
    FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
    FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName); // Attach the camera to the end of the boom and let the boom adjust to match the controller orientation
    FollowCamera->bUsePawnControlRotation = false; // Camera does not rotate relative to arm

    // Initialize stats
    Health = MaxHealth;
    Mana = MaxMana;
}

void AAuracronCharacter::BeginPlay()
{
    Super::BeginPlay();

    // Add Input Mapping Context
    if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
    {
        if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
        {
            if (DefaultMappingContext)
            {
                Subsystem->AddMappingContext(DefaultMappingContext, 0);
                UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Input mapping context added"));
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AuracronCharacter: DefaultMappingContext is null"));
            }
        }
    }

    // Set initial movement speed
    UpdateMovementSpeed();
}

void AAuracronCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);

    // Set up action bindings
    if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent))
    {
        // Moving
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &AAuracronCharacter::Move);
        }

        // Looking
        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &AAuracronCharacter::Look);
        }

        // Jumping
        if (JumpAction)
        {
            EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Started, this, &AAuracronCharacter::StartJump);
            EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Completed, this, &AAuracronCharacter::StopJump);
        }

        // Sprinting
        if (SprintAction)
        {
            EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Started, this, &AAuracronCharacter::StartSprint);
            EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Completed, this, &AAuracronCharacter::StopSprint);
        }

        // Combat
        if (PrimaryAttackAction)
        {
            EnhancedInputComponent->BindAction(PrimaryAttackAction, ETriggerEvent::Triggered, this, &AAuracronCharacter::PrimaryAttack);
        }

        if (SecondaryAttackAction)
        {
            EnhancedInputComponent->BindAction(SecondaryAttackAction, ETriggerEvent::Triggered, this, &AAuracronCharacter::SecondaryAttack);
        }

        // Abilities
        if (Ability1Action)
        {
            EnhancedInputComponent->BindAction(Ability1Action, ETriggerEvent::Triggered, this, &AAuracronCharacter::UseAbility1);
        }

        if (Ability2Action)
        {
            EnhancedInputComponent->BindAction(Ability2Action, ETriggerEvent::Triggered, this, &AAuracronCharacter::UseAbility2);
        }

        if (UltimateAction)
        {
            EnhancedInputComponent->BindAction(UltimateAction, ETriggerEvent::Triggered, this, &AAuracronCharacter::UseUltimate);
        }

        UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Input actions bound"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronCharacter: Failed to get EnhancedInputComponent"));
    }
}

void AAuracronCharacter::Move(const FInputActionValue& Value)
{
    // Input is a Vector2D
    FVector2D MovementVector = Value.Get<FVector2D>();

    if (Controller != nullptr)
    {
        // Find out which way is forward
        const FRotator Rotation = Controller->GetControlRotation();
        const FRotator YawRotation(0, Rotation.Yaw, 0);

        // Get forward direction
        const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);

        // Get right direction
        const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

        // Add movement
        AddMovementInput(ForwardDirection, MovementVector.Y);
        AddMovementInput(RightDirection, MovementVector.X);
    }
}

void AAuracronCharacter::Look(const FInputActionValue& Value)
{
    // Input is a Vector2D
    FVector2D LookAxisVector = Value.Get<FVector2D>();

    if (Controller != nullptr)
    {
        // Add yaw and pitch input to controller
        AddControllerYawInput(LookAxisVector.X);
        AddControllerPitchInput(LookAxisVector.Y);
    }
}

void AAuracronCharacter::StartJump()
{
    Jump();
}

void AAuracronCharacter::StopJump()
{
    StopJumping();
}

void AAuracronCharacter::StartSprint()
{
    bIsSprinting = true;
    UpdateMovementSpeed();
    UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Started sprinting"));
}

void AAuracronCharacter::StopSprint()
{
    bIsSprinting = false;
    UpdateMovementSpeed();
    UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Stopped sprinting"));
}

void AAuracronCharacter::PrimaryAttack()
{
    if (bIsAttacking)
    {
        return; // Already attacking
    }

    bIsAttacking = true;
    UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Primary attack"));

    // Reset attack state after a short delay
    FTimerHandle AttackTimerHandle;
    GetWorld()->GetTimerManager().SetTimer(
        AttackTimerHandle,
        [this]() { bIsAttacking = false; },
        1.0f,
        false
    );
}

void AAuracronCharacter::SecondaryAttack()
{
    UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Secondary attack"));
}

void AAuracronCharacter::UseAbility1()
{
    if (Mana >= 20.0f) // Cost 20 mana
    {
        Mana -= 20.0f;
        UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Used Ability 1"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronCharacter: Not enough mana for Ability 1"));
    }
}

void AAuracronCharacter::UseAbility2()
{
    if (Mana >= 30.0f) // Cost 30 mana
    {
        Mana -= 30.0f;
        UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Used Ability 2"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronCharacter: Not enough mana for Ability 2"));
    }
}

void AAuracronCharacter::UseUltimate()
{
    if (Mana >= 50.0f) // Cost 50 mana
    {
        Mana -= 50.0f;
        UE_LOG(LogTemp, Log, TEXT("AuracronCharacter: Used Ultimate"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronCharacter: Not enough mana for Ultimate"));
    }
}

void AAuracronCharacter::UpdateMovementSpeed()
{
    if (GetCharacterMovement())
    {
        float NewSpeed = bIsSprinting ? MovementSpeed * SprintSpeedMultiplier : MovementSpeed;
        GetCharacterMovement()->MaxWalkSpeed = NewSpeed;
    }
}
