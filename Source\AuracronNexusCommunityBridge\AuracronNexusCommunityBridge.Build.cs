using UnrealBuildTool;

public class AuracronNexusCommunityBridge : ModuleRules
{
    public AuracronNexusCommunityBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable latest C++ standard
        CppStandard = CppStandardVersion.Latest;
        bEnableExceptions = true;
        
        PublicIncludePaths.AddRange(
            new string[] {
                "AuracronNexusCommunityBridge/Public"
            }
        );
        
        PrivateIncludePaths.AddRange(
            new string[] {
                "AuracronNexusCommunityBridge/Private"
            }
        );
        
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "UMG",
                "Slate",
                "SlateCore",
                "OnlineSubsystem",
                "OnlineSubsystemUtils",
                "OnlineServicesInterface",
                "OnlineServicesCommon",
                "HTTP",
                "<PERSON>son",
                "JsonUtilities",
                "WebBrowser",
                "ApplicationCore",
                "InputCore",
                "RenderCore",
                "RHI",
                "Networking",
                "Sockets",
                "NetCore",
                "PacketHandler",
                "ReliabilityHandlerComponent",
                "Analytics",
                "AnalyticsET"
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "AuracronDynamicRealmBridge",
                "AuracronHarmonyEngineBridge",
                "Settings",
                "DeveloperSettings",
                "GameplayTags",
                "GameplayTasks",
                "AIModule",
                "NavigationSystem",
                "Niagara",
                "NiagaraCore",
                "NiagaraShader",
                "CinematicCamera",
                "LevelSequence",
                "MovieScene",
                "MovieSceneTracks",
                "TimeManagement",
                "MediaAssets",
                "Media",
                "MediaUtils",
                "AudioMixer",
                "AudioExtensions",
                "SignalProcessing",
                "SoundFieldRendering",
                "AudioCapture",
                "Voice",
                "VoiceChat",
                "AudioPlatformConfiguration",
                "Landscape",
                "Foliage",
                "Chaos",
                "ChaosCore",
                "ChaosSolverEngine",
                "GeometryCollectionEngine",
                "FieldSystemEngine",
                "PhysicsCore",
                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeCommon",
                "ClothingSystemRuntimeNv",
                "ApexDestruction",
                "ChaosCloth",
                "GeometryFramework",
                "DynamicMesh",
                "GeometryAlgorithms",
                "ModelingComponents",
                "InteractiveToolsFramework",
                "MeshConversion",
                "MeshUtilitiesCommon",
                "ProceduralMeshComponent",
                "StaticMeshDescription",
                "SkeletalMeshDescription",
                "MeshDescription",
                "RawMesh",
                "ImageWrapper",
                "RenderCore",
                "Renderer",
                "RHI",
                "D3D12RHI",
                "VulkanRHI",
                "OpenGLDrv",
                "NullDrv",
                "TextureUtilitiesCommon",
                "MaterialShaderQualitySettings",
                "Landscape",
                "HeadMountedDisplay",
                "XRBase",
                "AugmentedReality",
                "MRMesh",
                "OpenXR"
            }
        );
        
        // Platform specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "DirectSound",
                    "AudioMixerXAudio2"
                }
            );
        }
        
        if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AudioMixerAudioUnit",
                    "MetalRHI"
                }
            );
        }
        
        if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AudioMixerSDL"
                }
            );
        }
        
        // Editor-only dependencies
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers",
                    "ActorPickerMode",
                    "SceneDepthPickerMode",
                    "PlacementMode",
                    "IntroTutorials",
                    "AssetTools",
                    "ContentBrowser",
                    "ContentBrowserData",
                    "CollectionManager",
                    "AddContentDialog",
                    "GameProjectGeneration",
                    "HardwareTargeting",
                    "InternationalizationSettings",
                    "MultiUserClient",
                    "MeshUtilities",
                    "ModelingComponentsEditorOnly",
                    "EditorInteractiveToolsFramework",
                    "LogVisualizer",
                    "AutomationController",
                    "AutomationWorker",
                    "AutomationMessages",
                    "FunctionalTesting",
                    "ScreenShotComparison",
                    "AutomationWindow",
                    "CollisionAnalyzer",
                    "StatsViewer"
                }
            );
        }
        
        // Development tools dependencies
        if (Target.bBuildDeveloperTools)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "TraceLog",
                "SlateReflector",
                "MessageLog"
            });
        }
        
        // Optimization settings for production builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
    }
}
