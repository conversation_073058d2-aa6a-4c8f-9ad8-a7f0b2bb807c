/**
 * AuracronAdaptiveEngagementBridge.h
 * 
 * Adaptive engagement system that personalizes player experience through
 * intelligent gameplay adaptation, wellness monitoring, and dynamic content
 * delivery to maximize player satisfaction and well-being.
 * 
 * Features:
 * - Intelligent gameplay personalization
 * - Player wellness monitoring
 * - Adaptive difficulty scaling
 * - Dynamic content recommendation
 * - Engagement pattern analysis
 * - Burnout prevention system
 * 
 * Uses UE 5.6 modern engagement frameworks for production-ready
 * adaptive player experience.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "Engine/DataTable.h"
#include "AuracronAdaptiveEngagementBridgeAPI.h"
#include "AuracronAdaptiveEngagementBridge.generated.h"

// Forward declarations
class UHarmonyEngineSubsystem;
class UAuracronNexusCommunityBridge;
class UAuracronLivingWorldBridge;
class UAuracronAdvancedPerformanceAnalyzer;

/**
 * Player engagement states
 */
UENUM(BlueprintType)
enum class EPlayerEngagementState : uint8
{
    Disengaged      UMETA(DisplayName = "Disengaged"),
    LowEngagement   UMETA(DisplayName = "Low Engagement"),
    Moderate        UMETA(DisplayName = "Moderate"),
    HighEngagement  UMETA(DisplayName = "High Engagement"),
    FlowState       UMETA(DisplayName = "Flow State"),
    Overwhelmed     UMETA(DisplayName = "Overwhelmed"),
    Burnout         UMETA(DisplayName = "Burnout")
};

/**
 * Wellness indicators
 */
UENUM(BlueprintType)
enum class EWellnessIndicator : uint8
{
    Healthy         UMETA(DisplayName = "Healthy"),
    Tired           UMETA(DisplayName = "Tired"),
    Stressed        UMETA(DisplayName = "Stressed"),
    Frustrated      UMETA(DisplayName = "Frustrated"),
    Overwhelmed     UMETA(DisplayName = "Overwhelmed"),
    AtRisk          UMETA(DisplayName = "At Risk"),
    NeedsBreak      UMETA(DisplayName = "Needs Break")
};

/**
 * Adaptation strategies
 */
UENUM(BlueprintType)
enum class EAdaptationStrategy : uint8
{
    IncreaseChallenge   UMETA(DisplayName = "Increase Challenge"),
    ReduceChallenge     UMETA(DisplayName = "Reduce Challenge"),
    ChangeContent       UMETA(DisplayName = "Change Content"),
    SuggestBreak        UMETA(DisplayName = "Suggest Break"),
    SocialEngagement    UMETA(DisplayName = "Social Engagement"),
    CreativeActivity    UMETA(DisplayName = "Creative Activity"),
    RelaxationMode      UMETA(DisplayName = "Relaxation Mode"),
    SkillBuilding       UMETA(DisplayName = "Skill Building")
};

/**
 * Player engagement profile
 */
USTRUCT(BlueprintType)
struct AURACRONADAPTIVEENGAGEMENTBRIDGE_API FAuracronPlayerEngagementProfile
{
    GENERATED_BODY()

    /** Player ID */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    FString PlayerID;

    /** Current engagement state */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    EPlayerEngagementState EngagementState;

    /** Current wellness indicator */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    EWellnessIndicator WellnessIndicator;

    /** Engagement score */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float EngagementScore;

    /** Wellness score */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float WellnessScore;

    /** Play session duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float SessionDuration;

    /** Preferred content types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    TArray<FString> PreferredContentTypes;

    /** Optimal challenge level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float OptimalChallengeLevel;

    /** Social preference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float SocialPreference;

    /** Competitive preference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float CompetitivePreference;

    /** Creative preference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    float CreativePreference;

    /** Last update time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement Profile")
    FDateTime LastUpdateTime;

    FAuracronPlayerEngagementProfile()
    {
        PlayerID = TEXT("");
        EngagementState = EPlayerEngagementState::Moderate;
        WellnessIndicator = EWellnessIndicator::Healthy;
        EngagementScore = 0.5f;
        WellnessScore = 1.0f;
        SessionDuration = 0.0f;
        OptimalChallengeLevel = 0.5f;
        SocialPreference = 0.5f;
        CompetitivePreference = 0.5f;
        CreativePreference = 0.5f;
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Adaptive content recommendation
 */
USTRUCT(BlueprintType)
struct AURACRONADAPTIVEENGAGEMENTBRIDGE_API FAuracronContentRecommendation
{
    GENERATED_BODY()

    /** Recommendation ID */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    FString RecommendationID;

    /** Content type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    FString ContentType;

    /** Content description */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    FString ContentDescription;

    /** Recommendation confidence */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    float RecommendationConfidence;

    /** Expected engagement boost */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    float ExpectedEngagementBoost;

    /** Adaptation strategy */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    EAdaptationStrategy AdaptationStrategy;

    /** Recommendation tags */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    FGameplayTagContainer RecommendationTags;

    /** Creation time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendation")
    FDateTime CreationTime;

    FAuracronContentRecommendation()
    {
        RecommendationID = TEXT("");
        ContentType = TEXT("");
        ContentDescription = TEXT("");
        RecommendationConfidence = 0.5f;
        ExpectedEngagementBoost = 0.1f;
        AdaptationStrategy = EAdaptationStrategy::ChangeContent;
        CreationTime = FDateTime::Now();
    }
};

/**
 * Wrapper structure for TArray<FAuracronContentRecommendation> to be used as TMap value
 */
USTRUCT(BlueprintType)
struct AURACRONADAPTIVEENGAGEMENTBRIDGE_API FContentRecommendationArray
{
    GENERATED_BODY()

    /** Array of content recommendations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content Recommendations")
    TArray<FAuracronContentRecommendation> Recommendations;

    FContentRecommendationArray()
    {
        Recommendations.Empty();
    }
};

/**
 * Wellness monitoring data
 */
USTRUCT(BlueprintType)
struct AURACRONADAPTIVEENGAGEMENTBRIDGE_API FAuracronWellnessMonitoringData
{
    GENERATED_BODY()

    /** Player ID */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    FString PlayerID;

    /** Session start time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    FDateTime SessionStartTime;

    /** Total session time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    float TotalSessionTime;

    /** Break frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    float BreakFrequency;

    /** Stress indicators */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    TMap<FString, float> StressIndicators;

    /** Wellness recommendations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    TArray<FString> WellnessRecommendations;

    /** Last wellness check */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wellness Monitoring")
    FDateTime LastWellnessCheck;

    FAuracronWellnessMonitoringData()
    {
        PlayerID = TEXT("");
        SessionStartTime = FDateTime::Now();
        TotalSessionTime = 0.0f;
        BreakFrequency = 0.0f;
        LastWellnessCheck = FDateTime::Now();
    }
};

/**
 * Auracron Adaptive Engagement Bridge
 * 
 * Adaptive engagement system that personalizes player experience through
 * intelligent gameplay adaptation, wellness monitoring, and dynamic content
 * delivery to maximize player satisfaction and well-being.
 */
UCLASS(BlueprintType)
class AURACRONADAPTIVEENGAGEMENTBRIDGE_API UAuracronAdaptiveEngagementBridge : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // === Core Engagement Management ===
    
    /** Initialize adaptive engagement bridge */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Engagement")
    void InitializeAdaptiveEngagementBridge();

    /** Update engagement systems */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Engagement")
    void UpdateEngagementSystems(float DeltaTime);

    /** Get player engagement profile */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Adaptive Engagement")
    FAuracronPlayerEngagementProfile GetPlayerEngagementProfile(const FString& PlayerID) const;

    /** Update player engagement profile */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Engagement")
    void UpdatePlayerEngagementProfile(const FString& PlayerID);

    // === Personalization System ===
    
    /** Personalize gameplay for player */
    UFUNCTION(BlueprintCallable, Category = "Personalization")
    void PersonalizeGameplayForPlayer(const FString& PlayerID);

    /** Get content recommendations for player */
    UFUNCTION(BlueprintCallable, Category = "Personalization")
    TArray<FAuracronContentRecommendation> GetContentRecommendationsForPlayer(const FString& PlayerID);

    /** Apply adaptive difficulty for player */
    UFUNCTION(BlueprintCallable, Category = "Personalization")
    void ApplyAdaptiveDifficultyForPlayer(const FString& PlayerID, float DifficultyAdjustment);

    /** Customize UI for player preferences */
    UFUNCTION(BlueprintCallable, Category = "Personalization")
    void CustomizeUIForPlayerPreferences(const FString& PlayerID);

    // === Wellness Monitoring ===
    
    /** Monitor player wellness */
    UFUNCTION(BlueprintCallable, Category = "Wellness Monitoring")
    void MonitorPlayerWellness(const FString& PlayerID);

    /** Get player wellness data */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Wellness Monitoring")
    FAuracronWellnessMonitoringData GetPlayerWellnessData(const FString& PlayerID) const;

    /** Suggest wellness break */
    UFUNCTION(BlueprintCallable, Category = "Wellness Monitoring")
    void SuggestWellnessBreak(const FString& PlayerID, const FString& Reason);

    /** Check for burnout indicators */
    UFUNCTION(BlueprintCallable, Category = "Wellness Monitoring")
    bool CheckForBurnoutIndicators(const FString& PlayerID);

    // === Engagement Analytics ===
    
    /** Analyze player engagement patterns */
    UFUNCTION(BlueprintCallable, Category = "Engagement Analytics")
    TMap<FString, float> AnalyzePlayerEngagementPatterns(const FString& PlayerID);

    /** Predict player engagement trends */
    UFUNCTION(BlueprintCallable, Category = "Engagement Analytics")
    TArray<FString> PredictPlayerEngagementTrends(const FString& PlayerID);

    /** Get global engagement metrics */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Engagement Analytics")
    TMap<FString, float> GetGlobalEngagementMetrics() const;

    // === Adaptive Content Delivery ===
    
    /** Deliver adaptive content to player */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Content")
    void DeliverAdaptiveContentToPlayer(const FString& PlayerID, const FAuracronContentRecommendation& Recommendation);

    /** Generate dynamic challenges for player */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Content")
    TArray<FString> GenerateDynamicChallengesForPlayer(const FString& PlayerID);

    /** Adapt social features for player */
    UFUNCTION(BlueprintCallable, Category = "Adaptive Content")
    void AdaptSocialFeaturesForPlayer(const FString& PlayerID);

    // === Events ===
    
    /** Called when player engagement state changes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Engagement Events")
    void OnPlayerEngagementStateChanged(const FString& PlayerID, EPlayerEngagementState OldState, EPlayerEngagementState NewState);

    /** Called when wellness concern is detected */
    UFUNCTION(BlueprintImplementableEvent, Category = "Engagement Events")
    void OnWellnessConcernDetected(const FString& PlayerID, EWellnessIndicator WellnessIndicator);

    /** Called when adaptive content is recommended */
    UFUNCTION(BlueprintImplementableEvent, Category = "Engagement Events")
    void OnAdaptiveContentRecommended(const FString& PlayerID, const FAuracronContentRecommendation& Recommendation);

protected:
    // === Configuration ===
    
    /** Enable adaptive engagement bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bAdaptiveEngagementEnabled;

    /** Enable wellness monitoring */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableWellnessMonitoring;

    /** Enable adaptive difficulty */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableAdaptiveDifficulty;

    /** Enable content personalization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableContentPersonalization;

    /** Engagement update frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float EngagementUpdateFrequency;

    /** Wellness check frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float WellnessCheckFrequency;

    // === Player Profiles ===
    
    /** Player engagement profiles */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Profiles")
    TMap<FString, FAuracronPlayerEngagementProfile> PlayerEngagementProfiles;

    /** Player wellness data */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Profiles")
    TMap<FString, FAuracronWellnessMonitoringData> PlayerWellnessData;

    /** Player content recommendations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Profiles")
    TMap<FString, FContentRecommendationArray> PlayerContentRecommendations;

    /** Global engagement metrics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Profiles")
    TMap<FString, float> GlobalEngagementMetrics;

private:
    // === Core Implementation ===
    void InitializeEngagementSubsystems();
    void SetupEngagementPipeline();
    void StartEngagementMonitoring();
    void ProcessEngagementUpdates();
    void AnalyzeEngagementHealth();
    void OptimizeEngagementExperience();
    
    // === Personalization Implementation ===
    void InitializePersonalizationSystem();
    void ProcessPersonalizationUpdates();
    void AnalyzePlayerPreferences();
    void GeneratePersonalizedContent();
    void ApplyPersonalizationSettings();
    
    // === Wellness Implementation ===
    void InitializeWellnessMonitoring();
    void ProcessWellnessChecks();
    void AnalyzeWellnessIndicators();
    void GenerateWellnessRecommendations();
    void ApplyWellnessInterventions();
    
    // === Engagement Analytics ===
    void AnalyzeEngagementPatterns();
    void TrackEngagementTrends();
    void PredictEngagementChanges();
    void GenerateEngagementInsights();
    void OptimizeEngagementStrategies();
    
    // === Adaptive Content Implementation ===
    void InitializeAdaptiveContentSystem();
    void ProcessContentRecommendations();
    void GenerateDynamicContent();
    void DeliverPersonalizedExperiences();
    void AnalyzeContentEffectiveness();
    
    // === Difficulty Adaptation ===
    void InitializeAdaptiveDifficulty();
    void ProcessDifficultyAdjustments();
    void AnalyzePlayerSkillProgression();
    void CalculateOptimalDifficulty();
    void ApplyDifficultyModifications();
    
    // === Utility Methods ===
    float CalculateEngagementScore(const FString& PlayerID);
    float CalculateWellnessScore(const FString& PlayerID);
    EPlayerEngagementState DetermineEngagementState(float EngagementScore, float WellnessScore);
    EWellnessIndicator DetermineWellnessIndicator(const FAuracronWellnessMonitoringData& WellnessData);
    EAdaptationStrategy DetermineOptimalAdaptationStrategy(const FAuracronPlayerEngagementProfile& Profile);
    bool ShouldTriggerWellnessIntervention(const FAuracronWellnessMonitoringData& WellnessData);
    void LogEngagementMetrics();
    void SaveEngagementData();
    void LoadEngagementData();

    // === Additional Utility Methods ===
    void AnalyzePlayerPreferencesForProfile(const FString& PlayerID, FAuracronPlayerEngagementProfile& Profile);
    void ApplyAdaptiveStrategiesForState(const FString& PlayerID, EPlayerEngagementState NewState);
    void PersonalizeContentForPlayer(const FString& PlayerID, const FAuracronPlayerEngagementProfile& Profile);
    void PersonalizeSocialFeaturesForPlayer(const FString& PlayerID, const FAuracronPlayerEngagementProfile& Profile);
    TArray<FAuracronContentRecommendation> GenerateContentRecommendationsForPlayer(const FString& PlayerID);
    APlayerController* FindPlayerControllerByID(const FString& PlayerID);
    void ApplyDifficultyModificationsToPlayer(APlayerController* PlayerController, float DifficultyAdjustment);
    void ApplyUICustomizationsForPlayer(APlayerController* PlayerController, const FAuracronPlayerEngagementProfile& Profile);
    void AnalyzeStressIndicators(const FString& PlayerID, FAuracronWellnessMonitoringData& WellnessData);
    void TriggerWellnessIntervention(const FString& PlayerID, EWellnessIndicator WellnessIndicator);
    void GenerateWellnessRecommendationsForPlayer(const FString& PlayerID, FAuracronWellnessMonitoringData& WellnessData);
    FString GenerateWellnessBreakMessage(const FString& PlayerID, const FString& Reason);
    void DisplayWellnessBreakSuggestion(APlayerController* PlayerController, const FString& Message);
    float CalculateEngagementConsistency(const FString& PlayerID);
    TArray<float> CalculatePeakEngagementTimes(const FString& PlayerID);
    float CalculateContentPreferenceStrength(const FString& PlayerID);
    float CalculateChallengeSeeking(const FString& PlayerID);
    float CalculateSessionLengthPreference(const FString& PlayerID);
    void DeliverChallengeContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void DeliverRelaxedContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void DeliverSocialContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void DeliverCreativeContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void DeliverRelaxationContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void DeliverGeneralContent(APlayerController* PlayerController, const FAuracronContentRecommendation& Recommendation);
    void TrackContentEffectiveness(const FString& PlayerID, const FAuracronContentRecommendation& Recommendation);
    void EnableEnhancedSocialFeatures(APlayerController* PlayerController);
    void RecommendSocialActivities(const FString& PlayerID);
    void MinimizeSocialInterruptions(APlayerController* PlayerController);
    void FocusOnSoloContent(const FString& PlayerID);
    void ApplyBalancedSocialFeatures(APlayerController* PlayerController);

    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UHarmonyEngineSubsystem> CachedHarmonyEngine;

    UPROPERTY()
    TObjectPtr<UAuracronNexusCommunityBridge> CachedCommunityBridge;

    UPROPERTY()
    TObjectPtr<UAuracronLivingWorldBridge> CachedLivingWorldBridge;

    UPROPERTY()
    TObjectPtr<UAuracronAdvancedPerformanceAnalyzer> CachedPerformanceAnalyzer;

    // === Engagement Analytics ===
    TMap<FString, TArray<float>> EngagementMetricHistory;
    TMap<FString, float> EngagementTrendPredictions;
    TArray<FString> EngagementInsights;
    TMap<EAdaptationStrategy, float> StrategyEffectiveness;
    
    // === Content Analytics ===
    TMap<FString, float> ContentTypePopularity;
    TMap<FString, float> ContentEffectivenessScores;
    TArray<FString> TrendingContentTypes;
    
    // === Wellness Analytics ===
    TMap<EWellnessIndicator, int32> WellnessIndicatorFrequency;
    TArray<FString> WellnessInterventionHistory;
    TMap<FString, float> WellnessImprovementScores;
    
    // === Timers ===
    FTimerHandle EngagementUpdateTimer;
    FTimerHandle WellnessCheckTimer;
    FTimerHandle PersonalizationUpdateTimer;
    FTimerHandle ContentRecommendationTimer;
    
    // === State Tracking ===
    bool bIsInitialized;
    float LastEngagementUpdate;
    float LastWellnessCheck;
    float LastPersonalizationUpdate;
    int32 TotalAdaptationsApplied;
    int32 TotalWellnessInterventions;
};
