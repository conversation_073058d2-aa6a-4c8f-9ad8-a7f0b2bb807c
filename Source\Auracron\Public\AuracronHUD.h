#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "AuracronHUD.generated.h"

// Forward declarations
class UUserWidget;
class <PERSON>uracronCharacter;

UCLASS()
class AURACRON_API AAuracronHUD : public AHUD
{
    GENERATED_BODY()

public:
    AAuracronHUD();

protected:
    virtual void BeginPlay() override;
    virtual void DrawHUD() override;

    // === Widget Classes ===
    
    /** Main HUD Widget Class */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "UI")
    TSubclassOf<UUserWidget> MainHUDWidgetClass;

    /** Main HUD Widget Instance */
    UPROPERTY(BlueprintReadOnly, Category = "UI")
    TObjectPtr<UUserWidget> MainHUDWidget;

    // === HUD Elements ===
    
    /** Show debug information */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowAuracronDebugInfo = true;

    /** Show player stats */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    bool bShowPlayerStats = true;

    /** Show minimap */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    bool bShowMinimap = true;

    /** Show ability cooldowns */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    bool bShowAbilityCooldowns = true;

public:
    // === HUD Functions ===
    
    /** Create and display main HUD */
    UFUNCTION(BlueprintCallable, Category = "UI")
    void CreateMainHUD();

    /** Remove main HUD */
    UFUNCTION(BlueprintCallable, Category = "UI")
    void RemoveMainHUD();

    /** Toggle debug information */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void ToggleDebugInfo();

    /** Update player stats display */
    UFUNCTION(BlueprintCallable, Category = "UI")
    void UpdatePlayerStats();

    /** Get main HUD widget */
    UFUNCTION(BlueprintPure, Category = "UI")
    UUserWidget* GetMainHUDWidget() const { return MainHUDWidget; }

protected:
    /** Draw debug information */
    void DrawDebugInfo();

    /** Draw player stats */
    void DrawPlayerStats();

    /** Draw crosshair */
    void DrawCrosshair();

    /** Get player character */
    AAuracronCharacter* GetPlayerCharacter() const;

private:
    /** Cached player character reference */
    UPROPERTY()
    TObjectPtr<AAuracronCharacter> CachedPlayerCharacter;

    /** Last time stats were updated */
    float LastStatsUpdateTime = 0.0f;

    /** Stats update interval */
    float StatsUpdateInterval = 0.1f; // Update 10 times per second
};
