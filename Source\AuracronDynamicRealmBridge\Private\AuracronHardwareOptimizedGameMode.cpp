#include "AuracronHardwareOptimizedGameMode.h"
#include "Engine/Engine.h"
#include "TimerManager.h"

AAuracronHardwareOptimizedGameMode::AAuracronHardwareOptimizedGameMode()
{
    // Criar componente de detecção de hardware
    HardwareDetectionComponent = CreateDefaultSubobject<UAuracronHardwareDetectionComponent>(TEXT("HardwareDetectionComponent"));
    
    // Configurações padrão
    bShowSettingsUIAfterDetection = false;
    DetectionDelay = 2.0f;
    
    // Configurar componente
    if (HardwareDetectionComponent)
    {
        HardwareDetectionComponent->bAutoDetectOnBeginPlay = false; // Vamos controlar manualmente
        HardwareDetectionComponent->bAutoApplySettings = true;
        HardwareDetectionComponent->bVerboseLogging = true;
    }
}

void AAuracronHardwareOptimizedGameMode::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar perfis personalizados
    InitializeDefaultCustomProfiles();
    
    // Conectar eventos
    if (HardwareDetectionComponent)
    {
        HardwareDetectionComponent->OnHardwareDetected.AddDynamic(this, &AAuracronHardwareOptimizedGameMode::OnHardwareDetected);
        HardwareDetectionComponent->OnQualitySettingsApplied.AddDynamic(this, &AAuracronHardwareOptimizedGameMode::OnQualitySettingsApplied);
    }
    
    // Executar detecção após delay
    if (DetectionDelay > 0.0f)
    {
        GetWorldTimerManager().SetTimer(DetectionTimerHandle, this, &AAuracronHardwareOptimizedGameMode::ExecuteDelayedDetection, DetectionDelay, false);
    }
    else
    {
        ExecuteDelayedDetection();
    }
}

void AAuracronHardwareOptimizedGameMode::ExecuteDelayedDetection()
{
    if (HardwareDetectionComponent)
    {
        UE_LOG(LogTemp, Log, TEXT("Starting hardware detection from GameMode..."));
        HardwareDetectionComponent->RunFullHardwareDetection();
    }
}

void AAuracronHardwareOptimizedGameMode::OnHardwareDetected(const FSimpleHardwareInfo& HardwareInfo)
{
    UE_LOG(LogTemp, Log, TEXT("GameMode: Hardware detected - GPU: %s, VRAM: %dMB"), *HardwareInfo.GPUName, HardwareInfo.VideoMemoryMB);
    
    // Verificar se existe perfil personalizado para esta GPU
    if (HasCustomProfileForGPU(HardwareInfo.GPUName))
    {
        UE_LOG(LogTemp, Log, TEXT("Found custom profile for GPU: %s"), *HardwareInfo.GPUName);
        ApplyCustomProfileForGPU(HardwareInfo.GPUName);
    }
    
    // Chamar evento Blueprint
    OnHardwareDetectedBP(HardwareInfo);
}

void AAuracronHardwareOptimizedGameMode::OnQualitySettingsApplied(const FSimpleQualitySettings& AppliedSettings)
{
    UE_LOG(LogTemp, Log, TEXT("GameMode: Quality settings applied - Overall Quality: %d, Resolution Scale: %.2f"), 
           AppliedSettings.OverallQuality, AppliedSettings.ResolutionScale);
    
    // Mostrar UI de configurações se solicitado
    if (bShowSettingsUIAfterDetection)
    {
        ShowSettingsUI();
    }
    
    // Chamar evento Blueprint
    OnQualitySettingsAppliedBP(AppliedSettings);
}

bool AAuracronHardwareOptimizedGameMode::HasCustomProfileForGPU(const FString& GPUName)
{
    // Verificar correspondência exata
    if (CustomHardwareProfiles.Contains(GPUName))
    {
        return true;
    }
    
    // Verificar correspondência parcial (para diferentes versões da mesma GPU)
    for (const auto& Profile : CustomHardwareProfiles)
    {
        if (GPUName.Contains(Profile.Key) || Profile.Key.Contains(GPUName))
        {
            return true;
        }
    }
    
    return false;
}

bool AAuracronHardwareOptimizedGameMode::ApplyCustomProfileForGPU(const FString& GPUName)
{
    // Tentar correspondência exata primeiro
    if (CustomHardwareProfiles.Contains(GPUName))
    {
        if (HardwareDetectionComponent)
        {
            HardwareDetectionComponent->ApplyQualitySettings(CustomHardwareProfiles[GPUName]);
            UE_LOG(LogTemp, Log, TEXT("Applied custom profile for GPU: %s"), *GPUName);
            return true;
        }
    }
    
    // Tentar correspondência parcial
    for (const auto& Profile : CustomHardwareProfiles)
    {
        if (GPUName.Contains(Profile.Key) || Profile.Key.Contains(GPUName))
        {
            if (HardwareDetectionComponent)
            {
                HardwareDetectionComponent->ApplyQualitySettings(Profile.Value);
                UE_LOG(LogTemp, Log, TEXT("Applied custom profile '%s' for GPU: %s"), *Profile.Key, *GPUName);
                return true;
            }
        }
    }
    
    return false;
}

void AAuracronHardwareOptimizedGameMode::CreateCustomProfileForGPU(const FString& GPUName, const FSimpleQualitySettings& Settings)
{
    CustomHardwareProfiles.Add(GPUName, Settings);
    UE_LOG(LogTemp, Log, TEXT("Created custom profile for GPU: %s"), *GPUName);
}

void AAuracronHardwareOptimizedGameMode::InitializeDefaultCustomProfiles()
{
    // Perfil para RTX 4090 - Configurações Ultra
    FSimpleQualitySettings RTX4090Settings;
    RTX4090Settings.OverallQuality = 4;
    RTX4090Settings.TextureQuality = 4;
    RTX4090Settings.ShadowQuality = 4;
    RTX4090Settings.PostProcessQuality = 4;
    RTX4090Settings.AntiAliasingQuality = 4;
    RTX4090Settings.ViewDistanceQuality = 4;
    RTX4090Settings.FoliageQuality = 4;
    RTX4090Settings.ShadingQuality = 4;
    RTX4090Settings.ResolutionScale = 1.0f;
    RTX4090Settings.TargetFPS = 60;
    RTX4090Settings.bEnableLumen = true;
    RTX4090Settings.bEnableNanite = true;
    RTX4090Settings.bEnableRayTracing = true;
    CustomHardwareProfiles.Add(TEXT("RTX 4090"), RTX4090Settings);
    
    // Perfil para RTX 4080 - Configurações High
    FSimpleQualitySettings RTX4080Settings;
    RTX4080Settings.OverallQuality = 3;
    RTX4080Settings.TextureQuality = 4;
    RTX4080Settings.ShadowQuality = 3;
    RTX4080Settings.PostProcessQuality = 3;
    RTX4080Settings.AntiAliasingQuality = 3;
    RTX4080Settings.ViewDistanceQuality = 3;
    RTX4080Settings.FoliageQuality = 3;
    RTX4080Settings.ShadingQuality = 3;
    RTX4080Settings.ResolutionScale = 0.9f;
    RTX4080Settings.TargetFPS = 60;
    RTX4080Settings.bEnableLumen = true;
    RTX4080Settings.bEnableNanite = true;
    RTX4080Settings.bEnableRayTracing = true;
    CustomHardwareProfiles.Add(TEXT("RTX 4080"), RTX4080Settings);
    
    // Perfil para RTX 3070 - Configurações Medium-High
    FSimpleQualitySettings RTX3070Settings;
    RTX3070Settings.OverallQuality = 2;
    RTX3070Settings.TextureQuality = 3;
    RTX3070Settings.ShadowQuality = 2;
    RTX3070Settings.PostProcessQuality = 2;
    RTX3070Settings.AntiAliasingQuality = 2;
    RTX3070Settings.ViewDistanceQuality = 2;
    RTX3070Settings.FoliageQuality = 2;
    RTX3070Settings.ShadingQuality = 2;
    RTX3070Settings.ResolutionScale = 0.8f;
    RTX3070Settings.TargetFPS = 60;
    RTX3070Settings.bEnableLumen = true;
    RTX3070Settings.bEnableNanite = true;
    RTX3070Settings.bEnableRayTracing = false;
    CustomHardwareProfiles.Add(TEXT("RTX 3070"), RTX3070Settings);
    
    // Perfil para GTX 1660 - Configurações Low-Medium
    FSimpleQualitySettings GTX1660Settings;
    GTX1660Settings.OverallQuality = 1;
    GTX1660Settings.TextureQuality = 2;
    GTX1660Settings.ShadowQuality = 1;
    GTX1660Settings.PostProcessQuality = 1;
    GTX1660Settings.AntiAliasingQuality = 1;
    GTX1660Settings.ViewDistanceQuality = 1;
    GTX1660Settings.FoliageQuality = 1;
    GTX1660Settings.ShadingQuality = 1;
    GTX1660Settings.ResolutionScale = 0.7f;
    GTX1660Settings.TargetFPS = 30;
    GTX1660Settings.bEnableLumen = false;
    GTX1660Settings.bEnableNanite = false;
    GTX1660Settings.bEnableRayTracing = false;
    CustomHardwareProfiles.Add(TEXT("GTX 1660"), GTX1660Settings);
    
    UE_LOG(LogTemp, Log, TEXT("Initialized %d custom hardware profiles"), CustomHardwareProfiles.Num());
}
