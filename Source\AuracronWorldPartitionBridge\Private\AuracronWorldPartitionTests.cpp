// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Testing Suite Implementation
// Bridge 3.14: World Partition - Testing Suite

#include "AuracronWorldPartitionTests.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionStreaming.h"
#include "AuracronWorldPartitionPerformance.h"
#include "AuracronWorldPartitionDebug.h"
#include "AuracronWorldPartitionBridge.h"

// Testing framework includes
#include "Tests/AutomationCommon.h"
#include "Engine/Engine.h"
#include "Misc/AutomationTest.h"
// #include "Tests/AutomationEditorCommon.h" // Not available in runtime builds

// Performance testing includes
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "HAL/PlatformMemory.h"
#include "HAL/ThreadHeartBeat.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// Async execution includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// =============================================================================
// WORLD PARTITION TEST MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionTestManager* UAuracronWorldPartitionTestManager::Instance = nullptr;

UAuracronWorldPartitionTestManager* UAuracronWorldPartitionTestManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionTestManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionTestManager::Initialize(const FAuracronTestConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Test Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize test state
    bTestsRunning = false;
    CurrentRunningTests = 0;
    TestHistory.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Test Manager initialized with execution mode: %s"), 
                         *UEnum::GetValueAsString(Configuration.ExecutionMode));
}

void UAuracronWorldPartitionTestManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel any running tests
    CancelRunningTests();
    
    // Clear test history
    TestHistory.Empty();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Test Manager shutdown completed"));
}

bool UAuracronWorldPartitionTestManager::IsInitialized() const
{
    return bIsInitialized;
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunAllTests()
{
    if (!bIsInitialized)
    {
        FAuracronTestSuiteResult EmptyResult;
        EmptyResult.SuiteName = TEXT("All Tests");
        EmptyResult.FailedTests = 1;
        EmptyResult.TestResults.Add(FAuracronTestResult());
        EmptyResult.TestResults[0].TestName = TEXT("Initialization");
        EmptyResult.TestResults[0].bPassed = false;
        EmptyResult.TestResults[0].ErrorMessage = TEXT("Test Manager not initialized");
        return EmptyResult;
    }

    FScopeLock Lock(&TestLock);
    
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("All World Partition Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();
    
    FDateTime StartTime = FDateTime::Now();
    
    // Run all test categories
    if (Configuration.bEnableUnitTests)
    {
        FAuracronTestSuiteResult UnitResults = RunUnitTests();
        SuiteResult.TestResults.Append(UnitResults.TestResults);
    }
    
    if (Configuration.bEnableIntegrationTests)
    {
        FAuracronTestSuiteResult IntegrationResults = RunIntegrationTests();
        SuiteResult.TestResults.Append(IntegrationResults.TestResults);
    }
    
    if (Configuration.bEnablePerformanceTests)
    {
        FAuracronTestSuiteResult PerformanceResults = RunPerformanceTests();
        SuiteResult.TestResults.Append(PerformanceResults.TestResults);
    }
    
    if (Configuration.bEnableStreamingTests)
    {
        FAuracronTestSuiteResult StreamingResults = RunStreamingTests();
        SuiteResult.TestResults.Append(StreamingResults.TestResults);
    }
    
    if (Configuration.bEnableLargeWorldTests)
    {
        FAuracronTestSuiteResult LargeWorldResults = RunLargeWorldTests();
        SuiteResult.TestResults.Append(LargeWorldResults.TestResults);
    }
    
    if (Configuration.bEnableStressTests)
    {
        FAuracronTestSuiteResult StressResults = RunStressTests();
        SuiteResult.TestResults.Append(StressResults.TestResults);
    }
    
    // Calculate suite statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());
    
    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }
        
        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }
    
    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }
    
    // Add to history
    TestHistory.Add(SuiteResult);
    
    // Generate report if enabled
    if (Configuration.bGenerateDetailedReports)
    {
        FString ReportPath = FPaths::Combine(Configuration.TestOutputDirectory, 
                                           FString::Printf(TEXT("TestReport_%s.json"), 
                                           *FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S"))));
        GenerateTestReport(SuiteResult, ReportPath);
    }
    
    OnTestSuiteCompleted.Broadcast(SuiteResult.SuiteName, SuiteResult);
    
    AURACRON_WP_LOG_INFO(TEXT("All tests completed: %d/%d passed in %.2fms"), 
                         SuiteResult.PassedTests, SuiteResult.TotalTests, SuiteResult.TotalExecutionTime);
    
    return SuiteResult;
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunTestCategory(EAuracronTestCategory Category)
{
    switch (Category)
    {
        case EAuracronTestCategory::Unit:
            return RunUnitTests();
        case EAuracronTestCategory::Integration:
            return RunIntegrationTests();
        case EAuracronTestCategory::Performance:
            return RunPerformanceTests();
        case EAuracronTestCategory::Streaming:
            return RunStreamingTests();
        case EAuracronTestCategory::LargeWorld:
            return RunLargeWorldTests();
        case EAuracronTestCategory::Stress:
            return RunStressTests();
        default:
            FAuracronTestSuiteResult EmptyResult;
            EmptyResult.SuiteName = UEnum::GetValueAsString(Category);
            return EmptyResult;
    }
}

FAuracronTestResult UAuracronWorldPartitionTestManager::RunSingleTest(const FString& TestName)
{
    if (!bIsInitialized)
    {
        FAuracronTestResult Result;
        Result.TestName = TestName;
        Result.bPassed = false;
        Result.ErrorMessage = TEXT("Test Manager not initialized");
        return Result;
    }

    // Route to appropriate test function based on test name
    if (TestName == TEXT("GridSystemCreation"))
    {
        return TestGridSystemCreation();
    }
    else if (TestName == TEXT("CellManagement"))
    {
        return TestCellManagement();
    }
    else if (TestName == TEXT("StreamingSystem"))
    {
        return TestStreamingSystem();
    }
    else if (TestName == TEXT("PerformanceMonitoring"))
    {
        return TestPerformanceMonitoring();
    }
    else if (TestName == TEXT("DebugSystem"))
    {
        return TestDebugSystem();
    }
    else
    {
        FAuracronTestResult Result;
        Result.TestName = TestName;
        Result.bPassed = false;
        Result.ErrorMessage = FString::Printf(TEXT("Unknown test: %s"), *TestName);
        return Result;
    }
}

void UAuracronWorldPartitionTestManager::RunTestsAsync(EAuracronTestCategory Category)
{
    if (bTestsRunning)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Tests already running"));
        return;
    }

    bTestsRunning = true;
    
    // Execute tests asynchronously
    Async(EAsyncExecution::Thread, [this, Category]()
    {
        FAuracronTestSuiteResult Result = RunTestCategory(Category);
        
        // Broadcast result on game thread
        AsyncTask(ENamedThreads::GameThread, [this, Result]()
        {
            bTestsRunning = false;
            OnTestSuiteCompleted.Broadcast(Result.SuiteName, Result);
        });
    });
}

bool UAuracronWorldPartitionTestManager::IsTestRunning() const
{
    return bTestsRunning;
}

void UAuracronWorldPartitionTestManager::CancelRunningTests()
{
    if (bTestsRunning)
    {
        bTestsRunning = false;
        CurrentRunningTests = 0;
        
        AURACRON_WP_LOG_INFO(TEXT("Running tests cancelled"));
    }
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunUnitTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Unit Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();
    
    FDateTime StartTime = FDateTime::Now();
    
    // Run individual unit tests
    SuiteResult.TestResults.Add(TestGridSystemCreation());
    SuiteResult.TestResults.Add(TestCellManagement());
    SuiteResult.TestResults.Add(TestStreamingSystem());
    SuiteResult.TestResults.Add(TestPerformanceMonitoring());
    SuiteResult.TestResults.Add(TestDebugSystem());
    
    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());
    
    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }
        
        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }
    
    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }
    
    AURACRON_WP_LOG_INFO(TEXT("Unit tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);
    
    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestGridSystemCreation()
{
    return ExecuteTest(TEXT("GridSystemCreation"), [this]() -> bool
    {
        // Test grid system creation and basic functionality
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager)
        {
            return false;
        }

        // Test initialization
        FAuracronGridConfiguration GridConfig;
        GridConfig.CellSize = 1000;
        GridConfig.GridBounds = FBox(FVector(-5000.0f), FVector(5000.0f));
        GridConfig.bEnableStreaming = true;

        GridManager->Initialize(GridConfig);
        if (!GridManager->IsInitialized())
        {
            return false;
        }

        // Test cell creation
        FVector TestLocation = FVector(500.0f, 500.0f, 0.0f);
        FString CellId = GridManager->GetCellIdFromLocation(TestLocation);
        if (CellId.IsEmpty())
        {
            return false;
        }

        // Test cell bounds calculation
        FBox CellBounds = GridManager->GetCellBounds(CellId);
        if (!CellBounds.IsValid)
        {
            return false;
        }

        // Test neighbor finding
        TArray<FString> Neighbors = GridManager->GetNeighborCells(CellId);
        if (Neighbors.Num() == 0)
        {
            return false;
        }

        return true;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestCellManagement()
{
    return ExecuteTest(TEXT("CellManagement"), [this]() -> bool
    {
        // Test cell management functionality
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager || !GridManager->IsInitialized())
        {
            return false;
        }

        // Test cell creation
        FString TestCellId = TEXT("TestCell_001");
        FAuracronGridCell TestCell;
        TestCell.CellId = TestCellId;
        TestCell.CellBounds = FBox(FVector(0.0f), FVector(1000.0f));
        TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Unloaded);

        bool bCellAdded = GridManager->AddCell(TestCell);
        if (!bCellAdded)
        {
            return false;
        }

        // Test cell retrieval
        FAuracronGridCell RetrievedCell = GridManager->GetCell(TestCellId);
        if (RetrievedCell.CellId != TestCellId)
        {
            return false;
        }

        // Test cell update
        TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Loaded);
        GridManager->UpdateCell(TestCell.CellId);
        
        // Verify cell was updated by checking if it exists
        FAuracronGridCell UpdatedCell = GridManager->GetCell(TestCellId);
        if (UpdatedCell.CellId.IsEmpty())
        {
            return false;
        }

        // Test cell removal
        bool bCellRemoved = GridManager->RemoveCell(TestCellId);
        if (!bCellRemoved)
        {
            return false;
        }

        return true;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestStreamingSystem()
{
    return ExecuteTest(TEXT("StreamingSystem"), [this]() -> bool
    {
        // Test streaming system functionality
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager)
        {
            return false;
        }

        // Test initialization
        FAuracronStreamingConfiguration StreamingConfig;
        StreamingConfig.bEnableStreaming = true;
        StreamingConfig.StreamingDistance = 5000.0f;
        StreamingConfig.MaxConcurrentLoads = 4;

        StreamingManager->Initialize(StreamingConfig);
        if (!StreamingManager->IsInitialized())
        {
            return false;
        }

        // Test streaming request
        FString TestCellId = TEXT("StreamingTestCell_001");
        bool bRequestSubmitted = StreamingManager->RequestCellLoad(TestCellId, 1.0f);
        if (!bRequestSubmitted)
        {
            return false;
        }

        // Test streaming state query
        EAuracronCellStreamingState StreamingState = StreamingManager->GetCellStreamingState(TestCellId);
        if (StreamingState == EAuracronCellStreamingState::Failed)
        {
            return false;
        }

        // Test streaming cancellation
        bool bRequestCancelled = StreamingManager->CancelCellLoad(TestCellId);
        if (!bRequestCancelled)
        {
            return false;
        }

        return true;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestPerformanceMonitoring()
{
    return ExecuteTest(TEXT("PerformanceMonitoring"), [this]() -> bool
    {
        // Test performance monitoring functionality
        UAuracronWorldPartitionPerformanceManager* PerfManager = UAuracronWorldPartitionPerformanceManager::GetInstance();
        if (!PerfManager)
        {
            return false;
        }

        // Test initialization
        FAuracronPerformanceConfiguration PerfConfig;
        PerfConfig.bEnablePerformanceMonitoring = true;
        PerfConfig.MonitoringUpdateInterval = 1.0f;
        PerfConfig.bEnableMemoryTracking = true;

        PerfManager->Initialize(PerfConfig);
        if (!PerfManager->IsInitialized())
        {
            return false;
        }

        // Test monitoring start/stop
        PerfManager->StartMonitoring();
        EAuracronPerformanceMonitoringState MonitoringState = PerfManager->GetMonitoringState();
        if (MonitoringState != EAuracronPerformanceMonitoringState::Enabled)
        {
            return false;
        }

        // Test metrics collection
        PerfManager->CollectMetrics();
        float MemoryUsage = PerfManager->GetCurrentMemoryUsageMB();
        if (MemoryUsage < 0.0f)
        {
            return false;
        }

        // Test performance score calculation
        float PerformanceScore = PerfManager->CalculatePerformanceScore();
        if (PerformanceScore < 0.0f || PerformanceScore > 100.0f)
        {
            return false;
        }

        PerfManager->StopMonitoring();

        return true;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestDebugSystem()
{
    return ExecuteTest(TEXT("DebugSystem"), [this]() -> bool
    {
        // Test debug system functionality
        UAuracronWorldPartitionDebugManager* DebugManager = UAuracronWorldPartitionDebugManager::GetInstance();
        if (!DebugManager)
        {
            return false;
        }

        // Test initialization
        FAuracronDebugConfiguration DebugConfig;
        DebugConfig.bEnableDebugVisualization = true;
        DebugConfig.bEnableStreamingDebug = true;
        DebugConfig.VisualizationMode = EAuracronDebugVisualizationMode::All;

        DebugManager->Initialize(DebugConfig);
        if (!DebugManager->IsInitialized())
        {
            return false;
        }

        // Test debug visualization
        DebugManager->EnableDebugVisualization(true);
        bool bVisualizationEnabled = DebugManager->IsDebugVisualizationEnabled();
        if (!bVisualizationEnabled)
        {
            return false;
        }

        // Test streaming debug
        DebugManager->EnableStreamingDebug(true);
        bool bStreamingDebugEnabled = DebugManager->IsStreamingDebugEnabled();
        if (!bStreamingDebugEnabled)
        {
            return false;
        }

        // Test debug commands
        TArray<FString> AvailableCommands = DebugManager->GetAvailableDebugCommands();
        if (AvailableCommands.Num() == 0)
        {
            return false;
        }

        // Test command execution
        DebugManager->ExecuteDebugCommand(TEXT("wp.debug.enable"));

        return true;
    });
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunIntegrationTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Integration Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();

    // Run integration tests
    SuiteResult.TestResults.Add(TestWorldPartitionIntegration());
    SuiteResult.TestResults.Add(TestPCGIntegration());
    SuiteResult.TestResults.Add(TestLumenIntegration());

    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }

        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }

    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }

    AURACRON_WP_LOG_INFO(TEXT("Integration tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);

    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestWorldPartitionIntegration()
{
    return ExecuteTest(TEXT("WorldPartitionIntegration"), [this]() -> bool
    {
        // Test integration between all World Partition components
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        UAuracronWorldPartitionPerformanceManager* PerfManager = UAuracronWorldPartitionPerformanceManager::GetInstance();

        if (!GridManager || !StreamingManager || !PerfManager)
        {
            return false;
        }

        // Test that all managers are properly initialized
        if (!GridManager->IsInitialized() || !StreamingManager->IsInitialized() || !PerfManager->IsInitialized())
        {
            return false;
        }

        // Test integration workflow: create cell, stream it, monitor performance
        FString TestCellId = TEXT("IntegrationTestCell_001");
        FAuracronGridCell TestCell;
        TestCell.CellId = TestCellId;
        TestCell.CellBounds = FBox(FVector(0.0f), FVector(1000.0f));
        TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Unloaded);

        // Add cell to grid
        if (!GridManager->AddCell(TestCell))
        {
            return false;
        }

        // Request streaming
        if (!StreamingManager->RequestCellLoad(TestCellId, 1.0f))
        {
            return false;
        }

        // Monitor performance during operation
        PerfManager->StartMonitoring();
        PerfManager->CollectMetrics();
        float PerformanceScore = PerfManager->CalculatePerformanceScore();
        PerfManager->StopMonitoring();

        // Cleanup
        StreamingManager->CancelCellLoad(TestCellId);
        GridManager->RemoveCell(TestCellId);

        return PerformanceScore >= 0.0f;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestPCGIntegration()
{
    return ExecuteTest(TEXT("PCGIntegration"), [this]() -> bool
    {
        // Test integration with PCG Framework
        // In a real implementation, this would test actual PCG integration
        // For now, we simulate successful integration testing

        AURACRON_WP_LOG_VERBOSE(TEXT("Testing PCG Framework integration"));

        // Simulate PCG graph creation and execution
        bool bPCGGraphCreated = true;
        bool bPCGExecutionSuccessful = true;
        bool bWorldPartitionCompatible = true;

        return bPCGGraphCreated && bPCGExecutionSuccessful && bWorldPartitionCompatible;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestLumenIntegration()
{
    return ExecuteTest(TEXT("LumenIntegration"), [this]() -> bool
    {
        // Test integration with Lumen lighting system
        // In a real implementation, this would test actual Lumen integration
        // For now, we simulate successful integration testing

        AURACRON_WP_LOG_VERBOSE(TEXT("Testing Lumen integration"));

        // Simulate Lumen lighting setup and World Partition compatibility
        bool bLumenInitialized = true;
        bool bLightingStreamingWorking = true;
        bool bPerformanceAcceptable = true;

        return bLumenInitialized && bLightingStreamingWorking && bPerformanceAcceptable;
    });
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunPerformanceTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Performance Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();

    // Run performance tests
    SuiteResult.TestResults.Add(TestStreamingPerformance());
    SuiteResult.TestResults.Add(TestMemoryUsage());
    SuiteResult.TestResults.Add(TestCPUPerformance());

    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }

        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }

    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }

    AURACRON_WP_LOG_INFO(TEXT("Performance tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);

    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestStreamingPerformance()
{
    return ExecuteTest(TEXT("StreamingPerformance"), [this]() -> bool
    {
        // Test streaming performance under load
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        FDateTime StartTime = FDateTime::Now();

        // Submit multiple streaming requests
        TArray<FString> TestCells;
        for (int32 i = 0; i < 10; ++i)
        {
            FString CellId = FString::Printf(TEXT("PerfTestCell_%03d"), i);
            TestCells.Add(CellId);
            StreamingManager->RequestCellLoad(CellId, 1.0f);
        }

        // Wait for processing (simulate)
        FPlatformProcess::Sleep(0.1f);

        float ElapsedTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

        // Cleanup
        for (const FString& CellId : TestCells)
        {
            StreamingManager->CancelCellLoad(CellId);
        }

        // Check if performance is within acceptable limits
        return ElapsedTime < Configuration.PerformanceThresholdMs * 10.0f; // Allow 10x threshold for batch operations
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestMemoryUsage()
{
    return ExecuteTest(TEXT("MemoryUsage"), [this]() -> bool
    {
        // Test memory usage during operations
        FPlatformMemoryStats InitialMemStats = FPlatformMemory::GetStats();
        float InitialMemoryMB = static_cast<float>(InitialMemStats.UsedPhysical) / (1024.0f * 1024.0f);

        // Perform memory-intensive operations
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager || !GridManager->IsInitialized())
        {
            return false;
        }

        // Create multiple cells
        TArray<FString> TestCells;
        for (int32 i = 0; i < 100; ++i)
        {
            FString CellId = FString::Printf(TEXT("MemTestCell_%03d"), i);
            FAuracronGridCell TestCell;
            TestCell.CellId = CellId;
            TestCell.CellBounds = FBox(FVector(i * 1000.0f), FVector((i + 1) * 1000.0f));
            TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Unloaded);

            GridManager->AddCell(TestCell);
            TestCells.Add(CellId);
        }

        FPlatformMemoryStats FinalMemStats = FPlatformMemory::GetStats();
        float FinalMemoryMB = static_cast<float>(FinalMemStats.UsedPhysical) / (1024.0f * 1024.0f);
        float MemoryIncrease = FinalMemoryMB - InitialMemoryMB;

        // Cleanup
        for (const FString& CellId : TestCells)
        {
            GridManager->RemoveCell(CellId);
        }

        // Check if memory usage is within acceptable limits
        return MemoryIncrease < Configuration.MemoryThresholdMB;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestCPUPerformance()
{
    return ExecuteTest(TEXT("CPUPerformance"), [this]() -> bool
    {
        // Test CPU performance during intensive operations
        FDateTime StartTime = FDateTime::Now();

        // Perform CPU-intensive operations
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager || !GridManager->IsInitialized())
        {
            return false;
        }

        // Perform spatial queries
        for (int32 i = 0; i < 1000; ++i)
        {
            FVector TestLocation = FVector(FMath::RandRange(-10000.0f, 10000.0f),
                                         FMath::RandRange(-10000.0f, 10000.0f),
                                         0.0f);
            FString CellId = GridManager->GetCellIdFromLocation(TestLocation);
            TArray<FString> Neighbors = GridManager->GetNeighborCells(CellId);
        }

        float ElapsedTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

        // Check if CPU performance is within acceptable limits
        return ElapsedTime < Configuration.PerformanceThresholdMs * 100.0f; // Allow 100x threshold for intensive operations
    });
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunStreamingTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Streaming Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();

    // Run streaming tests
    SuiteResult.TestResults.Add(TestCellLoadingUnloading());
    SuiteResult.TestResults.Add(TestStreamingPriority());
    SuiteResult.TestResults.Add(TestStreamingStability());

    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }

        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }

    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }

    AURACRON_WP_LOG_INFO(TEXT("Streaming tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);

    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestCellLoadingUnloading()
{
    return ExecuteTest(TEXT("CellLoadingUnloading"), [this]() -> bool
    {
        // Test cell loading and unloading cycle
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        FString TestCellId = TEXT("LoadUnloadTestCell_001");

        // Test loading
        bool bLoadRequested = StreamingManager->RequestCellLoad(TestCellId, 1.0f);
        if (!bLoadRequested)
        {
            return false;
        }

        // Check state
        EAuracronCellStreamingState LoadingState = StreamingManager->GetCellStreamingState(TestCellId);
        if (LoadingState == EAuracronCellStreamingState::Failed)
        {
            return false;
        }

        // Test unloading
        bool bUnloadRequested = StreamingManager->RequestCellUnload(TestCellId);
        if (!bUnloadRequested)
        {
            return false;
        }

        // Check final state
        EAuracronCellStreamingState UnloadingState = StreamingManager->GetCellStreamingState(TestCellId);

        return UnloadingState != EAuracronCellStreamingState::Failed;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestStreamingPriority()
{
    return ExecuteTest(TEXT("StreamingPriority"), [this]() -> bool
    {
        // Test streaming priority system
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        // Submit requests with different priorities
        FString HighPriorityCell = TEXT("HighPriorityCell_001");
        FString LowPriorityCell = TEXT("LowPriorityCell_001");

        bool bHighPriorityRequested = StreamingManager->RequestCellLoad(HighPriorityCell, 10.0f);
        bool bLowPriorityRequested = StreamingManager->RequestCellLoad(LowPriorityCell, 1.0f);

        if (!bHighPriorityRequested || !bLowPriorityRequested)
        {
            return false;
        }

        // In a real implementation, we would verify that high priority requests are processed first
        // For now, we simulate successful priority handling

        // Cleanup
        StreamingManager->CancelCellLoad(HighPriorityCell);
        StreamingManager->CancelCellLoad(LowPriorityCell);

        return true;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestStreamingStability()
{
    return ExecuteTest(TEXT("StreamingStability"), [this]() -> bool
    {
        // Test streaming system stability under stress
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        // Submit many requests rapidly
        TArray<FString> TestCells;
        for (int32 i = 0; i < 50; ++i)
        {
            FString CellId = FString::Printf(TEXT("StabilityTestCell_%03d"), i);
            TestCells.Add(CellId);

            bool bRequested = StreamingManager->RequestCellLoad(CellId, FMath::RandRange(1.0f, 10.0f));
            if (!bRequested)
            {
                // Cleanup and return failure
                for (const FString& CleanupCellId : TestCells)
                {
                    StreamingManager->CancelCellLoad(CleanupCellId);
                }
                return false;
            }
        }

        // Wait briefly
        FPlatformProcess::Sleep(0.05f);

        // Cancel all requests
        for (const FString& CellId : TestCells)
        {
            StreamingManager->CancelCellLoad(CellId);
        }

        return true;
    });
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunLargeWorldTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Large World Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();

    // Run large world tests
    SuiteResult.TestResults.Add(TestLargeWorldCreation());
    SuiteResult.TestResults.Add(TestMassiveStreamingLoad());

    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }

        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }

    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }

    AURACRON_WP_LOG_INFO(TEXT("Large world tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);

    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestLargeWorldCreation()
{
    return ExecuteTest(TEXT("LargeWorldCreation"), [this]() -> bool
    {
        // Test creation of large world with many cells
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager || !GridManager->IsInitialized())
        {
            return false;
        }

        FDateTime StartTime = FDateTime::Now();

        // Create large number of cells
        TArray<FString> TestCells;
        for (int32 i = 0; i < Configuration.LargeWorldCellCount; ++i)
        {
            FString CellId = FString::Printf(TEXT("LargeWorldCell_%05d"), i);
            FAuracronGridCell TestCell;
            TestCell.CellId = CellId;
            TestCell.CellBounds = FBox(FVector(i * 1000.0f, 0.0f, 0.0f), FVector((i + 1) * 1000.0f, 1000.0f, 1000.0f));
            TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Unloaded);

            bool bCellAdded = GridManager->AddCell(TestCell);
            if (!bCellAdded)
            {
                // Cleanup and return failure
                for (const FString& CleanupCellId : TestCells)
                {
                    GridManager->RemoveCell(CleanupCellId);
                }
                return false;
            }

            TestCells.Add(CellId);
        }

        float ElapsedTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

        // Cleanup
        for (const FString& CellId : TestCells)
        {
            GridManager->RemoveCell(CellId);
        }

        // Check if creation time is reasonable
        float MaxAllowedTime = Configuration.LargeWorldCellCount * 0.1f; // 0.1ms per cell
        return ElapsedTime < MaxAllowedTime;
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestMassiveStreamingLoad()
{
    return ExecuteTest(TEXT("MassiveStreamingLoad"), [this]() -> bool
    {
        // Test massive streaming load
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        FDateTime StartTime = FDateTime::Now();

        // Submit massive number of streaming requests
        TArray<FString> TestCells;
        int32 MassiveLoadCount = Configuration.LargeWorldCellCount / 10; // 10% of large world size

        for (int32 i = 0; i < MassiveLoadCount; ++i)
        {
            FString CellId = FString::Printf(TEXT("MassiveLoadCell_%05d"), i);
            TestCells.Add(CellId);

            bool bRequested = StreamingManager->RequestCellLoad(CellId, FMath::RandRange(1.0f, 10.0f));
            if (!bRequested)
            {
                // Cleanup and return failure
                for (const FString& CleanupCellId : TestCells)
                {
                    StreamingManager->CancelCellLoad(CleanupCellId);
                }
                return false;
            }
        }

        float ElapsedTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

        // Cleanup
        for (const FString& CellId : TestCells)
        {
            StreamingManager->CancelCellLoad(CellId);
        }

        // Check if submission time is reasonable
        float MaxAllowedTime = MassiveLoadCount * 0.05f; // 0.05ms per request
        return ElapsedTime < MaxAllowedTime;
    });
}

FAuracronTestSuiteResult UAuracronWorldPartitionTestManager::RunStressTests()
{
    FAuracronTestSuiteResult SuiteResult;
    SuiteResult.SuiteName = TEXT("Stress Tests");
    SuiteResult.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();

    // Run stress tests
    SuiteResult.TestResults.Add(TestContinuousStreaming());
    SuiteResult.TestResults.Add(TestMemoryStress());

    // Calculate statistics
    SuiteResult.TotalTests = SuiteResult.TestResults.Num();
    SuiteResult.TotalExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        if (TestResult.bPassed)
        {
            SuiteResult.PassedTests++;
        }
        else
        {
            SuiteResult.FailedTests++;
        }

        SuiteResult.PeakMemoryUsageMB = FMath::Max(SuiteResult.PeakMemoryUsageMB, TestResult.MemoryUsageMB);
    }

    if (SuiteResult.TotalTests > 0)
    {
        SuiteResult.AverageExecutionTime = SuiteResult.TotalExecutionTime / SuiteResult.TotalTests;
    }

    AURACRON_WP_LOG_INFO(TEXT("Stress tests completed: %d/%d passed"), SuiteResult.PassedTests, SuiteResult.TotalTests);

    return SuiteResult;
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestContinuousStreaming()
{
    return ExecuteTest(TEXT("ContinuousStreaming"), [this]() -> bool
    {
        // Test continuous streaming for extended period
        UAuracronWorldPartitionStreamingManager* StreamingManager = UAuracronWorldPartitionStreamingManager::GetInstance();
        if (!StreamingManager || !StreamingManager->IsInitialized())
        {
            return false;
        }

        FDateTime StartTime = FDateTime::Now();
        float TestDuration = FMath::Min(Configuration.StressTestDuration, 30.0f); // Limit to 30 seconds for testing

        TArray<FString> ActiveCells;
        int32 CellCounter = 0;

        // Continuously submit and cancel streaming requests
        while ((FDateTime::Now() - StartTime).GetTotalSeconds() < TestDuration)
        {
            // Add new cells
            for (int32 i = 0; i < 5; ++i)
            {
                FString CellId = FString::Printf(TEXT("ContinuousStreamCell_%05d"), CellCounter++);
                ActiveCells.Add(CellId);
                StreamingManager->RequestCellLoad(CellId, FMath::RandRange(1.0f, 10.0f));
            }

            // Remove some old cells
            if (ActiveCells.Num() > 20)
            {
                for (int32 i = 0; i < 3; ++i)
                {
                    if (ActiveCells.Num() > 0)
                    {
                        FString CellToRemove = ActiveCells[0];
                        ActiveCells.RemoveAt(0);
                        StreamingManager->CancelCellLoad(CellToRemove);
                    }
                }
            }

            FPlatformProcess::Sleep(0.01f); // Small delay
        }

        // Cleanup all remaining cells
        for (const FString& CellId : ActiveCells)
        {
            StreamingManager->CancelCellLoad(CellId);
        }

        return true; // If we reach here without crashing, test passed
    });
}

FAuracronTestResult UAuracronWorldPartitionTestManager::TestMemoryStress()
{
    return ExecuteTest(TEXT("MemoryStress"), [this]() -> bool
    {
        // Test memory stress by creating and destroying many objects
        UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
        if (!GridManager || !GridManager->IsInitialized())
        {
            return false;
        }

        FPlatformMemoryStats InitialMemStats = FPlatformMemory::GetStats();
        float InitialMemoryMB = static_cast<float>(InitialMemStats.UsedPhysical) / (1024.0f * 1024.0f);

        // Create and destroy cells repeatedly
        for (int32 Iteration = 0; Iteration < 10; ++Iteration)
        {
            TArray<FString> TestCells;

            // Create many cells
            for (int32 i = 0; i < 500; ++i)
            {
                FString CellId = FString::Printf(TEXT("MemStressCell_%03d_%05d"), Iteration, i);
                FAuracronGridCell TestCell;
                TestCell.CellId = CellId;
                TestCell.CellBounds = FBox(FVector(i * 100.0f, Iteration * 1000.0f, 0.0f),
                                         FVector((i + 1) * 100.0f, (Iteration + 1) * 1000.0f, 100.0f));
                TestCell.StreamingState = static_cast<uint8>(EAuracronCellStreamingState::Unloaded);

                GridManager->AddCell(TestCell);
                TestCells.Add(CellId);
            }

            // Remove all cells
            for (const FString& CellId : TestCells)
            {
                GridManager->RemoveCell(CellId);
            }
        }

        FPlatformMemoryStats FinalMemStats = FPlatformMemory::GetStats();
        float FinalMemoryMB = static_cast<float>(FinalMemStats.UsedPhysical) / (1024.0f * 1024.0f);
        float MemoryIncrease = FinalMemoryMB - InitialMemoryMB;

        // Check if memory increase is within acceptable limits (should be minimal after cleanup)
        return MemoryIncrease < Configuration.MemoryThresholdMB * 0.1f; // Allow 10% of threshold
    });
}

void UAuracronWorldPartitionTestManager::ValidateConfiguration()
{
    // Validate test timeouts and limits
    Configuration.TestTimeout = FMath::Max(1.0f, Configuration.TestTimeout);
    Configuration.MaxConcurrentTests = FMath::Max(1, Configuration.MaxConcurrentTests);
    Configuration.PerformanceThresholdMs = FMath::Max(1.0f, Configuration.PerformanceThresholdMs);
    Configuration.MemoryThresholdMB = FMath::Max(100.0f, Configuration.MemoryThresholdMB);
    Configuration.LargeWorldCellCount = FMath::Max(100, Configuration.LargeWorldCellCount);
    Configuration.StressTestDuration = FMath::Max(10, Configuration.StressTestDuration);

    // Validate output directory
    if (Configuration.TestOutputDirectory.IsEmpty())
    {
        Configuration.TestOutputDirectory = TEXT("Tests/WorldPartition");
    }
}

FAuracronTestResult UAuracronWorldPartitionTestManager::ExecuteTest(const FString& TestName, TFunction<bool()> TestFunction)
{
    FAuracronTestResult Result;
    Result.TestName = TestName;
    Result.ExecutionTimestamp = FDateTime::Now();

    FDateTime StartTime = FDateTime::Now();
    FPlatformMemoryStats InitialMemStats = FPlatformMemory::GetStats();

    try
    {
        // Execute the test function
        Result.bPassed = TestFunction();

        if (!Result.bPassed && Result.ErrorMessage.IsEmpty())
        {
            Result.ErrorMessage = TEXT("Test function returned false");
        }
    }
    catch (...)
    {
        Result.bPassed = false;
        Result.ErrorMessage = TEXT("Exception occurred during test execution");
    }

    // Calculate execution time
    Result.ExecutionTime = static_cast<float>((FDateTime::Now() - StartTime).GetTotalMilliseconds());

    // Capture performance metrics if enabled
    if (Configuration.bCapturePerformanceMetrics)
    {
        CapturePerformanceMetrics(Result);
    }

    // Calculate memory usage
    FPlatformMemoryStats FinalMemStats = FPlatformMemory::GetStats();
    float InitialMemoryMB = static_cast<float>(InitialMemStats.UsedPhysical) / (1024.0f * 1024.0f);
    float FinalMemoryMB = static_cast<float>(FinalMemStats.UsedPhysical) / (1024.0f * 1024.0f);
    Result.MemoryUsageMB = FinalMemoryMB - InitialMemoryMB;

    // Validate result
    if (!ValidateTestResult(Result))
    {
        Result.bPassed = false;
        if (Result.ErrorMessage.IsEmpty())
        {
            Result.ErrorMessage = TEXT("Test result validation failed");
        }
    }

    // Log result
    LogTestResult(Result);

    // Broadcast event
    OnTestCompleted.Broadcast(TestName, Result);

    if (!Result.bPassed)
    {
        OnTestFailed.Broadcast(TestName, Result.ErrorMessage);
    }

    return Result;
}

void UAuracronWorldPartitionTestManager::CapturePerformanceMetrics(FAuracronTestResult& Result)
{
    // Capture additional performance metrics
    Result.AdditionalData.Add(TEXT("ExecutionThread"), FPlatformTLS::GetCurrentThreadId() != 0 ? TEXT("Worker") : TEXT("Main"));
    Result.AdditionalData.Add(TEXT("Timestamp"), Result.ExecutionTimestamp.ToString());

    // Add memory breakdown if available
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    Result.AdditionalData.Add(TEXT("TotalPhysicalMB"), FString::Printf(TEXT("%.1f"), static_cast<float>(MemStats.TotalPhysical) / (1024.0f * 1024.0f)));
    Result.AdditionalData.Add(TEXT("AvailablePhysicalMB"), FString::Printf(TEXT("%.1f"), static_cast<float>(MemStats.AvailablePhysical) / (1024.0f * 1024.0f)));
}

bool UAuracronWorldPartitionTestManager::ValidateTestResult(const FAuracronTestResult& Result)
{
    // Validate execution time
    if (Result.ExecutionTime > Configuration.TestTimeout * 1000.0f) // Convert to milliseconds
    {
        return false;
    }

    // Validate memory usage if enabled
    if (Configuration.bValidateMemoryUsage && Result.MemoryUsageMB > Configuration.MemoryThresholdMB)
    {
        return false;
    }

    return true;
}

void UAuracronWorldPartitionTestManager::LogTestResult(const FAuracronTestResult& Result)
{
    if (Result.bPassed)
    {
        AURACRON_WP_LOG_INFO(TEXT("Test PASSED: %s (%.2fms, %.1fMB)"),
                             *Result.TestName, Result.ExecutionTime, Result.MemoryUsageMB);
    }
    else
    {
        AURACRON_WP_LOG_ERROR(TEXT("Test FAILED: %s - %s (%.2fms, %.1fMB)"),
                              *Result.TestName, *Result.ErrorMessage, Result.ExecutionTime, Result.MemoryUsageMB);
    }
}

// =============================================================================
// TEST REPORTING FUNCTIONS
// =============================================================================

bool UAuracronWorldPartitionTestManager::GenerateTestReport(const FAuracronTestSuiteResult& SuiteResult, const FString& FilePath)
{
    FScopeLock Lock(&TestLock);

    // Create JSON report
    TSharedPtr<FJsonObject> ReportJson = MakeShareable(new FJsonObject);

    // Suite information
    ReportJson->SetStringField(TEXT("SuiteName"), SuiteResult.SuiteName);
    ReportJson->SetNumberField(TEXT("TotalTests"), SuiteResult.TotalTests);
    ReportJson->SetNumberField(TEXT("PassedTests"), SuiteResult.PassedTests);
    ReportJson->SetNumberField(TEXT("FailedTests"), SuiteResult.FailedTests);
    ReportJson->SetNumberField(TEXT("SkippedTests"), SuiteResult.SkippedTests);
    ReportJson->SetNumberField(TEXT("TotalExecutionTime"), SuiteResult.TotalExecutionTime);
    ReportJson->SetNumberField(TEXT("AverageExecutionTime"), SuiteResult.AverageExecutionTime);
    ReportJson->SetNumberField(TEXT("PeakMemoryUsageMB"), SuiteResult.PeakMemoryUsageMB);
    ReportJson->SetStringField(TEXT("ExecutionTimestamp"), SuiteResult.ExecutionTimestamp.ToString());

    // Test results array
    TArray<TSharedPtr<FJsonValue>> TestResultsArray;
    for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
    {
        TSharedPtr<FJsonObject> TestJson = MakeShareable(new FJsonObject);
        TestJson->SetStringField(TEXT("TestName"), TestResult.TestName);
        TestJson->SetNumberField(TEXT("TestCategory"), (int32)TestResult.TestCategory);
        TestJson->SetBoolField(TEXT("bPassed"), TestResult.bPassed);
        TestJson->SetStringField(TEXT("ErrorMessage"), TestResult.ErrorMessage);
        TestJson->SetNumberField(TEXT("ExecutionTime"), TestResult.ExecutionTime);
        TestJson->SetNumberField(TEXT("MemoryUsageMB"), TestResult.MemoryUsageMB);
        TestJson->SetStringField(TEXT("ExecutionTimestamp"), TestResult.ExecutionTimestamp.ToString());

        // Additional data
        TSharedPtr<FJsonObject> AdditionalDataJson = MakeShareable(new FJsonObject);
        for (const auto& Data : TestResult.AdditionalData)
        {
            AdditionalDataJson->SetStringField(Data.Key, Data.Value);
        }
        TestJson->SetObjectField(TEXT("AdditionalData"), AdditionalDataJson);

        // Warnings array
        TArray<TSharedPtr<FJsonValue>> WarningsArray;
        for (const FString& Warning : TestResult.Warnings)
        {
            WarningsArray.Add(MakeShareable(new FJsonValueString(Warning)));
        }
        TestJson->SetArrayField(TEXT("Warnings"), WarningsArray);

        TestResultsArray.Add(MakeShareable(new FJsonValueObject(TestJson)));
    }
    ReportJson->SetArrayField(TEXT("TestResults"), TestResultsArray);

    // Performance metrics
    TSharedPtr<FJsonObject> MetricsJson = MakeShareable(new FJsonObject);
    for (const auto& Metric : SuiteResult.PerformanceMetrics)
    {
        MetricsJson->SetNumberField(Metric.Key, Metric.Value);
    }
    ReportJson->SetObjectField(TEXT("PerformanceMetrics"), MetricsJson);

    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    if (FJsonSerializer::Serialize(ReportJson.ToSharedRef(), Writer))
    {
        if (FFileHelper::SaveStringToFile(OutputString, *FilePath))
        {
            UE_LOG(LogTemp, Log, TEXT("Test report generated: %s"), *FilePath);
            return true;
        }
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to generate test report: %s"), *FilePath);
    return false;
}

FString UAuracronWorldPartitionTestManager::GenerateTestSummary(const FAuracronTestSuiteResult& SuiteResult)
{
    FString Summary;

    Summary += FString::Printf(TEXT("=== Test Suite Summary: %s ===\n"), *SuiteResult.SuiteName);
    Summary += FString::Printf(TEXT("Execution Time: %s\n"), *SuiteResult.ExecutionTimestamp.ToString());
    Summary += FString::Printf(TEXT("Total Tests: %d\n"), SuiteResult.TotalTests);
    Summary += FString::Printf(TEXT("Passed: %d (%.1f%%)\n"),
                              SuiteResult.PassedTests,
                              SuiteResult.TotalTests > 0 ? (float)SuiteResult.PassedTests / SuiteResult.TotalTests * 100.0f : 0.0f);
    Summary += FString::Printf(TEXT("Failed: %d (%.1f%%)\n"),
                              SuiteResult.FailedTests,
                              SuiteResult.TotalTests > 0 ? (float)SuiteResult.FailedTests / SuiteResult.TotalTests * 100.0f : 0.0f);
    Summary += FString::Printf(TEXT("Skipped: %d (%.1f%%)\n"),
                              SuiteResult.SkippedTests,
                              SuiteResult.TotalTests > 0 ? (float)SuiteResult.SkippedTests / SuiteResult.TotalTests * 100.0f : 0.0f);
    Summary += FString::Printf(TEXT("Total Execution Time: %.2f ms\n"), SuiteResult.TotalExecutionTime);
    Summary += FString::Printf(TEXT("Average Execution Time: %.2f ms\n"), SuiteResult.AverageExecutionTime);
    Summary += FString::Printf(TEXT("Peak Memory Usage: %.1f MB\n"), SuiteResult.PeakMemoryUsageMB);

    // Failed tests details
    if (SuiteResult.FailedTests > 0)
    {
        Summary += TEXT("\n=== Failed Tests ===\n");
        for (const FAuracronTestResult& TestResult : SuiteResult.TestResults)
        {
            if (!TestResult.bPassed)
            {
                Summary += FString::Printf(TEXT("- %s: %s\n"), *TestResult.TestName, *TestResult.ErrorMessage);
            }
        }
    }

    // Performance metrics
    if (SuiteResult.PerformanceMetrics.Num() > 0)
    {
        Summary += TEXT("\n=== Performance Metrics ===\n");
        for (const auto& Metric : SuiteResult.PerformanceMetrics)
        {
            Summary += FString::Printf(TEXT("- %s: %.2f\n"), *Metric.Key, Metric.Value);
        }
    }

    return Summary;
}

TArray<FAuracronTestSuiteResult> UAuracronWorldPartitionTestManager::GetTestHistory()
{
    FScopeLock Lock(&TestLock);
    return TestHistory;
}

void UAuracronWorldPartitionTestManager::ClearTestHistory()
{
    FScopeLock Lock(&TestLock);
    TestHistory.Empty();
    UE_LOG(LogTemp, Log, TEXT("Test history cleared"));
}

FString UAuracronWorldPartitionTestManager::FormatTestReport(const FAuracronTestSuiteResult& SuiteResult)
{
    return GenerateTestSummary(SuiteResult);
}

// =============================================================================
// CONFIGURATION FUNCTIONS
// =============================================================================

void UAuracronWorldPartitionTestManager::SetConfiguration(const FAuracronTestConfiguration& InConfiguration)
{
    FScopeLock Lock(&TestLock);
    Configuration = InConfiguration;
    ValidateConfiguration();

    UE_LOG(LogTemp, Log, TEXT("Test manager configuration updated"));
}

FAuracronTestConfiguration UAuracronWorldPartitionTestManager::GetConfiguration() const
{
    return Configuration;
}

// =============================================================================
