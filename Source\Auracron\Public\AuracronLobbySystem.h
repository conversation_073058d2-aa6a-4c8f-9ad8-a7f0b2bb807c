/**
 * AuracronLobbySystem.h
 * 
 * Sistema de lobby e matchmaking do Auracron que gerencia:
 * - Criação e gerenciamento de lobbies para 10 jogadores (5v5)
 * - Sistema de matchmaking baseado em skill e preferências
 * - Seleção de campeões e sígilos
 * - Balanceamento de equipes
 * - Integração com Harmony Engine para experiência positiva
 * 
 * Usa UE 5.6 Subsystem moderno com integração completa dos bridges.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "TimerManager.h"
#include "OnlineSubsystem.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "AuracronLobbySystem.generated.h"

// Forward declarations
class AAuracronPlayerController;
// Temporariamente removidas para compilação inicial
// class UHarmonyEngineSubsystem;
// class UAuracronSigilosBridge;

/**
 * Estados do lobby
 */
UENUM(BlueprintType)
enum class EAuracronLobbyState : uint8
{
    Idle                UMETA(DisplayName = "Idle"),
    Searching           UMETA(DisplayName = "Searching"),
    FoundMatch          UMETA(DisplayName = "Found Match"),
    ChampionSelect      UMETA(DisplayName = "Champion Select"),
    LoadingGame         UMETA(DisplayName = "Loading Game"),
    InGame              UMETA(DisplayName = "In Game"),
    PostGame            UMETA(DisplayName = "Post Game")
};

/**
 * Tipos de matchmaking
 */
UENUM(BlueprintType)
enum class EAuracronMatchmakingType : uint8
{
    Casual              UMETA(DisplayName = "Casual"),
    Ranked              UMETA(DisplayName = "Ranked"),
    Custom              UMETA(DisplayName = "Custom"),
    Tutorial            UMETA(DisplayName = "Tutorial"),
    Practice            UMETA(DisplayName = "Practice")
};

/**
 * Dados do jogador para matchmaking
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronPlayerMatchmakingData
{
    GENERATED_BODY()

    /** ID único do jogador */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    FString PlayerID;

    /** Nome do jogador */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    FString PlayerName;

    /** Nível do jogador */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    int32 PlayerLevel = 1;

    /** Rating de skill */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    float SkillRating = 1000.0f;

    /** Campeões favoritos */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    TArray<int32> FavoriteChampions;

    /** Sígilos preferidos */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    TArray<FGameplayTag> PreferredSigils;

    /** Região preferida */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    FString PreferredRegion;

    /** Ping médio */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    float AveragePing = 50.0f;

    /** Score de comportamento (Harmony Engine) */
    UPROPERTY(BlueprintReadOnly, Category = "Player Data")
    float BehaviorScore = 100.0f;

    /** Tempo de busca máximo */
    UPROPERTY(BlueprintReadWrite, Category = "Player Data")
    float MaxSearchTime = 300.0f; // 5 minutos

    FAuracronPlayerMatchmakingData()
    {
        PlayerID = TEXT("");
        PlayerName = TEXT("");
        PlayerLevel = 1;
        SkillRating = 1000.0f;
        FavoriteChampions.Empty();
        PreferredSigils.Empty();
        PreferredRegion = TEXT("Auto");
        AveragePing = 50.0f;
        BehaviorScore = 100.0f;
        MaxSearchTime = 300.0f;
    }
};

/**
 * Dados do lobby
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronLobbyData
{
    GENERATED_BODY()

    /** ID único do lobby */
    UPROPERTY(BlueprintReadOnly, Category = "Lobby Data")
    FString LobbyID;

    /** Nome do lobby */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    FString LobbyName;

    /** Tipo de matchmaking */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    EAuracronMatchmakingType MatchmakingType = EAuracronMatchmakingType::Casual;

    /** Jogadores no lobby */
    UPROPERTY(BlueprintReadOnly, Category = "Lobby Data")
    TArray<FAuracronPlayerMatchmakingData> Players;

    /** Máximo de jogadores */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    int32 MaxPlayers = 10;

    /** Lobby privado */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    bool bIsPrivate = false;

    /** Senha do lobby */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    FString Password;

    /** Região do servidor */
    UPROPERTY(BlueprintReadWrite, Category = "Lobby Data")
    FString ServerRegion;

    /** Tempo de criação */
    UPROPERTY(BlueprintReadOnly, Category = "Lobby Data")
    float CreationTime = 0.0f;

    /** Estado atual */
    UPROPERTY(BlueprintReadOnly, Category = "Lobby Data")
    EAuracronLobbyState CurrentState = EAuracronLobbyState::Idle;

    FAuracronLobbyData()
    {
        LobbyID = TEXT("");
        LobbyName = TEXT("");
        MatchmakingType = EAuracronMatchmakingType::Casual;
        Players.Empty();
        MaxPlayers = 10;
        bIsPrivate = false;
        Password = TEXT("");
        ServerRegion = TEXT("Auto");
        CreationTime = 0.0f;
        CurrentState = EAuracronLobbyState::Idle;
    }
};

/**
 * Configurações de matchmaking
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronMatchmakingConfig
{
    GENERATED_BODY()

    /** Diferença máxima de skill rating */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float MaxSkillDifference = 200.0f;

    /** Ping máximo aceitável */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float MaxPing = 150.0f;

    /** Score mínimo de comportamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float MinBehaviorScore = 50.0f;

    /** Tempo máximo de busca */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float MaxSearchTime = 300.0f;

    /** Expandir critérios após tempo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float ExpandCriteriaTime = 120.0f;

    /** Priorizar balanceamento de equipes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    bool bPrioritizeTeamBalance = true;

    /** Considerar campeões favoritos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    bool bConsiderFavoriteChampions = true;

    /** Peso do comportamento no matchmaking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Matchmaking")
    float BehaviorWeight = 0.3f;

    FAuracronMatchmakingConfig()
    {
        MaxSkillDifference = 200.0f;
        MaxPing = 150.0f;
        MinBehaviorScore = 50.0f;
        MaxSearchTime = 300.0f;
        ExpandCriteriaTime = 120.0f;
        bPrioritizeTeamBalance = true;
        bConsiderFavoriteChampions = true;
        BehaviorWeight = 0.3f;
    }
};

/**
 * Sistema de Lobby e Matchmaking do Auracron
 */
UCLASS(BlueprintType)
class AURACRON_API UAuracronLobbySystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // === Lobby Management ===
    
    /** Criar novo lobby */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    FString CreateLobby(const FAuracronLobbyData& LobbyData);

    /** Entrar em lobby */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    bool JoinLobby(const FString& LobbyID, const FAuracronPlayerMatchmakingData& PlayerData, const FString& Password = TEXT(""));

    /** Sair do lobby */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void LeaveLobby(const FString& PlayerID);

    /** Obter dados do lobby atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Lobby")
    FAuracronLobbyData GetCurrentLobbyData() const { return CurrentLobby; }

    /** Obter estado do lobby */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Lobby")
    EAuracronLobbyState GetLobbyState() const { return CurrentLobby.CurrentState; }

    // === Matchmaking ===
    
    /** Iniciar busca por partida */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void StartMatchmaking(const FAuracronPlayerMatchmakingData& PlayerData, EAuracronMatchmakingType MatchmakingType);

    /** Cancelar busca por partida */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void CancelMatchmaking();

    /** Obter configurações de matchmaking */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Lobby")
    FAuracronMatchmakingConfig GetMatchmakingConfig() const { return MatchmakingConfig; }

    /** Atualizar configurações de matchmaking */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void UpdateMatchmakingConfig(const FAuracronMatchmakingConfig& NewConfig);

    // === Champion Selection ===
    
    /** Iniciar seleção de campeões */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void StartChampionSelection();

    /** Selecionar campeão */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    bool SelectChampion(const FString& PlayerID, int32 ChampionID);

    /** Banir campeão */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    bool BanChampion(const FString& PlayerID, int32 ChampionID);

    /** Confirmar seleção */
    UFUNCTION(BlueprintCallable, Category = "Auracron Lobby")
    void ConfirmSelection(const FString& PlayerID);

    // === Events ===
    
    /** Evento quando lobby é criado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLobbyCreated, FAuracronLobbyData, LobbyData);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Lobby")
    FOnLobbyCreated OnLobbyCreated;

    /** Evento quando jogador entra no lobby */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerJoinedLobby, FString, PlayerID, FAuracronPlayerMatchmakingData, PlayerData);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Lobby")
    FOnPlayerJoinedLobby OnPlayerJoinedLobby;

    /** Evento quando jogador sai do lobby */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerLeftLobby, FString, PlayerID);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Lobby")
    FOnPlayerLeftLobby OnPlayerLeftLobby;

    /** Evento quando partida é encontrada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMatchFound, FAuracronLobbyData, MatchData);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Lobby")
    FOnMatchFound OnMatchFound;

    /** Evento quando seleção de campeão inicia */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnChampionSelectionStarted);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Lobby")
    FOnChampionSelectionStarted OnChampionSelectionStarted;

protected:
    // === Configuration ===
    
    /** Configurações de matchmaking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronMatchmakingConfig MatchmakingConfig;

    /** Habilitar logs detalhados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bVerboseLogging = true;

    // === State ===
    
    /** Lobby atual */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    FAuracronLobbyData CurrentLobby;

    /** Dados do jogador local */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    FAuracronPlayerMatchmakingData LocalPlayerData;

    /** Tempo de início da busca */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    float SearchStartTime = 0.0f;

    /** Está buscando partida */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsSearching = false;

    // === Bridge References ===
    // Usando UObject* para evitar problemas de linkagem até os bridges estarem prontos

    /** Harmony Engine Subsystem */
    UPROPERTY()
    TObjectPtr<UObject> HarmonyEngineSubsystem;

private:
    // === Implementation ===
    void UpdateMatchmaking(float DeltaTime);
    void ProcessMatchmakingQueue();
    bool ValidatePlayerForMatch(const FAuracronPlayerMatchmakingData& PlayerData);
    float CalculateMatchQuality(const TArray<FAuracronPlayerMatchmakingData>& Players);
    void BalanceTeams(TArray<FAuracronPlayerMatchmakingData>& Players);
    void InitializeBridgeReferences();
    void ChangeLobbyState(EAuracronLobbyState NewState);
    FString GenerateUniqueID();

    /** Timer para atualização de matchmaking */
    FTimerHandle MatchmakingUpdateTimer;

    /** Fila de matchmaking */
    TArray<FAuracronPlayerMatchmakingData> MatchmakingQueue;

    /** Lobbies ativos */
    TMap<FString, FAuracronLobbyData> ActiveLobbies;

    /** Flag de inicialização */
    bool bSystemInitialized = false;
};
