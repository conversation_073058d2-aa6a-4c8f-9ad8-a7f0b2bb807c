#pragma once

#include "CoreMinimal.h"
#include "Engine/World.h"
#include "FoliageType.h"
#include "InstancedFoliageActor.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "UObject/NoExportTypes.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "AuracronFoliageTypes.h"
#include "AuracronFoliageEditUtility.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronFoliageEditUtility, Log, All);



/**
 * Utility class for foliage editing operations in Unreal Engine 5.6
 * Provides comprehensive foliage management functionality
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageEditUtility : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UAuracronFoliageEditUtility();

    // Core foliage painting functions
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool PaintFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, const FFoliageEditSettings& Settings);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool EraseFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool SelectFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool ReapplyFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius);

    // Foliage type management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static UFoliageType* CreateFoliageType(UStaticMesh* StaticMesh, const FString& Name);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool AddFoliageType(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool RemoveFoliageType(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static TArray<UFoliageType*> GetFoliageTypes(UWorld* World);

    // Instance management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static int32 GetFoliageInstanceCount(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static TArray<FAuracronFoliageInstanceData> GetFoliageInstances(UWorld* World, UFoliageType* FoliageType);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool UpdateFoliageInstances(UWorld* World, UFoliageType* FoliageType, const TArray<FAuracronFoliageInstanceData>& Instances);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static bool IsValidFoliageLocation(UWorld* World, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static FVector GetSurfaceNormal(UWorld* World, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage", CallInEditor)
    static float GetSurfaceHeight(UWorld* World, const FVector& Location);

private:
    // Helper functions
    static AInstancedFoliageActor* GetOrCreateFoliageActor(UWorld* World);
    static bool ValidateFoliageParameters(UWorld* World, UFoliageType* FoliageType);
    static FTransform GenerateInstanceTransform(const FVector& Location, const FVector& Normal, const FFoliageEditSettings& Settings);
    static bool IsLocationSuitable(UWorld* World, const FVector& Location, const FFoliageEditSettings& Settings);
};
