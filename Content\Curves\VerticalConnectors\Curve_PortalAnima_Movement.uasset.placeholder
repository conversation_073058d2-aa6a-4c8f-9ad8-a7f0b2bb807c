# Placeholder for Curve_PortalAnima_Movement.uasset

This is a placeholder file for the missing curve asset:
- **Asset Name**: Curve_PortalAnima_Movement
- **Type**: Float Curve
- **Purpose**: Movement curve for Portal Anima vertical connector
- **Status**: Missing - needs to be created by design team

## Required Specifications:
- **Type**: Float Curve Asset
- **Duration**: 1.0 seconds (default transition time)
- **Curve Shape**: Smooth ease-in-out for portal transition
- **Values**: 0.0 to 1.0 range for interpolation

## Notes:
- This placeholder prevents compilation errors
- Replace with actual asset when available
- Update references in code if asset path changes
