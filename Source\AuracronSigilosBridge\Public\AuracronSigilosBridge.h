// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de SÃ­gilos Auracron Bridge
// IntegraÃ§Ã£o C++ para o sistema de fusÃ£o de campeÃµes usando as APIs mais modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "AbilitySystemComponent.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayEffect.h"
#include "AttributeSet.h"
#include "GameplayAbilitySet.h"
#include "GameplayAbilitySpec.h"
#include "GameplayTagContainer.h"
#include "GameplayTask.h"
#include "Components/ActorComponent.h"
// Forward declaration for ModularGameplay compatibility
class UGameFrameworkComponentManager;
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"

#include "Materials/MaterialParameterCollection.h"
#include "Particles/ParticleSystem.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"
#include "MetasoundSource.h"

// Advanced UE 5.6 includes for new features
#include "EnhancedInputComponent.h"
#include "InputAction.h"
#include "InputActionValue.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Http.h"
#include "Engine/AssetManager.h"
#include "HAL/PlatformMemory.h"
#include "GenericTeamAgentInterface.h"

#include "AuracronSigilosBridge.generated.h"

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UAttributeSet;
class UNiagaraSystem;
class UNiagaraComponent;
class USoundCue;
class UMaterialParameterCollection;
class UCurveFloat;
struct FAuracronSigiloConfiguration;
struct FAuracronSigiloProperties;
struct FAuracronSigiloPassiveBonuses;
struct FAuracronSigiloExclusiveAbility;
struct FAuracronSigiloVisualEffects;
struct FAuracronSigiloAudioEffects;

// Realm bonus structure
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronRealmBonus
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Realm Bonus")
    FString BonusName;

    UPROPERTY(BlueprintReadWrite, Category = "Realm Bonus")
    float BonusMultiplier = 1.0f;

    FAuracronRealmBonus()
    {
        BonusName = TEXT("Default Bonus");
        BonusMultiplier = 1.0f;
    }
};

/**
 * EnumeraÃ§Ã£o para tipos de SÃ­gilos Auracron
 */
UENUM(BlueprintType)
enum class EAuracronSigiloType : uint8
{
    None        UMETA(DisplayName = "None"),
    Aegis       UMETA(DisplayName = "Aegis (Defense)"),
    Ruin        UMETA(DisplayName = "Ruin (Damage)"),
    Vesper      UMETA(DisplayName = "Vesper (Support)")
};

/**
 * Enumeração para subtipos de Sígilos Aegis (Defesa/Proteção)
 */
UENUM(BlueprintType)
enum class EAuracronAegisSigilType : uint8
{
    None            UMETA(DisplayName = "None"),
    Primordial      UMETA(DisplayName = "Aegis Primordial"),
    Cristalino      UMETA(DisplayName = "Aegis Cristalino"),
    Temporal        UMETA(DisplayName = "Aegis Temporal"),
    Espectral       UMETA(DisplayName = "Aegis Espectral"),
    Absoluto        UMETA(DisplayName = "Aegis Absoluto")
};

/**
 * Enumeração para subtipos de Sígilos Ruin (Dano/Destruição)
 */
UENUM(BlueprintType)
enum class EAuracronRuinSigilType : uint8
{
    None            UMETA(DisplayName = "None"),
    Flamejante      UMETA(DisplayName = "Ruin Flamejante"),
    Gelido          UMETA(DisplayName = "Ruin Gélido"),
    Sombrio         UMETA(DisplayName = "Ruin Sombrio"),
    Corrosivo       UMETA(DisplayName = "Ruin Corrosivo"),
    Aniquilador     UMETA(DisplayName = "Ruin Aniquilador")
};

/**
 * Enumeração para subtipos de Sígilos Vesper (Suporte/Utilidade)
 */
UENUM(BlueprintType)
enum class EAuracronVesperSigilType : uint8
{
    None            UMETA(DisplayName = "None"),
    Curativo        UMETA(DisplayName = "Vesper Curativo"),
    Energetico      UMETA(DisplayName = "Vesper Energético"),
    Velocidade      UMETA(DisplayName = "Vesper Velocidade"),
    Visao           UMETA(DisplayName = "Vesper Visão"),
    Teleporte       UMETA(DisplayName = "Vesper Teleporte"),
    Temporal        UMETA(DisplayName = "Vesper Temporal")
};

/**
 * EnumeraÃ§Ã£o para estados de fusÃ£o do Sigilo
 */
UENUM(BlueprintType)
enum class EAuracronSigiloFusionState : uint8
{
    Inactive        UMETA(DisplayName = "Inactive"),
    Charging        UMETA(DisplayName = "Charging"),
    Active          UMETA(DisplayName = "Active"),
    Cooldown        UMETA(DisplayName = "Cooldown"),
    Reforging       UMETA(DisplayName = "Reforging")
};

/**
 * Estrutura para um Sígilo individual equipado
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronEquippedSigil
{
    GENERATED_BODY()

    /** Tipo principal do Sígilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil")
    EAuracronSigiloType MainType = EAuracronSigiloType::None;

    /** Subtipo específico do Sígilo Aegis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil")
    EAuracronAegisSigilType AegisSubtype = EAuracronAegisSigilType::None;

    /** Subtipo específico do Sígilo Ruin */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil")
    EAuracronRuinSigilType RuinSubtype = EAuracronRuinSigilType::None;

    /** Subtipo específico do Sígilo Vesper */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil")
    EAuracronVesperSigilType VesperSubtype = EAuracronVesperSigilType::None;

    /** Nível do Sígilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil", meta = (ClampMin = "1", ClampMax = "20"))
    int32 Level = 1;

    /** Experiência acumulada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipped Sigil", meta = (ClampMin = "0"))
    int32 Experience = 0;

    /** Cooldown atual (em segundos) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Equipped Sigil")
    float CurrentCooldown = 0.0f;

    /** Se o Sígilo está ativo */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Equipped Sigil")
    bool bIsActive = false;
};

/**
 * Estrutura para Arquétipo de Fusão 2.0 (150 combinações)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigilArchetype
{
    GENERATED_BODY()

    /** Tag único do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    FGameplayTag ArchetypeTag;

    /** Nome do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    FText ArchetypeName;

    /** Descrição do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    FText ArchetypeDescription;

    /** Combinação de Sígilos que forma este Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    EAuracronAegisSigilType AegisComponent = EAuracronAegisSigilType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    EAuracronRuinSigilType RuinComponent = EAuracronRuinSigilType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    EAuracronVesperSigilType VesperComponent = EAuracronVesperSigilType::None;

    /** Habilidade especial de Fusão do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    TSubclassOf<UGameplayAbility> FusionAbility;

    /** Efeitos passivos únicos do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    TArray<TSubclassOf<UGameplayEffect>> ArchetypePassiveEffects;

    /** Multiplicador de poder do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype", meta = (ClampMin = "0.5", ClampMax = "3.0"))
    float PowerMultiplier = 1.0f;

    /** Redução de cooldown do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype", meta = (ClampMin = "0.0", ClampMax = "0.5"))
    float CooldownReduction = 0.0f;

    /** Eficiência energética do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype", meta = (ClampMin = "0.5", ClampMax = "2.0"))
    float EnergyEfficiency = 1.0f;

    /** Nível mínimo requerido para usar este Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype", meta = (ClampMin = "1", ClampMax = "20"))
    int32 RequiredLevel = 1;

    /** Cor primária do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundária do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    FLinearColor SecondaryColor = FLinearColor::Gray;

    /** Efeitos visuais únicos do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    TSoftObjectPtr<UNiagaraSystem> ArchetypeVFX;

    /** Som único do Arquétipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Archetype")
    TSoftObjectPtr<UMetaSoundSource> ArchetypeAudio;

    /** Nível de maestria do Arquétipo */
    UPROPERTY(BlueprintReadOnly, Category = "Archetype")
    float MasteryLevel = 0.0f;
};

/**
 * Estrutura para propriedades base de um Sigilo
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloProperties
{
    GENERATED_BODY()

    /** Tipo do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    /** Nome do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FText SigiloName;

    /** DescriÃ§Ã£o do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FText SigiloDescription;

    /** Ãcone do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    TSoftObjectPtr<UTexture2D> SigiloIcon;

    /** Cor primÃ¡ria do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundÃ¡ria do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties")
    FLinearColor SecondaryColor = FLinearColor::Gray;

    /** Tempo para fusÃ£o (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "1.0", ClampMax = "600.0"))
    float FusionTime = 360.0f; // 6 minutos

    /** Cooldown para re-forjamento (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "30.0", ClampMax = "300.0"))
    float ReforgeCooldown = 120.0f; // 2 minutos

    /** NÃ­vel do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "1", ClampMax = "20"))
    int32 SigiloLevel = 1;

    /** ExperiÃªncia do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "0"))
    int32 SigiloExperience = 0;

    /** ExperiÃªncia necessÃ¡ria para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Properties", meta = (ClampMin = "100"))
    int32 ExperienceToNextLevel = 1000;
};

/**
 * Estrutura para bÃ´nus passivos do Sigilo
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloPassiveBonuses
{
    GENERATED_BODY()

    /** BÃ´nus de HP (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float HealthBonus = 0.0f;

    /** BÃ´nus de Armadura (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ArmorBonus = 0.0f;

    /** BÃ´nus de Ataque (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AttackBonus = 0.0f;

    /** BÃ´nus de Poder de Habilidade (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AbilityPowerBonus = 0.0f;

    /** BÃ´nus de Velocidade de Movimento (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MovementSpeedBonus = 0.0f;

    /** ReduÃ§Ã£o de Cooldown (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "0.5"))
    float CooldownReduction = 0.0f;

    /** RegeneraÃ§Ã£o de Mana (por segundo) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float ManaRegeneration = 0.0f;

    /** ResistÃªncia a Dano (percentual) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Passive Bonuses", meta = (ClampMin = "0.0", ClampMax = "0.8"))
    float DamageResistance = 0.0f;
};

/**
 * Estrutura para habilidade exclusiva do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloExclusiveAbility
{
    GENERATED_BODY()

    /** Classe da habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    TSoftClassPtr<UGameplayAbility> AbilityClass;

    /** Nome da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FText AbilityName;

    /** Descrição da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FText AbilityDescription;

    /** Ícone da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    TSoftObjectPtr<UTexture2D> AbilityIcon;

    /** Cooldown da habilidade (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Cooldown = 60.0f;

    /** Custo de mana da habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float ManaCost = 100.0f;

    /** Duração da habilidade (segundos, 0 = instantânea) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Duration = 0.0f;

    /** Alcance da habilidade (0 = self-target) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability", meta = (ClampMin = "0.0"))
    float Range = 0.0f;

    /** GameplayTags necessários para ativar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FGameplayTagContainer RequiredTags;

    /** GameplayTags bloqueados durante uso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusive Ability")
    FGameplayTagContainer BlockedTags;
};

/**
 * Estrutura para efeitos visuais do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloVisualEffects
{
    GENERATED_BODY()

    /** Sistema de partículas de fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> FusionParticleSystem;

    /** Sistema de partículas de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> ActivationParticleSystem;

    /** Sistema de partículas de habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> AbilityParticleSystem;

    /** Sistema de partículas de re-forjamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UNiagaraSystem> ReforgeParticleSystem;

    /** Material de overlay durante fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSoftObjectPtr<UMaterialInterface> FusionOverlayMaterial;

    /** Cor primária do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    FLinearColor PrimaryColor = FLinearColor::White;

    /** Cor secundária do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    FLinearColor SecondaryColor = FLinearColor::Black;

    /** Intensidade dos efeitos visuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float EffectIntensity = 1.0f;
};

/**
 * Estrutura para efeitos sonoros do Sigilo (moved before FAuracronSigiloConfiguration)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloAudioEffects
{
    GENERATED_BODY()

    /** Som de fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<USoundBase> FusionSound;

    /** Som de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<USoundBase> ActivationSound;

    /** Som de habilidade exclusiva */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<USoundBase> AbilitySound;

    /** Som de re-forjamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects")
    TSoftObjectPtr<USoundBase> ReforgeSound;

    /** Volume dos efeitos sonoros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Effects", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float EffectVolume = 1.0f;
};

/**
 * Estrutura completa de configuração de um Sigilo (moved before FAuracronSigiloConfigurationEntry)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloConfiguration
{
    GENERATED_BODY()

    /** Propriedades básicas do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloProperties Properties;

    /** Bônus passivos do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloPassiveBonuses PassiveBonuses;

    /** Habilidade exclusiva do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloExclusiveAbility ExclusiveAbility;

    /** Efeitos visuais do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloVisualEffects VisualEffects;

    /** Efeitos sonoros do Sigilo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloAudioEffects AudioEffects;

    /** GameplayEffects aplicados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    TArray<TSoftClassPtr<UGameplayEffect>> FusionGameplayEffects;

    /** GameplayTags adicionados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FGameplayTagContainer FusionTags;

    /** GameplayTags bloqueados durante a fusão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FGameplayTagContainer BlockedTags;
};

/**
 * Estrutura para salvar progressão dos sígilos
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilProgressionSaveData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    FAuracronEquippedSigil AegisSigilData;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    FAuracronEquippedSigil RuinSigilData;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    FAuracronEquippedSigil VesperSigilData;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    FDateTime SaveTimestamp;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    int32 PlayerLevel;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    int32 TotalPlayTime;

    UPROPERTY(BlueprintReadWrite, Category = "Save Data")
    int32 FusionActivationCount;

    FSigilProgressionSaveData()
    {
        SaveTimestamp = FDateTime::Now();
        PlayerLevel = 1;
        TotalPlayTime = 0;
        FusionActivationCount = 0;
    }
};

/**
 * Estrutura para métricas de performance do sistema
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilSystemPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    double TotalSessionTime = 0.0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ArchetypeGenerationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 SigilActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 Fusion20ActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    double TotalFrameTime = 0.0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int64 PeakMemoryUsage = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 NetworkBytesTransmitted = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float NetworkLatency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 FrameDropCount = 0;

    void Reset()
    {
        TotalSessionTime = 0.0;
        ArchetypeGenerationCount = 0;
        SigilActivationCount = 0;
        Fusion20ActivationCount = 0;
        TotalFrameTime = 0.0;
        PeakMemoryUsage = 0;
        NetworkBytesTransmitted = 0;
        NetworkLatency = 0.0f;
        FrameDropCount = 0;
    }
};

/**
 * Estrutura para análise de uso dos sígilos
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilUsageAnalytics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float AegisUsageFrequency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float RuinUsageFrequency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float VesperUsageFrequency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float AegisEffectiveness = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float RuinEffectiveness = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float VesperEffectiveness = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float ArchetypeWinRate = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    float FusionSuccessRate = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analytics")
    FDateTime AnalysisTimestamp;

    FSigilUsageAnalytics()
    {
        AnalysisTimestamp = FDateTime::Now();
    }
};

/**
 * Estrutura para modificadores dinâmicos de balanceamento
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilDynamicBalanceModifiers
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float AegisPowerMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float RuinPowerMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float VesperPowerMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float AegisCooldownMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float RuinCooldownMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float VesperCooldownMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float FusionPowerMultiplier = 1.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Balance")
    float FusionCooldownMultiplier = 1.0f;

    void Reset()
    {
        AegisPowerMultiplier = 1.0f;
        RuinPowerMultiplier = 1.0f;
        VesperPowerMultiplier = 1.0f;
        AegisCooldownMultiplier = 1.0f;
        RuinCooldownMultiplier = 1.0f;
        VesperCooldownMultiplier = 1.0f;
        FusionPowerMultiplier = 1.0f;
        FusionCooldownMultiplier = 1.0f;
    }
};

/**
 * Enum para severidade de erros do sistema
 */
UENUM(BlueprintType)
enum class ESigilSystemErrorSeverity : uint8
{
    Warning     UMETA(DisplayName = "Warning"),
    Error       UMETA(DisplayName = "Error"),
    Critical    UMETA(DisplayName = "Critical")
};

/**
 * Estrutura para dados de performance de combate
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FCombatPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    float Duration = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    int32 AegisActivations = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    int32 RuinActivations = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    int32 VesperActivations = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    int32 FusionActivations = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    float ArchetypePower = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    float OverallMastery = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Combat Performance")
    FDateTime CombatTimestamp;

    FCombatPerformanceData()
    {
        CombatTimestamp = FDateTime::Now();
    }
};

/**
 * Estrutura para erros do sistema
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilSystemError
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "System Error")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "System Error")
    ESigilSystemErrorSeverity Severity = ESigilSystemErrorSeverity::Warning;

    UPROPERTY(BlueprintReadOnly, Category = "System Error")
    FDateTime Timestamp;

    UPROPERTY(BlueprintReadOnly, Category = "System Error")
    FString SystemState;

    FSigilSystemError()
    {
        Timestamp = FDateTime::Now();
    }
};

/**
 * Estrutura para snapshot de telemetria
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilTelemetrySnapshot
{
    GENERATED_BODY()

    UPROPERTY()
    FDateTime Timestamp;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    double SessionTime = 0.0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 AegisActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 RuinActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 VesperActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 Fusion20ActivationCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 CurrentAegisType = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 CurrentRuinType = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 CurrentVesperType = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 AegisLevel = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 RuinLevel = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 VesperLevel = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 AegisExperience = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 RuinExperience = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 VesperExperience = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FString ArchetypeName;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    float ArchetypePowerMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FString ArchetypeCategory;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    float OverallMastery = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    float FrameRate = 60.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int64 MemoryUsage = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    float NetworkLatency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    bool bInCombat = false;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 CombatSessionCount = 0;

    FSigilTelemetrySnapshot()
    {
        Timestamp = FDateTime::Now();
    }
};

/**
 * Estrutura para dados completos de telemetria
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilSystemAdvancedTelemetryData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FString SessionID;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 PlayerID = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FDateTime SessionStartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    TArray<FSigilTelemetrySnapshot> Snapshots;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 SuccessfulTransmissions = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 FailedTransmissions = 0;

    void Reset()
    {
        SessionID.Empty();
        PlayerID = 0;
        SessionStartTime = FDateTime::Now();
        Snapshots.Empty();
        SuccessfulTransmissions = 0;
        FailedTransmissions = 0;
    }
};

/**
 * Estrutura para dados completos de telemetria
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FSigilSystemTelemetryData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FString SessionID;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 PlayerID = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    FDateTime SessionStartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    TArray<FSigilTelemetrySnapshot> Snapshots;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 SuccessfulTransmissions = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Telemetry")
    int32 FailedTransmissions = 0;

    void Reset()
    {
        SessionID.Empty();
        PlayerID = 0;
        SessionStartTime = FDateTime::Now();
        Snapshots.Empty();
        SuccessfulTransmissions = 0;
        FailedTransmissions = 0;
    }
};

/**
 * Estrutura para entrada de configuração de Sigilo (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONSIGILOSBRIDGE_API FAuracronSigiloConfigurationEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sigilo Configuration")
    FAuracronSigiloConfiguration Configuration;

    FAuracronSigiloConfigurationEntry()
    {
        SigiloType = EAuracronSigiloType::None;
    }

    FAuracronSigiloConfigurationEntry(EAuracronSigiloType InSigiloType, const FAuracronSigiloConfiguration& InConfiguration)
        : SigiloType(InSigiloType), Configuration(InConfiguration)
    {
    }

    // Production Ready: UE 5.6 compatible equality operators for TArray::Contains()
    bool operator==(const FAuracronSigiloConfigurationEntry& Other) const
    {
        return SigiloType == Other.SigiloType;
    }

    bool operator==(const EAuracronSigiloType& OtherType) const
    {
        return SigiloType == OtherType;
    }

    bool operator!=(const FAuracronSigiloConfigurationEntry& Other) const
    {
        return !(*this == Other);
    }

    bool operator!=(const EAuracronSigiloType& OtherType) const
    {
        return !(*this == OtherType);
    }
};

// FAuracronSigiloExclusiveAbility moved to before FAuracronSigiloConfiguration

// Incorrect FAuracronSigiloVisualEffects structure removed - correct definition is above
// All orphaned content completely removed

    /** Ãcone da habilidade */






    /** DuraÃ§Ã£o da habilidade (em segundos) */




    /** Ãrea de efeito (em unidades) */


// Duplicate structures removed - definitions moved to before FAuracronSigiloConfiguration





// FAuracronSigiloConfiguration moved to before FAuracronSigiloConfigurationEntry

/**
 * Classe principal do Bridge para Sistema de SÃ­gilos Auracron
 * ResponsÃ¡vel pela fusÃ£o de campeÃµes e gerenciamento de habilidades alternativas
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Sigilos", meta = (DisplayName = "AURACRON Sigilos Bridge", BlueprintSpawnableComponent))
class AURACRONSIGILOSBRIDGE_API UAuracronSigilosBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronSigilosBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Sigilo Management ===

    /**
     * Selecionar Sigilo durante a fase de seleÃ§Ã£o de campeÃµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Selection", CallInEditor)
    bool SelectSigilo(EAuracronSigiloType SigiloType);

    /**
     * Iniciar processo de fusÃ£o do Sigilo (aos 6 minutos)
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool StartSigiloFusion();

    /**
     * Completar fusÃ£o do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool CompleteSigiloFusion();

    /**
     * Re-forjar Sigilo no Nexus
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Reforge", CallInEditor)
    bool ReforgeSigilo(EAuracronSigiloType NewSigiloType);

    /**
     * Cancelar fusÃ£o em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion", CallInEditor)
    bool CancelSigiloFusion();

    // === Fusion 2.0 System ===

    /**
     * Equipar Sígilo Aegis específico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    bool EquipAegisSigil(EAuracronAegisSigilType AegisType);

    /**
     * Equipar Sígilo Ruin específico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    bool EquipRuinSigil(EAuracronRuinSigilType RuinType);

    /**
     * Equipar Sígilo Vesper específico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    bool EquipVesperSigil(EAuracronVesperSigilType VesperType);

    /**
     * Ativar Sígilo Aegis equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Activation", CallInEditor)
    bool ActivateAegisSigil();

    /**
     * Ativar Sígilo Ruin equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Activation", CallInEditor)
    bool ActivateRuinSigil();

    // Production Ready: Enhanced Input callbacks for UE 5.6
    void ActivateAegisSigilInput(const FInputActionInstance& Instance);
    void ActivateRuinSigilInput(const FInputActionInstance& Instance);
    void ActivateVesperSigilInput(const FInputActionInstance& Instance);

    /**
     * Ativar Sígilo Vesper equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Activation", CallInEditor)
    bool ActivateVesperSigil();

    /**
     * Ativar Fusão 2.0 (todos os 3 Sígilos simultaneamente)
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    bool ActivateFusion20();

    /**
     * Enhanced Input wrapper for ActivateFusion20
     */
    void ActivateFusion20Input(const FInputActionValue& Value);

    /**
     * Obter Arquétipo atual baseado na combinação equipada
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    FAuracronSigilArchetype GetCurrentArchetype() const;

    /**
     * Verificar se pode ativar Fusão 2.0
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    bool CanActivateFusion20() const;

    /**
     * Obter todos os Arquétipos disponíveis (150 combinações)
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    TArray<FAuracronSigilArchetype> GetAvailableArchetypes() const;

    /**
     * Gerar nome do Arquétipo baseado na combinação
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Fusion2.0", CallInEditor)
    FText GenerateArchetypeName(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    // === Ability Management ===

    /**
     * Ativar habilidade exclusiva do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Abilities", CallInEditor)
    bool ActivateExclusiveAbility();

    /**
     * Obter Ã¡rvore de habilidades alternativas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Abilities", CallInEditor)
    TArray<TSubclassOf<UGameplayAbility>> GetAlternativeAbilityTree() const;

    /**
     * Aplicar bÃ´nus passivos do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Bonuses", CallInEditor)
    bool ApplyPassiveBonuses();

    /**
     * Remover bÃ´nus passivos do Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Bonuses", CallInEditor)
    bool RemovePassiveBonuses();

    // === State Management ===

    /**
     * Obter estado atual da fusÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    EAuracronSigiloFusionState GetFusionState() const { return CurrentFusionState; }

    /**
     * Obter Sigilo atualmente selecionado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    EAuracronSigiloType GetSelectedSigilo() const { return SelectedSigiloType; }

    /**
     * Obter Sígilo Aegis equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    FAuracronEquippedSigil GetEquippedAegisSigil() const { return EquippedAegisSigil; }

    /**
     * Obter Sígilo Ruin equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    FAuracronEquippedSigil GetEquippedRuinSigil() const { return EquippedRuinSigil; }

    /**
     * Obter Sígilo Vesper equipado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    FAuracronEquippedSigil GetEquippedVesperSigil() const { return EquippedVesperSigil; }

    /**
     * Obter tempo restante para fusÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    float GetTimeToFusion() const;

    /**
     * Obter tempo restante de cooldown para re-forjamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    float GetReforgeCooldownRemaining() const;

    /**
     * Verificar se pode re-forjar
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|State", CallInEditor)
    bool CanReforge() const;

    // === Configuration Management ===

    /**
     * Obter configuraÃ§Ã£o de um Sigilo especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    FAuracronSigiloConfiguration GetSigiloConfiguration(EAuracronSigiloType SigiloType) const;

    /**
     * Definir configuraÃ§Ã£o de um Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    void SetSigiloConfiguration(EAuracronSigiloType SigiloType, const FAuracronSigiloConfiguration& Configuration);

    /**
     * Carregar configuraÃ§Ãµes padrÃ£o dos SÃ­gilos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Sigilos|Configuration", CallInEditor)
    bool LoadDefaultSigiloConfigurations();

    /** Métodos de progressão de sígilos - Movidos para public para acesso em testes */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Progression")
    int32 CalculateSigilLevel(int32 Experience) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Progression")
    int32 GetExperienceRequiredForLevel(int32 Level) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Progression")
    void AddSigilExperience(EAuracronSigiloType SigilType, int32 ExperienceAmount);

    // Realm integration methods
    UFUNCTION(BlueprintCallable, Category = "Auracron Sigilos")
    void RegisterRealmBonus(const struct FAuracronRealmBonus& RealmBonus);

    UFUNCTION(BlueprintCallable, Category = "Auracron Sigilos")
    void RegisterLayerVFXOverride(const FString& LayerName, class UNiagaraSystem* VFXOverride);

    UFUNCTION(BlueprintCallable, Category = "Auracron Sigilos")
    void RegisterLayerAudioOverride(const FString& LayerName, class USoundBase* AudioOverride);

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de SÃ­gilos */
    bool InitializeSigiloSystem();

    /** Aplicar GameplayEffects do Sigilo */
    bool ApplySigiloGameplayEffects(const FAuracronSigiloConfiguration& Configuration);

    /** Remover GameplayEffects do Sigilo */
    bool RemoveSigiloGameplayEffects();

    /** Atualizar efeitos visuais */
    bool UpdateVisualEffects(const FAuracronSigiloVisualEffects& VisualEffects);

    /** Reproduzir efeitos sonoros */
    bool PlayAudioEffects(const FAuracronSigiloAudioEffects& AudioEffects);

    /** Validar seleÃ§Ã£o de Sigilo */
    bool ValidateSigiloSelection(EAuracronSigiloType SigiloType) const;

    /** Processar fusÃ£o em andamento */
    void ProcessFusion(float DeltaTime);

    /** Processar cooldown de re-forjamento */
    void ProcessReforgeCooldown(float DeltaTime);

    // === Fusion 2.0 Internal Methods ===

    /** Atualizar Arquétipo atual baseado nos Sígilos equipados */
    void UpdateCurrentArchetype();

    /** Aplicar efeitos específicos dos Sígilos Aegis */
    bool ApplyAegisSigilEffects(EAuracronAegisSigilType AegisType);
    bool ApplyRuinSigilEffects(EAuracronRuinSigilType RuinType);
    bool ApplyVesperSigilEffects(EAuracronVesperSigilType VesperType);

    /** Implementações específicas dos efeitos Aegis */
    bool ApplyPrimordialAegisEffect();
    bool ApplyCristalinoAegisEffect();
    bool ApplyTemporalAegisEffect();
    bool ApplyEspectralAegisEffect();
    bool ApplyAbsolutoAegisEffect();

    /** Implementações específicas dos efeitos Ruin */
    bool ApplyFlamejanteRuinEffect();
    bool ApplyGelidoRuinEffect();
    bool ApplySombrioRuinEffect();
    bool ApplyCorrosivoRuinEffect();
    bool ApplyAniquiladorRuinEffect();

    /** Implementações específicas dos efeitos Vesper */
    bool ApplyCurativoVesperEffect();
    bool ApplyEnergeticoVesperEffect();
    bool ApplyVelocidadeVesperEffect();
    bool ApplyVisaoVesperEffect();
    bool ApplyTeleporteVesperEffect();
    bool ApplyTemporalVesperEffect();

    /** Métodos de desativação dos Sígilos */
    void DeactivateAegisSigil();
    void DeactivateRuinSigil();
    void DeactivateVesperSigil();
    void EndFusion20();

    /** Aplicar efeitos do Arquétipo */
    bool ApplyArchetypeEffects(const FAuracronSigilArchetype& Archetype);

    /** Calcular propriedades do Arquétipo */
    float CalculateArchetypePowerMultiplier() const;
    float CalculateArchetypeCooldownReduction() const;
    float CalculateArchetypeEnergyEfficiency() const;

    /** Gerar descrição do Arquétipo */
    FText GenerateArchetypeDescription(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    /** Obter nomes dos Sígilos */
    FString GetAegisSigilName(EAuracronAegisSigilType AegisType) const;
    FString GetRuinSigilName(EAuracronRuinSigilType RuinType) const;
    FString GetVesperSigilName(EAuracronVesperSigilType VesperType) const;

    /** Obter durações e cooldowns dos Sígilos */
    float GetAegisSigilDuration(EAuracronAegisSigilType AegisType) const;
    float GetAegisSigilCooldown(EAuracronAegisSigilType AegisType) const;
    float GetRuinSigilDuration(EAuracronRuinSigilType RuinType) const;
    float GetRuinSigilCooldown(EAuracronRuinSigilType RuinType) const;
    float GetVesperSigilDuration(EAuracronVesperSigilType VesperType) const;
    float GetVesperSigilCooldown(EAuracronVesperSigilType VesperType) const;

    /** Obter GameplayEffects dos Sígilos */
    TSubclassOf<UGameplayEffect> GetAegisPrimordialGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetAegisCristalinoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetAegisTemporalGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetAegisEspectralGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetAegisAbsolutoGameplayEffect() const;

    /** Efeitos visuais e sonoros */
    void SpawnAegisSigilVFX(EAuracronAegisSigilType AegisType);
    void SpawnRuinSigilVFX(EAuracronRuinSigilType RuinType);
    void SpawnVesperSigilVFX(EAuracronVesperSigilType VesperType);
    void PlayAegisSigilAudio(EAuracronAegisSigilType AegisType);
    void PlayRuinSigilAudio(EAuracronRuinSigilType RuinType);
    void PlayVesperSigilAudio(EAuracronVesperSigilType VesperType);

    /** Utilitários de cores e efeitos */
    FLinearColor BlendSigilColors() const;
    void ApplyProjectileTimeDilation();
    void GrantTemporaryInvulnerability(float Duration);

    // Realm integration methods - moved to public section

    /** Métodos auxiliares de cores */
    FLinearColor GetAegisSigilColor(EAuracronAegisSigilType AegisType) const;
    FLinearColor GetRuinSigilColor(EAuracronRuinSigilType RuinType) const;
    FLinearColor GetVesperSigilColor(EAuracronVesperSigilType VesperType) const;

    /** Métodos de aplicação de efeitos específicos */
    void ApplySpreadingFireEffect(float Radius, float DamagePerSecond);
    void ApplyIceFieldEffect(float Radius, float SlowPercentage, float FreezeChance);
    void ApplyShadowFieldEffect(float Radius, float VisionReduction, float AccuracyReduction);
    void ApplyCorrosiveAuraEffect(float Radius, float ArmorReduction, float ResistanceReduction);
    void ApplyExecutionEffect(float Radius, float ExecutionThreshold);
    void ApplyAreaHealingEffect(float Radius, float HealingPerSecond, float OverhealPercentage);
    void ApplyEnergyFieldEffect(float Radius, float ManaPerSecond, float CostReduction);
    void ApplyTimeManipulationField(float Radius, float TimeAcceleration, float TimeDeceleration);
    void ApplyTrueSightEffect(float Radius);

    /** Métodos de concessão de bônus */
    void GrantStealthBonuses();
    void GrantArmorPenetrationBonus(float Bonus);
    void GrantCriticalDamageBonus(float Bonus);
    void GrantHealingReceivedBonus(float Bonus);
    void GrantMaxManaBonus(float Bonus);
    void GrantCooldownRecoveryBonus(float Bonus);
    void GrantDodgeChanceBonus(float Bonus);
    void GrantDashCharges(int32 Charges);
    void GrantTeleportCharges(int32 Charges, float Range, float ChargeCooldown);

    /** Métodos de detecção de alvos */
    bool IsAllyTarget(AActor* Target) const;
    bool IsEnemyTarget(AActor* Target) const;

    /** Métodos de gerenciamento de efeitos visuais */
    void ApplyArchetypeVisualEffects(const FAuracronSigilArchetype& Archetype);
    void RemoveArchetypeVisualEffects();
    void ApplyFusion20VisualEffects();
    void RemoveFusion20VisualEffects();
    void RemoveAegisSigilVFX();
    void RemoveRuinSigilVFX();
    void RemoveVesperSigilVFX();

    /** Métodos de gerenciamento de arquétipo */
    void RemoveArchetypeEffects();
    FString GetArchetypeCategory(const FAuracronSigilArchetype& Archetype) const;

    /** Métodos auxiliares de cálculo */
    float CalculateSynergyBonus() const;

    /** Métodos de armazenamento de parâmetros */
    void StoreTeleportParameters(FGameplayAbilitySpecHandle SpecHandle, float Range, float ChargeCooldown);

    /** Métodos de melhoria de capacidades */
    void EnhanceVisionRange(float VisionRangeBonus);
    void ApplyWindTrailEffect();

    /** Métodos de geração de arquétipo */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Archetype")
    FAuracronSigilArchetype GenerateArchetype(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Archetype")
    int32 CalculateArchetypeRequiredLevel(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Archetype")
    bool IsLegendaryArchetypeCombination(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    /** Métodos de obtenção de assets */
    TSoftObjectPtr<UNiagaraSystem> GetArchetypeVFXAsset(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;
    TSoftObjectPtr<UMetaSoundSource> GetArchetypeAudioAsset(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;
    TSubclassOf<UGameplayAbility> GetArchetypeFusionAbility(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;
    TArray<TSubclassOf<UGameplayEffect>> GetArchetypePassiveEffects(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType) const;

    /** Métodos de obtenção de GameplayEffects específicos */
    TSubclassOf<UGameplayEffect> GetRuinFlamejanteGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetRuinGelidoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetRuinSombrioGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetRuinCorrosivoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetRuinAniquiladorGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperCurativoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperEnergeticoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperVelocidadeGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperVisaoGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperTeleporteGameplayEffect() const;
    TSubclassOf<UGameplayEffect> GetVesperTemporalGameplayEffect() const;

    /** Métodos de descrição */
    FString GetAegisSigilDescription(EAuracronAegisSigilType AegisType) const;
    FString GetRuinSigilDescription(EAuracronRuinSigilType RuinType) const;
    FString GetVesperSigilDescription(EAuracronVesperSigilType VesperType) const;

    /** Métodos de progressão e milestone */
    void ApplySigilLevelUpBonuses(EAuracronSigiloType SigilType, int32 NewLevel);
    void UnlockMilestoneAbility(EAuracronSigiloType SigilType, int32 Level);
    bool HasAllSigilsEquipped() const;

    /** Métodos de persistência */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Save")
    void SaveSigilProgression();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Save")
    void LoadSigilProgression();

    /** Métodos de monitoramento de performance */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Performance")
    void StartPerformanceMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Performance")
    void StopPerformanceMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Performance")
    FSigilSystemPerformanceMetrics GetPerformanceMetrics() const { return PerformanceMetrics; }

    /** Métodos internos de performance */
    void UpdatePerformanceMetrics();
    void LogPerformanceSummary() const;

    /** Métodos de analytics e otimização */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Analytics")
    void AnalyzeUsagePatterns();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Analytics")
    FSigilUsageAnalytics GetCurrentUsageAnalytics() const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Balance")
    FSigilDynamicBalanceModifiers GetDynamicBalanceModifiers() const { return DynamicBalanceModifiers; }

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Balance")
    void ResetDynamicBalance();

    /** Métodos internos de analytics */
    float CalculateSigilUsageFrequency(EAuracronSigiloType SigilType) const;
    float CalculateSigilEffectiveness(EAuracronSigiloType SigilType) const;
    float CalculateArchetypeWinRate() const;
    float CalculateFusionSuccessRate() const;
    void OptimizeSigilBalance();
    void ApplyDynamicBalanceAdjustments(const FSigilUsageAnalytics& Analytics);

    /** Métodos de sistema de maestria */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Mastery")
    void UpdateSigilMastery();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Mastery")
    float GetSigilMastery(EAuracronSigiloType SigilType) const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Mastery")
    float GetOverallMastery() const;

    /** Métodos internos de maestria */
    float CalculateIndividualSigilMastery(EAuracronSigiloType SigilType) const;
    void ApplyMasteryBonuses(float MasteryLevel);

    /** Métodos de validação e diagnóstico */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Diagnostics")
    bool ValidateSystemIntegrity() const;

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Diagnostics")
    void RunDiagnostics();

    /** Métodos de integração com combate */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Combat")
    void OnCombatStart();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Combat")
    void OnCombatEnd();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Combat")
    bool IsInCombat() const { return bInCombat; }

    /** Métodos internos de combate */
    void ApplyCombatModifiers();
    void RemoveCombatModifiers();
    void AnalyzeCombatPerformance(float CombatDuration);
    void AwardCombatExperience(const FCombatPerformanceData& CombatData);

    /** Métodos de otimização de rede */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Network")
    void OptimizeNetworkReplication();

    void CompressReplicationData();
    void DecompressReplicationData();

    /** Métodos de tratamento de erros */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Error")
    void HandleSystemError(const FString& ErrorMessage, ESigilSystemErrorSeverity Severity);

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Error")
    TArray<FSigilSystemError> GetSystemErrorHistory() const { return SystemErrorHistory; }

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Error")
    int32 GetSystemErrorCount() const { return SystemErrorCount; }

    /** Métodos internos de recuperação */
    void AttemptSystemRecovery();
    void EmergencySystemShutdown();
    void ClearAllActiveEffects();
    void ResetToSafeState();
    FString GetCurrentSystemState() const;
    void RemoveAllVisualEffects();

    /** Métodos de gerenciamento de assets */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Assets")
    void PreloadCriticalAssets();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Assets")
    void UnloadUnusedAssets();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Assets")
    bool AreCriticalAssetsLoaded() const { return bCriticalAssetsLoaded; }

    /** Métodos internos de assets */
    void OnCriticalAssetsLoaded();
    bool IsAssetCurrentlyNeeded(const FSoftObjectPath& AssetPath) const;

    /** Métodos de cooldown avançado */
    void UpdateSigilCooldownsWithBalance(float DeltaTime);

    /** Métodos de telemetria */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Telemetry")
    void InitializeTelemetrySystem();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Telemetry")
    void EnableTelemetry(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Telemetry")
    void SetTelemetryEndpoint(const FString& Endpoint);

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Telemetry")
    FSigilSystemTelemetryData GetTelemetryData() const;

    /** Métodos internos de telemetria */
    void CollectTelemetryData();
    void SendTelemetryToAnalyticsService(const FSigilTelemetrySnapshot& Snapshot);
    void OnTelemetryRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful);

    /** Métodos de integração final */
    UFUNCTION(BlueprintCallable, Category = "Sigil System|Integration")
    void IntegrateWithGameMode();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Integration")
    void FinalizeSystemInitialization();

    UFUNCTION(BlueprintCallable, Category = "Sigil System|Integration")
    bool IsSystemFullyInitialized() const { return bSystemFullyInitialized; }

    /** Métodos internos de integração */
    void BindSigilInputActions(UEnhancedInputComponent* InputComponent);

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes dos SÃ­gilos disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronSigiloConfigurationEntry> SigiloConfigurations;

    /** Sigilo atualmente selecionado */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_SelectedSigiloType)
    EAuracronSigiloType SelectedSigiloType = EAuracronSigiloType::None;

    /** Estado atual da fusÃ£o */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_FusionState)
    EAuracronSigiloFusionState CurrentFusionState = EAuracronSigiloFusionState::Inactive;

    /** Tempo de jogo quando a fusÃ£o foi iniciada */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float FusionStartTime = 0.0f;

    /** Tempo quando o Ãºltimo re-forjamento foi feito */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float LastReforgeTime = -1000.0f;

    /** Componente de timeline para animaÃ§Ãµes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UTimelineComponent> FusionTimeline;

    /** ReferÃªncia ao AbilitySystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    // === Fusion 2.0 Properties ===

    /** Sígilo Aegis atualmente equipado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0", ReplicatedUsing = OnRep_EquippedSigils)
    FAuracronEquippedSigil EquippedAegisSigil;

    /** Sígilo Ruin atualmente equipado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0", ReplicatedUsing = OnRep_EquippedSigils)
    FAuracronEquippedSigil EquippedRuinSigil;

    /** Sígilo Vesper atualmente equipado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0", ReplicatedUsing = OnRep_EquippedSigils)
    FAuracronEquippedSigil EquippedVesperSigil;

    /** Arquétipo atual baseado na combinação */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Fusion 2.0", ReplicatedUsing = OnRep_CurrentArchetype)
    FAuracronSigilArchetype CurrentArchetype;

    /** Todos os Arquétipos disponíveis (150 combinações) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0")
    TArray<FAuracronSigilArchetype> AvailableArchetypes;

    /** Se a Fusão 2.0 está ativa */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Fusion 2.0", ReplicatedUsing = OnRep_Fusion20Active)
    bool bFusion20Active = false;

    /** Tempo quando a Fusão 2.0 foi ativada */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Fusion 2.0", Replicated)
    float Fusion20StartTime = 0.0f;

    /** Duração da Fusão 2.0 (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0", meta = (ClampMin = "5.0", ClampMax = "30.0"))
    float Fusion20Duration = 15.0f;

    /** Cooldown da Fusão 2.0 (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fusion 2.0", meta = (ClampMin = "30.0", ClampMax = "180.0"))
    float Fusion20Cooldown = 90.0f;

    /** Último tempo que a Fusão 2.0 foi usada */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Fusion 2.0", Replicated)
    float LastFusion20Time = -1000.0f;

private:
    // === Internal State ===

    /** GameplayEffects ativos do Sigilo */
    TArray<FActiveGameplayEffectHandle> ActiveSigiloEffects;

    /** Timer handles para funcionalidades temporárias */
    FTimerHandle AegisShieldTimerHandle;
    FTimerHandle RuinDamageTimerHandle;
    FTimerHandle VesperVisionTimerHandle;
    FTimerHandle TeleportCooldownTimerHandle;

    /** GameplayEffects passivos ativos */
    TArray<FActiveGameplayEffectHandle> ActivePassiveEffects;

    /** Componentes de efeitos visuais */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveVisualEffects;

    /** Componentes de Ã¡udio */
    TArray<TObjectPtr<UAudioComponent>> ActiveAudioComponents;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** FusÃ£o disponÃ­vel (apÃ³s 6 minutos) */
    bool bFusionAvailable = false;

    /** Timer para verificaÃ§Ãµes periÃ³dicas */
    FTimerHandle SystemUpdateTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection SigiloMutex;

    // === Fusion 2.0 Internal State ===

    /** Handles ativos dos efeitos dos Sígilos equipados */
    TArray<FActiveGameplayEffectHandle> ActiveAegisSigilEffects;
    TArray<FActiveGameplayEffectHandle> ActiveRuinSigilEffects;
    TArray<FActiveGameplayEffectHandle> ActiveVesperSigilEffects;
    TArray<FActiveGameplayEffectHandle> ActiveArchetypeEffects;

    /** Componentes visuais ativos por tipo de Sígilo */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveAegisSigilVFX;
    TArray<TObjectPtr<UNiagaraComponent>> ActiveRuinSigilVFX;
    TArray<TObjectPtr<UNiagaraComponent>> ActiveVesperSigilVFX;
    TArray<TObjectPtr<UNiagaraComponent>> ActiveArchetypeVFX;

    /** Componentes de áudio ativos por tipo de Sígilo */
    TArray<TObjectPtr<UAudioComponent>> ActiveAegisSigilAudio;
    TArray<TObjectPtr<UAudioComponent>> ActiveRuinSigilAudio;
    TArray<TObjectPtr<UAudioComponent>> ActiveVesperSigilAudio;

    /** Componentes individuais do Arquétipo e Fusion 2.0 */
    TWeakObjectPtr<UAudioComponent> ActiveArchetypeAudio;
    TWeakObjectPtr<UNiagaraComponent> ActiveFusion20VFX;
    TWeakObjectPtr<UAudioComponent> ActiveFusion20Audio;

    /** Handle da habilidade de fusão do arquétipo */
    FGameplayAbilitySpecHandle ActiveArchetypeFusionAbility;

    /** Efeitos permanentes de progressão de nível */
    TArray<FActiveGameplayEffectHandle> PermanentAegisLevelEffects;

    TArray<FActiveGameplayEffectHandle> PermanentRuinLevelEffects;

    TArray<FActiveGameplayEffectHandle> PermanentVesperLevelEffects;

    /** Habilidades milestone desbloqueadas */
    TArray<FGameplayAbilitySpecHandle> AegisMilestoneAbilities;

    TArray<FGameplayAbilitySpecHandle> RuinMilestoneAbilities;

    TArray<FGameplayAbilitySpecHandle> VesperMilestoneAbilities;

    /** Sistema de monitoramento de performance */
    FSigilSystemPerformanceMetrics PerformanceMetrics;

    bool PerformanceMonitoringActive = false;

    double PerformanceStartTime = 0.0;

    FTimerHandle PerformanceMonitoringTimer;

    /** Sistema de analytics e balanceamento dinâmico */
    TArray<FSigilUsageAnalytics> UsageAnalyticsHistory;

    FSigilDynamicBalanceModifiers DynamicBalanceModifiers;

    /** Contadores de ativação para analytics */
    int32 AegisActivationCount = 0;

    int32 RuinActivationCount = 0;

    int32 VesperActivationCount = 0;

    /** Sistema de maestria dos sígilos */
    UPROPERTY()
    FActiveGameplayEffectHandle ActiveMasteryEffect;

    /** Sistema de combate e performance */
    UPROPERTY()
    bool bInCombat = false;

    UPROPERTY()
    float CombatStartTime = 0.0f;

    UPROPERTY()
    FActiveGameplayEffectHandle ActiveCombatModifierEffect;

    UPROPERTY()
    TArray<FCombatPerformanceData> CombatPerformanceHistory;

    /** Sistema de tratamento de erros */
    UPROPERTY()
    TArray<FSigilSystemError> SystemErrorHistory;

    UPROPERTY()
    int32 SystemErrorCount = 0;

    /** Dados de replicação comprimidos */
    UPROPERTY(Replicated)
    uint8 CompressedSigilState = 0;

    UPROPERTY(Replicated)
    uint16 CompressedAegisCooldown = 0;

    UPROPERTY(Replicated)
    uint16 CompressedRuinCooldown = 0;

    UPROPERTY(Replicated)
    uint16 CompressedVesperCooldown = 0;

    UPROPERTY(Replicated)
    uint16 CompressedFusion20Cooldown = 0;

    /** Sistema de carregamento de assets */
    UPROPERTY()
    bool bCriticalAssetsLoaded = false;

    /** Sistema de telemetria */
    UPROPERTY()
    bool bTelemetryEnabled = false;

    UPROPERTY()
    double TelemetryStartTime = 0.0;

    UPROPERTY()
    FTimerHandle TelemetryCollectionTimer;

    UPROPERTY()
    FString TelemetryEndpoint;

    UPROPERTY()
    FSigilSystemTelemetryData TelemetryData;

    /** Estado de inicialização completa */
    UPROPERTY()
    bool bSystemFullyInitialized = false;

    /** Cache de Arquétipos para performance */
    mutable TMap<FString, FAuracronSigilArchetype> ArchetypeCache;

    /** Timer para duração da Fusão 2.0 */
    FTimerHandle Fusion20DurationTimer;

    /** Timer para cooldown da Fusão 2.0 */
    FTimerHandle Fusion20CooldownTimer;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_SelectedSigiloType();

    UFUNCTION()
    void OnRep_FusionState();

    UFUNCTION()
    void OnRep_EquippedSigils();

    UFUNCTION()
    void OnRep_CurrentArchetype();

    UFUNCTION()
    void OnRep_Fusion20Active();

public:
    // === Delegates ===

    /** Delegate chamado quando Sigilo Ã© selecionado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigiloSelected, EAuracronSigiloType, SigiloType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnSigiloSelected OnSigiloSelected;

    /** Delegate chamado quando fusÃ£o Ã© iniciada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnFusionStarted);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnFusionStarted OnFusionStarted;

    /** Delegate chamado quando fusÃ£o Ã© completada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFusionCompleted, EAuracronSigiloType, SigiloType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnFusionCompleted OnFusionCompleted;

    /** Delegate chamado quando Sigilo Ã© re-forjado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigiloReforged, EAuracronSigiloType, OldSigilo, EAuracronSigiloType, NewSigilo);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Events")
    FOnSigiloReforged OnSigiloReforged;

    // === Fusion 2.0 Delegates ===

    /** Delegate chamado quando Sígilo individual é equipado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilEquipped, EAuracronSigiloType, SigiloType, int32, SubtypeIndex, int32, Level);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnSigilEquipped OnSigilEquipped;

    /** Delegate chamado quando Sígilo individual é ativado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSigilActivated, EAuracronSigiloType, SigiloType, float, Duration);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnSigilActivated OnSigilActivated;

    /** Delegate chamado quando Arquétipo é formado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnArchetypeFormed, const FAuracronSigilArchetype&, Archetype);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnArchetypeFormed OnArchetypeFormed;

    /** Delegate chamado quando Fusão 2.0 é ativada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFusion20Activated, const FAuracronSigilArchetype&, Archetype, float, Duration);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnFusion20Activated OnFusion20Activated;

    /** Delegate chamado quando Fusão 2.0 termina */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFusion20Ended, const FAuracronSigilArchetype&, Archetype);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnFusion20Ended OnFusion20Ended;

    /** Delegate chamado quando Sígilo ganha experiência */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilExperienceGained, EAuracronSigiloType, SigiloType, int32, ExperienceGained, int32, NewLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Fusion2.0 Events")
    FOnSigilExperienceGained OnSigilExperienceGained;

    /** Delegates para sistema de progressão */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSigilLevelUp, EAuracronSigiloType, SigiloType, int32, NewLevel, int32, TotalExperience);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Progression Events")
    FOnSigilLevelUp OnSigilLevelUp;

    /** Delegates para cooldowns */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnAegisSigilCooldownComplete);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Cooldown Events")
    FOnAegisSigilCooldownComplete OnAegisSigilCooldownComplete;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRuinSigilCooldownComplete);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Cooldown Events")
    FOnRuinSigilCooldownComplete OnRuinSigilCooldownComplete;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnVesperSigilCooldownComplete);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Cooldown Events")
    FOnVesperSigilCooldownComplete OnVesperSigilCooldownComplete;

    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnFusion20CooldownComplete);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Cooldown Events")
    FOnFusion20CooldownComplete OnFusion20CooldownComplete;

    /** Delegate para carregamento de assets */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnCriticalAssetsLoadComplete);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|Asset Events")
    FOnCriticalAssetsLoadComplete OnCriticalAssetsLoadComplete;

    /** Delegate para inicialização completa do sistema */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnSystemFullyInitialized);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Sigilos|System Events")
    FOnSystemFullyInitialized OnSystemFullyInitialized;
};

