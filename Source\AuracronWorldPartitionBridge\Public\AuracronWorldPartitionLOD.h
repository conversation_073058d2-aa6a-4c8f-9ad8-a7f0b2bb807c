// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition LOD Integration Header
// Bridge 3.6: World Partition - LOD Integration

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// LOD includes for UE5.6
// UE 5.6 Compatible - HierarchicalLODVolume.h removed, using WorldPartition LOD system
#include "WorldPartition/HLOD/HLODLayer.h"
#include "Engine/LODActor.h"
// #include "Developer/HierarchicalLODUtilities/Public/HierarchicalLODUtilities.h" // Commented due to API issues in UE 5.6
// #include "Developer/HierarchicalLODUtilities/Public/HierarchicalLODUtilitiesModule.h" // Commented due to API issues in UE 5.6
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionLOD.generated.h"

// Forward declarations
class UAuracronWorldPartitionLODManager;
class UStaticMesh;
class ALODActor;

// =============================================================================
// LOD TYPES AND ENUMS
// =============================================================================

// LOD generation states
UENUM(BlueprintType)
enum class EAuracronLODGenerationState : uint8
{
    NotGenerated            UMETA(DisplayName = "Not Generated"),
    Generating              UMETA(DisplayName = "Generating"),
    Generated               UMETA(DisplayName = "Generated"),
    Failed                  UMETA(DisplayName = "Failed"),
    Outdated                UMETA(DisplayName = "Outdated")
};

// LOD types
UENUM(BlueprintType)
enum class EAuracronLODType : uint8
{
    StaticMesh              UMETA(DisplayName = "Static Mesh"),
    HLOD                    UMETA(DisplayName = "Hierarchical LOD"),
    Nanite                  UMETA(DisplayName = "Nanite"),
    Impostor                UMETA(DisplayName = "Impostor"),
    Billboard               UMETA(DisplayName = "Billboard")
};

// LOD quality levels
UENUM(BlueprintType)
enum class EAuracronLODQuality : uint8
{
    Lowest                  UMETA(DisplayName = "Lowest"),
    Low                     UMETA(DisplayName = "Low"),
    Medium                  UMETA(DisplayName = "Medium"),
    High                    UMETA(DisplayName = "High"),
    Highest                 UMETA(DisplayName = "Highest"),
    Lossless                UMETA(DisplayName = "Lossless")
};

// LOD transition types
UENUM(BlueprintType)
enum class EAuracronWorldPartitionLODTransitionType : uint8
{
    Instant                 UMETA(DisplayName = "Instant"),
    Fade                    UMETA(DisplayName = "Fade"),
    Dither                  UMETA(DisplayName = "Dither"),
    Smooth                  UMETA(DisplayName = "Smooth"),
    Custom                  UMETA(DisplayName = "Custom")
};

/**
 * Culling Types - Production Ready
 * Defines different culling methods for LOD system
 */
UENUM(BlueprintType)
enum class EAuracronCullingType : uint8
{
    None        UMETA(DisplayName = "None"),
    Distance    UMETA(DisplayName = "Distance"),
    Frustum     UMETA(DisplayName = "Frustum"),
    Occlusion   UMETA(DisplayName = "Occlusion"),
    Combined    UMETA(DisplayName = "Combined")
};

/**
 * LOD Transition Types - Production Ready
 * Defines different transition methods between LOD levels for foliage
 */
UENUM(BlueprintType)
enum class EAuracronLODTransitionType : uint8
{
    Instant     UMETA(DisplayName = "Instant"),
    Fade        UMETA(DisplayName = "Fade"),
    Crossfade   UMETA(DisplayName = "Crossfade"),
    Dither      UMETA(DisplayName = "Dither"),
    Smooth      UMETA(DisplayName = "Smooth")
};

/**
 * Impostor Types - Production Ready
 * Defines different types of impostors for foliage rendering
 */
UENUM(BlueprintType)
enum class EAuracronImpostorType : uint8
{
    FullSphere          UMETA(DisplayName = "Full Sphere"),
    UpperHemisphere     UMETA(DisplayName = "Upper Hemisphere"),
    TraditionalBillboard UMETA(DisplayName = "Traditional Billboard"),
    Octahedral          UMETA(DisplayName = "Octahedral"),
    Custom              UMETA(DisplayName = "Custom")
};

// =============================================================================
// LOD DATA STRUCTURES
// =============================================================================

/**
 * Impostor Data - Production Ready
 * Configuration for impostor generation
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronImpostorData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 TextureResolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 FramesXY = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float CaptureDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bUseAlphaChannel = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bGenerateNormalMap = true;

    // Additional members needed for production-ready impostor system
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FString ImpostorId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UStaticMesh> SourceMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UStaticMesh> ImpostorMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TSoftObjectPtr<UMaterialInterface> ImpostorMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    EAuracronImpostorType ImpostorType = EAuracronImpostorType::UpperHemisphere;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 Resolution = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bCaptureUsingGBuffer = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bOrthographic = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float CameraDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 SceneCaptureResolution = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TArray<FString> ColorMapsToRender;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    TMap<FString, FString> ChannelPackedMasks;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantSpecular = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantRoughness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    float ConstantOpacity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FLinearColor SubsurfaceColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FDateTime GenerationTime;

    FAuracronImpostorData()
    {
        TextureResolution = 1024;
        FramesXY = 16;
        CaptureDistance = 1000.0f;
        bUseAlphaChannel = true;
        bGenerateNormalMap = true;

        // Initialize additional members - Production Ready
        ImpostorType = EAuracronImpostorType::UpperHemisphere;
        Resolution = 2048;
        bCaptureUsingGBuffer = true;
        bOrthographic = true;
        CameraDistance = 1000.0f;
        SceneCaptureResolution = 512;
        ColorMapsToRender = {"BaseColor", "Normal"};
        ConstantSpecular = 0.5f;
        ConstantRoughness = 0.5f;
        ConstantOpacity = 1.0f;
        SubsurfaceColor = FLinearColor::White;
        bIsGenerated = false;
        GenerationTime = FDateTime::Now();
    }
};

// =============================================================================
// LOD CONFIGURATION
// =============================================================================

/**
 * LOD Configuration
 * Configuration settings for LOD management in world partition
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLODConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableLODSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableHLODGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableAutomaticLODTransitions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableNaniteSupport = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float BaseLODDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float LODDistanceMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float MaxLODDistance = 50000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronLODQuality DefaultLODQuality = EAuracronLODQuality::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float MeshSimplificationRatio = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 MaxLODLevels = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentLODOperations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODGenerationTimeout = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    EAuracronWorldPartitionLODTransitionType TransitionType = EAuracronWorldPartitionLODTransitionType::Fade;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    float TransitionDuration = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MaxLODMemoryUsageMB = 512.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableLODCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableLODDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogLODOperations = false;

    // Compatibility fields for legacy code
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Legacy")
    bool bEnableLOD = true;

    // Additional LOD Distance Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float LOD0Distance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float LOD1Distance = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float LOD2Distance = 4000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance")
    float LOD3Distance = 8000.0f;

    // Culling Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float StartCullDistance = 6000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float EndCullDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    EAuracronCullingType CullingType = EAuracronCullingType::Combined;

    // Performance Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxLODUpdatesPerFrame = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableGPUCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableHISMOptimization = true;

    // Transition Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    float FadeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    EAuracronLODTransitionType LODTransitionType = EAuracronLODTransitionType::Fade;

    // Frustum Culling Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum")
    float FrustumFOV = 90.0f;

    // Impostor Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    bool bEnableImpostorGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 ImpostorResolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    int32 ImpostorFramesXY = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impostor")
    FAuracronImpostorData ImpostorSettings;

    // Billboard Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    bool bEnableBillboardGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Billboard")
    int32 BillboardResolution = 512;

    // HISM Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM")
    int32 MaxInstancesPerCluster = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HISM")
    float ClusterRadius = 2000.0f;

    // HLOD Settings - Production Ready
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HLOD")
    float HLODMeshSimplificationRatio = 0.3f;

    FAuracronLODConfiguration()
    {
        bEnableLODSystem = true;
        bEnableHLODGeneration = true;
        bEnableAutomaticLODTransitions = true;
        bEnableNaniteSupport = true;
        BaseLODDistance = 1000.0f;
        LODDistanceMultiplier = 2.0f;
        MaxLODDistance = 50000.0f;
        DefaultLODQuality = EAuracronLODQuality::Medium;
        MeshSimplificationRatio = 0.5f;
        MaxLODLevels = 5;
        MaxConcurrentLODOperations = 4;
        LODGenerationTimeout = 30.0f;
        TransitionType = EAuracronWorldPartitionLODTransitionType::Fade;
        TransitionDuration = 0.5f;
        MaxLODMemoryUsageMB = 512.0f;
        bEnableLODCaching = true;
        bEnableLODDebug = false;
        bLogLODOperations = false;
        bEnableLOD = true;

        // Initialize additional members - Production Ready
        LOD0Distance = 1000.0f;
        LOD1Distance = 2000.0f;
        LOD2Distance = 4000.0f;
        LOD3Distance = 8000.0f;
        StartCullDistance = 6000.0f;
        EndCullDistance = 10000.0f;
        CullingType = EAuracronCullingType::Combined;
        LODUpdateInterval = 0.1f;
        MaxLODUpdatesPerFrame = 10;
        bEnablePerformanceMonitoring = true;
        bEnableGPUCulling = true;
        bEnableHISMOptimization = true;
        FadeMultiplier = 1.0f;
        LODTransitionType = EAuracronLODTransitionType::Fade;
        FrustumFOV = 90.0f;
        bEnableImpostorGeneration = true;
        ImpostorResolution = 1024;
        ImpostorFramesXY = 16;
        bEnableBillboardGeneration = true;
        BillboardResolution = 512;
        MaxInstancesPerCluster = 1000;
        ClusterRadius = 2000.0f;
        HLODMeshSimplificationRatio = 0.3f;
    }
};

/**
 * World Partition LOD Configuration
 * Specific configuration for World Partition LOD management
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronWorldPartitionLODConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    FAuracronLODConfiguration BaseLODConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    bool bEnableWorldPartitionLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float CellLODDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    int32 MaxActiveLODCells = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float BaseLODDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float LODDistanceMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    int32 MaxLODLevels = 4;

    // Additional fields for compatibility
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    EAuracronLODQuality DefaultLODQuality = EAuracronLODQuality::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float MeshSimplificationRatio = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    bool bEnableAutomaticLODTransitions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    bool bEnableHLODGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    bool bEnableLODDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float MaxLODDistance = 50000.0f;

    // Additional fields for compatibility with implementation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    int32 MaxConcurrentLODOperations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float LODGenerationTimeout = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float TransitionDuration = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition LOD")
    float MaxLODMemoryUsageMB = 1024.0f;

    FAuracronWorldPartitionLODConfiguration()
    {
        bEnableWorldPartitionLOD = true;
        CellLODDistance = 5000.0f;
        MaxActiveLODCells = 100;
        BaseLODDistance = 1000.0f;
        LODDistanceMultiplier = 2.0f;
        MaxLODLevels = 4;
        DefaultLODQuality = EAuracronLODQuality::Medium;
        MeshSimplificationRatio = 0.5f;
        bEnableAutomaticLODTransitions = true;
        bEnableHLODGeneration = true;
        bEnableLODDebug = false;
        MaxLODDistance = 50000.0f;
        MaxConcurrentLODOperations = 4;
        LODGenerationTimeout = 30.0f;
        TransitionDuration = 1.0f;
        MaxLODMemoryUsageMB = 1024.0f;
    }
};

// =============================================================================
// LOD DESCRIPTOR
// =============================================================================

/**
 * LOD Descriptor
 * Descriptor for LOD levels and their properties
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLODDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FString LODId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FString LODName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    EAuracronLODType LODType = EAuracronLODType::StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    int32 LODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    float LODDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    EAuracronLODQuality Quality = EAuracronLODQuality::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    EAuracronLODGenerationState GenerationState = EAuracronLODGenerationState::NotGenerated;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    float SimplificationRatio = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    int32 TriangleCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    int32 VertexCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TArray<FString> SourceActorIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FBox Bounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    FDateTime LastUpdateTime;

    // Additional fields for impostor and mesh generation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TObjectPtr<UTextureRenderTarget2D> ImpostorTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TObjectPtr<UMaterialInterface> ImpostorMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TObjectPtr<UStaticMesh> SimplifiedMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TObjectPtr<UStaticMesh> GeneratedMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Descriptor")
    TArray<TObjectPtr<UMaterialInterface>> GeneratedMaterials;

    FAuracronLODDescriptor()
    {
        LODType = EAuracronLODType::StaticMesh;
        LODLevel = 0;
        LODDistance = 1000.0f;
        Quality = EAuracronLODQuality::Medium;
        GenerationState = EAuracronLODGenerationState::NotGenerated;
        SimplificationRatio = 0.5f;
        TriangleCount = 0;
        VertexCount = 0;
        MemoryUsageMB = 0.0f;
        bIsGenerated = false;
        bIsActive = false;
        CreationTime = FDateTime::Now();
        LastUpdateTime = CreationTime;
        ImpostorTexture = nullptr;
        ImpostorMaterial = nullptr;
        SimplifiedMesh = nullptr;
        GeneratedMesh = nullptr;
    }
};

// =============================================================================
// HLOD GENERATION PARAMETERS
// =============================================================================

/**
 * HLOD Generation Parameters
 * Parameters for HLOD generation process
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronHLODGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    TArray<FString> SourceActorIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    EAuracronLODQuality TargetQuality = EAuracronLODQuality::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float SimplificationRatio = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 TargetTriangleCount = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bMergeMaterials = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bGenerateCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bOptimizeForDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FVector2D TextureResolution = FVector2D(512, 512);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bUseNanite = false;

    // Additional fields for HLOD generation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float ClusterRadius = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float TransitionScreenSize = 0.25f;

    FAuracronHLODGenerationParameters()
    {
        TargetQuality = EAuracronLODQuality::Medium;
        SimplificationRatio = 0.5f;
        TargetTriangleCount = 1000;
        bMergeMaterials = true;
        bGenerateCollision = false;
        bOptimizeForDistance = true;
        TextureResolution = FVector2D(512, 512);
        bUseNanite = false;
        ClusterRadius = 2000.0f;
        TransitionScreenSize = 0.25f;
    }
};

// =============================================================================
// LOD STATISTICS
// =============================================================================

/**
 * LOD Statistics
 * Performance and usage statistics for LOD system
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLODStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalLODs = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 GeneratedLODs = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 ActiveLODs = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 HLODCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageGenerationTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LODTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedGenerations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float LODEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TriangleReduction = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronLODStatistics()
    {
        TotalLODs = 0;
        GeneratedLODs = 0;
        ActiveLODs = 0;
        HLODCount = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageGenerationTime = 0.0f;
        LODTransitions = 0;
        FailedGenerations = 0;
        LODEfficiency = 0.0f;
        TriangleReduction = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION LOD MANAGER
// =============================================================================

/**
 * World Partition LOD Manager
 * Central manager for LOD operations in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionLODManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    static UAuracronWorldPartitionLODManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Initialize(const FAuracronLODConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void Tick(float DeltaTime);

    // LOD creation and management
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FString CreateLOD(const FString& SourceActorId, int32 LODLevel, EAuracronLODType LODType = EAuracronLODType::StaticMesh);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool RemoveLOD(const FString& LODId);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODDescriptor GetLODDescriptor(const FString& LODId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODDescriptor> GetAllLODs() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FString> GetLODIds() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool DoesLODExist(const FString& LODId) const;

    // HLOD generation
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FString GenerateHLOD(const FAuracronHLODGenerationParameters& Parameters);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool GenerateHLODForCell(const FString& CellId, const FAuracronHLODGenerationParameters& Parameters);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool GenerateHLODForActors(const TArray<FString>& ActorIds, const FAuracronHLODGenerationParameters& Parameters);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    EAuracronLODGenerationState GetHLODGenerationState(const FString& LODId) const;

    // LOD transitions
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool SetLODLevel(const FString& ActorId, int32 LODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 GetCurrentLODLevel(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool TransitionToLOD(const FString& ActorId, int32 TargetLODLevel, float TransitionTime = -1.0f);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateLODTransitions(float DeltaTime);

    // Distance-based LOD
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void UpdateDistanceBasedLODs(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 CalculateLODLevelForDistance(float Distance) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float GetLODDistanceForLevel(int32 LODLevel) const;

    // Mesh simplification
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool SimplifyMesh(const FString& ActorId, float SimplificationRatio, int32 TargetLODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool SimplifyMeshToTriangleCount(const FString& ActorId, int32 TargetTriangleCount, int32 TargetLODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool GenerateImpostorLOD(const FString& ActorId, const FVector2D& TextureResolution = FVector2D(512, 512));

    // LOD queries
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODDescriptor> GetLODsForActor(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODDescriptor> GetLODsInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODDescriptor> GetLODsByType(EAuracronLODType LODType) const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    TArray<FAuracronLODDescriptor> GetActiveLODs() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void SetConfiguration(const FAuracronLODConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronWorldPartitionLODConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    FAuracronLODStatistics GetLODStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 GetTotalLODCount() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 GetGeneratedLODCount() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void EnableLODDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    bool IsLODDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void LogLODState() const;

    // Utility functions for triangle and vertex counting
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float GetActualTriangleCount(const FString& ActorId);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    int32 CalculateVertexCountFromTriangles(int32 TriangleCount);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    float CalculateActualMemoryUsage(int32 TriangleCount, int32 VertexCount);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    UStaticMesh* CreateSimplifiedMesh(UStaticMesh* OriginalMesh, float SimplificationRatio, int32 TargetLODLevel);

    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    void DrawDebugLODInfo(UWorld* World) const;

    // Helper functions for impostor generation
    UFUNCTION(BlueprintCallable, Category = "LOD Manager")
    UMaterialInterface* CreateImpostorMaterial(UTextureRenderTarget2D* RenderTarget);

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLODGenerated, FString, LODId, EAuracronLODType, LODType);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLODRemoved, FString, LODId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnLODTransition, FString, ActorId, int32, FromLOD, int32, ToLOD);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHLODGenerationCompleted, FString, LODId, bool, bSuccess);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLODGenerated OnLODGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLODRemoved OnLODRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLODTransition OnLODTransition;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnHLODGenerationCompleted OnHLODGenerationCompleted;

private:
    static UAuracronWorldPartitionLODManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronWorldPartitionLODConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // LOD data
    TMap<FString, FAuracronLODDescriptor> LODDescriptors;
    TMap<FString, TArray<FString>> ActorToLODsMap; // ActorId -> LODIds
    TMap<FString, TArray<FString>> CellToLODsMap; // CellId -> LODIds
    TMap<FString, int32> CurrentLODLevels; // ActorId -> Current LOD Level

    // Statistics
    FAuracronLODStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection LODLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateLODId(const FString& ActorId, int32 LODLevel) const;
    bool ValidateLODId(const FString& LODId) const;
    void OnLODGeneratedInternal(const FString& LODId, EAuracronLODType LODType);
    void OnLODRemovedInternal(const FString& LODId);
    void ValidateConfiguration();
    bool PerformMeshSimplification(const FString& ActorId, float SimplificationRatio, int32 TargetLODLevel);
    bool GenerateHLODMesh(const FAuracronHLODGenerationParameters& Parameters, FString& OutLODId);
};
