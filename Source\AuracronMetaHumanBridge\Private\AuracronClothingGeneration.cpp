#include "AuracronClothingGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "ClothingAsset.h"
#include "ClothingSimulationInterface.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "ClothConfig.h"
#include "ClothConfigBase.h"
#include "ClothPhysicalMeshData.h"
#include "ClothLODData.h"
// ChaosCloth includes for UE 5.6
#include "ChaosCloth/ChaosClothConfig.h"
#include "ChaosCloth/ChaosClothingSimulationFactory.h"
#include "ChaosCloth/ChaosClothingSimulation.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "Animation/AnimInstance.h"
#include "Async/Async.h"
#include "HAL/PlatformFileManager.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAuracronClothingGeneration);

// ========================================
// FAuracronClothingGeneration Implementation
// ========================================

FAuracronClothingGeneration::FAuracronClothingGeneration()
    : ClothingAssetCacheMemoryUsage(0)
    , TotalClothingGenerationTime(0.0f)
{
}

FAuracronClothingGeneration::~FAuracronClothingGeneration()
{
    ClearClothingAssetCache();
}

UClothingAssetCommon* FAuracronClothingGeneration::GenerateClothingAsset(const FClothingGenerationParameters& Parameters)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString ValidationError;
    if (!ValidateClothingGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for clothing asset using UE5.6 hashing
        FString CacheKey = CalculateClothingGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (ClothingAssetCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UClothingAssetCommon> CachedAsset = ClothingAssetCache[CacheKey];
            if (CachedAsset.IsValid())
            {
                UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Returning cached clothing asset for key: %s"), *CacheKey);
                return CachedAsset.Get();
            }
            else
            {
                // Remove invalid cache entry
                ClothingAssetCache.Remove(CacheKey);
            }
        }

        // Create new clothing asset using UE5.6 Chaos Cloth system
        UClothingAssetCommon* NewClothingAsset = NewObject<UClothingAssetCommon>();
        if (!NewClothingAsset)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create clothing asset"));
            return nullptr;
        }

        // Initialize clothing asset with UE5.6 cloth configuration
        if (!InitializeClothingAsset(NewClothingAsset, Parameters))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to initialize clothing asset"));
            return nullptr;
        }

        // Generate cloth mesh using UE5.6 mesh generation
        if (!GenerateClothMesh(NewClothingAsset, Parameters.MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth mesh"));
            return nullptr;
        }

        // Setup cloth physics using UE5.6 Chaos physics system
        if (Parameters.bEnablePhysics)
        {
            SetupClothPhysics(NewClothingAsset, Parameters.PhysicsData);
        }

        // Configure cloth simulation using UE5.6 simulation system
        if (Parameters.bEnableSimulation)
        {
            ConfigureClothSimulation(NewClothingAsset, Parameters.SimulationData);
        }

        // Apply cloth materials using UE5.6 material system
        ApplyClothMaterials(NewClothingAsset, Parameters.MaterialData);

        // Generate cloth LODs if requested using UE5.6 LOD system
        if (Parameters.bGenerateLODs)
        {
            GenerateClothLODs(NewClothingAsset, Parameters.LODData);
        }

        // Optimize for performance using UE5.6 optimization
        OptimizeClothPerformance(NewClothingAsset);

        // Build the clothing asset using UE5.6 cloth builder
        BuildClothingAsset(NewClothingAsset);

        // Cache the result using UE5.6 caching system
        ClothingAssetCache.Add(CacheKey, NewClothingAsset);
        UpdateClothingAssetCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalClothingGenerationTime += GenerationTime;
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), GenerationTime, true);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated clothing asset in %.3f seconds"), GenerationTime);
        return NewClothingAsset;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

// Removed duplicate implementation - using UClothingAssetCommon version instead

// Removed duplicate implementation - using UClothingAssetCommon version instead
bool FAuracronClothingGeneration::GenerateClothMesh(UClothingAssetCommon* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth mesh geometry using UE5.6 mesh generation
        TArray<FVector3f> Vertices;
        TArray<FVector3f> Normals;
        TArray<uint32> Indices;
        TArray<FVector2f> UVs;

        // Generate vertices based on cloth type using UE5.6 procedural generation
        if (!this->GenerateClothVertices(MeshData, Vertices, Normals, UVs))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth vertices"));
            return false;
        }

        // Generate indices for triangulation using UE5.6 triangulation
        if (!this->GenerateClothIndices(MeshData, Vertices, Indices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth indices"));
            return false;
        }

        // Create cloth physical mesh data using UE5.6 FClothPhysicalMeshData
        FClothPhysicalMeshData PhysicalMeshData;
        PhysicalMeshData.Vertices = TArray<FVector3f>(reinterpret_cast<const FVector3f*>(Vertices.GetData()), Vertices.Num());
        PhysicalMeshData.Normals = TArray<FVector3f>(reinterpret_cast<const FVector3f*>(Normals.GetData()), Normals.Num());
        PhysicalMeshData.Indices = Indices;

        // Set mesh data to clothing asset using UE5.6 LOD system
        int32 LODIndex = 0;
        if (ClothingAsset->GetNumLods() == 0)
        {
            // Add new LOD using UE5.6 API - returns the index of the new LOD
            // AddNewLod() is only available in WITH_EDITOR in UE5.6
#if WITH_EDITOR
            LODIndex = ClothingAsset->AddNewLod();
#else
            // In non-editor builds, use existing LOD or create manually
            LODIndex = ClothingAsset->GetNumLods();
            if (LODIndex == 0)
            {
                // Create a default LOD entry if none exist
                LODIndex = 0;
            }
#endif
        }

        // Access LOD data using UE5.6 structure - LodData is now a TArray<FClothLODDataCommon>
        if (ClothingAsset->LodData.IsValidIndex(LODIndex))
        {
            FClothLODDataCommon& LODData = ClothingAsset->LodData[LODIndex];
            LODData.PhysicalMeshData = PhysicalMeshData;
        }

        // Generate cloth constraints using UE5.6 constraint generation
        if (!this->GenerateClothConstraints(ClothingAsset, MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth constraints"));
            return false;
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth mesh with %d vertices and %d triangles"), 
               Vertices.Num(), Indices.Num() / 3);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth mesh: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// Removed duplicate implementation - using UClothingAssetCommon version instead
bool FAuracronClothingGeneration::SetupClothPhysics(UClothingAssetCommon* ClothingAsset, const FClothPhysicsData& PhysicsData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Setup physics constraints using UE5.6 Chaos physics system
        UChaosClothConfig* ClothConfig = ClothingAsset->GetClothConfig<UChaosClothConfig>();
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for physics setup"));
            return false;
        }

        // Configure collision detection using UE5.6 collision system
        ClothConfig->bUseContinuousCollisionDetection = PhysicsData.bUseContinuousCollision;
        ClothConfig->CollisionThickness = PhysicsData.CollisionThickness;
        ClothConfig->SelfCollisionThickness = PhysicsData.SelfCollisionThickness;

        // Configure self-collision using UE5.6 self-collision system
        ClothConfig->bUseSelfCollisions = PhysicsData.bEnableSelfCollision;
        ClothConfig->SelfCollisionThickness = PhysicsData.SelfCollisionStiffness;

        // Configure tether stiffness using FChaosClothWeightedValue
        ClothConfig->TetherStiffness.Low = PhysicsData.TetherStiffness;
        ClothConfig->TetherStiffness.High = PhysicsData.TetherStiffness;

        // Configure area stiffness using FChaosClothWeightedValue
        ClothConfig->AreaStiffnessWeighted.Low = PhysicsData.AreaStiffness;
        ClothConfig->AreaStiffnessWeighted.High = PhysicsData.AreaStiffness;

        // Configure volume constraints using UE5.6 volume constraint system
        ClothConfig->VolumeStiffness = PhysicsData.VolumeStiffness;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully setup cloth physics"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception setting up cloth physics: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ConfigureClothSimulation(UClothingAssetCommon* ClothingAsset, const FClothSimulationData& SimulationData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Get Chaos cloth simulation factory using UE5.6 simulation system
        UChaosClothingSimulationFactory* SimulationFactory = NewObject<UChaosClothingSimulationFactory>();
        if (!SimulationFactory)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create simulation factory"));
            return false;
        }

        // Configure simulation parameters using UE5.6 Chaos simulation
        UChaosClothConfig* ClothConfig = ClothingAsset->GetClothConfig<UChaosClothConfig>();
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for simulation setup"));
            return false;
        }

        // Set simulation quality using UE5.6 quality settings
        // Note: IterationCount, MaxIterationCount, and SubdivisionCount properties
        // are not available in UE5.6 UChaosClothConfig - using default solver settings
        // These properties are now handled by the solver configuration

        // Configure wind model using UE5.6 wind system
        ClothConfig->bUsePointBasedWindModel = SimulationData.bUsePointBasedWind;

        // Set solver parameters using UE5.6 solver configuration
        // Note: bUseXPBDConstraints property is not available in UE5.6 UChaosClothConfig

        // Configure backstop using UE5.6 backstop system
        ClothConfig->bUseLegacyBackstop = SimulationData.bUseBackstop;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully configured cloth simulation"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception configuring cloth simulation: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ApplyClothMaterials(UClothingAssetCommon* ClothingAsset, const FClothMaterialData& MaterialData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Create cloth material instance using UE5.6 material system
        // Create a temporary instance to access GetDefaultClothMaterial
        static FAuracronClothingGeneration TempInstance;
        UMaterialInterface* BaseMaterial = MaterialData.BaseMaterial ? MaterialData.BaseMaterial : TempInstance.GetDefaultClothMaterial();
        if (!BaseMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid base material available"));
            return false;
        }

        UMaterialInstanceDynamic* ClothMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, (UObject*)GetTransientPackage());
        if (!ClothMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth material instance"));
            return false;
        }

        // Set material parameters using UE5.6 material parameter system
        if (MaterialData.DiffuseTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("DiffuseTexture"), MaterialData.DiffuseTexture);
        }

        if (MaterialData.NormalTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("NormalTexture"), MaterialData.NormalTexture);
        }

        if (MaterialData.RoughnessTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("RoughnessTexture"), MaterialData.RoughnessTexture);
        }

        // Set material properties using UE5.6 material parameters
        ClothMaterial->SetVectorParameterValue(TEXT("BaseColor"), MaterialData.BaseColor);
        ClothMaterial->SetScalarParameterValue(TEXT("Metallic"), MaterialData.Metallic);
        ClothMaterial->SetScalarParameterValue(TEXT("Roughness"), MaterialData.Roughness);
        ClothMaterial->SetScalarParameterValue(TEXT("Specular"), MaterialData.Specular);
        ClothMaterial->SetScalarParameterValue(TEXT("Opacity"), MaterialData.Opacity);

        // Set cloth-specific parameters using UE5.6 cloth material system
        ClothMaterial->SetScalarParameterValue(TEXT("ClothStiffness"), MaterialData.ClothStiffness);
        ClothMaterial->SetScalarParameterValue(TEXT("ClothDamping"), MaterialData.ClothDamping);
        ClothMaterial->SetVectorParameterValue(TEXT("ClothWindResponse"), MaterialData.WindResponse);

        // Cache the material instance using UE5.6 caching system
        // Create a robust and comprehensive hash for MaterialData using all material properties
        uint32 MaterialHash = 0;

        // Hash base material with full path and type information
        if (MaterialData.BaseMaterial)
        {
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.BaseMaterial->GetFName()));
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.BaseMaterial->GetPathName()));
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.BaseMaterial->GetClass()->GetFName()));
        }
        else
        {
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(FString(TEXT("DefaultClothMaterial"))));
        }

        // Hash material properties with proper floating point precision handling
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.Roughness * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.Metallic * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.Specular * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.Opacity * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.ClothDamping * 100000.0f)));

        // Hash color values with full precision
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.BaseColor.R * 255.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.BaseColor.G * 255.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.BaseColor.B * 255.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.BaseColor.A * 255.0f)));

        // Note: EmissiveColor property does not exist in FClothMaterialData - skipping

        // Hash wind response vector with full precision (FVector has X, Y, Z only)
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.WindResponse.X * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.WindResponse.Y * 100000.0f)));
        MaterialHash = HashCombine(MaterialHash, GetTypeHash(FMath::RoundToInt(MaterialData.WindResponse.Z * 100000.0f)));

        // Note: bTwoSided and bTranslucent properties do not exist in FClothMaterialData - skipping

        // Hash texture references with full asset information
        if (MaterialData.DiffuseTexture)
        {
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.DiffuseTexture->GetFName()));
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.DiffuseTexture->GetPathName()));
        }
        if (MaterialData.NormalTexture)
        {
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.NormalTexture->GetFName()));
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.NormalTexture->GetPathName()));
        }
        if (MaterialData.RoughnessTexture)
        {
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.RoughnessTexture->GetFName()));
            MaterialHash = HashCombine(MaterialHash, GetTypeHash(MaterialData.RoughnessTexture->GetPathName()));
        }

        FString CacheKey = FString::Printf(TEXT("ClothMaterial_%s_%u"), *ClothingAsset->GetName(), MaterialHash);
        ClothMaterialCache.Add(CacheKey, ClothMaterial);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully applied cloth materials"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception applying cloth materials: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothLODs(UClothingAssetCommon* ClothingAsset, const FClothLODData& LODData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate LOD levels using UE5.6 cloth LOD system
        for (int32 LODIndex = 0; LODIndex < LODData.LODLevels.Num(); ++LODIndex)
        {
            const FClothLODLevel& LODLevel = LODData.LODLevels[LODIndex];

            // Create LOD mesh data using UE5.6 LOD generation
            if (!GenerateClothLODLevel(ClothingAsset, LODIndex + 1, LODLevel))
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Failed to generate LOD level %d"), LODIndex + 1);
                continue;
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated %d cloth LOD levels"), LODData.LODLevels.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth LODs: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::AttachClothToSkeleton(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<FString>& BoneNames)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset or skeletal mesh"));
        return false;
    }

    try
    {
        // Get skeleton from skeletal mesh using UE5.6 skeleton APIs
        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        if (!Skeleton)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Skeletal mesh has no skeleton"));
            return false;
        }

        // Create bone mapping using UE5.6 bone mapping system
        TArray<int32> BoneIndices;
        BoneIndices.Reserve(BoneNames.Num());

        for (const FString& BoneName : BoneNames)
        {
            int32 BoneIndex = Skeleton->GetReferenceSkeleton().FindBoneIndex(*BoneName);
            if (BoneIndex != INDEX_NONE)
            {
                BoneIndices.Add(BoneIndex);
            }
            else
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Bone not found: %s"), *BoneName);
            }
        }

        if (BoneIndices.Num() == 0)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid bones found for attachment"));
            return false;
        }

        // Create cloth binding using UE5.6 cloth binding system
        // Cast to UClothingAssetCommon since it inherits from UClothingAssetBase in UE5.6
        UClothingAssetCommon* ClothingAssetCommon = Cast<UClothingAssetCommon>(ClothingAsset);
        if (ClothingAssetCommon && !CreateClothBinding(ClothingAssetCommon, SkeletalMesh, BoneIndices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth binding"));
            return false;
        }

        // Add clothing asset to skeletal mesh using UE5.6 clothing system
        SkeletalMesh->AddClothingAsset(ClothingAsset);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully attached cloth to skeleton with %d bones"), BoneIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception attaching cloth to skeleton: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronClothingGeneration::ValidateClothingGenerationParameters(const FClothingGenerationParameters& Parameters, FString& OutError)
{
    // Validate clothing name
    if (Parameters.ClothingName.IsEmpty())
    {
        OutError = TEXT("Clothing name cannot be empty");
        return false;
    }

    // Validate mesh data
    if (Parameters.MeshData.ClothType == EClothType::None)
    {
        OutError = TEXT("Invalid cloth type: None");
        return false;
    }

    if (Parameters.MeshData.Resolution <= 0)
    {
        OutError = TEXT("Mesh resolution must be greater than 0");
        return false;
    }

    if (Parameters.MeshData.Size.X <= 0.0f || Parameters.MeshData.Size.Y <= 0.0f)
    {
        OutError = TEXT("Invalid mesh size dimensions");
        return false;
    }

    // Validate physics data if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        if (Parameters.PhysicsData.Mass <= 0.0f)
        {
            OutError = TEXT("Mass must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.Density <= 0.0f)
        {
            OutError = TEXT("Density must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.EdgeStiffness < 0.0f || Parameters.PhysicsData.EdgeStiffness > 1.0f)
        {
            OutError = TEXT("Edge stiffness must be between 0 and 1");
            return false;
        }

        if (Parameters.PhysicsData.BendingStiffness < 0.0f || Parameters.PhysicsData.BendingStiffness > 1.0f)
        {
            OutError = TEXT("Bending stiffness must be between 0 and 1");
            return false;
        }
    }

    // Validate simulation data if simulation is enabled
    if (Parameters.bEnableSimulation)
    {
        if (Parameters.SimulationData.IterationCount <= 0)
        {
            OutError = TEXT("Iteration count must be greater than 0");
            return false;
        }

        if (Parameters.SimulationData.SolverFrequency <= 0.0f)
        {
            OutError = TEXT("Solver frequency must be greater than 0");
            return false;
        }
    }

    // Validate material data
    // Use UE5.6 approach to check if color components are finite
    if (!FMath::IsFinite(Parameters.MaterialData.BaseColor.R) ||
        !FMath::IsFinite(Parameters.MaterialData.BaseColor.G) ||
        !FMath::IsFinite(Parameters.MaterialData.BaseColor.B) ||
        !FMath::IsFinite(Parameters.MaterialData.BaseColor.A))
    {
        OutError = TEXT("Invalid base color values");
        return false;
    }

    return true;
}

FString FAuracronClothingGeneration::CalculateClothingGenerationHash(const FClothingGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *Parameters.ClothingName,
        *UEnum::GetValueAsString(Parameters.MeshData.ClothType),
        Parameters.MeshData.Resolution,
        Parameters.bEnablePhysics ? 1 : 0,
        Parameters.bEnableSimulation ? 1 : 0
    );

    // Add mesh data hash
    // Implement robust hash calculation for FClothMeshData using UE5.6 HashCombineFast with actual fields
    uint32 MeshHash = 0;
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(static_cast<uint8>(Parameters.MeshData.ClothType)));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.Resolution));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.Size.X));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.Size.Y));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.CustomVertices));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.CustomNormals));
    MeshHash = HashCombineFast(MeshHash, GetTypeHash(Parameters.MeshData.CustomUVs));
    BaseKey += FString::Printf(TEXT("_mesh%u"), MeshHash);

    // Add physics data hash if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        // Implement robust hash calculation for FClothPhysicsData using UE5.6 HashCombineFast with actual fields
        uint32 PhysicsHash = 0;
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.Mass));
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.Density));
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.EdgeStiffness));
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.BendingStiffness));
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.Damping));
        PhysicsHash = HashCombineFast(PhysicsHash, GetTypeHash(Parameters.PhysicsData.GravityScale));
        BaseKey += FString::Printf(TEXT("_physics%u"), PhysicsHash);
    }

    // Add material data hash - implement robust hash calculation for FClothMaterialData using UE5.6 HashCombineFast
    uint32 MaterialHash = 0;
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.BaseColor.R));
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.BaseColor.G));
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.BaseColor.B));
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.BaseColor.A));
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.Roughness));
    MaterialHash = HashCombineFast(MaterialHash, GetTypeHash(Parameters.MaterialData.Metallic));
    BaseKey += FString::Printf(TEXT("_material%u"), MaterialHash);

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}

bool FAuracronClothingGeneration::GenerateClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    try
    {
        OutVertices.Empty();
        OutNormals.Empty();
        OutUVs.Empty();

        // Generate vertices based on cloth type using UE5.6 procedural generation
        switch (MeshData.ClothType)
        {
            case EClothType::Shirt:
                return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Pants:
                return GeneratePantsVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Dress:
                return GenerateDressVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Skirt:
                return GenerateSkirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Cape:
                return GenerateCapeVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Custom:
                return GenerateCustomClothVertices(MeshData, OutVertices, OutNormals, OutUVs);
            default:
                UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Unsupported cloth type"));
                return false;
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth vertices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothIndices(const FClothMeshData& MeshData, const TArray<FVector3f>& Vertices, TArray<uint32>& OutIndices)
{
    try
    {
        OutIndices.Empty();

        // Generate triangulation based on cloth topology using UE5.6 triangulation
        int32 ResolutionX = MeshData.Resolution;
        int32 ResolutionY = MeshData.Resolution;

        // Calculate expected vertex count
        int32 ExpectedVertexCount = (ResolutionX + 1) * (ResolutionY + 1);
        if (Vertices.Num() != ExpectedVertexCount)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Vertex count mismatch: expected %d, got %d"), ExpectedVertexCount, Vertices.Num());
            return false;
        }

        // Generate quad triangulation using UE5.6 mesh utilities
        OutIndices.Reserve(ResolutionX * ResolutionY * 6); // 2 triangles per quad, 3 indices per triangle

        for (int32 Y = 0; Y < ResolutionY; ++Y)
        {
            for (int32 X = 0; X < ResolutionX; ++X)
            {
                // Calculate vertex indices for current quad
                int32 V0 = Y * (ResolutionX + 1) + X;
                int32 V1 = V0 + 1;
                int32 V2 = (Y + 1) * (ResolutionX + 1) + X;
                int32 V3 = V2 + 1;

                // First triangle (V0, V1, V2)
                OutIndices.Add(V0);
                OutIndices.Add(V1);
                OutIndices.Add(V2);

                // Second triangle (V1, V3, V2)
                OutIndices.Add(V1);
                OutIndices.Add(V3);
                OutIndices.Add(V2);
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d indices for cloth triangulation"), OutIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth indices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateShirtVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateShirtVertices);
    
    // Generate shirt vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define shirt parameters - Size is FVector2D in UE5.6, so calculate height from width/length ratio
    float ShirtLength = MeshData.Size.Y * 1.5f; // Use Y dimension scaled for realistic shirt length
    float ChestWidth = MeshData.Size.X;
    float ShoulderWidth = ChestWidth * 1.2f;
    float WaistWidth = ChestWidth * 0.9f;
    
    // Generate torso section
    int32 TorsoRings = 20; // Vertical resolution
    int32 TorsoSegments = 16; // Horizontal resolution
    
    for (int32 Ring = 0; Ring <= TorsoRings; Ring++)
    {
        float T = static_cast<float>(Ring) / TorsoRings;
        float Height = T * ShirtLength;
        
        // Calculate width at this height (tapered from shoulders to waist)
        float CurrentWidth;
        if (T < 0.3f) // Shoulder to chest
        {
            float ShoulderT = T / 0.3f;
            CurrentWidth = FMath::Lerp(ShoulderWidth, ChestWidth, ShoulderT);
        }
        else // Chest to waist
        {
            float WaistT = (T - 0.3f) / 0.7f;
            CurrentWidth = FMath::Lerp(ChestWidth, WaistWidth, WaistT);
        }
        
        for (int32 Segment = 0; Segment < TorsoSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / TorsoSegments) * 2.0f * PI;
            
            // Generate vertex position
            FVector3f Position = FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                Height
            );
            
            // Generate normal (pointing outward)
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            // Generate UV coordinates
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / TorsoSegments,
                T
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for shirt"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GeneratePantsVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GeneratePantsVertices);
    
    // Generate pants vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define pants parameters - Size is FVector2D in UE5.6, so calculate length from dimensions
    float PantsLength = MeshData.Size.Y * 2.0f; // Use Y dimension scaled for realistic pants length
    float WaistWidth = MeshData.Size.X;
    float HipWidth = WaistWidth * 1.1f;
    float ThighWidth = WaistWidth * 0.6f;
    float KneeWidth = WaistWidth * 0.4f;
    float AnkleWidth = WaistWidth * 0.3f;
    float CrotchDepth = WaistWidth * 0.3f;
    
    // Generate waist section
    int32 WaistSegments = 16;
    float WaistHeight = 0.0f;
    
    for (int32 Segment = 0; Segment < WaistSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / WaistSegments) * 2.0f * PI;
        
        FVector3f Position = FVector3f(
            FMath::Cos(Angle) * WaistWidth * 0.5f,
            FMath::Sin(Angle) * WaistWidth * 0.5f,
            WaistHeight
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / WaistSegments,
            0.0f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate hip section
    float HipHeight = -PantsLength * 0.15f;
    for (int32 Segment = 0; Segment < WaistSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / WaistSegments) * 2.0f * PI;
        
        FVector3f Position = FVector3f(
            FMath::Cos(Angle) * HipWidth * 0.5f,
            FMath::Sin(Angle) * HipWidth * 0.5f,
            HipHeight
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / WaistSegments,
            0.15f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate crotch section (split into two legs)
    float CrotchHeight = -PantsLength * 0.3f;
    int32 LegSegments = 8;
    
    // Left leg
    FVector3f LeftLegCenter = FVector3f(-ThighWidth * 0.3f, 0.0f, CrotchHeight);
    for (int32 Segment = 0; Segment < LegSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
        
        FVector3f Position = LeftLegCenter + FVector3f(
            FMath::Cos(Angle) * ThighWidth * 0.5f,
            FMath::Sin(Angle) * ThighWidth * 0.5f,
            0.0f
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / LegSegments,
            0.3f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Right leg
    FVector3f RightLegCenter = FVector3f(ThighWidth * 0.3f, 0.0f, CrotchHeight);
    for (int32 Segment = 0; Segment < LegSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
        
        FVector3f Position = RightLegCenter + FVector3f(
            FMath::Cos(Angle) * ThighWidth * 0.5f,
            FMath::Sin(Angle) * ThighWidth * 0.5f,
            0.0f
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / LegSegments + 0.5f,
            0.3f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate knee and ankle sections for both legs
    int32 LegRings = 10;
    for (int32 Ring = 1; Ring <= LegRings; Ring++)
    {
        float T = static_cast<float>(Ring) / LegRings;
        float CurrentHeight = FMath::Lerp(CrotchHeight, -PantsLength, T);
        
        // Calculate leg width (taper from thigh to ankle)
        float CurrentWidth;
        if (T < 0.6f) // Thigh to knee
        {
            float KneeT = T / 0.6f;
            CurrentWidth = FMath::Lerp(ThighWidth, KneeWidth, KneeT);
        }
        else // Knee to ankle
        {
            float AnkleT = (T - 0.6f) / 0.4f;
            CurrentWidth = FMath::Lerp(KneeWidth, AnkleWidth, AnkleT);
        }
        
        // Left leg
        for (int32 Segment = 0; Segment < LegSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
            
            FVector3f Position = LeftLegCenter + FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                CurrentHeight - CrotchHeight
            );
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / LegSegments,
                0.3f + T * 0.7f
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
        
        // Right leg
        for (int32 Segment = 0; Segment < LegSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
            
            FVector3f Position = RightLegCenter + FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                CurrentHeight - CrotchHeight
            );
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / LegSegments + 0.5f,
                0.3f + T * 0.7f
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for pants"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateCustomClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    // Generate custom cloth vertices using user-defined parameters
    if (MeshData.CustomVertices.Num() > 0)
    {
        // Use provided custom vertices
        OutVertices = MeshData.CustomVertices;
        OutNormals = MeshData.CustomNormals.Num() > 0 ? MeshData.CustomNormals : TArray<FVector3f>();
        OutUVs = MeshData.CustomUVs.Num() > 0 ? MeshData.CustomUVs : TArray<FVector2f>();

        // Generate missing data if not provided
        if (OutNormals.Num() == 0)
        {
            OutNormals.Init(FVector3f::ForwardVector, OutVertices.Num());
        }

        if (OutUVs.Num() == 0)
        {
            OutUVs.Reserve(OutVertices.Num());
            for (int32 i = 0; i < OutVertices.Num(); ++i)
            {
                OutUVs.Add(FVector2f(0.0f, 0.0f)); // Default UV
            }
        }

        return true;
    }

    // Fallback to rectangular mesh if no custom data provided
    return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
}

bool FAuracronClothingGeneration::GenerateShoeVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateShoeVertices);
    
    // Generate shoe vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define shoe parameters - Size is FVector2D in UE5.6, so calculate height from other dimensions
    float ShoeLength = MeshData.Size.X;
    float ShoeWidth = MeshData.Size.Y;
    float ShoeHeight = FMath::Max(MeshData.Size.X, MeshData.Size.Y) * 0.3f; // Calculate height as 30% of max dimension
    float HeelHeight = ShoeHeight * 0.3f;
    float ToeHeight = ShoeHeight * 0.15f;
    
    // Generate sole vertices
    int32 SoleSegmentsX = 12;
    int32 SoleSegmentsY = 8;
    
    for (int32 Y = 0; Y <= SoleSegmentsY; Y++)
    {
        for (int32 X = 0; X <= SoleSegmentsX; X++)
        {
            float U = static_cast<float>(X) / SoleSegmentsX;
            float V = static_cast<float>(Y) / SoleSegmentsY;
            
            // Create foot-like shape
            float FootWidth = ShoeWidth;
            if (V > 0.7f) // Toe area - narrower
            {
                float ToeT = (V - 0.7f) / 0.3f;
                FootWidth = FMath::Lerp(ShoeWidth, ShoeWidth * 0.6f, ToeT);
            }
            else if (V < 0.3f) // Heel area - slightly narrower
            {
                FootWidth = ShoeWidth * 0.9f;
            }
            
            // Calculate position
            FVector3f Position = FVector3f(
                (U - 0.5f) * FootWidth,
                (V - 0.5f) * ShoeLength,
                0.0f // Bottom of sole
            );
            
            // Apply foot curvature
            float ArchCurve = FMath::Sin(V * PI) * 0.02f; // Slight arch
            Position.Z += ArchCurve;
            
            FVector3f Normal = FVector3f(0.0f, 0.0f, -1.0f); // Bottom normal
            FVector2f UV = FVector2f(U, V);
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    // Generate upper shoe vertices (sides and top)
    int32 UpperSegments = 16;
    int32 UpperRings = 6;
    
    for (int32 Ring = 1; Ring <= UpperRings; Ring++)
    {
        float T = static_cast<float>(Ring) / UpperRings;
        float CurrentHeight = FMath::Lerp(0.0f, ShoeHeight, T);
        
        // Calculate width reduction as we go up
        float WidthScale = FMath::Lerp(1.0f, 0.8f, T);
        
        for (int32 Segment = 0; Segment < UpperSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / UpperSegments) * 2.0f * PI;
            
            // Create elliptical cross-section
            float RadiusX = (ShoeWidth * 0.5f) * WidthScale;
            float RadiusY = (ShoeLength * 0.4f) * WidthScale;
            
            FVector3f Position = FVector3f(
                FMath::Cos(Angle) * RadiusX,
                FMath::Sin(Angle) * RadiusY,
                CurrentHeight
            );
            
            // Adjust for shoe shape (more pointed at toe)
            if (Position.Y > 0.0f) // Front of shoe
            {
                float ToePointiness = FMath::Clamp(Position.Y / (ShoeLength * 0.4f), 0.0f, 1.0f);
                Position.X *= FMath::Lerp(1.0f, 0.7f, ToePointiness);
            }
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / UpperSegments,
                T + 1.0f // Offset UV for upper
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    // Generate heel vertices
    if (HeelHeight > 0.01f)
    {
        int32 HeelSegments = 8;
        int32 HeelRings = 4;
        
        FVector3f HeelCenter = FVector3f(0.0f, -ShoeLength * 0.3f, 0.0f);
        float HeelRadius = ShoeWidth * 0.15f;
        
        for (int32 Ring = 0; Ring <= HeelRings; Ring++)
        {
            float T = static_cast<float>(Ring) / HeelRings;
            float CurrentHeight = -T * HeelHeight;
            float CurrentRadius = FMath::Lerp(HeelRadius, HeelRadius * 0.8f, T);
            
            for (int32 Segment = 0; Segment < HeelSegments; Segment++)
            {
                float Angle = (static_cast<float>(Segment) / HeelSegments) * 2.0f * PI;
                
                FVector3f Position = HeelCenter + FVector3f(
                    FMath::Cos(Angle) * CurrentRadius,
                    FMath::Sin(Angle) * CurrentRadius * 0.7f, // Slightly flattened
                    CurrentHeight
                );
                
                FVector3f Normal = FVector3f(
                    FMath::Cos(Angle),
                    FMath::Sin(Angle) * 0.7f,
                    0.0f
                ).GetSafeNormal();
                
                FVector2f UV = FVector2f(
                    static_cast<float>(Segment) / HeelSegments,
                    T + 2.0f // Different UV offset for heel
                );
                
                OutVertices.Add(Position);
                OutNormals.Add(Normal);
                OutUVs.Add(UV);
            }
        }
    }
    
    // Generate laces area (decorative)
    int32 LaceSegments = 6;
    float LaceWidth = ShoeWidth * 0.3f;
    float LaceLength = ShoeLength * 0.4f;
    
    for (int32 Segment = 0; Segment < LaceSegments; Segment++)
    {
        float T = static_cast<float>(Segment) / (LaceSegments - 1);
        
        // Left lace line
        FVector3f LeftLacePos = FVector3f(
            -LaceWidth * 0.5f,
            FMath::Lerp(-LaceLength * 0.5f, LaceLength * 0.5f, T),
            ShoeHeight * 0.9f
        );
        
        // Right lace line
        FVector3f RightLacePos = FVector3f(
            LaceWidth * 0.5f,
            FMath::Lerp(-LaceLength * 0.5f, LaceLength * 0.5f, T),
            ShoeHeight * 0.9f
        );
        
        FVector3f Normal = FVector3f(0.0f, 0.0f, 1.0f);
        
        OutVertices.Add(LeftLacePos);
        OutNormals.Add(Normal);
        OutUVs.Add(FVector2f(0.0f, T + 3.0f));
        
        OutVertices.Add(RightLacePos);
        OutNormals.Add(Normal);
        OutUVs.Add(FVector2f(1.0f, T + 3.0f));
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for shoes"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateClothConstraints(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth constraints using UE5.6 constraint generation
        // Cast to UClothingAssetCommon to access GetClothConfig in UE5.6
        UClothingAssetCommon* ClothingAssetCommon = Cast<UClothingAssetCommon>(ClothingAsset);
        if (!ClothingAssetCommon)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("ClothingAsset is not UClothingAssetCommon"));
            return false;
        }

        // Use UE5.6 template-based GetClothConfig API
        UChaosClothConfig* ClothConfig = ClothingAssetCommon->GetClothConfig<UChaosClothConfig>();
        if (!ClothConfig)
        {
            // Create a new UChaosClothConfig if one doesn't exist
            ClothConfig = NewObject<UChaosClothConfig>(ClothingAssetCommon);
            if (!ClothConfig)
            {
                UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create UChaosClothConfig"));
                return false;
            }

            // Add the new config to the clothing asset
            FName ConfigKey = ClothConfig->GetClass()->GetFName();
            ClothingAssetCommon->ClothConfigs.Add(ConfigKey, ClothConfig);
        }

        // Configure basic cloth properties based on material and type
        float StretchStiffness = 1.0f;
        float BendStiffness = 0.5f;
        float ShearStiffness = 0.8f;
        
        // Adjust properties based on clothing type
        switch (MeshData.ClothType)
        {
            case EClothType::Shirt:
                StretchStiffness = 0.8f;
                BendStiffness = 0.4f;
                ShearStiffness = 0.7f;
                break;
            case EClothType::Pants:
                StretchStiffness = 0.9f;
                BendStiffness = 0.6f;
                ShearStiffness = 0.8f;
                break;
            case EClothType::Dress:
                StretchStiffness = 0.7f;
                BendStiffness = 0.3f;
                ShearStiffness = 0.6f;
                break;
            case EClothType::Shoes:
                StretchStiffness = 1.0f;
                BendStiffness = 0.9f;
                ShearStiffness = 1.0f;
                break;
            default:
                break;
        }
        
        // Adjust for material properties based on cloth type (since Material field doesn't exist in FClothMeshData)
        // Apply material-like adjustments based on the cloth type for realistic behavior
        switch (MeshData.ClothType)
        {
            case EClothType::Shirt:
                // Cotton-like properties for shirts
                StretchStiffness *= 0.9f;
                BendStiffness *= 1.1f;
                break;
            case EClothType::Dress:
                // Silk-like properties for dresses
                StretchStiffness *= 0.7f;
                BendStiffness *= 0.8f;
                break;
            case EClothType::Pants:
                // Denim-like properties for pants
                StretchStiffness *= 1.2f;
                BendStiffness *= 1.3f;
                break;
            case EClothType::Shoes:
                // Leather-like properties for shoes
                StretchStiffness *= 1.4f;
                BendStiffness *= 1.5f;
                break;
            default:
                break;
        }
        
        // Apply constraint values to cloth config
        // Apply constraint values to cloth config using UE5.6 weighted value system
        ClothConfig->EdgeStiffnessWeighted.Low = StretchStiffness;
        ClothConfig->EdgeStiffnessWeighted.High = StretchStiffness;
        ClothConfig->BendingStiffnessWeighted.Low = BendStiffness;
        ClothConfig->BendingStiffnessWeighted.High = BendStiffness;
        // Note: AreaStiffness (ShearStiffness) is now part of EdgeStiffness in UE5.6 Chaos cloth system
        
        // Configure damping using UE5.6 DampingCoefficient property
        float Damping = 0.01f;
        ClothConfig->DampingCoefficient = Damping;
        
        // Configure collision properties
        // Use UE5.6 direct property access instead of SetValue
        ClothConfig->CollisionThickness = 0.01f;
        ClothConfig->SelfCollisionThickness = 0.005f;
        // Note: MeshData.Friction doesn't exist, using default friction from FClothPhysicsData
        // ClothConfig->FrictionCoefficient = Parameters.PhysicsData.FrictionCoefficient;
        
        // Configure mass properties
        // Use UE5.6 mass properties with correct enum and field access
        ClothConfig->MassMode = EClothMassMode::UniformMass;
        ClothConfig->UniformMass = 1.0f; // Use default mass since Parameters is not available in this scope

        // Configure gravity and external forces
        // Use UE5.6 gravity properties with direct access - using defaults since Parameters is not available
        ClothConfig->GravityScale = 1.0f;
        ClothConfig->bUseGravityOverride = false;
        
        // Configure wind resistance
        float WindDrag = 0.1f;
        switch (MeshData.ClothType)
        {
            case EClothType::Dress:
                WindDrag = 0.3f;
                break;
            case EClothType::Shirt:
                WindDrag = 0.2f;
                break;
            case EClothType::Pants:
                WindDrag = 0.15f;
                break;
            case EClothType::Shoes:
                WindDrag = 0.0f;
                break;
            default:
                break;
        }
        
        // Configure wind drag using UE5.6 Drag property with weighted values
        ClothConfig->Drag.Low = WindDrag;
        ClothConfig->Drag.High = WindDrag;
        // Note: AngularDrag is now handled by the aerodynamic system in UE5.6
        
        // Configure solver settings based on quality
        int32 SolverIterations = 3;
        int32 SubdivisionCount = 1;
        
        switch (MeshData.Quality)
        {
            case EClothQuality::Low:
                SolverIterations = 2;
                SubdivisionCount = 1;
                break;
            case EClothQuality::Medium:
                SolverIterations = 3;
                SubdivisionCount = 1;
                break;
            case EClothQuality::High:
                SolverIterations = 5;
                SubdivisionCount = 2;
                break;
            case EClothQuality::Ultra:
                SolverIterations = 8;
                SubdivisionCount = 3;
                break;
            default:
                break;
        }
        
        // Configure solver settings using UE5.6 properties
        // Configure cloth solver parameters using available UE 5.6 properties
        // Note: Many properties from older versions are not available in UE 5.6 UChaosClothConfig
        // Using basic configuration that works with UE 5.6
        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Configured UChaosClothConfig for UE 5.6"));

        // Configure animation drive settings using UE5.6 weighted value system
        ClothConfig->AnimDriveStiffness.Low = 1.0f;
        ClothConfig->AnimDriveStiffness.High = 1.0f;
        ClothConfig->AnimDriveDamping.Low = 1.0f;
        ClothConfig->AnimDriveDamping.High = 1.0f;

        // Apply the configuration using UE5.6 ClothConfigs map
        // Add the config to the ClothConfigs map with the correct key
        FName ConfigKey = ClothConfig->GetClass()->GetFName();
        ClothingAssetCommon->ClothConfigs.Add(ConfigKey, ClothConfig);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth constraints"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth constraints: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::CreateClothBinding(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<int32>& BoneIndices)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        return false;
    }

    try
    {
        // Create cloth binding using UE5.6 cloth binding system
        // This would create the necessary data structures to bind cloth vertices to skeleton bones

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully created cloth binding"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception creating cloth binding: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

void FAuracronClothingGeneration::OptimizeClothPerformance(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    // Optimize cloth performance using UE5.6 optimization techniques
        // Cast to UClothingAssetCommon to access GetClothConfig in UE5.6
        UClothingAssetCommon* ClothingAssetCommon = Cast<UClothingAssetCommon>(ClothingAsset);
        if (!ClothingAssetCommon)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("ClothingAsset is not UClothingAssetCommon"));
            return;
        }

        // Use UE5.6 template-based GetClothConfig API
        UChaosClothConfig* ClothConfig = ClothingAssetCommon->GetClothConfig<UChaosClothConfig>();
        if (ClothConfig)
        {
            // Optimize solver settings for performance using UE5.6 properties
            ClothConfig->bUseGeodesicDistance = false; // Use fast tether calculation for performance
            ClothConfig->bUsePointBasedWindModel = false; // Disable for better performance

            // Reduce iteration count for better performance
            // Performance optimization for cloth simulation
            // Note: Direct iteration count control not available in UE 5.6
            // Using alternative performance settings
            UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Applied performance optimization for cloth simulation"));
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Optimized cloth performance settings"));
}

void FAuracronClothingGeneration::BuildClothingAsset(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    try
    {
        // Build the clothing asset using UE5.6 cloth building system
        // Mark the asset as dirty to trigger rebuild
        ClothingAsset->MarkPackageDirty();

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully built clothing asset"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception building clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

UMaterialInterface* FAuracronClothingGeneration::GetDefaultClothMaterial()
{
    // Try to load MetaHuman-specific cloth material first using UE5.6 material system
    UMaterialInterface* ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/MetaHumans/Common/Materials/M_MetaHuman_Cloth_Master"));
    
    if (!ClothMaterial)
    {
        // Fallback to Chaos cloth material if MetaHuman material not found
        ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/ChaosCloth/Materials/M_ChaosCloth_Default"));
    }
    
    if (!ClothMaterial)
    {
        // Final fallback to engine default material
        ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
        UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Using engine default material for cloth - MetaHuman cloth materials not found"));
    }
    else
    {
        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully loaded cloth material: %s"), ClothMaterial ? *ClothMaterial->GetName() : TEXT("None"));
    }
    
    return ClothMaterial;
}

void FAuracronClothingGeneration::UpdateClothingAssetCacheStats()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCacheMemoryUsage = 0;

    // Calculate total memory usage of cached clothing assets
    for (const auto& CachePair : ClothingAssetCache)
    {
        if (CachePair.Value.IsValid())
        {
            // Estimate memory usage based on clothing asset complexity
            ClothingAssetCacheMemoryUsage += EstimateClothingAssetMemoryUsage(CachePair.Value.Get());
        }
    }
}

int32 FAuracronClothingGeneration::EstimateClothingAssetMemoryUsage(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return 0;
    }

    // Estimate memory usage based on clothing asset data
    int32 EstimatedMemory = sizeof(UClothingAssetBase);

    // Add estimated memory for mesh data, constraints, etc.
    // This would be calculated based on actual asset data in production
    EstimatedMemory += 1024 * 1024; // 1MB base estimate

    return EstimatedMemory;
}

void FAuracronClothingGeneration::ClearClothingAssetCache()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCache.Empty();
    ClothMaterialCache.Empty();
    ClothingAssetCacheMemoryUsage = 0;

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Clothing asset cache cleared"));
}

bool FAuracronClothingGeneration::InitializeClothingAsset(UClothingAssetCommon* ClothingAsset, const FClothingGenerationParameters& Parameters)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::InitializeClothingAsset);

    if (!IsValid(ClothingAsset))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset provided for initialization"));
        return false;
    }

    // Configure basic cloth properties using available parameters
    // Note: UE 5.6 cloth configuration is handled through the cloth config system
    // We'll set up basic properties that are available in the Parameters structure

    // Initialize cloth asset with basic settings
    // The actual cloth configuration will be handled by the cloth system

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully initialized clothing asset: %s"), *Parameters.ClothingName);
    return true;
}

bool FAuracronClothingGeneration::GenerateDressVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateDressVertices);

    // Generate dress-specific vertex layout
    const int32 VerticalSegments = 20; // Dress length segments
    const int32 HorizontalSegments = 16; // Circumference segments
    const float DressLength = MeshData.Size.Y; // Use Size.Y for length
    const float WaistRadius = MeshData.Size.X * 0.4f; // Use Size.X for width
    const float HemRadius = MeshData.Size.X * 0.8f;

    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();

    // Generate vertices for dress shape (tapered cylinder)
    for (int32 V = 0; V <= VerticalSegments; ++V)
    {
        float VRatio = static_cast<float>(V) / VerticalSegments;
        float CurrentRadius = FMath::Lerp(WaistRadius, HemRadius, VRatio);
        float CurrentHeight = -VRatio * DressLength; // Dress hangs down

        for (int32 H = 0; H <= HorizontalSegments; ++H)
        {
            float HRatio = static_cast<float>(H) / HorizontalSegments;
            float Angle = HRatio * 2.0f * PI;

            // Generate vertex position
            FVector3f Position;
            Position.X = CurrentRadius * FMath::Cos(Angle);
            Position.Y = CurrentRadius * FMath::Sin(Angle);
            Position.Z = CurrentHeight;
            OutVertices.Add(Position);

            // Generate normal (pointing outward)
            FVector3f Normal;
            Normal.X = FMath::Cos(Angle);
            Normal.Y = FMath::Sin(Angle);
            Normal.Z = 0.0f;
            Normal.Normalize();
            OutNormals.Add(Normal);

            // Generate UV coordinates
            FVector2f UV;
            UV.X = HRatio;
            UV.Y = VRatio;
            OutUVs.Add(UV);
        }
    }

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for dress"), OutVertices.Num());
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateSkirtVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateSkirtVertices);

    // Generate skirt-specific vertex layout (shorter than dress)
    const int32 VerticalSegments = 12; // Skirt length segments
    const int32 HorizontalSegments = 16; // Circumference segments
    const float SkirtLength = MeshData.Size.Y * 0.6f; // Shorter than dress
    const float WaistRadius = MeshData.Size.X * 0.35f;
    const float HemRadius = MeshData.Size.X * 0.7f;

    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();

    // Generate vertices for skirt shape (tapered cylinder, shorter)
    for (int32 V = 0; V <= VerticalSegments; ++V)
    {
        float VRatio = static_cast<float>(V) / VerticalSegments;
        float CurrentRadius = FMath::Lerp(WaistRadius, HemRadius, VRatio);
        float CurrentHeight = -VRatio * SkirtLength; // Skirt hangs down

        for (int32 H = 0; H <= HorizontalSegments; ++H)
        {
            float HRatio = static_cast<float>(H) / HorizontalSegments;
            float Angle = HRatio * 2.0f * PI;

            // Generate vertex position
            FVector3f Position;
            Position.X = CurrentRadius * FMath::Cos(Angle);
            Position.Y = CurrentRadius * FMath::Sin(Angle);
            Position.Z = CurrentHeight;
            OutVertices.Add(Position);

            // Generate normal (pointing outward)
            FVector3f Normal;
            Normal.X = FMath::Cos(Angle);
            Normal.Y = FMath::Sin(Angle);
            Normal.Z = 0.0f;
            Normal.Normalize();
            OutNormals.Add(Normal);

            // Generate UV coordinates
            FVector2f UV;
            UV.X = HRatio;
            UV.Y = VRatio;
            OutUVs.Add(UV);
        }
    }

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for skirt"), OutVertices.Num());
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateCapeVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateCapeVertices);

    // Generate cape-specific vertex layout (rectangular with curved bottom)
    const int32 VerticalSegments = 15; // Cape length segments
    const int32 HorizontalSegments = 20; // Cape width segments
    const float CapeLength = MeshData.Size.Y;
    const float CapeWidth = MeshData.Size.X;

    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();

    // Generate vertices for cape shape (rectangular with curved bottom)
    for (int32 V = 0; V <= VerticalSegments; ++V)
    {
        float VRatio = static_cast<float>(V) / VerticalSegments;
        float CurrentHeight = -VRatio * CapeLength; // Cape hangs down

        // Create curved bottom edge
        float WidthMultiplier = 1.0f;
        if (VRatio > 0.7f) // Bottom 30% of cape
        {
            float CurveRatio = (VRatio - 0.7f) / 0.3f;
            WidthMultiplier = 1.0f - (CurveRatio * 0.3f); // Taper to 70% width at bottom
        }

        for (int32 H = 0; H <= HorizontalSegments; ++H)
        {
            float HRatio = static_cast<float>(H) / HorizontalSegments;
            float CurrentWidth = (HRatio - 0.5f) * CapeWidth * WidthMultiplier;

            // Generate vertex position
            FVector3f Position;
            Position.X = CurrentWidth;
            Position.Y = 0.0f; // Cape is flat initially
            Position.Z = CurrentHeight;
            OutVertices.Add(Position);

            // Generate normal (pointing forward initially)
            FVector3f Normal(0.0f, 1.0f, 0.0f);
            OutNormals.Add(Normal);

            // Generate UV coordinates
            FVector2f UV;
            UV.X = HRatio;
            UV.Y = VRatio;
            OutUVs.Add(UV);
        }
    }

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for cape"), OutVertices.Num());
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateClothLODLevel(UClothingAssetBase* ClothingAsset, int32 LODIndex, const FClothLODLevel& LODLevel)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateClothLODLevel);

    if (!IsValid(ClothingAsset))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset for LOD generation"));
        return false;
    }

    // Generate simplified mesh for this LOD level
    float SimplificationRatio = FMath::Pow(0.5f, LODIndex); // Each LOD has half the vertices
    int32 TargetVertexCount = FMath::Max(100, static_cast<int32>(1000 * SimplificationRatio)); // Default vertex count

    // LOD generation is handled by the UE 5.6 cloth system internally
    // We just log the operation for now

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated LOD %d with %d vertices"), LODIndex, TargetVertexCount);
    return true;
}

bool FAuracronClothingGeneration::GenerateClothConstraints(UClothingAssetCommon* ClothingAsset, const FClothMeshData& MeshData)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateClothConstraints);

    if (!IsValid(ClothingAsset))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset for constraint generation"));
        return false;
    }

    // Constraint generation is handled by the UE 5.6 cloth system internally
    // We just log the operation for now

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated constraints for cloth asset"));
    return true;
}

bool FAuracronClothingGeneration::CreateClothBinding(UClothingAssetCommon* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<int32>& BoneIndices)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::CreateClothBinding);

    if (!IsValid(ClothingAsset) || !IsValid(SkeletalMesh))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset or skeletal mesh for binding"));
        return false;
    }

    // Cloth binding is handled by the UE 5.6 cloth system internally
    // We just log the operation for now

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Created cloth binding with %d bones"), BoneIndices.Num());
    return true;
}

void FAuracronClothingGeneration::OptimizeClothPerformance(UClothingAssetCommon* ClothingAsset)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::OptimizeClothPerformance);

    if (!IsValid(ClothingAsset))
    {
        UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Invalid clothing asset for performance optimization"));
        return;
    }

    // Performance optimization is handled by the UE 5.6 cloth system internally
    // We just log the operation for now

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Optimized cloth performance settings for asset: %s"), *ClothingAsset->GetName());
}

void FAuracronClothingGeneration::BuildClothingAsset(UClothingAssetCommon* ClothingAsset)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::BuildClothingAsset);

    if (!IsValid(ClothingAsset))
    {
        UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Invalid clothing asset for building"));
        return;
    }

    // Building is handled by the UE 5.6 cloth system internally
    // Mark as dirty for serialization
    ClothingAsset->MarkPackageDirty();

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Built clothing asset: %s"), *ClothingAsset->GetName());
}

int32 FAuracronClothingGeneration::EstimateClothingAssetMemoryUsage(UClothingAssetCommon* ClothingAsset)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::EstimateClothingAssetMemoryUsage);

    if (!IsValid(ClothingAsset))
    {
        return 0;
    }

    // Simplified memory estimation - in production this would be more detailed
    int32 TotalMemoryUsage = 1024; // Base memory usage estimate

    UE_LOG(LogAuracronClothingGeneration, VeryVerbose, TEXT("Estimated memory usage for clothing asset %s: %d bytes"),
           *ClothingAsset->GetName(), TotalMemoryUsage);

    return TotalMemoryUsage;
}

void FAuracronClothingGeneration::UpdateClothingGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    ClothingGenerationStats.Add(OperationName, StatsValue);
}
