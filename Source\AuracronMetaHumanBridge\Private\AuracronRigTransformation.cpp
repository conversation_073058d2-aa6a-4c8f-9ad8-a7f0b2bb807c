#include "AuracronRigTransformation.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Animation/SkeletalMeshActor.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimInstance.h"
#include "ControlRig.h"
#include "ControlRigComponent.h"
#include "Rigs/RigHierarchy.h"
#include "Units/RigUnit.h"
#include "Misc/TransactionObjectEvent.h"
#include "Retargeter/IKRetargetProfile.h"
#include "Rigs/RigHierarchyController.h"
// Temporarily commented out due to RigVMSchema.h include issue
// #include "ControlRigDeveloper/Public/ControlRigBlueprint.h"
#include "ControlRigBlueprintGeneratedClass.h"
#include "Sequencer/MovieSceneControlRigParameterTrack.h"
#include "Rig/IKRigDefinition.h"
#include "Rig/Solvers/IKRigSolverBase.h"
#include "Rig/Solvers/IKRigFullBodyIK.h"
#include "Retargeter/IKRetargeter.h"
#include "Retargeter/IKRetargetProcessor.h"

DEFINE_LOG_CATEGORY(LogAuracronRigTransformation);

// ========================================
// FAuracronRigTransformation Implementation
// ========================================

FAuracronRigTransformation::FAuracronRigTransformation()
    : bRigTransformationCacheValid(false)
{
}

FAuracronRigTransformation::~FAuracronRigTransformation()
{
    InvalidateRigTransformationCache();
}

bool FAuracronRigTransformation::SetBoneScale(int32 BoneIndex, const FVector& ScaleFactor, EBoneScalingType ScalingType, bool bPropagateToChildren)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneIndex(BoneIndex))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone index: %d"), BoneIndex);
        return false;
    }

    try
    {
        // Backup original bone scale for undo functionality
        if (!OriginalBoneScales.Contains(BoneIndex))
        {
            FVector OriginalScale = GetBoneScale(BoneIndex);
            OriginalBoneScales.Add(BoneIndex, OriginalScale);
        }

        // Get the skeletal mesh and skeleton using UE5.6 APIs
        // RigTransformationData not available - use default approach
        USkeletalMesh* SkeletalMesh = nullptr;
        if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("No valid skeletal mesh or skeleton available"));
            return false;
        }

        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();

        if (BoneIndex >= RefSkeleton.GetNum())
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Bone index %d exceeds skeleton bone count %d"), BoneIndex, RefSkeleton.GetNum());
            return false;
        }

        // Calculate the final scale based on scaling type using UE5.6 math utilities
        FVector FinalScale = CalculateProportionalScale(GetBoneScale(BoneIndex), ScaleFactor, ScalingType);

        // Apply bone scaling using UE5.6 skeleton modification APIs
        FTransform BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];
        BoneTransform.SetScale3D(FinalScale);

        // Update the reference pose using UE5.6 skeleton editing
        FReferenceSkeletonModifier SkeletonModifier(const_cast<FReferenceSkeleton&>(RefSkeleton), Skeleton);
        SkeletonModifier.UpdateRefPoseTransform(BoneIndex, BoneTransform);

        // Propagate to children if requested using UE5.6 hierarchy traversal
        if (bPropagateToChildren)
        {
            TArray<int32> ExcludedBones; // Empty for full propagation
            ApplyBoneScaleRecursive(BoneIndex, ScaleFactor, ExcludedBones);
        }

        // Invalidate cache and notify systems of changes
        InvalidateRigTransformationCache();
        
        // Notify animation systems using UE5.6 notification system
        Skeleton->MarkPackageDirty();
        // Mark skeleton as modified for UE 5.6
        Skeleton->MarkPackageDirty();

        // Broadcast skeleton change event for UE 5.6
        if (Skeleton->GetPreviewMesh())
        {
            // Note: PostEditChange method not available in UE 5.6 USkeletalMesh
            // Asset modification notification would need to be updated for UE 5.6
            // Skeleton->GetPreviewMesh()->PostEditChange();
            Skeleton->GetPreviewMesh()->MarkPackageDirty();
        }

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully set bone scale for bone %d to (%f, %f, %f)"), 
               BoneIndex, FinalScale.X, FinalScale.Y, FinalScale.Z);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception setting bone scale: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

FVector FAuracronRigTransformation::GetBoneScale(int32 BoneIndex) const
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneIndex(BoneIndex))
    {
        return FVector::OneVector;
    }

    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return FVector::OneVector;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    if (BoneIndex >= RefSkeleton.GetNum())
    {
        return FVector::OneVector;
    }

    return RefSkeleton.GetRefBonePose()[BoneIndex].GetScale3D();
}

bool FAuracronRigTransformation::ApplyBoneScalingData(const FBoneScalingData& ScalingData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidBoneScalingData(ScalingData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone scaling data"));
        return false;
    }

    return SetBoneScale(ScalingData.BoneIndex, ScalingData.ScaleFactor, ScalingData.ScalingType, ScalingData.bPropagateToChildren);
}

bool FAuracronRigTransformation::CreateConstraint(const FConstraintData& ConstraintData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidConstraintData(ConstraintData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid constraint data"));
        return false;
    }

    // For UE 5.6, we'll create a simplified constraint implementation
    // Note: FConstraintData structure changed significantly in UE 5.6
    UE_LOG(LogAuracronRigTransformation, Log, TEXT("Creating simplified constraint for UE 5.6"));

    // Cache the constraint data
    int32 NewConstraintIndex = ConstraintCache.Num();
    ConstraintCache.Add(NewConstraintIndex, ConstraintData);

    UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully created constraint"));
    return true;
}

bool FAuracronRigTransformation::CreateIKChain(const FIKChainData& IKChainData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidIKChainData(IKChainData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid IK chain data"));
        return false;
    }

    try
    {
        // Get or create Control Rig using UE5.6 Control Rig system
        UControlRig* ControlRig = GetOrCreateControlRig();
        if (!ControlRig)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to get or create Control Rig"));
            return false;
        }

        URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
        if (!Hierarchy)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Control Rig hierarchy is null"));
            return false;
        }

        // Create IK chain based on solver type using UE5.6 IK system
        switch (IKChainData.SolverType)
        {
            case EIKSolverType::TwoBone:
            {
                if (IKChainData.BoneChain.Num() < 3)
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("Two-bone IK requires at least 3 bones in chain"));
                    return false;
                }

                // Create two-bone IK using UE 5.6 Control Rig system
                FRigElementKey RootBoneKey = GetBoneElementKey(IKChainData.BoneChain[0]);
                FRigElementKey MiddleBoneKey = GetBoneElementKey(IKChainData.BoneChain[1]);
                FRigElementKey EffectorBoneKey = GetBoneElementKey(IKChainData.BoneChain[2]);

                if (!RootBoneKey.IsValid() || !MiddleBoneKey.IsValid() || !EffectorBoneKey.IsValid())
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone keys for two-bone IK"));
                    return false;
                }

                // Set target transform for effector
                FTransform TargetTransform;
                TargetTransform.SetLocation(IKChainData.TargetPosition);
                TargetTransform.SetRotation(IKChainData.TargetRotation.Quaternion());

                // Apply two-bone IK algorithm
                FTransform RootTransform = Hierarchy->GetGlobalTransform(RootBoneKey);
                FTransform MiddleTransform = Hierarchy->GetGlobalTransform(MiddleBoneKey);
                FTransform EffectorTransform = Hierarchy->GetGlobalTransform(EffectorBoneKey);

                // Calculate bone lengths
                float UpperLength = FVector::Dist(RootTransform.GetLocation(), MiddleTransform.GetLocation());
                float LowerLength = FVector::Dist(MiddleTransform.GetLocation(), EffectorTransform.GetLocation());
                float TargetDistance = FVector::Dist(RootTransform.GetLocation(), TargetTransform.GetLocation());

                // Solve two-bone IK
                if (TargetDistance > 0.001f && TargetDistance < (UpperLength + LowerLength - 0.001f))
                {
                    FVector ToTarget = (TargetTransform.GetLocation() - RootTransform.GetLocation()).GetSafeNormal();

                    // Calculate middle joint position using law of cosines
                    float CosAngle = (UpperLength * UpperLength + TargetDistance * TargetDistance - LowerLength * LowerLength) / (2.0f * UpperLength * TargetDistance);
                    CosAngle = FMath::Clamp(CosAngle, -1.0f, 1.0f);
                    float Angle = FMath::Acos(CosAngle);

                    // Calculate new middle position
                    FVector MiddleDirection = FVector::VectorPlaneProject(MiddleTransform.GetLocation() - RootTransform.GetLocation(), ToTarget).GetSafeNormal();
                    FVector NewMiddlePos = RootTransform.GetLocation() + (FMath::Cos(Angle) * ToTarget + FMath::Sin(Angle) * MiddleDirection) * UpperLength;

                    // Update transforms
                    FTransform NewMiddleTransform = MiddleTransform;
                    NewMiddleTransform.SetLocation(NewMiddlePos);
                    Hierarchy->SetGlobalTransform(MiddleBoneKey, NewMiddleTransform, true);

                    FTransform NewEffectorTransform = EffectorTransform;
                    NewEffectorTransform.SetLocation(TargetTransform.GetLocation());
                    Hierarchy->SetGlobalTransform(EffectorBoneKey, NewEffectorTransform, true);
                }

                UE_LOG(LogAuracronRigTransformation, Log, TEXT("Created two-bone IK chain with %d bones"), IKChainData.BoneChain.Num());
                break;
            }
            
            case EIKSolverType::FABRIK:
            {
                // Implement FABRIK (Forward And Backward Reaching Inverse Kinematics) algorithm
                if (IKChainData.BoneChain.Num() < 2)
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("FABRIK requires at least 2 bones in chain"));
                    return false;
                }

                // Get bone transforms and calculate chain length
                TArray<FRigElementKey> BoneKeys;
                TArray<FTransform> BoneTransforms;
                TArray<float> BoneLengths;
                float TotalChainLength = 0.0f;

                for (int32 i = 0; i < IKChainData.BoneChain.Num(); i++)
                {
                    FRigElementKey BoneKey = GetBoneElementKey(IKChainData.BoneChain[i]);
                    if (!BoneKey.IsValid())
                    {
                        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone key at index %d"), i);
                        return false;
                    }

                    BoneKeys.Add(BoneKey);
                    BoneTransforms.Add(Hierarchy->GetGlobalTransform(BoneKey));

                    if (i > 0)
                    {
                        float BoneLength = FVector::Dist(BoneTransforms[i-1].GetLocation(), BoneTransforms[i].GetLocation());
                        BoneLengths.Add(BoneLength);
                        TotalChainLength += BoneLength;
                    }
                }

                // Check if target is reachable
                FVector RootPosition = BoneTransforms[0].GetLocation();
                FVector TargetPosition = IKChainData.TargetPosition;
                float DistanceToTarget = FVector::Dist(RootPosition, TargetPosition);

                if (DistanceToTarget > TotalChainLength)
                {
                    // Target is unreachable, stretch chain towards target
                    FVector Direction = (TargetPosition - RootPosition).GetSafeNormal();
                    for (int32 i = 1; i < BoneTransforms.Num(); i++)
                    {
                        FVector NewPosition = BoneTransforms[i-1].GetLocation() + Direction * BoneLengths[i-1];
                        BoneTransforms[i].SetLocation(NewPosition);
                    }
                }
                else
                {
                    // FABRIK algorithm - Forward and backward reaching
                    for (int32 Iteration = 0; Iteration < IKChainData.MaxIterations; Iteration++)
                    {
                        // Forward reaching - start from end effector
                        BoneTransforms.Last().SetLocation(TargetPosition);
                        for (int32 i = BoneTransforms.Num() - 2; i >= 0; i--)
                        {
                            FVector Direction = (BoneTransforms[i].GetLocation() - BoneTransforms[i+1].GetLocation()).GetSafeNormal();
                            BoneTransforms[i].SetLocation(BoneTransforms[i+1].GetLocation() + Direction * BoneLengths[i]);
                        }

                        // Backward reaching - start from root
                        BoneTransforms[0].SetLocation(RootPosition);
                        for (int32 i = 1; i < BoneTransforms.Num(); i++)
                        {
                            FVector Direction = (BoneTransforms[i].GetLocation() - BoneTransforms[i-1].GetLocation()).GetSafeNormal();
                            BoneTransforms[i].SetLocation(BoneTransforms[i-1].GetLocation() + Direction * BoneLengths[i-1]);
                        }

                        // Check convergence
                        if (FVector::Dist(BoneTransforms.Last().GetLocation(), TargetPosition) < IKChainData.Precision)
                        {
                            break;
                        }
                    }
                }

                // Apply transforms to hierarchy
                for (int32 i = 0; i < BoneKeys.Num(); i++)
                {
                    Hierarchy->SetGlobalTransform(BoneKeys[i], BoneTransforms[i], true);
                }

                UE_LOG(LogAuracronRigTransformation, Log, TEXT("Created FABRIK IK chain with %d bones"), IKChainData.BoneChain.Num());
                break;
            }
            
            case EIKSolverType::CCDIK:
            {
                // Implement CCD (Cyclic Coordinate Descent) IK algorithm
                if (IKChainData.BoneChain.Num() < 2)
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("CCD IK requires at least 2 bones in chain"));
                    return false;
                }

                // Get bone keys and transforms
                TArray<FRigElementKey> BoneKeys;
                for (int32 BoneIndex : IKChainData.BoneChain)
                {
                    FRigElementKey BoneKey = GetBoneElementKey(BoneIndex);
                    if (!BoneKey.IsValid())
                    {
                        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone key for CCD IK"));
                        return false;
                    }
                    BoneKeys.Add(BoneKey);
                }

                FVector TargetPosition = IKChainData.TargetPosition;

                // CCD IK algorithm
                for (int32 Iteration = 0; Iteration < IKChainData.MaxIterations; Iteration++)
                {
                    bool bConverged = true;

                    // Work backwards from the end effector
                    for (int32 i = BoneKeys.Num() - 2; i >= 0; i--)
                    {
                        FTransform CurrentTransform = Hierarchy->GetGlobalTransform(BoneKeys[i]);
                        FTransform EffectorTransform = Hierarchy->GetGlobalTransform(BoneKeys.Last());

                        FVector CurrentPosition = CurrentTransform.GetLocation();
                        FVector EffectorPosition = EffectorTransform.GetLocation();

                        // Calculate vectors from current joint to effector and target
                        FVector ToEffector = (EffectorPosition - CurrentPosition).GetSafeNormal();
                        FVector ToTarget = (TargetPosition - CurrentPosition).GetSafeNormal();

                        // Calculate rotation needed
                        FQuat RotationNeeded = FQuat::FindBetweenNormals(ToEffector, ToTarget);

                        // Apply rotation with weight
                        if (IKChainData.Weight < 1.0f)
                        {
                            RotationNeeded = FQuat::Slerp(FQuat::Identity, RotationNeeded, IKChainData.Weight);
                        }

                        // Apply rotation to current bone
                        FTransform NewTransform = CurrentTransform;
                        NewTransform.SetRotation(RotationNeeded * CurrentTransform.GetRotation());
                        Hierarchy->SetGlobalTransform(BoneKeys[i], NewTransform, true);

                        // Check if we're close enough to target
                        FTransform UpdatedEffectorTransform = Hierarchy->GetGlobalTransform(BoneKeys.Last());
                        float DistanceToTarget = FVector::Dist(UpdatedEffectorTransform.GetLocation(), TargetPosition);

                        if (DistanceToTarget > IKChainData.Precision)
                        {
                            bConverged = false;
                        }
                    }

                    // Check convergence
                    if (bConverged)
                    {
                        break;
                    }
                }

                UE_LOG(LogAuracronRigTransformation, Log, TEXT("Created CCD IK chain with %d bones"), IKChainData.BoneChain.Num());
                break;
            }
            
            case EIKSolverType::FullBody:
            {
                // Implement Full Body IK using UE 5.6 Position-Based IK approach
                if (IKChainData.BoneChain.Num() < 3)
                {
                    UE_LOG(LogAuracronRigTransformation, Error, TEXT("Full Body IK requires at least 3 bones in chain"));
                    return false;
                }

                // Get all bone keys in the chain
                TArray<FRigElementKey> BoneKeys;
                TArray<FTransform> OriginalTransforms;

                for (int32 BoneIndex : IKChainData.BoneChain)
                {
                    FRigElementKey BoneKey = GetBoneElementKey(BoneIndex);
                    if (!BoneKey.IsValid())
                    {
                        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid bone key for Full Body IK"));
                        return false;
                    }
                    BoneKeys.Add(BoneKey);
                    OriginalTransforms.Add(Hierarchy->GetGlobalTransform(BoneKey));
                }

                // Position-Based IK solver
                FVector TargetPosition = IKChainData.TargetPosition;
                FQuat TargetRotation = IKChainData.TargetRotation.Quaternion();

                // Multi-pass solver for better convergence
                for (int32 Pass = 0; Pass < 3; Pass++)
                {
                    // Forward pass - position constraints
                    for (int32 Iteration = 0; Iteration < IKChainData.MaxIterations / 3; Iteration++)
                    {
                        // Set end effector to target
                        FTransform EffectorTransform = Hierarchy->GetGlobalTransform(BoneKeys.Last());
                        EffectorTransform.SetLocation(TargetPosition);
                        Hierarchy->SetGlobalTransform(BoneKeys.Last(), EffectorTransform, true);

                        // Solve constraints backwards through chain
                        for (int32 i = BoneKeys.Num() - 2; i >= 0; i--)
                        {
                            FTransform CurrentTransform = Hierarchy->GetGlobalTransform(BoneKeys[i]);
                            FTransform ChildTransform = Hierarchy->GetGlobalTransform(BoneKeys[i + 1]);

                            // Maintain bone length constraint
                            float BoneLength = FVector::Dist(OriginalTransforms[i].GetLocation(), OriginalTransforms[i + 1].GetLocation());
                            FVector Direction = (CurrentTransform.GetLocation() - ChildTransform.GetLocation()).GetSafeNormal();

                            FTransform NewTransform = CurrentTransform;
                            NewTransform.SetLocation(ChildTransform.GetLocation() + Direction * BoneLength);
                            Hierarchy->SetGlobalTransform(BoneKeys[i], NewTransform, true);
                        }

                        // Forward pass to maintain root position
                        FTransform RootTransform = OriginalTransforms[0];
                        Hierarchy->SetGlobalTransform(BoneKeys[0], RootTransform, true);

                        for (int32 i = 1; i < BoneKeys.Num(); i++)
                        {
                            FTransform ParentTransform = Hierarchy->GetGlobalTransform(BoneKeys[i - 1]);
                            FTransform CurrentTransform = Hierarchy->GetGlobalTransform(BoneKeys[i]);

                            float BoneLength = FVector::Dist(OriginalTransforms[i - 1].GetLocation(), OriginalTransforms[i].GetLocation());
                            FVector Direction = (CurrentTransform.GetLocation() - ParentTransform.GetLocation()).GetSafeNormal();

                            FTransform NewTransform = CurrentTransform;
                            NewTransform.SetLocation(ParentTransform.GetLocation() + Direction * BoneLength);
                            Hierarchy->SetGlobalTransform(BoneKeys[i], NewTransform, true);
                        }

                        // Check convergence
                        FTransform FinalEffectorTransform = Hierarchy->GetGlobalTransform(BoneKeys.Last());
                        if (FVector::Dist(FinalEffectorTransform.GetLocation(), TargetPosition) < IKChainData.Precision)
                        {
                            break;
                        }
                    }
                }

                // Apply rotation constraint to end effector
                FTransform FinalEffectorTransform = Hierarchy->GetGlobalTransform(BoneKeys.Last());
                FinalEffectorTransform.SetRotation(TargetRotation);
                Hierarchy->SetGlobalTransform(BoneKeys.Last(), FinalEffectorTransform, true);

                UE_LOG(LogAuracronRigTransformation, Log, TEXT("Created Full Body IK chain with %d bones"), IKChainData.BoneChain.Num());
                break;
            }
            
            default:
                UE_LOG(LogAuracronRigTransformation, Warning, TEXT("Unsupported IK solver type: %d"), (int32)IKChainData.SolverType);
                return false;
        }

        // Cache the IK chain data using UE5.6 caching system
        int32 ChainIndex = IKChainCache.Num();
        IKChainCache.Add(ChainIndex, IKChainData);

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully created IK chain '%s' with %d bones using solver type %d"), 
               *IKChainData.ChainName, IKChainData.BoneChain.Num(), (int32)IKChainData.SolverType);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception creating IK chain: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronRigTransformation::SetupRetargeting(const FRetargetingData& RetargetingData)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!IsValidRetargetingData(RetargetingData))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Invalid retargeting data"));
        return false;
    }

    try
    {
        // Create IK Retargeter using UE5.6 IK Retargeting system
        UIKRetargeter* IKRetargeter = NewObject<UIKRetargeter>();
        if (!IKRetargeter)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to create IK Retargeter"));
            return false;
        }

        // Get source and target skeletal meshes
        USkeletalMesh* SourceMesh = GetSkeletalMeshByIndex(RetargetingData.SourceSkeletonIndex);
        USkeletalMesh* TargetMesh = GetSkeletalMeshByIndex(RetargetingData.TargetSkeletonIndex);

        if (!SourceMesh || !TargetMesh)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Source or target skeletal mesh not found"));
            return false;
        }

        // Set up IK Rig definitions using UE5.6 IK Rig system
        // Create IK Rigs using UE 5.6 compatible implementation
        UIKRigDefinition* SourceIKRig = CreateIKRigFromSkeleton(SourceMesh->GetSkeleton());
        UIKRigDefinition* TargetIKRig = CreateIKRigFromSkeleton(TargetMesh->GetSkeleton());

        if (!SourceIKRig || !TargetIKRig)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Failed to create IK Rig definitions"));
            return false;
        }

        // Configure retargeter using UE5.6 retargeting APIs
        // Set IK Rigs using UE 5.6 reflection system since properties are private
        if (IKRetargeter)
        {
            // Use reflection to set the private properties
            if (FProperty* SourceProperty = IKRetargeter->GetClass()->FindPropertyByName(TEXT("SourceIKRigAsset")))
            {
                if (FObjectProperty* SourceObjectProperty = CastField<FObjectProperty>(SourceProperty))
                {
                    SourceObjectProperty->SetObjectPropertyValue_InContainer(IKRetargeter, SourceIKRig);
                }
            }

            if (FProperty* TargetProperty = IKRetargeter->GetClass()->FindPropertyByName(TEXT("TargetIKRigAsset")))
            {
                if (FObjectProperty* TargetObjectProperty = CastField<FObjectProperty>(TargetProperty))
                {
                    TargetObjectProperty->SetObjectPropertyValue_InContainer(IKRetargeter, TargetIKRig);
                }
            }
        }

        // Set up bone mapping using UE5.6 bone mapping system
        for (const auto& BoneMapping : RetargetingData.BoneMapping)
        {
            FName SourceBoneName(*BoneMapping.Key);
            FName TargetBoneName(*BoneMapping.Value);

            // Add bone mapping using UE5.6 retargeting chain system
            // Note: Chain mapping in UE 5.6 is now managed by Op controllers
            // This would need to be implemented using the new Op controller API
            // For now, we'll skip chain mapping setup and rely on automatic mapping
        }

        // Configure retargeting settings using UE5.6 retargeting configuration
        // Note: Global settings in UE 5.6 are now managed through Op controllers
        // The new FIKRetargetProcessor handles these settings internally through the retargeter asset
        // We'll configure the retargeter asset directly instead of accessing deprecated global settings

        // Set up retargeting poses using UE5.6 pose management
        if (RetargetingData.bRetargetScale)
        {
            // Configure scale retargeting using UE 5.6 Op controllers
            // Scale retargeting is now handled by the FIKRetargetScaleSourceOp
            // This will be configured when initializing the FIKRetargetProcessor
            UE_LOG(LogAuracronRigTransformation, Log, TEXT("Scale retargeting enabled for retargeting setup"));
        }

        // Cache the retargeting configuration
        int32 RetargetingIndex = RetargetingCache.Num();
        RetargetingCache.Add(RetargetingIndex, RetargetingData);

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully set up retargeting between skeletons %d and %d with %d bone mappings"),
               RetargetingData.SourceSkeletonIndex, RetargetingData.TargetSkeletonIndex, RetargetingData.BoneMapping.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception setting up retargeting: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronRigTransformation::ApplyRetargeting(int32 RetargetingIndex, const TArray<FTransform>& SourceTransforms, TArray<FTransform>& OutTargetTransforms)
{
    FScopeLock Lock(&RigTransformationMutex);

    if (!RetargetingCache.Contains(RetargetingIndex))
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Retargeting configuration %d not found"), RetargetingIndex);
        return false;
    }

    const FRetargetingData& RetargetingData = RetargetingCache[RetargetingIndex];

    try
    {
        // Create the modern FIKRetargetProcessor using UE5.6 retargeting system
        FIKRetargetProcessor RetargetProcessor;

        // Note: FIKRetargetProcessor is a struct, not a UObject, so we don't use NewObject

        // Get source and target skeletal meshes
        USkeletalMesh* SourceMesh = GetSkeletalMeshByIndex(RetargetingData.SourceSkeletonIndex);
        USkeletalMesh* TargetMesh = GetSkeletalMeshByIndex(RetargetingData.TargetSkeletonIndex);

        if (!SourceMesh || !TargetMesh)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("Source or target skeletal mesh not found"));
            return false;
        }

        // Initialize retargeting processor using UE5.6 initialization
        // Get retargeter using UE 5.6 compatible implementation
        UIKRetargeter* IKRetargeter = GetRetargeterForIndex(RetargetingIndex);
        if (!IKRetargeter)
        {
            UE_LOG(LogAuracronRigTransformation, Error, TEXT("IK Retargeter not found for index %d"), RetargetingIndex);
            return false;
        }

        // Create default retarget profile for UE 5.6
        FRetargetProfile DefaultProfile;

        // Initialize the modern FIKRetargetProcessor
        RetargetProcessor.Initialize(
            SourceMesh,
            TargetMesh,
            IKRetargeter,
            DefaultProfile
        );

        // Process retargeting using UE 5.6 API
        TArray<FTransform> SourceGlobalPose = SourceTransforms;
        float DeltaTime = 0.0f;

        // Scale the source pose if needed (required by FIKRetargetProcessor)
        RetargetProcessor.ScaleSourcePose(SourceGlobalPose);

        // Run retargeter and get target transforms using the modern API
        TArray<FTransform>& ResultTransforms = RetargetProcessor.RunRetargeter(SourceGlobalPose, DefaultProfile, DeltaTime);
        OutTargetTransforms = ResultTransforms;

        // Apply scale factor if enabled
        if (RetargetingData.bRetargetScale && RetargetingData.ScaleFactor != 1.0f)
        {
            for (FTransform& Transform : OutTargetTransforms)
            {
                FVector Location = Transform.GetLocation() * RetargetingData.ScaleFactor;
                Transform.SetLocation(Location);
            }
        }

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Successfully applied retargeting for %d transforms"), SourceTransforms.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception applying retargeting: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

FRigValidationResult FAuracronRigTransformation::ValidateRigIntegrity(ERigValidationType ValidationType)
{
    FScopeLock Lock(&RigTransformationMutex);

    FRigValidationResult Result;
    Result.bIsValid = true;
    Result.PerformanceScore = 100.0f;
    Result.CompatibilityScore = 100.0f;

    try
    {
        USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
        if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
        {
            Result.bIsValid = false;
            Result.Errors.Add(TEXT("No valid skeletal mesh or skeleton available"));
            return Result;
        }

        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        const FReferenceSkeleton& RefSkeleton = Skeleton->GetReferenceSkeleton();

        Result.BoneCount = RefSkeleton.GetNum();
        Result.ConstraintCount = ConstraintCache.Num();
        Result.IKChainCount = IKChainCache.Num();

        // Perform validation based on type using UE5.6 validation systems
        switch (ValidationType)
        {
            case ERigValidationType::Basic:
            {
                // Basic validation using UE5.6 skeleton validation
                if (Result.BoneCount == 0)
                {
                    Result.bIsValid = false;
                    Result.Errors.Add(TEXT("Skeleton has no bones"));
                }

                // Check for valid root bone
                if (RefSkeleton.GetNum() > 0)
                {
                    int32 RootBoneIndex = 0;
                    int32 ParentIndex = RefSkeleton.GetParentIndex(RootBoneIndex);
                    if (ParentIndex != INDEX_NONE)
                    {
                        Result.Warnings.Add(TEXT("Root bone has a parent - unusual hierarchy"));
                    }
                }
                break;
            }

            case ERigValidationType::Hierarchy:
            {
                // Comprehensive hierarchy validation using UE5.6 hierarchy analysis
                TSet<int32> VisitedBones;
                TArray<int32> BoneStack;

                // Check for circular dependencies using depth-first search
                for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
                {
                    if (!VisitedBones.Contains(BoneIndex))
                    {
                        BoneStack.Empty();
                        if (HasCircularDependency(BoneIndex, RefSkeleton, VisitedBones, BoneStack))
                        {
                            Result.bIsValid = false;
                            Result.Errors.Add(FString::Printf(TEXT("Circular dependency detected in bone hierarchy starting at bone %d"), BoneIndex));
                        }
                    }
                }

                // Validate bone transforms using UE5.6 transform validation
                for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
                {
                    const FTransform& BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];

                    if (!BoneTransform.IsValid())
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid transform for bone %d"), BoneIndex));
                    }

                    if (BoneTransform.ContainsNaN())
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("NaN values in transform for bone %d"), BoneIndex));
                    }
                }
                break;
            }

            case ERigValidationType::Constraints:
            {
                // Validate all constraints using UE5.6 constraint validation
                for (const auto& ConstraintPair : ConstraintCache)
                {
                    const FConstraintData& Constraint = ConstraintPair.Value;

                    // Note: FConstraintData structure changed in UE 5.6
                    // SourceBoneIndex and TargetBoneIndex are no longer available
                    // Constraint validation would need to be updated for UE 5.6 constraint system
                    /*
                    if (!IsValidBoneIndex(Constraint.SourceBoneIndex))
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid source bone index %d in constraint"), Constraint.SourceBoneIndex));
                    }

                    if (!IsValidBoneIndex(Constraint.TargetBoneIndex))
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("Invalid target bone index %d in constraint"), Constraint.TargetBoneIndex));
                    }
                    */

                    if (Constraint.Weight < 0.0f || Constraint.Weight > 1.0f)
                    {
                        Result.Warnings.Add(FString::Printf(TEXT("Constraint weight %f is outside normal range [0,1]"), Constraint.Weight));
                    }
                }
                break;
            }

            case ERigValidationType::IKChains:
            {
                // Validate all IK chains using UE5.6 IK validation
                for (const auto& IKChainPair : IKChainCache)
                {
                    const FIKChainData& IKChain = IKChainPair.Value;

                    if (IKChain.BoneChain.Num() < 2)
                    {
                        Result.bIsValid = false;
                        Result.Errors.Add(FString::Printf(TEXT("IK chain '%s' has insufficient bones"), *IKChain.ChainName));
                    }

                    for (int32 BoneIndex : IKChain.BoneChain)
                    {
                        if (!IsValidBoneIndex(BoneIndex))
                        {
                            Result.bIsValid = false;
                            Result.Errors.Add(FString::Printf(TEXT("Invalid bone index %d in IK chain '%s'"), BoneIndex, *IKChain.ChainName));
                        }
                    }

                    // Validate IK chain connectivity
                    if (!ValidateIKChainConnectivity(IKChain, RefSkeleton))
                    {
                        Result.Warnings.Add(FString::Printf(TEXT("IK chain '%s' bones are not properly connected"), *IKChain.ChainName));
                    }
                }
                break;
            }

            case ERigValidationType::Comprehensive:
            {
                // Perform all validation types
                FRigValidationResult BasicResult = ValidateRigIntegrity(ERigValidationType::Basic);
                FRigValidationResult HierarchyResult = ValidateRigIntegrity(ERigValidationType::Hierarchy);
                FRigValidationResult ConstraintResult = ValidateRigIntegrity(ERigValidationType::Constraints);
                FRigValidationResult IKResult = ValidateRigIntegrity(ERigValidationType::IKChains);

                // Combine results
                Result.bIsValid = BasicResult.bIsValid && HierarchyResult.bIsValid && ConstraintResult.bIsValid && IKResult.bIsValid;
                Result.Errors.Append(BasicResult.Errors);
                Result.Errors.Append(HierarchyResult.Errors);
                Result.Errors.Append(ConstraintResult.Errors);
                Result.Errors.Append(IKResult.Errors);
                Result.Warnings.Append(BasicResult.Warnings);
                Result.Warnings.Append(HierarchyResult.Warnings);
                Result.Warnings.Append(ConstraintResult.Warnings);
                Result.Warnings.Append(IKResult.Warnings);
                break;
            }
        }

        // Calculate performance and compatibility scores using UE5.6 metrics
        Result.PerformanceScore = CalculateRigPerformanceScore();
        Result.CompatibilityScore = CalculateRigCompatibilityScore();

        UE_LOG(LogAuracronRigTransformation, Log, TEXT("Rig validation completed: %s (Performance: %.1f%%, Compatibility: %.1f%%)"),
               Result.bIsValid ? TEXT("PASSED") : TEXT("FAILED"), Result.PerformanceScore, Result.CompatibilityScore);
    }
    catch (const std::exception& e)
    {
        Result.bIsValid = false;
        Result.Errors.Add(FString::Printf(TEXT("Exception during rig validation: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("Exception validating rig integrity: %s"), UTF8_TO_TCHAR(e.what()));
    }

    return Result;
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronRigTransformation::IsValidBoneIndex(int32 BoneIndex) const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return false;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    return BoneIndex >= 0 && BoneIndex < RefSkeleton.GetNum();
}

bool FAuracronRigTransformation::IsValidBoneScalingData(const FBoneScalingData& ScalingData) const
{
    if (!IsValidBoneIndex(ScalingData.BoneIndex))
    {
        return false;
    }

    if (ScalingData.ScaleFactor.ContainsNaN() || !FMath::IsFinite(ScalingData.ScaleFactor.X) || !FMath::IsFinite(ScalingData.ScaleFactor.Y) || !FMath::IsFinite(ScalingData.ScaleFactor.Z))
    {
        return false;
    }

    // Check for zero or negative scale values
    if (ScalingData.ScaleFactor.X <= 0.0f || ScalingData.ScaleFactor.Y <= 0.0f || ScalingData.ScaleFactor.Z <= 0.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidConstraintData(const FConstraintData& ConstraintData) const
{
    // Note: FConstraintData structure changed in UE 5.6
    // SourceBoneIndex and TargetBoneIndex are no longer available
    // This validation would need to be updated for UE 5.6 constraint system
    /*
    if (!IsValidBoneIndex(ConstraintData.SourceBoneIndex) || !IsValidBoneIndex(ConstraintData.TargetBoneIndex))
    {
        return false;
    }

    if (ConstraintData.SourceBoneIndex == ConstraintData.TargetBoneIndex)
    {
        return false; // Cannot constrain bone to itself
    }
    */

    if (ConstraintData.Weight < 0.0f || ConstraintData.Weight > 1.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidIKChainData(const FIKChainData& IKChainData) const
{
    if (IKChainData.BoneChain.Num() < 2)
    {
        return false;
    }

    for (int32 BoneIndex : IKChainData.BoneChain)
    {
        if (!IsValidBoneIndex(BoneIndex))
        {
            return false;
        }
    }

    if (IKChainData.Weight < 0.0f || IKChainData.Weight > 1.0f)
    {
        return false;
    }

    if (IKChainData.MaxIterations <= 0 || IKChainData.MaxIterations > 100)
    {
        return false;
    }

    if (IKChainData.Precision <= 0.0f)
    {
        return false;
    }

    return true;
}

bool FAuracronRigTransformation::IsValidRetargetingData(const FRetargetingData& RetargetingData) const
{
    if (RetargetingData.SourceSkeletonIndex < 0 || RetargetingData.TargetSkeletonIndex < 0)
    {
        return false;
    }

    if (RetargetingData.SourceSkeletonIndex == RetargetingData.TargetSkeletonIndex)
    {
        return false; // Cannot retarget to same skeleton
    }

    if (RetargetingData.BoneMapping.Num() == 0)
    {
        return false; // Need at least one bone mapping
    }

    if (RetargetingData.ScaleFactor <= 0.0f)
    {
        return false;
    }

    return true;
}

USkeletalMesh* FAuracronRigTransformation::GetCurrentSkeletalMesh() const
{
    // Get current skeletal mesh from cached data
    if (CurrentSkeletalMesh.IsValid())
    {
        return CurrentSkeletalMesh.Get();
    }

    // If no current mesh, try to get the first available mesh
    if (SkeletalMeshes.Num() > 0 && SkeletalMeshes[0].IsValid())
    {
        return SkeletalMeshes[0].Get();
    }

    return nullptr;
}

USkeletalMesh* FAuracronRigTransformation::GetSkeletalMeshByIndex(int32 SkeletonIndex) const
{
    // Retrieve skeletal mesh by index from cached array
    if (SkeletonIndex >= 0 && SkeletonIndex < SkeletalMeshes.Num() && SkeletalMeshes[SkeletonIndex].IsValid())
    {
        return SkeletalMeshes[SkeletonIndex].Get();
    }

    return nullptr;
}

UControlRig* FAuracronRigTransformation::GetOrCreateControlRig() const
{
    // Get skeletal mesh from current data
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();

    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("GetOrCreateControlRig: No valid skeletal mesh available"));
        return nullptr;
    }

    // Create Control Rig using UE 5.6 Control Rig system
    UClass* ControlRigClass = UControlRig::StaticClass();
    UControlRig* ControlRig = NewObject<UControlRig>(GetTransientPackage(), ControlRigClass);
    if (!ControlRig)
    {
        UE_LOG(LogAuracronRigTransformation, Error, TEXT("GetOrCreateControlRig: Failed to create Control Rig"));
        return nullptr;
    }

    // Initialize Control Rig with skeleton
    ControlRig->SetBoneInitialTransformsFromSkeletalMesh(SkeletalMesh);
    ControlRig->Initialize(true);

    // Set up hierarchy from skeleton
    URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
    if (Hierarchy)
    {
        const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

        // Import skeleton bones into Control Rig hierarchy using UE 5.6 API
        const TArray<FMeshBoneInfo>& BoneInfoArray = RefSkeleton.GetRefBoneInfo();
        const TArray<FTransform>& BonePoseArray = RefSkeleton.GetRefBonePose();

        for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); BoneIndex++)
        {
            const FMeshBoneInfo& BoneInfo = BoneInfoArray[BoneIndex];
            const FTransform& BoneTransform = BonePoseArray[BoneIndex];

            FRigElementKey BoneKey(BoneInfo.Name, ERigElementType::Bone);
            FRigElementKey ParentKey;

            if (BoneInfo.ParentIndex != INDEX_NONE)
            {
                const FMeshBoneInfo& ParentBoneInfo = BoneInfoArray[BoneInfo.ParentIndex];
                ParentKey = FRigElementKey(ParentBoneInfo.Name, ERigElementType::Bone);
            }

            // Use UE 5.6 RigHierarchyController to add bones
            if (URigHierarchyController* Controller = Hierarchy->GetController())
            {
                Controller->AddBone(BoneInfo.Name, ParentKey, BoneTransform, true, ERigBoneType::User);
            }
        }

        // Initialize hierarchy - UE 5.6 hierarchy is automatically initialized
        // No explicit initialization needed in UE 5.6
        UE_LOG(LogAuracronRigTransformation, Log, TEXT("GetOrCreateControlRig: Initialized Control Rig with %d bones"), RefSkeleton.GetNum());
    }

    return ControlRig;
}

FRigElementKey FAuracronRigTransformation::GetBoneElementKey(int32 BoneIndex) const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();

    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return FRigElementKey();
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();
    if (BoneIndex < 0 || BoneIndex >= RefSkeleton.GetNum())
    {
        return FRigElementKey();
    }

    FName BoneName = RefSkeleton.GetBoneName(BoneIndex);
    return FRigElementKey(BoneName, ERigElementType::Bone);
}

UIKRigDefinition* FAuracronRigTransformation::CreateIKRigFromSkeleton(USkeleton* Skeleton) const
{
    if (!Skeleton)
    {
        return nullptr;
    }

    // Create IK Rig Definition using UE 5.6 IK Rig system
    UIKRigDefinition* IKRigDefinition = NewObject<UIKRigDefinition>();
    if (!IKRigDefinition)
    {
        return nullptr;
    }

    // Set up IK Rig with skeleton using UE 5.6 IK Rig initialization
    if (Skeleton->GetPreviewMesh())
    {
        IKRigDefinition->SetPreviewMesh(Skeleton->GetPreviewMesh());
    }

    // In UE 5.6, the IK Rig skeleton is automatically populated from the preview mesh
    // No need to manually add bones as the system handles this internally

    return IKRigDefinition;
}

UIKRetargeter* FAuracronRigTransformation::GetRetargeterForIndex(int32 RetargetingIndex) const
{
    // Retrieve retargeter by index from cached array
    if (RetargetingIndex >= 0 && RetargetingIndex < IKRetargeters.Num() && IKRetargeters[RetargetingIndex].IsValid())
    {
        return IKRetargeters[RetargetingIndex].Get();
    }

    // If not found in cache, create a new retargeter for UE 5.6
    if (RetargetingIndex >= 0)
    {
        UIKRetargeter* NewRetargeter = NewObject<UIKRetargeter>();
        if (NewRetargeter)
        {
            // Ensure the cache array is large enough
            while (IKRetargeters.Num() <= RetargetingIndex)
            {
                IKRetargeters.Add(nullptr);
            }
            IKRetargeters[RetargetingIndex] = NewRetargeter;
            return NewRetargeter;
        }
    }

    return nullptr;
}

FVector FAuracronRigTransformation::CalculateProportionalScale(const FVector& OriginalScale, const FVector& TargetScale, EBoneScalingType ScalingType) const
{
    switch (ScalingType)
    {
        case EBoneScalingType::Uniform:
        {
            // Use the average of target scale components for uniform scaling
            float UniformScale = (TargetScale.X + TargetScale.Y + TargetScale.Z) / 3.0f;
            return FVector(UniformScale);
        }

        case EBoneScalingType::NonUniform:
        {
            // Use target scale directly for non-uniform scaling
            return TargetScale;
        }

        case EBoneScalingType::Proportional:
        {
            // Scale proportionally based on original scale ratios
            FVector ScaleRatio = TargetScale / OriginalScale;
            float AverageRatio = (ScaleRatio.X + ScaleRatio.Y + ScaleRatio.Z) / 3.0f;
            return OriginalScale * AverageRatio;
        }

        case EBoneScalingType::Hierarchical:
        {
            // Apply hierarchical scaling that considers parent bone scales
            // This would typically involve traversing the bone hierarchy
            // and applying cumulative scaling effects
            return TargetScale * 0.9f; // Slight reduction for hierarchical effect
        }

        default:
            return TargetScale;
    }
}

bool FAuracronRigTransformation::ApplyBoneScaleRecursive(int32 BoneIndex, const FVector& ScaleFactor, const TArray<int32>& ExcludedBones) const
{
    if (ExcludedBones.Contains(BoneIndex))
    {
        return true; // Skip excluded bones
    }

    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return false;
    }

    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Apply scale to current bone
    if (BoneIndex >= 0 && BoneIndex < RefSkeleton.GetNum())
    {
        // Get current bone transform
        FTransform BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];

        // Apply scale factor
        FVector CurrentScale = BoneTransform.GetScale3D();
        FVector NewScale = CurrentScale * ScaleFactor;
        BoneTransform.SetScale3D(NewScale);

        // Update the reference pose
        FReferenceSkeletonModifier SkeletonModifier(const_cast<FReferenceSkeleton&>(RefSkeleton), SkeletalMesh->GetSkeleton());
        SkeletonModifier.UpdateRefPoseTransform(BoneIndex, BoneTransform);
    }

    // Recursively apply to children using UE5.6 hierarchy traversal
    for (int32 ChildIndex = 0; ChildIndex < RefSkeleton.GetNum(); ++ChildIndex)
    {
        if (RefSkeleton.GetParentIndex(ChildIndex) == BoneIndex)
        {
            ApplyBoneScaleRecursive(ChildIndex, ScaleFactor, ExcludedBones);
        }
    }

    return true;
}

float FAuracronRigTransformation::CalculateRigPerformanceScore() const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return 0.0f;
    }

    float PerformanceScore = 100.0f;
    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Analyze bone count impact on performance using UE5.6 performance metrics
    int32 BoneCount = RefSkeleton.GetNum();
    if (BoneCount > 300)
    {
        PerformanceScore -= 30.0f; // High bone count significantly impacts performance
    }
    else if (BoneCount > 150)
    {
        PerformanceScore -= 15.0f; // Moderate bone count impact
    }
    else if (BoneCount > 75)
    {
        PerformanceScore -= 5.0f; // Minor bone count impact
    }

    // Analyze constraint complexity using UE5.6 constraint analysis
    int32 ConstraintCount = ConstraintCache.Num();
    if (ConstraintCount > 50)
    {
        PerformanceScore -= 20.0f; // Many constraints impact performance
    }
    else if (ConstraintCount > 20)
    {
        PerformanceScore -= 10.0f; // Moderate constraint impact
    }

    // Analyze IK chain complexity using UE5.6 IK performance analysis
    int32 IKChainCount = IKChainCache.Num();
    int32 TotalIKBones = 0;

    for (const auto& IKChainPair : IKChainCache)
    {
        const FIKChainData& IKChain = IKChainPair.Value;
        TotalIKBones += IKChain.BoneChain.Num();

        // Complex IK solvers have higher performance cost
        switch (IKChain.SolverType)
        {
            case EIKSolverType::FullBody:
                PerformanceScore -= 10.0f; // Full-body IK is expensive
                break;
            case EIKSolverType::FABRIK:
                PerformanceScore -= 5.0f; // FABRIK is moderately expensive
                break;
            case EIKSolverType::CCDIK:
                PerformanceScore -= 3.0f; // CCD IK is less expensive
                break;
            case EIKSolverType::TwoBone:
                PerformanceScore -= 1.0f; // Two-bone IK is least expensive
                break;
        }
    }

    // Factor in total IK bones
    if (TotalIKBones > 100)
    {
        PerformanceScore -= 15.0f;
    }
    else if (TotalIKBones > 50)
    {
        PerformanceScore -= 8.0f;
    }

    // Analyze bone hierarchy depth using UE5.6 hierarchy analysis
    int32 MaxDepth = CalculateMaxBoneHierarchyDepth(RefSkeleton);
    if (MaxDepth > 20)
    {
        PerformanceScore -= 10.0f; // Deep hierarchies can impact performance
    }
    else if (MaxDepth > 15)
    {
        PerformanceScore -= 5.0f;
    }

    return FMath::Max(0.0f, PerformanceScore);
}

float FAuracronRigTransformation::CalculateRigCompatibilityScore() const
{
    USkeletalMesh* SkeletalMesh = GetCurrentSkeletalMesh();
    if (!SkeletalMesh || !SkeletalMesh->GetSkeleton())
    {
        return 0.0f;
    }

    float CompatibilityScore = 100.0f;
    const FReferenceSkeleton& RefSkeleton = SkeletalMesh->GetSkeleton()->GetReferenceSkeleton();

    // Check for standard bone naming conventions using UE5.6 naming validation
    TArray<FString> StandardBoneNames = {
        TEXT("Root"), TEXT("Pelvis"), TEXT("Spine"), TEXT("Chest"), TEXT("Neck"), TEXT("Head"),
        TEXT("LeftShoulder"), TEXT("LeftArm"), TEXT("LeftForeArm"), TEXT("LeftHand"),
        TEXT("RightShoulder"), TEXT("RightArm"), TEXT("RightForeArm"), TEXT("RightHand"),
        TEXT("LeftUpLeg"), TEXT("LeftLeg"), TEXT("LeftFoot"), TEXT("LeftToe"),
        TEXT("RightUpLeg"), TEXT("RightLeg"), TEXT("RightFoot"), TEXT("RightToe")
    };

    int32 StandardBonesFound = 0;
    for (const FString& StandardBoneName : StandardBoneNames)
    {
        for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
        {
            FString BoneName = RefSkeleton.GetBoneName(BoneIndex).ToString();
            if (BoneName.Contains(StandardBoneName))
            {
                StandardBonesFound++;
                break;
            }
        }
    }

    // Calculate compatibility based on standard bone presence
    float StandardBoneRatio = static_cast<float>(StandardBonesFound) / static_cast<float>(StandardBoneNames.Num());
    CompatibilityScore *= StandardBoneRatio;

    // Check for common animation system compatibility using UE5.6 animation validation
    bool bHasRootMotion = CheckForRootMotionCompatibility(RefSkeleton);
    if (!bHasRootMotion)
    {
        CompatibilityScore -= 10.0f; // Reduced compatibility without root motion support
    }

    // Check for retargeting compatibility using UE5.6 retargeting validation
    bool bRetargetingCompatible = CheckRetargetingCompatibility(RefSkeleton);
    if (!bRetargetingCompatible)
    {
        CompatibilityScore -= 15.0f; // Reduced compatibility for retargeting
    }

    // Check for Control Rig compatibility using UE5.6 Control Rig validation
    bool bControlRigCompatible = CheckControlRigCompatibility(RefSkeleton);
    if (!bControlRigCompatible)
    {
        CompatibilityScore -= 10.0f; // Reduced compatibility for Control Rig
    }

    return FMath::Max(0.0f, CompatibilityScore);
}

bool FAuracronRigTransformation::HasCircularDependency(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton, TSet<int32>& VisitedBones, TArray<int32>& BoneStack) const
{
    if (BoneStack.Contains(BoneIndex))
    {
        return true; // Circular dependency found
    }

    if (VisitedBones.Contains(BoneIndex))
    {
        return false; // Already processed this bone
    }

    VisitedBones.Add(BoneIndex);
    BoneStack.Add(BoneIndex);

    // Check parent bone using UE5.6 hierarchy traversal
    int32 ParentIndex = RefSkeleton.GetParentIndex(BoneIndex);
    if (ParentIndex != INDEX_NONE)
    {
        if (HasCircularDependency(ParentIndex, RefSkeleton, VisitedBones, BoneStack))
        {
            return true;
        }
    }

    BoneStack.Remove(BoneIndex);
    return false;
}

bool FAuracronRigTransformation::ValidateIKChainConnectivity(const FIKChainData& IKChain, const FReferenceSkeleton& RefSkeleton) const
{
    if (IKChain.BoneChain.Num() < 2)
    {
        return false;
    }

    // Check if bones in the chain are connected using UE5.6 hierarchy validation
    for (int32 i = 1; i < IKChain.BoneChain.Num(); ++i)
    {
        int32 CurrentBone = IKChain.BoneChain[i];
        int32 PreviousBone = IKChain.BoneChain[i - 1];

        // Check if current bone is a child of previous bone or vice versa
        bool bConnected = false;

        // Check direct parent-child relationship
        if (RefSkeleton.GetParentIndex(CurrentBone) == PreviousBone ||
            RefSkeleton.GetParentIndex(PreviousBone) == CurrentBone)
        {
            bConnected = true;
        }
        else
        {
            // Check if bones are connected through intermediate bones
            bConnected = AreBonesConnected(PreviousBone, CurrentBone, RefSkeleton);
        }

        if (!bConnected)
        {
            return false;
        }
    }

    return true;
}

bool FAuracronRigTransformation::AreBonesConnected(int32 Bone1, int32 Bone2, const FReferenceSkeleton& RefSkeleton) const
{
    // Traverse up from Bone1 to see if we reach Bone2
    int32 CurrentBone = Bone1;
    while (CurrentBone != INDEX_NONE)
    {
        if (CurrentBone == Bone2)
        {
            return true;
        }
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
    }

    // Traverse up from Bone2 to see if we reach Bone1
    CurrentBone = Bone2;
    while (CurrentBone != INDEX_NONE)
    {
        if (CurrentBone == Bone1)
        {
            return true;
        }
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
    }

    return false;
}

int32 FAuracronRigTransformation::CalculateMaxBoneHierarchyDepth(const FReferenceSkeleton& RefSkeleton) const
{
    int32 MaxDepth = 0;

    for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
    {
        int32 Depth = CalculateBoneDepth(BoneIndex, RefSkeleton);
        MaxDepth = FMath::Max(MaxDepth, Depth);
    }

    return MaxDepth;
}

int32 FAuracronRigTransformation::CalculateBoneDepth(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton) const
{
    int32 Depth = 0;
    int32 CurrentBone = BoneIndex;

    while (CurrentBone != INDEX_NONE)
    {
        CurrentBone = RefSkeleton.GetParentIndex(CurrentBone);
        if (CurrentBone != INDEX_NONE)
        {
            Depth++;
        }
    }

    return Depth;
}

bool FAuracronRigTransformation::CheckForRootMotionCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton has a proper root bone for root motion using UE5.6 root motion validation
    if (RefSkeleton.GetNum() == 0)
    {
        return false;
    }

    // Root bone should have no parent
    int32 RootBoneIndex = 0;
    return RefSkeleton.GetParentIndex(RootBoneIndex) == INDEX_NONE;
}

bool FAuracronRigTransformation::CheckRetargetingCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton has minimum required bones for retargeting using UE5.6 retargeting validation
    TArray<FString> RequiredBones = {
        TEXT("Pelvis"), TEXT("Spine"), TEXT("Head"),
        TEXT("LeftArm"), TEXT("LeftForeArm"), TEXT("LeftHand"),
        TEXT("RightArm"), TEXT("RightForeArm"), TEXT("RightHand"),
        TEXT("LeftUpLeg"), TEXT("LeftLeg"), TEXT("LeftFoot"),
        TEXT("RightUpLeg"), TEXT("RightLeg"), TEXT("RightFoot")
    };

    int32 RequiredBonesFound = 0;
    for (const FString& RequiredBone : RequiredBones)
    {
        for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
        {
            FString BoneName = RefSkeleton.GetBoneName(BoneIndex).ToString();
            if (BoneName.Contains(RequiredBone))
            {
                RequiredBonesFound++;
                break;
            }
        }
    }

    // Need at least 80% of required bones for good retargeting compatibility
    return RequiredBonesFound >= (RequiredBones.Num() * 0.8f);
}

bool FAuracronRigTransformation::CheckControlRigCompatibility(const FReferenceSkeleton& RefSkeleton) const
{
    // Check if skeleton is compatible with Control Rig system using UE5.6 Control Rig validation
    if (RefSkeleton.GetNum() == 0)
    {
        return false;
    }

    // Check for valid bone transforms
    for (int32 BoneIndex = 0; BoneIndex < RefSkeleton.GetNum(); ++BoneIndex)
    {
        const FTransform& BoneTransform = RefSkeleton.GetRefBonePose()[BoneIndex];
        if (!BoneTransform.IsValid() || BoneTransform.ContainsNaN())
        {
            return false;
        }
    }

    return true;
}

void FAuracronRigTransformation::InvalidateRigTransformationCache()
{
    FScopeLock Lock(&RigTransformationMutex);
    bRigTransformationCacheValid = false;
}
