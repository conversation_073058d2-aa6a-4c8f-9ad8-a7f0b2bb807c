// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "FoliageType.h"
#include "InstancedFoliage.h"
#include "AuracronFoliageTypes.h"
#include "AuracronFoliage.h"





// Forward declaration
class UAuracronFoliageEditUtility;

/**
 * Auracron Foliage Edit Module Interface
 */
class AURACRONFOLIAGEBRIDGE_API IAuracronFoliageEditModule : public IModuleInterface
{
public:
    /**
     * Singleton-like access to this module's interface. This is just for convenience!
     * Beware of calling this during the shutdown phase, though. Your module might have been unloaded already.
     *
     * @return Returns singleton instance, loading the module on demand if needed
     */
    static inline IAuracronFoliageEditModule& Get()
    {
        return FModuleManager::LoadModuleChecked<IAuracronFoliageEditModule>("AuracronFoliageBridge");
    }

    /**
     * Checks to see if this module is loaded and ready. It is only valid to call Get() if IsAvailable() returns true.
     *
     * @return True if the module is loaded and ready to use
     */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronFoliageBridge");
    }

    // Module Interface
    virtual void StartupModule() override {}
    virtual void ShutdownModule() override {}

    // Foliage Edit Interface
    virtual UAuracronFoliageEditUtility* GetFoliageEditUtility() = 0;
    virtual void SetCurrentEditMode(EFoliageEditMode EditMode) = 0;
    virtual EFoliageEditMode GetCurrentEditMode() const = 0;
    virtual void SetCurrentToolType(EFoliageEditToolType ToolType) = 0;
    virtual EFoliageEditToolType GetCurrentToolType() const = 0;
};