// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de ComunicaÃ§Ã£o por Voz Bridge
// IntegraÃ§Ã£o C++ para voice chat usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "Interfaces/VoiceInterface.h"
#include "VoiceChat.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundWave.h"
#include "AudioMixerBlueprintLibrary.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Templates/SharedPointer.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronVoiceBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de canal de voz
 */
UENUM(BlueprintType)
enum class EAuracronVoiceChannelType : uint8
{
    None                UMETA(DisplayName = "None"),
    Team                UMETA(DisplayName = "Team Chat"),
    Proximity           UMETA(DisplayName = "Proximity Chat"),
    Global              UMETA(DisplayName = "Global Chat"),
    Party               UMETA(DisplayName = "Party Chat"),
    Whisper             UMETA(DisplayName = "Whisper"),
    Broadcast           UMETA(DisplayName = "Broadcast"),
    Emergency           UMETA(DisplayName = "Emergency"),
    Coach               UMETA(DisplayName = "Coach")
};

/**
 * EnumeraÃ§Ã£o para qualidade de voz
 */
UENUM(BlueprintType)
enum class EAuracronVoiceQuality : uint8
{
    Low                 UMETA(DisplayName = "Low Quality (8kHz)"),
    Medium              UMETA(DisplayName = "Medium Quality (16kHz)"),
    High                UMETA(DisplayName = "High Quality (24kHz)"),
    Ultra               UMETA(DisplayName = "Ultra Quality (48kHz)")
};

/**
 * EnumeraÃ§Ã£o para estado de voz
 */
UENUM(BlueprintType)
enum class EAuracronVoiceState : uint8
{
    Disconnected        UMETA(DisplayName = "Disconnected"),
    Connecting          UMETA(DisplayName = "Connecting"),
    Connected           UMETA(DisplayName = "Connected"),
    Speaking            UMETA(DisplayName = "Speaking"),
    Listening           UMETA(DisplayName = "Listening"),
    Muted               UMETA(DisplayName = "Muted"),
    Deafened            UMETA(DisplayName = "Deafened"),
    Error               UMETA(DisplayName = "Error")
};

/**
 * Estrutura para configuraÃ§Ã£o de voz
 */
USTRUCT(BlueprintType)
struct AURACRONVOICEBRIDGE_API FAuracronVoiceConfiguration
{
    GENERATED_BODY()

    /** Usar voice chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseVoiceChat = true;

    /** Qualidade de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    EAuracronVoiceQuality VoiceQuality = EAuracronVoiceQuality::High;

    /** Volume de entrada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float InputVolume = 1.0f;

    /** Volume de saÃ­da */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float OutputVolume = 1.0f;

    /** Usar push-to-talk */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUsePushToTalk = false;

    /** Usar detecÃ§Ã£o de atividade de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseVoiceActivityDetection = true;

    /** Threshold de atividade de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float VoiceActivityThreshold = 0.3f;

    /** Usar supressÃ£o de ruÃ­do */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseNoiseSuppression = true;

    /** Intensidade da supressÃ£o de ruÃ­do */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseSuppressionIntensity = 0.7f;

    /** Usar cancelamento de eco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseEchoCancellation = true;

    /** Usar compressÃ£o de Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseAudioCompression = true;

    /** Taxa de bits */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "8", ClampMax = "320"))
    int32 BitRate = 64;

    /** Usar Ã¡udio 3D para proximity chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUse3DVoice = true;

    /** DistÃ¢ncia mÃ¡xima para proximity chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "100.0", ClampMax = "5000.0"))
    float ProximityDistance = 1500.0f;

    /** Usar atenuaÃ§Ã£o por distÃ¢ncia */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseDistanceAttenuation = true;

    /** Usar filtros de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseVoiceFilters = true;

    /** Usar modulaÃ§Ã£o de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseVoiceModulation = false;

    /** Tipo de modulaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    FString VoiceModulationType = TEXT("None");

    /** Usar gravaÃ§Ã£o de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration")
    bool bUseVoiceRecording = false;

    /** DuraÃ§Ã£o mÃ¡xima de gravaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Configuration", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float MaxRecordingDuration = 30.0f;
};

/**
 * Estrutura para canal de voz
 */
USTRUCT(BlueprintType)
struct AURACRONVOICEBRIDGE_API FAuracronVoiceChannel
{
    GENERATED_BODY()

    /** ID do canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    FString ChannelID;

    /** Nome do canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    FString ChannelName;

    /** Tipo do canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    EAuracronVoiceChannelType ChannelType = EAuracronVoiceChannelType::Team;

    /** Jogadores no canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    TArray<FString> PlayersInChannel;

    /** Canal estÃ¡ ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    bool bIsActive = false;

    /** Canal Ã© privado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    bool bIsPrivate = false;

    /** Requer permissÃ£o para entrar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    bool bRequiresPermission = false;

    /** Volume do canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float ChannelVolume = 1.0f;

    /** Canal estÃ¡ mutado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    bool bIsMuted = false;

    /** Usar filtros de canal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    bool bUseChannelFilters = false;

    /** Filtros aplicados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    TArray<FString> AppliedFilters;

    /** Data de criaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    FDateTime CreationTime;

    /** Ãšltima atividade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Channel")
    FDateTime LastActivity;
};

/**
 * Estrutura para participante de voz
 */
USTRUCT(BlueprintType)
struct AURACRONVOICEBRIDGE_API FAuracronVoiceParticipant
{
    GENERATED_BODY()

    /** ID do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FString PlayerID;

    /** Nome do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FString PlayerName;

    /** Estado de voz atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    EAuracronVoiceState VoiceState = EAuracronVoiceState::Disconnected;

    /** EstÃ¡ falando */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    bool bIsSpeaking = false;

    /** EstÃ¡ mutado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    bool bIsMuted = false;

    /** EstÃ¡ surdo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    bool bIsDeafened = false;

    /** Volume do participante */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float ParticipantVolume = 1.0f;

    /** Qualidade da conexÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ConnectionQuality = 1.0f;

    /** LatÃªncia de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant", meta = (ClampMin = "0.0"))
    float VoiceLatency = 0.0f;

    /** PosiÃ§Ã£o 3D para proximity chat */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FVector Position3D = FVector::ZeroVector;

    /** Usar posiÃ§Ã£o 3D */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    bool bUse3DPosition = false;

    /** Canais ativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    TArray<FString> ActiveChannels;

    /** Filtros ativos aplicados à voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    TMap<FString, float> ActiveFilters;

    /** Tipo de modulação de voz aplicada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FString VoiceModulation;

    /** PermissÃµes de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    TArray<FString> VoicePermissions;

    /** Tempo conectado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FDateTime ConnectedTime;

    /** Ãšltima vez que falou */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Voice Participant")
    FDateTime LastSpokeTime;
};

/**
 * Classe principal do Bridge para Sistema de ComunicaÃ§Ã£o por Voz
 * ResponsÃ¡vel pelo gerenciamento completo de voice chat
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Voice", meta = (DisplayName = "AURACRON Voice Bridge", BlueprintSpawnableComponent))
class AURACRONVOICEBRIDGE_API UAuracronVoiceBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronVoiceBridge(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Voice Management ===

    /**
     * Inicializar voice chat
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Core", CallInEditor)
    bool InitializeVoiceChat();

    /**
     * Conectar ao voice chat
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Core", CallInEditor)
    bool ConnectToVoiceChat();

    /**
     * Desconectar do voice chat
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Core", CallInEditor)
    bool DisconnectFromVoiceChat();

    /**
     * Mutar microfone
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Core", CallInEditor)
    bool MuteMicrophone(bool bMute);

    /**
     * Ensurdecer Ã¡udio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Core", CallInEditor)
    bool DeafenAudio(bool bDeafen);

    // === Channel Management ===

    /**
     * Criar canal de voz
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Channels", CallInEditor)
    bool CreateVoiceChannel(const FAuracronVoiceChannel& ChannelConfig);

    /**
     * Entrar em canal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Channels", CallInEditor)
    bool JoinVoiceChannel(const FString& ChannelID);

    /**
     * Sair de canal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Channels", CallInEditor)
    bool LeaveVoiceChannel(const FString& ChannelID);

    /**
     * Mutar canal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Channels", CallInEditor)
    bool MuteVoiceChannel(const FString& ChannelID, bool bMute);

    /**
     * Definir volume do canal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Channels", CallInEditor)
    bool SetChannelVolume(const FString& ChannelID, float Volume);

    // === Participant Management ===

    /**
     * Mutar participante
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Participants", CallInEditor)
    bool MuteParticipant(const FString& PlayerID, bool bMute);

    /**
     * Definir volume do participante
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Participants", CallInEditor)
    bool SetParticipantVolume(const FString& PlayerID, float Volume);

    /**
     * Obter participantes do canal
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Participants", CallInEditor)
    TArray<FAuracronVoiceParticipant> GetChannelParticipants(const FString& ChannelID) const;

    /**
     * Verificar se participante estÃ¡ falando
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Participants", CallInEditor)
    bool IsParticipantSpeaking(const FString& PlayerID) const;

    // === 3D Voice ===

    /**
     * Habilitar voz 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|3D", CallInEditor)
    bool Enable3DVoice(bool bEnable);

    /**
     * Atualizar posiÃ§Ã£o 3D do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|3D", CallInEditor)
    bool UpdatePlayer3DPosition(const FString& PlayerID, const FVector& Position);

    /**
     * Definir distÃ¢ncia de proximity chat
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|3D", CallInEditor)
    bool SetProximityDistance(float Distance);

    // === Voice Effects ===

    /**
     * Aplicar filtro de voz
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Effects", CallInEditor)
    bool ApplyVoiceFilter(const FString& PlayerID, const FString& FilterType, float Intensity = 1.0f);

    /**
     * Remover filtro de voz
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Effects", CallInEditor)
    bool RemoveVoiceFilter(const FString& PlayerID, const FString& FilterType);

    /**
     * Aplicar modulaÃ§Ã£o de voz
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Effects", CallInEditor)
    bool ApplyVoiceModulation(const FString& PlayerID, const FString& ModulationType);

    // === Recording ===

    /**
     * Iniciar gravaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Recording", CallInEditor)
    bool StartVoiceRecording(const FString& RecordingID);

    /**
     * Parar gravaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Recording", CallInEditor)
    bool StopVoiceRecording();

    /**
     * Reproduzir gravaÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Voice|Recording", CallInEditor)
    bool PlayVoiceRecording(const FString& RecordingID);

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de voz */
    bool InitializeVoiceSystem();
    
    /** Configurar voice chat */
    bool SetupVoiceChat();
    
    /** Processar dados de voz */
    void ProcessVoiceData(float DeltaTime);
    
    /** Atualizar participantes */
    void UpdateParticipants(float DeltaTime);
    
    /**
     * Validar configuraÃ§Ã£o de voz
     */
    bool ValidateVoiceConfiguration(const FAuracronVoiceConfiguration& Config) const;
    
    /**
     * Verificar se jogador está na mesma equipe
     */
    bool IsPlayerInSameTeam(APlayerController* PlayerController) const;
    
    /**
     * Verificar se jogador está na mesma guilda
     */
    bool IsPlayerInSameGuild(APlayerController* PlayerController) const;
    
    /**
     * Verificar se jogador foi convidado para canal privado
     */
    bool IsPlayerInvitedToChannel(APlayerController* PlayerController, const FString& ChannelID) const;

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ã£o de voz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronVoiceConfiguration VoiceConfiguration;

    /** Canais de voz ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronVoiceChannel> ActiveVoiceChannels;

    /** Participantes conectados */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronVoiceParticipant> ConnectedParticipants;

    /** Estado de voz atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_VoiceState)
    EAuracronVoiceState CurrentVoiceState = EAuracronVoiceState::Disconnected;

    /** Interface de voz (não pode ser UPROPERTY) */
    TSharedPtr<IVoiceChat> VoiceChatInterface;

    /** Timer handle para conexões */
    FTimerHandle ConnectionTimerHandle;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para atualizaÃ§Ãµes */
    FTimerHandle VoiceUpdateTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection VoiceMutex;

    // === Replication Callbacks ===
    
    UFUNCTION()
    void OnRep_VoiceState();

public:
    // === Delegates ===
    
    /** Delegate chamado quando participante entra no canal */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnParticipantJoined, FString, ChannelID, FAuracronVoiceParticipant, Participant);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Voice|Events")
    FOnParticipantJoined OnParticipantJoined;
    
    /** Delegate chamado quando participante sai do canal */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnParticipantLeft, FString, ChannelID, FString, PlayerID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Voice|Events")
    FOnParticipantLeft OnParticipantLeft;
    
    /** Delegate chamado quando participante comeÃ§a a falar */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnParticipantStartedSpeaking, FString, PlayerID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Voice|Events")
    FOnParticipantStartedSpeaking OnParticipantStartedSpeaking;
    
    /** Delegate chamado quando participante para de falar */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnParticipantStoppedSpeaking, FString, PlayerID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Voice|Events")
    FOnParticipantStoppedSpeaking OnParticipantStoppedSpeaking;

    /** Delegate chamado quando estado de voz muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnVoiceStateChanged, EAuracronVoiceState, NewState);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Voice|Events")
    FOnVoiceStateChanged OnVoiceStateChanged;

private:
    // === Recording Variables ===

    /** Se está gravando atualmente */
    UPROPERTY()
    bool bIsRecording = false;

    /** ID da gravação atual */
    UPROPERTY()
    FString CurrentRecordingID;

    /** Tempo de início da gravação */
    FDateTime RecordingStartTime;

    /** Timer para processar dados de gravação */
    FTimerHandle RecordingProcessTimer;

    /** Tamanho dos dados de gravação */
    int32 RecordingDataSize = 0;

    // === Helper Methods ===

    /** Verificar se jogador está na mesma party */
    bool IsPlayerInSameParty(APlayerController* PlayerController) const;

    /** Processar dados de gravação */
    void ProcessRecordingData();

    /** Salvar dados de gravação */
    void SaveRecordingData();
};

