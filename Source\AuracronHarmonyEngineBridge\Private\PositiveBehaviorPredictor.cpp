#include "PositiveBehaviorPredictor.h"
#include "AuracronHarmonyEngineBridge.h"
#include "Math/UnrealMathUtility.h"
#include "Engine/Engine.h"

UPositiveBehaviorPredictor::UPositiveBehaviorPredictor()
{
    // Default configuration
    PositiveBehaviorThreshold = 0.7f;
    LeadershipThreshold = 0.8f;
    ConsistencyRequirement = 0.6f;
    MinimumDataPointsForPrediction = 5;
    RewardAmplificationFactor = 1.5f;
    bEnableRewardAmplification = true;
    
    // Initialize ML model state
    CurrentModelAccuracy = 0.0f;
    TrainingIterations = 0;
    bModelTrained = false;
}

float UPositiveBehaviorPredictor::PredictPositiveBehaviorProbability(const FString& PlayerID)
{
    if (!PlayerPositivityScores.Contains(PlayerID))
    {
        return 0.5f; // Neutral probability for unknown players
    }
    
    float BasePositivity = PlayerPositivityScores[PlayerID];
    float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.5f;
    float LeadershipScore = PlayerLeadershipScores.Contains(PlayerID) ? PlayerLeadershipScores[PlayerID] : 0.0f;
    
    // Weighted combination of factors
    float PredictedProbability = (BasePositivity * 0.5f) + (ConsistencyScore * 0.3f) + (LeadershipScore * 0.2f);
    
    // Apply sigmoid function for smooth probability curve
    return ApplySigmoidFunction(PredictedProbability);
}

bool UPositiveBehaviorPredictor::IsPlayerLikelyToBePositive(const FString& PlayerID, float Threshold)
{
    float Probability = PredictPositiveBehaviorProbability(PlayerID);
    return Probability >= Threshold;
}

TArray<FString> UPositiveBehaviorPredictor::IdentifyPotentialMentors()
{
    TArray<FString> PotentialMentors;
    
    for (const auto& ScorePair : PlayerPositivityScores)
    {
        const FString& PlayerID = ScorePair.Key;
        float PositivityScore = ScorePair.Value;
        
        // Check if player meets mentor criteria
        if (PositivityScore >= PositiveBehaviorThreshold)
        {
            float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.0f;
            
            if (ConsistencyScore >= ConsistencyRequirement && DetectMentorshipPatterns(PlayerID))
            {
                PotentialMentors.Add(PlayerID);
            }
        }
    }
    
    // Sort by positivity score (highest first)
    PotentialMentors.Sort([this](const FString& A, const FString& B) {
        float ScoreA = PlayerPositivityScores.Contains(A) ? PlayerPositivityScores[A] : 0.0f;
        float ScoreB = PlayerPositivityScores.Contains(B) ? PlayerPositivityScores[B] : 0.0f;
        return ScoreA > ScoreB;
    });
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Identified %d potential mentors"), PotentialMentors.Num());
    
    return PotentialMentors;
}

TArray<FString> UPositiveBehaviorPredictor::IdentifyPotentialCommunityLeaders()
{
    TArray<FString> PotentialLeaders;
    
    for (const auto& ScorePair : PlayerLeadershipScores)
    {
        const FString& PlayerID = ScorePair.Key;
        float LeadershipScore = ScorePair.Value;
        
        if (LeadershipScore >= LeadershipThreshold)
        {
            float PositivityScore = PlayerPositivityScores.Contains(PlayerID) ? PlayerPositivityScores[PlayerID] : 0.0f;
            
            if (PositivityScore >= PositiveBehaviorThreshold && DetectLeadershipPatterns(PlayerID))
            {
                PotentialLeaders.Add(PlayerID);
            }
        }
    }
    
    // Sort by leadership score (highest first)
    PotentialLeaders.Sort([this](const FString& A, const FString& B) {
        float ScoreA = PlayerLeadershipScores.Contains(A) ? PlayerLeadershipScores[A] : 0.0f;
        float ScoreB = PlayerLeadershipScores.Contains(B) ? PlayerLeadershipScores[B] : 0.0f;
        return ScoreA > ScoreB;
    });
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Identified %d potential community leaders"), PotentialLeaders.Num());
    
    return PotentialLeaders;
}

float UPositiveBehaviorPredictor::CalculateLeadershipPotential(const FString& PlayerID)
{
    if (!PlayerPositivityScores.Contains(PlayerID))
    {
        return 0.0f;
    }
    
    float PositivityScore = PlayerPositivityScores[PlayerID];
    float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.0f;
    
    // Leadership potential is combination of positivity, consistency, and helping behavior
    float HelpingBehaviorScore = DetectHelpingBehavior(PlayerID) ? 1.0f : 0.0f;
    
    float LeadershipPotential = (PositivityScore * 0.4f) + (ConsistencyScore * 0.3f) + (HelpingBehaviorScore * 0.3f);
    
    // Update leadership score
    PlayerLeadershipScores.Add(PlayerID, LeadershipPotential);
    
    return LeadershipPotential;
}

void UPositiveBehaviorPredictor::AddTrainingData(const FPlayerBehaviorSnapshot& BehaviorData)
{
    // Add to training dataset
    TrainingDataset.Add(BehaviorData);
    
    // Update player scores
    PlayerPositivityScores.Add(BehaviorData.PlayerID, BehaviorData.PositivityScore);
    
    // Calculate and update consistency score
    float ConsistencyScore = CalculateConsistencyScore(BehaviorData.PlayerID);
    PlayerConsistencyScores.Add(BehaviorData.PlayerID, ConsistencyScore);
    
    // Limit training dataset size to prevent memory issues
    if (TrainingDataset.Num() > 10000)
    {
        CleanupOldTrainingData();
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Added training data for player: %s"), *BehaviorData.PlayerID);
}

void UPositiveBehaviorPredictor::TrainPredictionModel()
{
    if (TrainingDataset.Num() < MinimumDataPointsForPrediction)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Insufficient training data (%d samples, need %d)"), 
            TrainingDataset.Num(), MinimumDataPointsForPrediction);
        return;
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Training positive behavior prediction model with %d samples"), TrainingDataset.Num());
    
    // Normalize training data
    NormalizeTrainingData();
    
    // Balance dataset to prevent bias
    BalanceTrainingDataset();
    
    // Simple neural network training simulation
    // In a full implementation, this would use actual ML libraries
    float TotalError = 0.0f;
    int32 ValidationSamples = FMath::Min(TrainingDataset.Num() / 5, 100); // 20% for validation
    
    for (int32 i = 0; i < ValidationSamples; i++)
    {
        const FPlayerBehaviorSnapshot& Sample = TrainingDataset[TrainingDataset.Num() - 1 - i];
        
        // Predict behavior based on current model
        float PredictedPositivity = PredictPositiveBehaviorProbability(Sample.PlayerID);
        float ActualPositivity = Sample.PositivityScore;
        
        // Calculate error
        float Error = FMath::Abs(PredictedPositivity - ActualPositivity);
        TotalError += Error;
    }
    
    // Update model accuracy
    CurrentModelAccuracy = 1.0f - (TotalError / ValidationSamples);
    CurrentModelAccuracy = FMath::Clamp(CurrentModelAccuracy, 0.0f, 1.0f);
    
    TrainingIterations++;
    bModelTrained = true;
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Model training completed - Accuracy: %.3f, Iteration: %d"), 
        CurrentModelAccuracy, TrainingIterations);
    
    // Update model weights based on training
    UpdateModelWeights();
}

float UPositiveBehaviorPredictor::GetModelAccuracy() const
{
    return CurrentModelAccuracy;
}

void UPositiveBehaviorPredictor::ResetTrainingData()
{
    TrainingDataset.Empty();
    PlayerPositivityScores.Empty();
    PlayerConsistencyScores.Empty();
    PlayerLeadershipScores.Empty();
    CurrentModelAccuracy = 0.0f;
    TrainingIterations = 0;
    bModelTrained = false;
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Reset all training data and model state"));
}

TArray<FGameplayTag> UPositiveBehaviorPredictor::AnalyzePositiveBehaviorPatterns(const FString& PlayerID)
{
    TArray<FGameplayTag> DetectedPatterns;
    
    if (!PlayerPositivityScores.Contains(PlayerID))
    {
        return DetectedPatterns;
    }
    
    float PositivityScore = PlayerPositivityScores[PlayerID];
    float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.0f;
    
    // Detect specific positive behavior patterns
    if (PositivityScore > 0.8f)
    {
        DetectedPatterns.Add(HarmonyEngineGameplayTags::Behavior_Positive);
    }
    
    if (DetectMentorshipPatterns(PlayerID))
    {
        DetectedPatterns.Add(HarmonyEngineGameplayTags::Behavior_Mentoring);
    }
    
    if (DetectHelpingBehavior(PlayerID))
    {
        DetectedPatterns.Add(HarmonyEngineGameplayTags::Behavior_Healing);
    }
    
    if (ConsistencyScore > 0.7f)
    {
        DetectedPatterns.Add(HarmonyEngineGameplayTags::Reward_Kindness);
    }
    
    return DetectedPatterns;
}

float UPositiveBehaviorPredictor::CalculateConsistencyScore(const FString& PlayerID)
{
    // Find all behavior snapshots for this player in training data
    TArray<float> PositivityValues;
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PlayerID == PlayerID)
        {
            PositivityValues.Add(Sample.PositivityScore);
        }
    }
    
    if (PositivityValues.Num() < 2)
    {
        return 0.0f;
    }
    
    // Calculate variance to determine consistency
    float Mean = 0.0f;
    for (float Value : PositivityValues)
    {
        Mean += Value;
    }
    Mean /= PositivityValues.Num();
    
    float Variance = 0.0f;
    for (float Value : PositivityValues)
    {
        Variance += FMath::Pow(Value - Mean, 2.0f);
    }
    Variance /= PositivityValues.Num();
    
    // Consistency is inverse of variance (lower variance = higher consistency)
    float ConsistencyScore = 1.0f / (1.0f + Variance);
    
    return FMath::Clamp(ConsistencyScore, 0.0f, 1.0f);
}

bool UPositiveBehaviorPredictor::HasPositiveTrend(const FString& PlayerID, float TimeWindow)
{
    // Analyze if player has positive trend in recent time window
    TArray<FPlayerBehaviorSnapshot> RecentSamples;
    FDateTime CurrentTime = FDateTime::Now();
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PlayerID == PlayerID)
        {
            float TimeDiff = (CurrentTime - Sample.Timestamp).GetTotalSeconds();
            if (TimeDiff <= TimeWindow)
            {
                RecentSamples.Add(Sample);
            }
        }
    }
    
    if (RecentSamples.Num() < 2)
    {
        return false;
    }
    
    // Sort by timestamp
    RecentSamples.Sort([](const FPlayerBehaviorSnapshot& A, const FPlayerBehaviorSnapshot& B) {
        return A.Timestamp < B.Timestamp;
    });
    
    // Calculate trend using linear regression
    float SumX = 0.0f, SumY = 0.0f, SumXY = 0.0f, SumX2 = 0.0f;
    int32 N = RecentSamples.Num();
    
    FDateTime BaseTime = RecentSamples[0].Timestamp;
    
    for (int32 i = 0; i < N; i++)
    {
        float X = (RecentSamples[i].Timestamp - BaseTime).GetTotalSeconds();
        float Y = RecentSamples[i].PositivityScore;
        
        SumX += X;
        SumY += Y;
        SumXY += X * Y;
        SumX2 += X * X;
    }
    
    // Calculate slope (trend)
    float Slope = (N * SumXY - SumX * SumY) / (N * SumX2 - SumX * SumX);
    
    // Positive trend if slope > 0.01 (increasing positivity)
    return Slope > 0.01f;
}

float UPositiveBehaviorPredictor::CalculateRewardMultiplier(const FString& PlayerID)
{
    if (!bEnableRewardAmplification)
    {
        return 1.0f;
    }
    
    if (!IsPlayerLikelyToBePositive(PlayerID))
    {
        return 1.0f;
    }
    
    float PositivityScore = PlayerPositivityScores.Contains(PlayerID) ? PlayerPositivityScores[PlayerID] : 0.0f;
    float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.0f;
    
    // Calculate multiplier based on positivity and consistency
    float BaseMultiplier = 1.0f + (PositivityScore * RewardAmplificationFactor - 1.0f);
    float ConsistencyBonus = ConsistencyScore * 0.5f;
    
    float FinalMultiplier = BaseMultiplier + ConsistencyBonus;
    
    return FMath::Clamp(FinalMultiplier, 1.0f, 3.0f); // Cap at 3x multiplier
}

bool UPositiveBehaviorPredictor::ShouldAmplifyRewards(const FString& PlayerID)
{
    return bEnableRewardAmplification && IsPlayerLikelyToBePositive(PlayerID, PositiveBehaviorThreshold);
}

int32 UPositiveBehaviorPredictor::CalculateBonusKindnessPoints(const FString& PlayerID, int32 BasePoints)
{
    if (!ShouldAmplifyRewards(PlayerID))
    {
        return BasePoints;
    }
    
    float Multiplier = CalculateRewardMultiplier(PlayerID);
    int32 BonusPoints = FMath::RoundToInt(BasePoints * Multiplier) - BasePoints;
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Calculated bonus kindness points for %s: %d (multiplier: %.2f)"), 
        *PlayerID, BonusPoints, Multiplier);
    
    return BonusPoints;
}

// Private helper function implementations

float UPositiveBehaviorPredictor::CalculatePositivityTrend(const FString& PlayerID)
{
    TArray<float> RecentPositivityValues;
    FDateTime CurrentTime = FDateTime::Now();
    
    // Collect recent positivity values (last 24 hours)
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PlayerID == PlayerID)
        {
            float TimeDiff = (CurrentTime - Sample.Timestamp).GetTotalSeconds();
            if (TimeDiff <= 86400.0f) // 24 hours
            {
                RecentPositivityValues.Add(Sample.PositivityScore);
            }
        }
    }
    
    if (RecentPositivityValues.Num() < 2)
    {
        return 0.0f;
    }
    
    // Calculate simple moving average trend
    float EarlyAverage = 0.0f;
    float LateAverage = 0.0f;
    int32 HalfPoint = RecentPositivityValues.Num() / 2;
    
    for (int32 i = 0; i < HalfPoint; i++)
    {
        EarlyAverage += RecentPositivityValues[i];
    }
    EarlyAverage /= HalfPoint;
    
    for (int32 i = HalfPoint; i < RecentPositivityValues.Num(); i++)
    {
        LateAverage += RecentPositivityValues[i];
    }
    LateAverage /= (RecentPositivityValues.Num() - HalfPoint);
    
    return LateAverage - EarlyAverage; // Positive value indicates improving trend
}

bool UPositiveBehaviorPredictor::DetectMentorshipPatterns(const FString& PlayerID)
{
    // Analyze if player shows mentorship behavior patterns
    int32 HelpingActions = 0;
    int32 PositiveInteractions = 0;
    int32 TotalInteractions = 0;
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PlayerID == PlayerID)
        {
            HelpingActions += Sample.PositiveActionsCount;
            TotalInteractions += Sample.PositiveActionsCount + Sample.NegativeActionsCount;
            
            if (Sample.PositiveActionsCount > Sample.NegativeActionsCount)
            {
                PositiveInteractions++;
            }
        }
    }
    
    if (TotalInteractions == 0)
    {
        return false;
    }
    
    float PositiveRatio = static_cast<float>(PositiveInteractions) / TotalInteractions;
    float HelpingRatio = static_cast<float>(HelpingActions) / TotalInteractions;
    
    // Player shows mentorship patterns if they consistently help others
    return (PositiveRatio > 0.7f) && (HelpingRatio > 0.3f);
}

bool UPositiveBehaviorPredictor::DetectLeadershipPatterns(const FString& PlayerID)
{
    // Leadership patterns: high positivity + consistency + helping others
    float PositivityScore = PlayerPositivityScores.Contains(PlayerID) ? PlayerPositivityScores[PlayerID] : 0.0f;
    float ConsistencyScore = PlayerConsistencyScores.Contains(PlayerID) ? PlayerConsistencyScores[PlayerID] : 0.0f;
    bool HasHelpingBehavior = DetectHelpingBehavior(PlayerID);
    
    return (PositivityScore > 0.75f) && (ConsistencyScore > 0.6f) && HasHelpingBehavior;
}

bool UPositiveBehaviorPredictor::DetectHelpingBehavior(const FString& PlayerID)
{
    int32 HelpingInstances = 0;
    int32 TotalSamples = 0;
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PlayerID == PlayerID)
        {
            TotalSamples++;
            
            // Consider it helping behavior if positive actions significantly outweigh negative
            if (Sample.PositiveActionsCount > (Sample.NegativeActionsCount * 2))
            {
                HelpingInstances++;
            }
        }
    }
    
    if (TotalSamples == 0)
    {
        return false;
    }
    
    float HelpingRatio = static_cast<float>(HelpingInstances) / TotalSamples;
    return HelpingRatio > 0.5f; // More than half of interactions show helping behavior
}

float UPositiveBehaviorPredictor::ApplySigmoidFunction(float Input)
{
    // Sigmoid function to normalize probability between 0 and 1
    return 1.0f / (1.0f + FMath::Exp(-Input));
}

void UPositiveBehaviorPredictor::UpdateModelWeights()
{
    // In a full ML implementation, this would update neural network weights
    // For now, we adjust thresholds based on model performance
    
    if (CurrentModelAccuracy > 0.8f)
    {
        // Model is performing well, can be more selective
        PositiveBehaviorThreshold = FMath::Min(PositiveBehaviorThreshold + 0.01f, 0.9f);
    }
    else if (CurrentModelAccuracy < 0.6f)
    {
        // Model needs improvement, be less selective
        PositiveBehaviorThreshold = FMath::Max(PositiveBehaviorThreshold - 0.01f, 0.5f);
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated model weights - New threshold: %.3f"), PositiveBehaviorThreshold);
}

void UPositiveBehaviorPredictor::CleanupOldTrainingData()
{
    // Remove oldest 20% of training data to maintain performance
    int32 RemoveCount = TrainingDataset.Num() / 5;
    
    // Sort by timestamp and remove oldest
    TrainingDataset.Sort([](const FPlayerBehaviorSnapshot& A, const FPlayerBehaviorSnapshot& B) {
        return A.Timestamp < B.Timestamp;
    });
    
    TrainingDataset.RemoveAt(0, RemoveCount);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Cleaned up %d old training samples"), RemoveCount);
}

void UPositiveBehaviorPredictor::NormalizeTrainingData()
{
    if (TrainingDataset.Num() == 0)
    {
        return;
    }
    
    // Find min/max values for normalization
    float MinPositivity = 1.0f, MaxPositivity = 0.0f;
    float MinToxicity = 1.0f, MaxToxicity = 0.0f;
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        MinPositivity = FMath::Min(MinPositivity, Sample.PositivityScore);
        MaxPositivity = FMath::Max(MaxPositivity, Sample.PositivityScore);
        MinToxicity = FMath::Min(MinToxicity, Sample.ToxicityScore);
        MaxToxicity = FMath::Max(MaxToxicity, Sample.ToxicityScore);
    }
    
    // Normalize values to 0-1 range
    for (FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (MaxPositivity > MinPositivity)
        {
            Sample.PositivityScore = (Sample.PositivityScore - MinPositivity) / (MaxPositivity - MinPositivity);
        }
        
        if (MaxToxicity > MinToxicity)
        {
            Sample.ToxicityScore = (Sample.ToxicityScore - MinToxicity) / (MaxToxicity - MinToxicity);
        }
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Normalized training data"));
}

void UPositiveBehaviorPredictor::BalanceTrainingDataset()
{
    // Ensure balanced representation of positive and negative behaviors
    int32 PositiveSamples = 0;
    int32 NegativeSamples = 0;
    
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (Sample.PositivityScore > 0.6f)
        {
            PositiveSamples++;
        }
        else if (Sample.ToxicityScore > 0.4f)
        {
            NegativeSamples++;
        }
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Training dataset balance - Positive: %d, Negative: %d"), 
        PositiveSamples, NegativeSamples);
    
    // In a full implementation, this would balance the dataset by sampling
    // For now, we just log the balance for monitoring
}
