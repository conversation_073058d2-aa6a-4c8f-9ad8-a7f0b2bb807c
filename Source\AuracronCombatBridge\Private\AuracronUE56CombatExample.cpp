// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Advanced Combat System Example for UE 5.6
// This file demonstrates production-ready implementation using UE 5.6 APIs

#include "AuracronUE56CombatExample.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/AudioComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "ControlRig.h"
#include "ControlRigComponent.h"
#include "RigVMHost.h"
#include "MetasoundSource.h"
#if WITH_EDITOR
#include "ControlRigBlueprint.h"
#endif
#include "ControlRigObjectBinding.h"
#include "Units/Execution/RigUnit_BeginExecution.h"

// Forward declarations for optional includes
#if WITH_EDITOR
#include "Units/RigUnit.h"
#endif

DEFINE_LOG_CATEGORY(LogAuracronUE56Combat);

AAuracronUE56CombatExample::AAuracronUE56CombatExample()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Initialize components using UE 5.6 best practices
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Skeletal Mesh Component for character
    SkeletalMeshComponent = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("SkeletalMeshComponent"));
    SkeletalMeshComponent->SetupAttachment(RootComponent);
    
    // Audio Component for MetaSound integration
    AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("AudioComponent"));
    AudioComponent->SetupAttachment(RootComponent);
    
    // Niagara Component for VFX
    NiagaraComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("NiagaraComponent"));
    NiagaraComponent->SetupAttachment(RootComponent);
    
    // Control Rig Component for advanced animation
    ControlRigComponent = CreateDefaultSubobject<UControlRigComponent>(TEXT("ControlRigComponent"));
    
    // Initialize default values
    CombatState = EAuracronCombatState::Idle;
    Health = 100.0f;
    MaxHealth = 100.0f;
    AttackDamage = 25.0f;
    AttackRange = 200.0f;
    bIsInCombat = false;
}

void AAuracronUE56CombatExample::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize systems using UE 5.6 APIs
    InitializeAudioSystem();
    InitializeVFXSystem();
    InitializeControlRigSystem();
    
    UE_LOG(LogAuracronUE56Combat, Log, TEXT("AURACRON UE 5.6 Combat Example initialized successfully"));
}

void AAuracronUE56CombatExample::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Update combat systems
    UpdateCombatState(DeltaTime);
    UpdateAudioSystem(DeltaTime);
    UpdateVFXSystem(DeltaTime);
}

void AAuracronUE56CombatExample::InitializeAudioSystem()
{
    if (!AudioComponent)
    {
        UE_LOG(LogAuracronUE56Combat, Error, TEXT("AudioComponent is null"));
        return;
    }
    
    // Load MetaSound asset using UE 5.6 asset loading
    if (CombatMetaSoundAsset.IsValid())
    {
        UMetaSoundSource* MetaSoundSource = Cast<UMetaSoundSource>(CombatMetaSoundAsset.LoadSynchronous());
        if (MetaSoundSource)
        {
            AudioComponent->SetSound(MetaSoundSource);
            
            // Set up MetaSound parameters using UE 5.6 parameter system
            TArray<FAudioParameter> AudioParameters;
            
            // Combat intensity parameter
            FAudioParameter IntensityParam;
            IntensityParam.ParamName = FName("CombatIntensity");
            IntensityParam.FloatParam = 0.0f;
            AudioParameters.Add(IntensityParam);
            
            // Health percentage parameter
            FAudioParameter HealthParam;
            HealthParam.ParamName = FName("HealthPercentage");
            HealthParam.FloatParam = Health / MaxHealth;
            AudioParameters.Add(HealthParam);
            
            // Apply parameters to MetaSound
            AudioComponent->SetParameters(MoveTemp(AudioParameters));
            
            UE_LOG(LogAuracronUE56Combat, Log, TEXT("MetaSound system initialized with %d parameters"), AudioParameters.Num());
        }
        else
        {
            UE_LOG(LogAuracronUE56Combat, Error, TEXT("Failed to load MetaSound asset"));
        }
    }
}

void AAuracronUE56CombatExample::InitializeVFXSystem()
{
    if (!NiagaraComponent)
    {
        UE_LOG(LogAuracronUE56Combat, Error, TEXT("NiagaraComponent is null"));
        return;
    }
    
    // Load Niagara System using UE 5.6 asset loading
    if (CombatVFXAsset.IsValid())
    {
        UNiagaraSystem* NiagaraSystem = CombatVFXAsset.LoadSynchronous();
        if (NiagaraSystem)
        {
            NiagaraComponent->SetAsset(NiagaraSystem);
            
            // Set up Niagara parameters using UE 5.6 parameter system
            NiagaraComponent->SetFloatParameter(FName("CombatIntensity"), 0.0f);
            NiagaraComponent->SetFloatParameter(FName("HealthPercentage"), Health / MaxHealth);
            NiagaraComponent->SetVectorParameter(FName("EffectColor"), FVector(1.0f, 1.0f, 1.0f));
            
            // Initialize but don't activate yet
            NiagaraComponent->SetAutoActivate(false);
            
            UE_LOG(LogAuracronUE56Combat, Log, TEXT("Niagara VFX system initialized"));
        }
        else
        {
            UE_LOG(LogAuracronUE56Combat, Error, TEXT("Failed to load Niagara system asset"));
        }
    }
}

void AAuracronUE56CombatExample::InitializeControlRigSystem()
{
    if (!ControlRigComponent || !SkeletalMeshComponent)
    {
        UE_LOG(LogAuracronUE56Combat, Error, TEXT("ControlRigComponent or SkeletalMeshComponent is null"));
        return;
    }
    
    // Load Control Rig Blueprint using UE 5.6 asset loading - Production Ready Implementation
#if WITH_EDITOR
    // Editor build - full ControlRigBlueprint support
    if (CombatControlRigAsset.IsValid())
    {
        UControlRigBlueprint* ControlRigBP = Cast<UControlRigBlueprint>(CombatControlRigAsset.LoadSynchronous());
        if (ControlRigBP)
        {
            // Set up Control Rig with skeletal mesh using UE 5.6 APIs
            ControlRigComponent->SetControlRigClass(ControlRigBP->GetControlRigClass());

            // Get the Control Rig instance
            UControlRig* ControlRig = ControlRigComponent->GetControlRig();
            if (ControlRig)
            {
                // Initialize Control Rig with skeletal mesh using UE 5.6 API
                TSharedPtr<FControlRigObjectBinding> ObjectBinding = MakeShared<FControlRigObjectBinding>();
                ObjectBinding->BindToObject(SkeletalMeshComponent);
                ControlRig->SetObjectBinding(ObjectBinding);

                // Set up initial control values using UE 5.6 Control Rig API
                SetupControlRigControls(ControlRig);

                UE_LOG(LogAuracronUE56Combat, Log, TEXT("Control Rig system initialized with Blueprint"));
            }
            else
            {
                UE_LOG(LogAuracronUE56Combat, Error, TEXT("Failed to get Control Rig instance from Blueprint"));
            }
        }
        else
        {
            UE_LOG(LogAuracronUE56Combat, Error, TEXT("Failed to load Control Rig Blueprint asset"));
        }
    }
    else
    {
        UE_LOG(LogAuracronUE56Combat, Warning, TEXT("No Control Rig Blueprint asset specified"));
    }
#else
    // Runtime build - direct ControlRig initialization
    if (ControlRigComponent)
    {
        // Get the Control Rig instance
        UControlRig* ControlRig = ControlRigComponent->GetControlRig();
        if (ControlRig)
        {
            // Initialize Control Rig with skeletal mesh using UE 5.6 API
            TSharedPtr<FControlRigObjectBinding> ObjectBinding = MakeShared<FControlRigObjectBinding>();
            ObjectBinding->BindToObject(SkeletalMeshComponent);
            ControlRig->SetObjectBinding(ObjectBinding);

            // Set up initial control values using UE 5.6 Control Rig API
            SetupControlRigControls(ControlRig);

            UE_LOG(LogAuracronUE56Combat, Log, TEXT("Control Rig system initialized for runtime"));
        }
        else
        {
            UE_LOG(LogAuracronUE56Combat, Warning, TEXT("Control Rig instance not available in runtime"));
        }
    }
    else
    {
        UE_LOG(LogAuracronUE56Combat, Warning, TEXT("No Control Rig component available"));
    }
#endif
}

void AAuracronUE56CombatExample::SetupControlRigControls(UControlRig* ControlRig)
{
    if (!ControlRig)
    {
        return;
    }
    
    // Get the hierarchy for control setup
    URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
    if (!Hierarchy)
    {
        UE_LOG(LogAuracronUE56Combat, Error, TEXT("Control Rig hierarchy is null"));
        return;
    }
    
    // Set up combat-specific controls using UE 5.6 Control Rig API
    TArray<FRigElementKey> ControlKeys;
    Hierarchy->ForEach<FRigControlElement>([&ControlKeys](FRigControlElement* ControlElement) -> bool
    {
        ControlKeys.Add(ControlElement->GetKey());
        return true;
    });
    
    for (const FRigElementKey& ControlKey : ControlKeys)
    {
        if (ControlKey.Name.ToString().Contains(TEXT("Combat")))
        {
            // Set initial control values
            FRigControlValue ControlValue;
            ControlValue.SetFromTransform(FTransform::Identity, ERigControlType::Transform, ERigControlAxis::X);
            
            Hierarchy->SetControlValue(ControlKey, ControlValue, ERigControlValueType::Current);
            
            UE_LOG(LogAuracronUE56Combat, Log, TEXT("Initialized combat control: %s"), *ControlKey.Name.ToString());
        }
    }
}

void AAuracronUE56CombatExample::UpdateCombatState(float DeltaTime)
{
    // Update combat state machine
    switch (CombatState)
    {
        case EAuracronCombatState::Idle:
            HandleIdleState(DeltaTime);
            break;
            
        case EAuracronCombatState::Attacking:
            HandleAttackingState(DeltaTime);
            break;
            
        case EAuracronCombatState::Defending:
            HandleDefendingState(DeltaTime);
            break;
            
        case EAuracronCombatState::Stunned:
            HandleStunnedState(DeltaTime);
            break;
            
        case EAuracronCombatState::Dead:
            HandleDeadState(DeltaTime);
            break;
    }
}

void AAuracronUE56CombatExample::UpdateAudioSystem(float DeltaTime)
{
    if (!AudioComponent)
    {
        return;
    }
    
    // Update MetaSound parameters based on combat state using UE 5.6 APIs
    TArray<FAudioParameter> UpdatedParameters;
    
    // Combat intensity based on state
    float CombatIntensity = GetCombatIntensity();
    FAudioParameter IntensityParam;
    IntensityParam.ParamName = FName("CombatIntensity");
    IntensityParam.FloatParam = CombatIntensity;
    UpdatedParameters.Add(IntensityParam);
    
    // Health percentage
    FAudioParameter HealthParam;
    HealthParam.ParamName = FName("HealthPercentage");
    HealthParam.FloatParam = Health / MaxHealth;
    UpdatedParameters.Add(HealthParam);
    
    // Apply updated parameters
    AudioComponent->SetParameters(MoveTemp(UpdatedParameters));
}

void AAuracronUE56CombatExample::UpdateVFXSystem(float DeltaTime)
{
    if (!NiagaraComponent)
    {
        return;
    }
    
    // Update Niagara parameters based on combat state using UE 5.6 APIs
    float CombatIntensity = GetCombatIntensity();
    float HealthPercentage = Health / MaxHealth;
    
    NiagaraComponent->SetFloatParameter(FName("CombatIntensity"), CombatIntensity);
    NiagaraComponent->SetFloatParameter(FName("HealthPercentage"), HealthPercentage);
    
    // Update effect color based on health
    FVector EffectColor = FMath::Lerp(FVector(1.0f, 0.0f, 0.0f), FVector(0.0f, 1.0f, 0.0f), HealthPercentage);
    NiagaraComponent->SetVectorParameter(FName("EffectColor"), EffectColor);
    
    // Activate/deactivate effects based on combat state
    if (bIsInCombat && !NiagaraComponent->IsActive())
    {
        NiagaraComponent->Activate(true); // UE 5.6 API: Activate with reset parameter
    }
    else if (!bIsInCombat && NiagaraComponent->IsActive())
    {
        NiagaraComponent->Deactivate();
    }
}

float AAuracronUE56CombatExample::GetCombatIntensity() const
{
    switch (CombatState)
    {
        case EAuracronCombatState::Idle:
            return 0.0f;
        case EAuracronCombatState::Attacking:
            return 1.0f;
        case EAuracronCombatState::Defending:
            return 0.7f;
        case EAuracronCombatState::Stunned:
            return 0.3f;
        case EAuracronCombatState::Dead:
            return 0.0f;
        default:
            return 0.0f;
    }
}

void AAuracronUE56CombatExample::StartAttack()
{
    if (CombatState == EAuracronCombatState::Idle || CombatState == EAuracronCombatState::Defending)
    {
        CombatState = EAuracronCombatState::Attacking;
        bIsInCombat = true;
        
        // Play attack animation using UE 5.6 animation system
        PlayAttackAnimation();
        
        // Trigger attack VFX using UE 5.6 Niagara system
        TriggerAttackVFX();
        
        // Play attack audio using UE 5.6 MetaSound system
        PlayAttackAudio();
        
        // Execute Control Rig attack sequence using UE 5.6 Control Rig API
        ExecuteControlRigAttack();
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Attack started"));
    }
}

void AAuracronUE56CombatExample::PlayAttackAnimation()
{
    if (!SkeletalMeshComponent || !AttackMontage)
    {
        return;
    }
    
    UAnimInstance* AnimInstance = SkeletalMeshComponent->GetAnimInstance();
    if (AnimInstance)
    {
        // Play montage using UE 5.6 animation system
        AnimInstance->Montage_Play(AttackMontage, 1.0f);
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Attack animation started"));
    }
}

void AAuracronUE56CombatExample::TriggerAttackVFX()
{
    if (!NiagaraComponent)
    {
        return;
    }
    
    // Set attack-specific parameters using UE 5.6 Niagara API
    NiagaraComponent->SetFloatParameter(FName("AttackIntensity"), 1.0f);
    NiagaraComponent->SetVectorParameter(FName("AttackDirection"), GetActorForwardVector());
    NiagaraComponent->SetFloatParameter(FName("AttackDamage"), AttackDamage);
    
    // Activate the effect using UE 5.6 API
    NiagaraComponent->Activate(true);
    
    UE_LOG(LogAuracronUE56Combat, Log, TEXT("Attack VFX triggered"));
}

void AAuracronUE56CombatExample::PlayAttackAudio()
{
    if (!AudioComponent)
    {
        return;
    }
    
    // Update MetaSound parameters for attack using UE 5.6 API
    TArray<FAudioParameter> AttackParameters;
    
    FAudioParameter AttackTrigger;
    AttackTrigger.ParamName = FName("TriggerAttack");
    AttackTrigger.BoolParam = true;
    AttackParameters.Add(AttackTrigger);
    
    FAudioParameter AttackType;
    AttackType.ParamName = FName("AttackType");
    AttackType.IntParam = static_cast<int32>(EAuracronAttackType::Melee);
    AttackParameters.Add(AttackType);
    
    // Apply parameters and play
    AudioComponent->SetParameters(MoveTemp(AttackParameters));
    AudioComponent->Play();
    
    UE_LOG(LogAuracronUE56Combat, Log, TEXT("Attack audio triggered"));
}

void AAuracronUE56CombatExample::ExecuteControlRigAttack()
{
    if (!ControlRigComponent)
    {
        return;
    }
    
    UControlRig* ControlRig = ControlRigComponent->GetControlRig();
    if (!ControlRig)
    {
        return;
    }
    
    // Set up attack-specific control values using UE 5.6 Control Rig API
    URigHierarchy* Hierarchy = ControlRig->GetHierarchy();
    if (Hierarchy)
    {
        // Find attack control
        FRigElementKey AttackControlKey = FRigElementKey(FName("AttackControl"), ERigElementType::Control);
        if (AttackControlKey.IsValid())
        {
            // Set attack transform
            FTransform AttackTransform = FTransform::Identity;
            AttackTransform.SetLocation(GetActorForwardVector() * AttackRange);
            
            FRigControlValue AttackValue;
            AttackValue.SetFromTransform(AttackTransform, ERigControlType::Transform, ERigControlAxis::X);
            
            Hierarchy->SetControlValue(AttackControlKey, AttackValue, ERigControlValueType::Current);
            
            // Execute Control Rig using UE 5.6 API
            ControlRig->Execute(FRigUnit_BeginExecution::EventName);
            
            UE_LOG(LogAuracronUE56Combat, Log, TEXT("Control Rig attack executed"));
        }
    }
}

void AAuracronUE56CombatExample::HandleIdleState(float DeltaTime)
{
    // Handle idle state logic
    if (bIsInCombat)
    {
        bIsInCombat = false;
        
        // Reset audio parameters
        if (AudioComponent)
        {
            TArray<FAudioParameter> IdleParameters;
            
            FAudioParameter IntensityParam;
            IntensityParam.ParamName = FName("CombatIntensity");
            IntensityParam.FloatParam = 0.0f;
            IdleParameters.Add(IntensityParam);
            
            AudioComponent->SetParameters(MoveTemp(IdleParameters));
        }
        
        // Deactivate VFX
        if (NiagaraComponent && NiagaraComponent->IsActive())
        {
            NiagaraComponent->Deactivate();
        }
    }
}

void AAuracronUE56CombatExample::HandleAttackingState(float DeltaTime)
{
    // Handle attacking state logic
    AttackTimer += DeltaTime;
    
    if (AttackTimer >= AttackDuration)
    {
        // Attack finished, return to idle
        CombatState = EAuracronCombatState::Idle;
        AttackTimer = 0.0f;
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Attack completed, returning to idle"));
    }
}

void AAuracronUE56CombatExample::HandleDefendingState(float DeltaTime)
{
    // Handle defending state logic
    DefenseTimer += DeltaTime;
    
    if (DefenseTimer >= DefenseDuration)
    {
        CombatState = EAuracronCombatState::Idle;
        DefenseTimer = 0.0f;
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Defense completed, returning to idle"));
    }
}

void AAuracronUE56CombatExample::HandleStunnedState(float DeltaTime)
{
    // Handle stunned state logic
    StunTimer += DeltaTime;
    
    if (StunTimer >= StunDuration)
    {
        CombatState = EAuracronCombatState::Idle;
        StunTimer = 0.0f;
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Stun effect ended, returning to idle"));
    }
}

void AAuracronUE56CombatExample::HandleDeadState(float DeltaTime)
{
    // Handle death state logic
    if (!bDeathSequenceStarted)
    {
        bDeathSequenceStarted = true;
        
        // Stop all audio
        if (AudioComponent)
        {
            AudioComponent->Stop();
        }
        
        // Play death VFX
        if (NiagaraComponent)
        {
            NiagaraComponent->SetFloatParameter(FName("DeathEffect"), 1.0f);
            NiagaraComponent->Activate(true);
        }
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Death sequence started"));
    }
}

float AAuracronUE56CombatExample::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
    if (CombatState == EAuracronCombatState::Dead)
    {
        return 0.0f;
    }

    // Call parent implementation first
    float ActualDamage = Super::TakeDamage(DamageAmount, DamageEvent, EventInstigator, DamageCauser);

    Health = FMath::Max(0.0f, Health - ActualDamage);

    if (Health <= 0.0f)
    {
        CombatState = EAuracronCombatState::Dead;
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Character died"));
    }
    else
    {
        // Trigger damage effects
        TriggerDamageEffects();
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Took %.2f damage, health: %.2f"), ActualDamage, Health);
    }

    return ActualDamage;
}

void AAuracronUE56CombatExample::TriggerDamageEffects()
{
    // Update audio for damage using UE 5.6 MetaSound API
    if (AudioComponent)
    {
        TArray<FAudioParameter> DamageParameters;
        
        FAudioParameter DamageTrigger;
        DamageTrigger.ParamName = FName("TriggerDamage");
        DamageTrigger.BoolParam = true;
        DamageParameters.Add(DamageTrigger);
        
        FAudioParameter HealthParam;
        HealthParam.ParamName = FName("HealthPercentage");
        HealthParam.FloatParam = Health / MaxHealth;
        DamageParameters.Add(HealthParam);
        
        AudioComponent->SetParameters(MoveTemp(DamageParameters));
    }
    
    // Update VFX for damage using UE 5.6 Niagara API
    if (NiagaraComponent)
    {
        NiagaraComponent->SetFloatParameter(FName("DamageIntensity"), 1.0f);
        NiagaraComponent->SetFloatParameter(FName("HealthPercentage"), Health / MaxHealth);
        
        // Trigger damage effect burst
        NiagaraComponent->Activate(true);
    }
}

void AAuracronUE56CombatExample::StartDefense()
{
    if (CombatState == EAuracronCombatState::Idle)
    {
        CombatState = EAuracronCombatState::Defending;
        bIsInCombat = true;
        DefenseTimer = 0.0f;
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Defense started"));
    }
}

void AAuracronUE56CombatExample::ApplyStun(float StunDurationOverride)
{
    if (CombatState != EAuracronCombatState::Dead)
    {
        CombatState = EAuracronCombatState::Stunned;
        StunTimer = 0.0f;
        
        if (StunDurationOverride > 0.0f)
        {
            StunDuration = StunDurationOverride;
        }
        
        UE_LOG(LogAuracronUE56Combat, Log, TEXT("Stun applied for %.2f seconds"), StunDuration);
    }
}
