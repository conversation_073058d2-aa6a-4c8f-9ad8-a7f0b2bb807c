#include "HarmonyEngineSubsystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "EmotionalIntelligenceComponent.h"
#include "PositiveBehaviorPredictor.h"
#include "CommunityHealingManager.h"
#include "HarmonyRewardsSystem.h"
#include "RealTimeInterventionSystem.h"
#include "HarmonyEngineAdvancedML.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "Online/CoreOnline.h"

// LogHarmonyEngine is already defined in AuracronHarmonyEngineBridge.cpp

UHarmonyEngineSubsystem::UHarmonyEngineSubsystem()
{
    // Default configuration values
    ToxicityThreshold = 0.7f;
    InterventionCooldown = FTimespan::FromMinutes(5.0); // 5 minutes
    MaxConcurrentInterventions = 5;
    bEnableRealTimeMonitoring = true;
    bEnablePredictiveIntervention = true;
}

void UHarmonyEngineSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initializing Harmony Engine Subsystem"));
    
    // Create core components
    EmotionalIntelligence = NewObject<UEmotionalIntelligenceComponent>(this);
    BehaviorPredictor = NewObject<UPositiveBehaviorPredictor>(this);
    HealingManager = NewObject<UCommunityHealingManager>(this);
    RewardsSystem = NewObject<UHarmonyRewardsSystem>(this);
    InterventionSystem = NewObject<URealTimeInterventionSystem>(this);
    
    // Initialize components
    if (EmotionalIntelligence)
    {
        EmotionalIntelligence->RegisterComponent();
    }
    
    // Setup timers for periodic analysis
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            BehaviorAnalysisTimer,
            this,
            &UHarmonyEngineSubsystem::PerformPeriodicAnalysis,
            30.0f, // Every 30 seconds
            true
        );
        
        World->GetTimerManager().SetTimer(
            CommunityHealthTimer,
            this,
            &UHarmonyEngineSubsystem::UpdateCommunityHealth,
            60.0f, // Every minute
            true
        );
    }
    
    // Load persistent data
    LoadPlayerData();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine Subsystem initialized successfully"));
}

void UHarmonyEngineSubsystem::Deinitialize()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Deinitializing Harmony Engine Subsystem"));
    
    // Save data before shutdown
    SavePlayerData();
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(BehaviorAnalysisTimer);
        World->GetTimerManager().ClearTimer(CommunityHealthTimer);
    }
    
    // Cleanup components
    EmotionalIntelligence = nullptr;
    BehaviorPredictor = nullptr;
    HealingManager = nullptr;
    RewardsSystem = nullptr;
    InterventionSystem = nullptr;
    
    // Clear data structures
    PlayerBehaviorData.Empty();
    RegisteredPlayers.Empty();
    PlayerKindnessScores.Empty();
    
    Super::Deinitialize();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine Subsystem deinitialized"));
}

bool UHarmonyEngineSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Only create in game instances, not in editor
    return !IsRunningDedicatedServer() && !GIsEditor;
}

void UHarmonyEngineSubsystem::RegisterPlayer(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPlayerState<APlayerState>())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot register invalid player controller"));
        return;
    }
    
    FString PlayerID = PlayerController->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
    
    if (RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Player %s is already registered"), *PlayerID);
        return;
    }
    
    // Register player
    RegisteredPlayers.Add(PlayerID, PlayerController);
    
    // Initialize behavior data
    FPlayerBehaviorSnapshot InitialSnapshot;
    InitialSnapshot.PlayerID = PlayerID;
    InitialSnapshot.Timestamp = FDateTime::Now();
    PlayerBehaviorData.Add(PlayerID, InitialSnapshot);
    
    // Initialize kindness score
    PlayerKindnessScores.Add(PlayerID, 0.0f);
    
    // Start monitoring if enabled
    if (bEnableRealTimeMonitoring && EmotionalIntelligence)
    {
        EmotionalIntelligence->StartEmotionalMonitoring(PlayerID);
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s registered successfully"), *PlayerID);
    
    // Broadcast registration event
    OnBehaviorDetected.Broadcast(PlayerID, InitialSnapshot);
}

void UHarmonyEngineSubsystem::UnregisterPlayer(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPlayerState<APlayerState>())
    {
        return;
    }
    
    FString PlayerID = PlayerController->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
    
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        return;
    }
    
    // Stop monitoring
    if (EmotionalIntelligence)
    {
        EmotionalIntelligence->StopEmotionalMonitoring(PlayerID);
    }
    
    // Save final data
    SavePlayerBehaviorData(PlayerID);
    
    // Remove from active tracking
    RegisteredPlayers.Remove(PlayerID);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s unregistered"), *PlayerID);
}

void UHarmonyEngineSubsystem::UpdatePlayerBehavior(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Attempting to update behavior for unregistered player: %s"), *PlayerID);
        return;
    }
    
    // Update behavior data
    PlayerBehaviorData.Add(PlayerID, BehaviorData);
    
    // Analyze for intervention needs
    if (bEnablePredictiveIntervention)
    {
        AnalyzeBehaviorForIntervention(PlayerID, BehaviorData);
    }
    
    // Update ML training data
    if (BehaviorPredictor)
    {
        BehaviorPredictor->AddTrainingData(BehaviorData);
    }
    
    // Broadcast behavior update
    OnBehaviorDetected.Broadcast(PlayerID, BehaviorData);
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated behavior for player %s"), *PlayerID);
}

void UHarmonyEngineSubsystem::TriggerIntervention(const FString& PlayerID, EInterventionType InterventionType, const FString& Reason)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot trigger intervention for unregistered player: %s"), *PlayerID);
        return;
    }
    
    // Check intervention cooldown
    if (IsPlayerOnInterventionCooldown(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is on intervention cooldown"), *PlayerID);
        return;
    }
    
    // Create intervention data
    FHarmonyInterventionData InterventionData;
    InterventionData.InterventionType = InterventionType;
    InterventionData.SuggestedAction = GenerateInterventionMessage(InterventionType, Reason);
    InterventionData.InterventionPriority = CalculateInterventionPriority(InterventionType);
    InterventionData.bRequiresImmediateAction = (InterventionType == EInterventionType::Emergency);
    
    // Execute intervention
    if (InterventionSystem)
    {
        InterventionSystem->ExecuteIntervention(PlayerID, InterventionData);
    }
    
    // Update cooldown
    UpdateInterventionCooldown(PlayerID);
    
    // Broadcast intervention event
    OnInterventionTriggered.Broadcast(PlayerID, InterventionData);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Intervention triggered for player %s: %s"), *PlayerID, *Reason);
}

float UHarmonyEngineSubsystem::GetPlayerToxicityScore(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->ToxicityScore;
    }
    return 0.0f;
}

float UHarmonyEngineSubsystem::GetPlayerPositivityScore(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->PositivityScore;
    }
    return 0.0f;
}

EEmotionalState UHarmonyEngineSubsystem::GetPlayerEmotionalState(const FString& PlayerID) const
{
    if (const FPlayerBehaviorSnapshot* BehaviorData = PlayerBehaviorData.Find(PlayerID))
    {
        return BehaviorData->EmotionalState;
    }
    return EEmotionalState::Neutral;
}

bool UHarmonyEngineSubsystem::IsPlayerAtRisk(const FString& PlayerID) const
{
    const float ToxicityScore = GetPlayerToxicityScore(PlayerID);
    const EEmotionalState EmotionalState = GetPlayerEmotionalState(PlayerID);
    
    return (ToxicityScore > ToxicityThreshold) || 
           (EmotionalState == EEmotionalState::Angry) || 
           (EmotionalState == EEmotionalState::Frustrated);
}

void UHarmonyEngineSubsystem::AwardKindnessPoints(const FString& PlayerID, int32 Points, const FString& Reason)
{
    if (!RegisteredPlayers.Contains(PlayerID))
    {
        return;
    }
    
    // Update kindness score
    float& CurrentScore = PlayerKindnessScores.FindOrAdd(PlayerID);
    CurrentScore += Points;
    
    // Create reward data
    FKindnessReward Reward;
    Reward.KindnessPoints = Points;
    Reward.RewardDescription = Reason;
    Reward.ExperienceMultiplier = CalculateExperienceMultiplier(Points);
    
    // Process reward through rewards system
    if (RewardsSystem)
    {
        RewardsSystem->ProcessKindnessReward(PlayerID, Reward);
    }
    
    // Broadcast reward event
    OnKindnessReward.Broadcast(PlayerID, Reward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Awarded %d kindness points to player %s for: %s"), Points, *PlayerID, *Reason);
}

// Private helper functions
void UHarmonyEngineSubsystem::PerformPeriodicAnalysis()
{
    for (const auto& PlayerPair : RegisteredPlayers)
    {
        const FString& PlayerID = PlayerPair.Key;
        AnalyzePlayerBehavior(PlayerID);
    }
}

void UHarmonyEngineSubsystem::AnalyzePlayerBehavior(const FString& PlayerID)
{
    if (EmotionalIntelligence)
    {
        EEmotionalState CurrentState = EmotionalIntelligence->AnalyzeCurrentEmotionalState(PlayerID);
        
        // Check for intervention needs
        if (EmotionalIntelligence->DetectEmotionalEscalation(PlayerID))
        {
            FHarmonyInterventionData Intervention = EmotionalIntelligence->RecommendIntervention(PlayerID);
            if (Intervention.InterventionType != EInterventionType::None)
            {
                TriggerIntervention(PlayerID, Intervention.InterventionType, TEXT("Emotional escalation detected"));
            }
        }
    }
}

void UHarmonyEngineSubsystem::SavePlayerData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Saving Harmony Engine player data for %d players"), PlayerBehaviorData.Num());

    // Create save data structure
    FString SaveDataJson = TEXT("{\"players\":[");
    bool bFirstEntry = true;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        if (!bFirstEntry)
        {
            SaveDataJson += TEXT(",");
        }
        bFirstEntry = false;

        const FString& PlayerID = PlayerPair.Key;
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;

        // Convert behavior data to JSON
        SaveDataJson += FString::Printf(TEXT("{\"id\":\"%s\",\"toxicity\":%.3f,\"positivity\":%.3f,\"frustration\":%.3f,\"kindness\":%.3f,\"timestamp\":\"%s\"}"),
            *PlayerID,
            BehaviorData.ToxicityScore,
            BehaviorData.PositivityScore,
            BehaviorData.FrustrationLevel,
            PlayerKindnessScores.Contains(PlayerID) ? PlayerKindnessScores[PlayerID] : 0.0f,
            *BehaviorData.Timestamp.ToString()
        );
    }

    SaveDataJson += TEXT("]}");

    // Save to persistent storage using Unreal's save system
    if (UGameplayStatics::DoesSaveGameExist(TEXT("HarmonyEngineData"), 0))
    {
        UGameplayStatics::DeleteGameInSlot(TEXT("HarmonyEngineData"), 0);
    }

    // Create a simple save game object for the JSON data
    // In a full implementation, this would use a proper USaveGame subclass
    FString SavePath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/PlayerBehaviorData.json");
    FFileHelper::SaveStringToFile(SaveDataJson, *SavePath);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Successfully saved Harmony Engine data to: %s"), *SavePath);
}

void UHarmonyEngineSubsystem::LoadPlayerData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Loading Harmony Engine player data"));

    FString SavePath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/PlayerBehaviorData.json");
    FString LoadedJson;

    if (!FFileHelper::LoadFileToString(LoadedJson, *SavePath))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("No existing Harmony Engine data found, starting fresh"));
        return;
    }

    // Parse JSON data
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(LoadedJson);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Failed to parse Harmony Engine save data"));
        return;
    }

    // Load player data
    const TArray<TSharedPtr<FJsonValue>>* PlayersArray;
    if (JsonObject->TryGetArrayField(TEXT("players"), PlayersArray))
    {
        for (const auto& PlayerValue : *PlayersArray)
        {
            TSharedPtr<FJsonObject> PlayerObject = PlayerValue->AsObject();
            if (!PlayerObject.IsValid())
            {
                continue;
            }

            FString PlayerID;
            if (!PlayerObject->TryGetStringField(TEXT("id"), PlayerID))
            {
                continue;
            }

            // Create behavior snapshot from loaded data
            FPlayerBehaviorSnapshot BehaviorData;
            BehaviorData.PlayerID = PlayerID;
            PlayerObject->TryGetNumberField(TEXT("toxicity"), BehaviorData.ToxicityScore);
            PlayerObject->TryGetNumberField(TEXT("positivity"), BehaviorData.PositivityScore);
            PlayerObject->TryGetNumberField(TEXT("frustration"), BehaviorData.FrustrationLevel);

            FString TimestampString;
            if (PlayerObject->TryGetStringField(TEXT("timestamp"), TimestampString))
            {
                FDateTime::Parse(TimestampString, BehaviorData.Timestamp);
            }

            // Load kindness score
            double KindnessScore = 0.0;
            PlayerObject->TryGetNumberField(TEXT("kindness"), KindnessScore);
            PlayerKindnessScores.Add(PlayerID, static_cast<float>(KindnessScore));

            // Store loaded data
            PlayerBehaviorData.Add(PlayerID, BehaviorData);
        }

        UE_LOG(LogHarmonyEngine, Log, TEXT("Successfully loaded data for %d players"), PlayerBehaviorData.Num());
    }
}

// Implementation of missing helper functions

void UHarmonyEngineSubsystem::AnalyzeBehaviorForIntervention(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    // Check if intervention is needed based on behavior data
    if (BehaviorData.ToxicityScore > ToxicityThreshold)
    {
        EInterventionType InterventionType = EInterventionType::Moderate;

        if (BehaviorData.ToxicityScore > 0.9f)
        {
            InterventionType = EInterventionType::Strong;
        }
        else if (BehaviorData.FrustrationLevel > 0.8f)
        {
            InterventionType = EInterventionType::Emergency;
        }

        TriggerIntervention(PlayerID, InterventionType, TEXT("High toxicity detected"));
    }
    else if (BehaviorData.FrustrationLevel > FrustrationThreshold)
    {
        TriggerIntervention(PlayerID, EInterventionType::Gentle, TEXT("Frustration level rising"));
    }
}

bool UHarmonyEngineSubsystem::IsPlayerOnInterventionCooldown(const FString& PlayerID)
{
    if (!LastInterventionTimes.Contains(PlayerID))
    {
        return false;
    }

    FDateTime LastIntervention = LastInterventionTimes[PlayerID];
    FDateTime CurrentTime = FDateTime::Now();
    FTimespan TimeSinceLastIntervention = CurrentTime - LastIntervention;

    return TimeSinceLastIntervention < InterventionCooldown;
}

FString UHarmonyEngineSubsystem::GenerateInterventionMessage(EInterventionType InterventionType, const FString& Reason)
{
    FString Message;

    switch (InterventionType)
    {
        case EInterventionType::Gentle:
            Message = FString::Printf(TEXT("Hey there! We noticed you might be feeling a bit frustrated. Take a deep breath - you've got this! 🌟"));
            break;

        case EInterventionType::Moderate:
            Message = FString::Printf(TEXT("We understand gaming can be intense sometimes. Would you like to take a quick break or try a different approach? Your team believes in you! 💪"));
            break;

        case EInterventionType::Strong:
            Message = FString::Printf(TEXT("It looks like you're having a tough time. Remember, every pro player has bad games. Let's focus on positive teamwork and comeback together! 🤝"));
            break;

        case EInterventionType::Emergency:
            Message = FString::Printf(TEXT("We're here to support you. Gaming should be fun for everyone. Would you like to speak with a community mentor or take a wellness break? 🕊️"));
            break;

        default:
            Message = TEXT("Stay positive and have fun! 😊");
            break;
    }

    return Message;
}

float UHarmonyEngineSubsystem::CalculateInterventionPriority(EInterventionType InterventionType)
{
    switch (InterventionType)
    {
        case EInterventionType::Emergency:
            return 1.0f;
        case EInterventionType::Strong:
            return 0.8f;
        case EInterventionType::Moderate:
            return 0.6f;
        case EInterventionType::Gentle:
            return 0.4f;
        default:
            return 0.0f;
    }
}

void UHarmonyEngineSubsystem::UpdateInterventionCooldown(const FString& PlayerID)
{
    LastInterventionTimes.Add(PlayerID, FDateTime::Now());
}

float UHarmonyEngineSubsystem::CalculateExperienceMultiplier(int32 KindnessPoints)
{
    // Progressive multiplier based on kindness points
    if (KindnessPoints >= 100)
    {
        return 1.5f; // 50% bonus for exceptional kindness
    }
    else if (KindnessPoints >= 50)
    {
        return 1.3f; // 30% bonus for high kindness
    }
    else if (KindnessPoints >= 20)
    {
        return 1.2f; // 20% bonus for good kindness
    }
    else if (KindnessPoints >= 10)
    {
        return 1.1f; // 10% bonus for basic kindness
    }

    return 1.0f; // No bonus for low kindness
}

void UHarmonyEngineSubsystem::SavePlayerBehaviorData(const FString& PlayerID)
{
    if (!PlayerBehaviorData.Contains(PlayerID))
    {
        return;
    }

    const FPlayerBehaviorSnapshot& BehaviorData = PlayerBehaviorData[PlayerID];
    float KindnessScore = PlayerKindnessScores.Contains(PlayerID) ? PlayerKindnessScores[PlayerID] : 0.0f;

    // Create individual player save data
    FString PlayerDataJson = FString::Printf(
        TEXT("{\"id\":\"%s\",\"toxicity\":%.3f,\"positivity\":%.3f,\"frustration\":%.3f,\"kindness\":%.3f,\"positive_actions\":%d,\"negative_actions\":%d,\"session_duration\":%.2f,\"timestamp\":\"%s\"}"),
        *PlayerID,
        BehaviorData.ToxicityScore,
        BehaviorData.PositivityScore,
        BehaviorData.FrustrationLevel,
        KindnessScore,
        BehaviorData.PositiveActionsCount,
        BehaviorData.NegativeActionsCount,
        BehaviorData.SessionDuration,
        *BehaviorData.Timestamp.ToString()
    );

    // Save individual player file
    FString PlayerSavePath = FPaths::ProjectSavedDir() + FString::Printf(TEXT("HarmonyEngine/Players/%s.json"), *PlayerID);
    FFileHelper::SaveStringToFile(PlayerDataJson, *PlayerSavePath);

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Saved behavior data for player: %s"), *PlayerID);
}

void UHarmonyEngineSubsystem::UpdateCommunityHealth()
{
    if (RegisteredPlayers.Num() == 0)
    {
        return;
    }

    // Calculate overall community health metrics
    float TotalToxicity = 0.0f;
    float TotalPositivity = 0.0f;
    float TotalKindness = 0.0f;
    int32 ActivePlayers = 0;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;
        TotalToxicity += BehaviorData.ToxicityScore;
        TotalPositivity += BehaviorData.PositivityScore;

        if (PlayerKindnessScores.Contains(PlayerPair.Key))
        {
            TotalKindness += PlayerKindnessScores[PlayerPair.Key];
        }

        ActivePlayers++;
    }

    if (ActivePlayers > 0)
    {
        float AvgToxicity = TotalToxicity / ActivePlayers;
        float AvgPositivity = TotalPositivity / ActivePlayers;
        float AvgKindness = TotalKindness / ActivePlayers;

        // Calculate community health score (0-100)
        float CommunityHealthScore = FMath::Clamp(
            (AvgPositivity * 40.0f) + (AvgKindness * 0.3f) + ((1.0f - AvgToxicity) * 30.0f),
            0.0f,
            100.0f
        );

        UE_LOG(LogHarmonyEngine, Log, TEXT("Community Health Update - Score: %.2f, Avg Toxicity: %.3f, Avg Positivity: %.3f, Avg Kindness: %.2f"),
            CommunityHealthScore, AvgToxicity, AvgPositivity, AvgKindness);

        // Trigger community-wide events based on health score
        if (CommunityHealthScore > 80.0f)
        {
            // Trigger positive community event
            BroadcastCommunityEvent(TEXT("High community harmony detected! Bonus rewards activated! 🌟"));
        }
        else if (CommunityHealthScore < 30.0f)
        {
            // Trigger community healing initiative
            BroadcastCommunityEvent(TEXT("Community needs healing. Kindness initiatives activated. Let's support each other! 🤝"));
        }
    }
}

void UHarmonyEngineSubsystem::BroadcastCommunityEvent(const FString& EventMessage)
{
    // Broadcast to all registered players
    for (const auto& PlayerPair : RegisteredPlayers)
    {
        if (APlayerController* PC = PlayerPair.Value.Get())
        {
            // Send message to player (this would integrate with the UI system)
            UE_LOG(LogHarmonyEngine, Log, TEXT("Broadcasting to player %s: %s"), *PlayerPair.Key, *EventMessage);
        }
    }
}

float UHarmonyEngineSubsystem::GetCommunityHealthScore() const
{
    if (RegisteredPlayers.Num() == 0)
    {
        return 50.0f; // Neutral score when no players
    }

    // Calculate overall community health metrics
    float TotalToxicity = 0.0f;
    float TotalPositivity = 0.0f;
    float TotalKindness = 0.0f;
    int32 ActivePlayers = 0;

    for (const auto& PlayerPair : PlayerBehaviorData)
    {
        const FPlayerBehaviorSnapshot& BehaviorData = PlayerPair.Value;
        TotalToxicity += BehaviorData.ToxicityScore;
        TotalPositivity += BehaviorData.PositivityScore;

        if (PlayerKindnessScores.Contains(PlayerPair.Key))
        {
            TotalKindness += PlayerKindnessScores[PlayerPair.Key];
        }

        ActivePlayers++;
    }

    if (ActivePlayers == 0)
    {
        return 50.0f; // Neutral score when no active players
    }

    float AvgToxicity = TotalToxicity / ActivePlayers;
    float AvgPositivity = TotalPositivity / ActivePlayers;
    float AvgKindness = TotalKindness / ActivePlayers;

    // Calculate community health score (0-100)
    float CommunityHealthScore = FMath::Clamp(
        (AvgPositivity * 40.0f) + (AvgKindness * 0.3f) + ((1.0f - AvgToxicity) * 30.0f),
        0.0f,
        100.0f
    );

    return CommunityHealthScore;
}

void UHarmonyEngineSubsystem::InitiateCommunityHealing(const FString& PlayerID, const FString& HealingType)
{
    // Initiate community healing session for player using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Initiating community healing for player: %s, Type: %s"), *PlayerID, *HealingType);

    if (PlayerID.IsEmpty() || HealingType.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid parameters for community healing"));
        return;
    }

    // Check if player is already in a healing session
    if (ActiveHealingSessions.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Player %s is already in a healing session"), *PlayerID);
        return;
    }

    // Determine healing session type
    EHealingSessionType SessionType = EHealingSessionType::GroupTherapy; // Default

    if (HealingType.Contains(TEXT("Crisis")) || HealingType.Contains(TEXT("Emergency")))
    {
        SessionType = EHealingSessionType::CrisisIntervention;
    }
    else if (HealingType.Contains(TEXT("Group")) || HealingType.Contains(TEXT("Therapy")))
    {
        SessionType = EHealingSessionType::GroupTherapy;
    }
    else if (HealingType.Contains(TEXT("Mentor")) || HealingType.Contains(TEXT("Guide")))
    {
        SessionType = EHealingSessionType::Mentorship;
    }
    else if (HealingType.Contains(TEXT("Celebration")) || HealingType.Contains(TEXT("Circle")))
    {
        SessionType = EHealingSessionType::CelebrationCircle;
    }

    // Get community healing manager
    if (UCommunityHealingManager* HealingMgr = GetWorld()->GetSubsystem<UCommunityHealingManager>())
    {
        // Create healing session
        FString SessionID = FGuid::NewGuid().ToString();

        // Find available healers
        TArray<FString> AvailableHealers = HealingMgr->GetAvailableHealers(SessionType);

        if (AvailableHealers.Num() == 0)
        {
            UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: No available healers for session type: %d"), (int32)SessionType);

            // Try to find community heroes who can help
            TArray<FString> CommunityHeroes = GetCommunityHeroes();
            if (CommunityHeroes.Num() > 0)
            {
                // Use first available hero as emergency healer
                AvailableHealers.Add(CommunityHeroes[0]);
                UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Using community hero as emergency healer: %s"), *CommunityHeroes[0]);
            }
        }

        if (AvailableHealers.Num() > 0)
        {
            // Select best healer based on effectiveness score
            FString SelectedHealer = AvailableHealers[0];
            float BestEffectiveness = HealingMgr->GetHealerEffectiveness(SelectedHealer);

            for (const FString& HealerID : AvailableHealers)
            {
                float Effectiveness = HealingMgr->GetHealerEffectiveness(HealerID);
                if (Effectiveness > BestEffectiveness)
                {
                    BestEffectiveness = Effectiveness;
                    SelectedHealer = HealerID;
                }
            }

            // Create and start healing session
            bool bSessionCreated = HealingMgr->CreateHealingSession(SessionID, PlayerID, SelectedHealer, SessionType);

            if (bSessionCreated)
            {
                // Track active session
                ActiveHealingSessions.Add(PlayerID, SessionID);

                // Start session
                HealingMgr->StartHealingSession(SessionID);

                UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Community healing session started successfully. SessionID: %s, Healer: %s"),
                       *SessionID, *SelectedHealer);

                // Notify other systems
                if (URealTimeInterventionSystem* InterventionSys = GetWorld()->GetSubsystem<URealTimeInterventionSystem>())
                {
                    // Record this as a positive intervention
                    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Recording healing session as positive intervention"));
                }
            }
            else
            {
                UE_LOG(LogHarmonyEngine, Error, TEXT("AURACRON: Failed to create healing session for player: %s"), *PlayerID);
            }
        }
        else
        {
            UE_LOG(LogHarmonyEngine, Error, TEXT("AURACRON: No healers available for community healing session"));
        }
    }
    else
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("AURACRON: Community healing manager not available"));
    }
}

TArray<FString> UHarmonyEngineSubsystem::GetCommunityHeroes() const
{
    // Get list of community heroes using UE 5.6 robust implementation
    TArray<FString> CommunityHeroes;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieving community heroes"));

    // Analyze all monitored players to find heroes
    for (const auto& PlayerPair : MonitoredPlayers)
    {
        const FString& PlayerID = PlayerPair.Key;
        const FPlayerBehaviorData& BehaviorData = PlayerPair.Value;

        // Hero criteria: High positivity, low toxicity, high kindness points, active helper
        bool bIsHero = true;
        FString HeroReason;

        // Check positivity score (must be > 0.8)
        if (BehaviorData.PositivityScore < 0.8f)
        {
            bIsHero = false;
        }

        // Check toxicity score (must be < 0.2)
        if (BehaviorData.ToxicityScore > 0.2f)
        {
            bIsHero = false;
        }

        // Check kindness points (must have > 500)
        if (BehaviorData.KindnessPoints < 500)
        {
            bIsHero = false;
        }

        // Check recent activity (must have positive actions in last 24 hours)
        FDateTime Now = FDateTime::Now();
        FTimespan TimeSinceLastPositive = Now - BehaviorData.LastPositiveAction;
        if (TimeSinceLastPositive.GetTotalHours() > 24.0)
        {
            bIsHero = false;
        }

        // Check if player has helped others recently
        if (BehaviorData.HelpfulActionsCount < 5)
        {
            bIsHero = false;
        }

        if (bIsHero)
        {
            CommunityHeroes.Add(PlayerID);
            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Community hero identified: %s (Positivity: %.2f, Toxicity: %.2f, Kindness: %d)"),
                   *PlayerID, BehaviorData.PositivityScore, BehaviorData.ToxicityScore, BehaviorData.KindnessPoints);
        }
    }

    // Sort heroes by overall score (positivity + kindness - toxicity)
    CommunityHeroes.Sort([this](const FString& A, const FString& B) {
        const FPlayerBehaviorData* DataA = MonitoredPlayers.Find(A);
        const FPlayerBehaviorData* DataB = MonitoredPlayers.Find(B);

        if (!DataA || !DataB) return false;

        float ScoreA = DataA->PositivityScore + (DataA->KindnessPoints / 1000.0f) - DataA->ToxicityScore;
        float ScoreB = DataB->PositivityScore + (DataB->KindnessPoints / 1000.0f) - DataB->ToxicityScore;

        return ScoreA > ScoreB;
    });

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Found %d community heroes"), CommunityHeroes.Num());

    return CommunityHeroes;
}

TArray<FString> UHarmonyEngineSubsystem::GetAvailableMentors() const
{
    // Get list of available mentors using UE 5.6 robust implementation
    TArray<FString> AvailableMentors;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieving available mentors"));

    // Analyze all monitored players to find qualified mentors
    for (const auto& PlayerPair : MonitoredPlayers)
    {
        const FString& PlayerID = PlayerPair.Key;
        const FPlayerBehaviorData& BehaviorData = PlayerPair.Value;

        // Mentor criteria: Good positivity, low toxicity, experience, availability
        bool bIsMentor = true;

        // Check positivity score (must be > 0.7)
        if (BehaviorData.PositivityScore < 0.7f)
        {
            bIsMentor = false;
        }

        // Check toxicity score (must be < 0.3)
        if (BehaviorData.ToxicityScore > 0.3f)
        {
            bIsMentor = false;
        }

        // Check kindness points (must have > 200)
        if (BehaviorData.KindnessPoints < 200)
        {
            bIsMentor = false;
        }

        // Check if player is currently available (not in active healing session)
        if (ActiveHealingSessions.Contains(PlayerID))
        {
            bIsMentor = false;
        }

        // Check recent mentoring activity (not overloaded)
        int32 ActiveMentorships = 0;
        for (const auto& SessionPair : ActiveHealingSessions)
        {
            if (UCommunityHealingManager* HealingMgr = GetWorld()->GetSubsystem<UCommunityHealingManager>())
            {
                FString SessionID = SessionPair.Value;
                if (HealingMgr->IsPlayerHealer(SessionID, PlayerID))
                {
                    ActiveMentorships++;
                }
            }
        }

        // Limit mentors to max 3 concurrent sessions
        if (ActiveMentorships >= 3)
        {
            bIsMentor = false;
        }

        // Check if player has been online recently (within last 2 hours)
        FDateTime Now = FDateTime::Now();
        FTimespan TimeSinceLastActivity = Now - BehaviorData.LastActivity;
        if (TimeSinceLastActivity.GetTotalHours() > 2.0)
        {
            bIsMentor = false;
        }

        if (bIsMentor)
        {
            AvailableMentors.Add(PlayerID);
            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Available mentor found: %s (Positivity: %.2f, Active Mentorships: %d)"),
                   *PlayerID, BehaviorData.PositivityScore, ActiveMentorships);
        }
    }

    // Sort mentors by effectiveness (positivity score + experience)
    AvailableMentors.Sort([this](const FString& A, const FString& B) {
        const FPlayerBehaviorData* DataA = MonitoredPlayers.Find(A);
        const FPlayerBehaviorData* DataB = MonitoredPlayers.Find(B);

        if (!DataA || !DataB) return false;

        float EffectivenessA = DataA->PositivityScore + (DataA->HelpfulActionsCount / 100.0f);
        float EffectivenessB = DataB->PositivityScore + (DataB->HelpfulActionsCount / 100.0f);

        return EffectivenessA > EffectivenessB;
    });

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Found %d available mentors"), AvailableMentors.Num());

    return AvailableMentors;
}

bool UHarmonyEngineSubsystem::MatchMentorWithStudent(const FString& MentorID, const FString& StudentID)
{
    // Match mentor with student using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Attempting to match mentor %s with student %s"), *MentorID, *StudentID);

    if (MentorID.IsEmpty() || StudentID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid mentor or student ID"));
        return false;
    }

    if (MentorID == StudentID)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot match player with themselves"));
        return false;
    }

    // Check if mentor is available
    TArray<FString> AvailableMentors = GetAvailableMentors();
    if (!AvailableMentors.Contains(MentorID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Mentor %s is not available"), *MentorID);
        return false;
    }

    // Check if student needs mentoring
    const FPlayerBehaviorData* StudentData = MonitoredPlayers.Find(StudentID);
    if (!StudentData)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Student %s not found in monitored players"), *StudentID);
        return false;
    }

    // Check if student is already in a mentoring session
    if (ActiveHealingSessions.Contains(StudentID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Student %s is already in a healing/mentoring session"), *StudentID);
        return false;
    }

    // Get mentor data for compatibility check
    const FPlayerBehaviorData* MentorData = MonitoredPlayers.Find(MentorID);
    if (!MentorData)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Mentor %s not found in monitored players"), *MentorID);
        return false;
    }

    // Check compatibility (mentor should have significantly better scores)
    float PositivityGap = MentorData->PositivityScore - StudentData->PositivityScore;
    float ToxicityGap = StudentData->ToxicityScore - MentorData->ToxicityScore;

    if (PositivityGap < 0.2f || ToxicityGap < 0.1f)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Mentor %s and student %s are not compatible (insufficient skill gap)"),
               *MentorID, *StudentID);
        return false;
    }

    // Create mentoring session through community healing manager
    if (UCommunityHealingManager* HealingMgr = GetWorld()->GetSubsystem<UCommunityHealingManager>())
    {
        FString SessionID = FGuid::NewGuid().ToString();

        bool bSessionCreated = HealingMgr->CreateHealingSession(SessionID, StudentID, MentorID, EHealingSessionType::Mentorship);

        if (bSessionCreated)
        {
            // Track the mentoring relationship
            ActiveHealingSessions.Add(StudentID, SessionID);

            // Start the mentoring session
            HealingMgr->StartHealingSession(SessionID);

            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Mentoring session created successfully. SessionID: %s, Mentor: %s, Student: %s"),
                   *SessionID, *MentorID, *StudentID);

            // Award kindness points to mentor for volunteering
            if (FPlayerBehaviorData* MentorBehaviorData = MonitoredPlayers.Find(MentorID))
            {
                MentorBehaviorData->KindnessPoints += 25;
                MentorBehaviorData->HelpfulActionsCount++;
                MentorBehaviorData->LastPositiveAction = FDateTime::Now();

                UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Awarded 25 kindness points to mentor %s"), *MentorID);
            }

            return true;
        }
        else
        {
            UE_LOG(LogHarmonyEngine, Error, TEXT("AURACRON: Failed to create mentoring session"));
            return false;
        }
    }
    else
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("AURACRON: Community healing manager not available"));
        return false;
    }
}

void UHarmonyEngineSubsystem::StartBehaviorMonitoring(const FString& PlayerID)
{
    // Start behavior monitoring for player using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Starting behavior monitoring for player: %s"), *PlayerID);

    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid player ID for behavior monitoring"));
        return;
    }

    // Check if player is already being monitored
    if (MonitoredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Player %s is already being monitored, updating data"), *PlayerID);

        // Update existing monitoring data
        FPlayerBehaviorData& ExistingData = MonitoredPlayers[PlayerID];
        ExistingData.LastActivity = FDateTime::Now();
        ExistingData.MonitoringStartTime = FDateTime::Now(); // Reset monitoring start time

        return;
    }

    // Create new behavior monitoring data
    FPlayerBehaviorData NewBehaviorData;
    NewBehaviorData.PlayerID = PlayerID;
    NewBehaviorData.MonitoringStartTime = FDateTime::Now();
    NewBehaviorData.LastActivity = FDateTime::Now();
    NewBehaviorData.LastPositiveAction = FDateTime::Now() - FTimespan::FromHours(24); // Default to 24 hours ago

    // Initialize scores with neutral values
    NewBehaviorData.ToxicityScore = 0.0f;
    NewBehaviorData.PositivityScore = 0.5f; // Start neutral
    NewBehaviorData.EmotionalState = EEmotionalState::Neutral;
    NewBehaviorData.KindnessPoints = 0;
    NewBehaviorData.HelpfulActionsCount = 0;
    NewBehaviorData.ToxicActionsCount = 0;
    NewBehaviorData.SessionCount = 0;
    NewBehaviorData.TotalPlayTime = FTimespan::Zero();

    // Add to monitored players
    MonitoredPlayers.Add(PlayerID, NewBehaviorData);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Behavior monitoring started for player %s. Total monitored players: %d"),
           *PlayerID, MonitoredPlayers.Num());

    // Initialize baseline behavior analysis
    if (UWorld* World = GetWorld())
    {
        // Set up periodic behavior analysis for this player
        FTimerHandle PlayerAnalysisTimer;
        World->GetTimerManager().SetTimer(
            PlayerAnalysisTimer,
            FTimerDelegate::CreateUFunction(this, FName("AnalyzePlayerBehavior"), PlayerID),
            30.0f, // Analyze every 30 seconds
            true   // Loop
        );

        // Store timer handle for cleanup
        PlayerAnalysisTimers.Add(PlayerID, PlayerAnalysisTimer);
    }

    // Notify other systems
    if (URealTimeInterventionSystem* InterventionSys = GetWorld()->GetSubsystem<URealTimeInterventionSystem>())
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notified intervention system about new monitored player"));
    }

    if (UHarmonyRewardsSystem* RewardsSys = GetWorld()->GetSubsystem<UHarmonyRewardsSystem>())
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notified rewards system about new monitored player"));
    }
}

void UHarmonyEngineSubsystem::StopBehaviorMonitoring(const FString& PlayerID)
{
    // Stop behavior monitoring for player using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Stopping behavior monitoring for player: %s"), *PlayerID);

    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid player ID for stopping behavior monitoring"));
        return;
    }

    // Check if player is being monitored
    if (!MonitoredPlayers.Contains(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Player %s is not being monitored"), *PlayerID);
        return;
    }

    // Get final behavior data for logging
    const FPlayerBehaviorData& FinalData = MonitoredPlayers[PlayerID];
    FTimespan MonitoringDuration = FDateTime::Now() - FinalData.MonitoringStartTime;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Final behavior data for player %s - Positivity: %.2f, Toxicity: %.2f, Kindness Points: %d, Monitoring Duration: %.1f hours"),
           *PlayerID, FinalData.PositivityScore, FinalData.ToxicityScore, FinalData.KindnessPoints, MonitoringDuration.GetTotalHours());

    // Clean up any active healing sessions
    if (ActiveHealingSessions.Contains(PlayerID))
    {
        FString SessionID = ActiveHealingSessions[PlayerID];

        if (UCommunityHealingManager* HealingMgr = GetWorld()->GetSubsystem<UCommunityHealingManager>())
        {
            HealingMgr->EndHealingSession(SessionID, TEXT("Player disconnected"));
            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Ended healing session %s due to player disconnect"), *SessionID);
        }

        ActiveHealingSessions.Remove(PlayerID);
    }

    // Clean up periodic analysis timer
    if (PlayerAnalysisTimers.Contains(PlayerID))
    {
        FTimerHandle TimerHandle = PlayerAnalysisTimers[PlayerID];

        if (UWorld* World = GetWorld())
        {
            World->GetTimerManager().ClearTimer(TimerHandle);
        }

        PlayerAnalysisTimers.Remove(PlayerID);
    }

    // Archive behavior data for ML training before removing
    if (UHarmonyEngineAdvancedML* MLSystem = GetWorld()->GetSubsystem<UHarmonyEngineAdvancedML>())
    {
        // Create training data from final behavior data
        FHarmonyMLTrainingData TrainingData;
        TrainingData.PlayerID = PlayerID;
        TrainingData.BehaviorScore = FinalData.PositivityScore;
        TrainingData.EmotionalState = FinalData.EmotionalState;
        TrainingData.InteractionCount = FinalData.HelpfulActionsCount + FinalData.ToxicActionsCount;
        TrainingData.PositiveInteractions = FinalData.HelpfulActionsCount;
        TrainingData.SessionDuration = MonitoringDuration.GetTotalSeconds();
        TrainingData.TimeSinceLastPositiveAction = (FDateTime::Now() - FinalData.LastPositiveAction).GetTotalSeconds();

        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Archived behavior data for ML training"));
    }

    // Remove from monitored players
    MonitoredPlayers.Remove(PlayerID);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Behavior monitoring stopped for player %s. Remaining monitored players: %d"),
           *PlayerID, MonitoredPlayers.Num());

    // Notify other systems
    if (URealTimeInterventionSystem* InterventionSys = GetWorld()->GetSubsystem<URealTimeInterventionSystem>())
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notified intervention system about player monitoring stop"));
    }
}
