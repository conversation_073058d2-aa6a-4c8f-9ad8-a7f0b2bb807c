/**
 * AuracronMasterOrchestrator.Build.cs
 * 
 * Build configuration for Auracron Master Orchestrator module.
 * Coordinates all Auracron bridges and subsystems for seamless integration.
 * 
 * Uses UE 5.6 modern build system for production-ready compilation.
 */

using UnrealBuildTool;

public class AuracronMasterOrchestrator : ModuleRules
{
    public AuracronMasterOrchestrator(ReadOnlyTargetRules Target) : base(Target)
    {
        // UE 5.6 PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable modern C++ features
        CppStandard = CppStandardVersion.Cpp20;
        bUseUnity = true;
        
        // Core dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities",
            "UMG",
            "Slate",
            "SlateCore",
            "InputCore",
            "EnhancedInput",
            "NetCore",
            "OnlineSubsystem",
            "OnlineSubsystemUtils",
            "Sockets",
            "Networking",
            "Json",
            "JsonUtilities",
            "HTTP",
            "AudioMixer",
            "AudioExtensions",
            "MetasoundEngine",
            "MetasoundFrontend",
            "MetasoundStandardNodes",
            "Niagara",
            "NiagaraCore",
            "NiagaraShader",
            "RenderCore",
            "RHI",
            "Renderer",
            "DeveloperSettings",
            "ToolMenus",
            "EngineSettings",
            "ApplicationCore",
            "ToolWidgets",
            "AssetRegistry",
            "Projects",
            "DesktopPlatform",
            "LauncherPlatform",
            "LocalizationService",
            "TranslationEditor",
            "Localization",
            "PacketHandler",
            "ReliabilityHandlerComponent",
            "Analytics",
            "AnalyticsET",
            "PerfCounters",
            "TraceLog",
            "UdpMessaging",
            "TcpMessaging",
            "MessagingCommon",
            "Messaging",
            "FunctionalTesting",
            "AutomationController",
            "AutomationWorker",
            "AutomationMessages",
            "ImageWrapper",
            "RawMesh",
            "MeshDescription",
            "StaticMeshDescription",
            "SkeletalMeshDescription",
            "MeshConversion",
            "MeshUtilitiesCommon",
            "ProceduralMeshComponent",
            "GeometryCollectionEngine",
            "ChaosSolverEngine",
            "Chaos",
            "ChaosCore",
            "PhysicsCore",
            "FieldSystemEngine",
            "GeometryFramework",
            "DynamicMesh",
            "GeometryAlgorithms",
            "ModelingComponents",
            "InteractiveToolsFramework",

            "OnlineSubsystemSteam",
            "Steamworks",
            "OnlineSubsystemEOS",
            "EOSShared",
            "EOSSDK",
            "OnlineServicesEOS",
            "OnlineServicesEOSGS",
            "OnlineServicesCommon",
            "OnlineServicesInterface",
            "OnlineBase"
        });

        // Auracron bridge dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "AuracronDynamicRealmBridge",
            "AuracronHarmonyEngineBridge", 
            "AuracronSigilosBridge",
            "AuracronPCGBridge",
            "AuracronNexusCommunityBridge",
            "AuracronLivingWorldBridge",
            "AuracronAdaptiveEngagementBridge",
            "AuracronQuantumConsciousnessBridge",
            "AuracronIntelligentDocumentationBridge",
            "AuracronNetworkingBridge",
            "AuracronAnalyticsBridge",
            "AuracronUIBridge",
            "AuracronAudioBridge",
            "AuracronVFXBridge",
            "AuracronTutorialBridge",
            "AuracronAbismoUmbrioBridge",
            "AuracronHarmonyEngineBridge",
            "AuracronSigilosBridge"
        });

        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "Settings",
            "RenderCore",
            "ApplicationCore",
            "PreLoadScreen",
            "MoviePlayer",
            "HeadMountedDisplay",
            "AugmentedReality",
            "MobilePatchingUtils",
            "BuildPatchServices",
            "PakFile",
            "SandboxFile",
            "StreamingPauseRendering"
        });

        // Platform-specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "AudioMixerXAudio2"
            });
        }

        // Development and editor dependencies
        if (Target.bBuildDeveloperTools)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "PlatformCryptoTypes",
                "PlatformCrypto",
                "DesktopPlatform",
                "LauncherPlatform",
                "LocalizationService",

                "SessionServices",
                "TraceLog"
            });
        }

        // Editor-specific dependencies
        if (Target.Type == TargetType.Editor)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "EditorStyle",
                "EditorWidgets",
                "UnrealEd",
                "PropertyEditor",
                "GraphEditor",
                "KismetCompiler",
                "BlueprintGraph",
                "KismetWidgets",
                "DetailCustomizations",
                "ComponentVisualizers",
                "StatusBar",
                "MainFrame",
                "LevelEditor",
                "SceneOutliner",
                "ContentBrowser",
                "AssetTools",
                "EditorSubsystem",
                "UnrealEdMessages",
                "SourceControl",
                "SharedSettingsWidgets",
                "AddContentDialog",
                "GameProjectGeneration",
                "HardwareTargeting",
                "InternationalizationSettings",
                "MultiUserClient"
            });

            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "MessageLog",
                "CollisionAnalyzer",
                "LogVisualizer",
                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "ScreenShotComparison",
                "AutomationWindow",
                "MeshUtilities",
                "ModelingComponentsEditorOnly",
                "EditorInteractiveToolsFramework",
                "ConcertSyncCore",
                "ConcertSyncClient",
                "ConcertSyncServer",
                "ConcertSharedSlate",
                "ConcertTransport",
                "Concert",
                "SerializedRecorderInterface"
            });
        }

        // Optimization settings for production builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_OPTIMIZED=1");
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_DEVELOPMENT=1");
        }

        // Enable advanced features
        PublicDefinitions.Add("WITH_AURACRON_MASTER_ORCHESTRATOR=1");
        PublicDefinitions.Add("WITH_BRIDGE_COORDINATION=1");
        PublicDefinitions.Add("WITH_SYSTEM_HEALTH_MONITORING=1");
        PublicDefinitions.Add("WITH_PERFORMANCE_OPTIMIZATION=1");
        PublicDefinitions.Add("WITH_ERROR_RECOVERY=1");
        PublicDefinitions.Add("WITH_QUALITY_ASSURANCE=1");

        // Version information
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_PATCH=0");
    }
}
