# Blueprint Example: Auracron Sigil System Usage

## BP_SigilSystemExample

This Blueprint demonstrates how to properly implement and use the Auracron Sigil System (Fusion 2.0) in your game.

### Blueprint Setup

#### Components Required
1. **Auracron Sigilos Bridge Component**
   - Add to your Player Character or Pawn
   - Configure in the Details panel

2. **Ability System Component**
   - Required for GameplayAbility integration
   - Must be initialized before Sigil System

3. **Enhanced Input Component**
   - For handling sigil activation inputs
   - Configure with Sigil Input Mapping Context

### Basic Implementation

#### 1. Character Setup (BeginPlay)

```blueprint
Event BeginPlay
├── Get Auracron Sigilos Bridge Component
├── Initialize Ability System Component
├── Load Default Sigil Configurations
├── Setup Input Mapping Context
└── Bind Input Events
```

#### 2. Sigil Equipment

```blueprint
Function: EquipSigilLoadout
├── Input: Aegis Type (EAuracronAegisSigilType)
├── Input: Ruin Type (EAuracronRuinSigilType)
├── Input: Vesper Type (EAuracronVesperSigilType)
├── Call: Equip Aegis Sigil
├── Call: Equip Ruin Sigil
├── Call: Equip Vesper Sigil
└── Output: Success (Boolean)
```

#### 3. Individual Sigil Activation

```blueprint
Input Action: Aegis Sigil (Q Key)
├── Check: Can Activate Aegis
├── Call: Activate Aegis Sigil
├── Play Activation Feedback
└── Update UI

Input Action: Ruin Sigil (W Key)
├── Check: Can Activate Ruin
├── Call: Activate Ruin Sigil
├── Play Activation Feedback
└── Update UI

Input Action: Vesper Sigil (E Key)
├── Check: Can Activate Vesper
├── Call: Activate Vesper Sigil
├── Play Activation Feedback
└── Update UI
```

#### 4. Fusion 2.0 Activation

```blueprint
Input Action: Fusion 2.0 (R Key - Hold)
├── Check: Can Activate Fusion 2.0
├── Start Charging Animation (1 second)
├── On Hold Complete:
│   ├── Call: Activate Fusion 2.0
│   ├── Get Current Archetype
│   ├── Display Archetype UI
│   ├── Play Fusion Effects
│   └── Start Fusion Timer
└── On Hold Cancelled:
    └── Cancel Charging Animation
```

### Advanced Features

#### 5. Archetype System

```blueprint
Function: UpdateArchetypeDisplay
├── Get Current Archetype
├── Check: Is Valid Archetype
├── Update Archetype Name UI
├── Update Archetype Description UI
├── Update Archetype Colors
├── Apply Archetype Visual Effects
└── Broadcast Archetype Changed Event
```

#### 6. Sigil Progression

```blueprint
Event: On Sigil Experience Gained
├── Input: Sigil Type
├── Input: Experience Amount
├── Input: New Level
├── Update Sigil Level UI
├── Check: Level Up Effects
├── Play Level Up Animation
└── Save Progression Data
```

#### 7. UI Integration

```blueprint
Widget: SigilStatusBar
├── Aegis Sigil Icon
│   ├── Cooldown Overlay
│   ├── Level Display
│   └── Experience Bar
├── Ruin Sigil Icon
│   ├── Cooldown Overlay
│   ├── Level Display
│   └── Experience Bar
├── Vesper Sigil Icon
│   ├── Cooldown Overlay
│   ├── Level Display
│   └── Experience Bar
└── Fusion Button
    ├── Charge Progress
    ├── Cooldown Timer
    └── Archetype Preview
```

### Event Handling

#### 8. Sigil Events

```blueprint
Event: On Sigil Equipped
├── Input: Sigil Type
├── Input: Subtype Index
├── Input: Level
├── Update Equipment UI
├── Play Equipment Sound
├── Check Archetype Formation
└── Update Archetype Display

Event: On Sigil Activated
├── Input: Sigil Type
├── Input: Duration
├── Start Cooldown Timer
├── Play Activation Effects
├── Update UI State
└── Log Activation

Event: On Archetype Formed
├── Input: Archetype Data
├── Display Archetype Name
├── Show Archetype Description
├── Apply Archetype Colors
├── Play Formation Animation
└── Update Fusion Button State

Event: On Fusion 2.0 Activated
├── Input: Archetype Data
├── Input: Duration
├── Start Fusion Timer
├── Apply Fusion Visual Effects
├── Play Fusion Audio
├── Update All UI Elements
└── Broadcast to Team

Event: On Fusion 2.0 Ended
├── Input: Archetype Data
├── Stop Fusion Effects
├── Start Cooldown Timers
├── Reset UI State
├── Play End Effects
└── Log Fusion End
```

### Configuration Examples

#### 9. Sigil Loadout Presets

```blueprint
Struct: SigilLoadoutPreset
├── Name: "Guardian Build"
├── Aegis: Aegis.Absoluto
├── Ruin: Ruin.Flamejante
├── Vesper: Vesper.Curativo
└── Description: "Tank build with healing support"

Struct: SigilLoadoutPreset
├── Name: "Assassin Build"
├── Aegis: Aegis.Espectral
├── Ruin: Ruin.Sombrio
├── Vesper: Vesper.Teleporte
└── Description: "Stealth build with mobility"

Struct: SigilLoadoutPreset
├── Name: "Destroyer Build"
├── Aegis: Aegis.Temporal
├── Ruin: Ruin.Aniquilador
├── Vesper: Vesper.Temporal
└── Description: "Maximum damage with time control"
```

### Performance Considerations

#### 10. Optimization Techniques

```blueprint
Function: OptimizeSigilEffects
├── Check: Player Distance
├── Adjust: Effect Quality
├── Manage: Particle Count
├── Control: Update Frequency
└── Pool: Unused Effects

Function: ManageNetworkTraffic
├── Batch: State Updates
├── Compress: Replication Data
├── Predict: Client Effects
└── Validate: Server Authority
```

### Error Handling

#### 11. Validation and Safety

```blueprint
Function: ValidateSigilActivation
├── Check: System Initialized
├── Check: Sigil Equipped
├── Check: Not On Cooldown
├── Check: Sufficient Energy
├── Check: Valid Target
└── Return: Can Activate

Function: HandleActivationFailure
├── Input: Failure Reason
├── Display: Error Message
├── Play: Error Sound
├── Log: Failure Details
└── Update: UI State
```

### Testing Framework

#### 12. Automated Testing

```blueprint
Function: TestSigilCombination
├── Input: Aegis Type
├── Input: Ruin Type
├── Input: Vesper Type
├── Equip: All Sigils
├── Test: Individual Activation
├── Test: Fusion Activation
├── Validate: Expected Effects
├── Measure: Performance
└── Log: Test Results
```

## Best Practices

### Development Guidelines

1. **Always validate inputs** before calling sigil methods
2. **Handle network replication** properly for multiplayer
3. **Use object pooling** for frequently spawned effects
4. **Implement proper error handling** for all operations
5. **Test all 150 combinations** thoroughly
6. **Monitor performance** during development
7. **Follow UE 5.6 best practices** for all implementations

### Common Pitfalls

1. **Not checking AbilitySystemComponent** before use
2. **Forgetting to handle replication** in multiplayer
3. **Not managing effect lifetimes** properly
4. **Ignoring performance implications** of complex combinations
5. **Not validating sigil combinations** before activation

### Debugging Tips

1. Use **Sigil Debug UI** for real-time monitoring
2. Enable **detailed logging** for troubleshooting
3. Check **GameplayTag states** for ability issues
4. Monitor **network replication** in multiplayer
5. Profile **performance impact** of effects

---

This Blueprint example provides a comprehensive foundation for implementing the Auracron Sigil System in your project. Customize and extend as needed for your specific gameplay requirements.
