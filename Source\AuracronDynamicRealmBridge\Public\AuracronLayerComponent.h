#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronLayerComponent.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class UAuracronRealmTransitionComponent;

/**
 * Layer interaction data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronLayerInteraction
{
    GENERATED_BODY()

    /** Can this actor interact with other layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Interaction")
    bool bCanInteractCrossLayer;

    /** Layers this actor can affect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Interaction")
    TArray<EAuracronRealmLayer> AffectableLayers;

    /** Damage multiplier when affecting other layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Interaction")
    float CrossLayerDamageMultiplier;

    /** Range multiplier when affecting other layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Interaction")
    float CrossLayerRangeMultiplier;

    /** Effect intensity when affecting other layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Interaction")
    float CrossLayerEffectIntensity;

    FAuracronLayerInteraction()
    {
        bCanInteractCrossLayer = false;
        CrossLayerDamageMultiplier = 0.5f;
        CrossLayerRangeMultiplier = 0.8f;
        CrossLayerEffectIntensity = 0.6f;
    }
};

/**
 * Layer movement data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronLayerMovement
{
    GENERATED_BODY()

    /** Can this actor move between layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    bool bCanTransitionLayers;

    /** Allowed destination layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    TArray<EAuracronRealmLayer> AllowedLayers;

    /** Transition speed modifier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    float TransitionSpeedMultiplier;

    /** Energy cost for transitions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    float TransitionEnergyCost;

    /** Cooldown between transitions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    float TransitionCooldown;

    /** Last transition time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Movement")
    float LastTransitionTime;

    FAuracronLayerMovement()
    {
        bCanTransitionLayers = true;
        TransitionSpeedMultiplier = 1.0f;
        TransitionEnergyCost = 10.0f;
        TransitionCooldown = 5.0f;
        LastTransitionTime = 0.0f;
    }
};

/**
 * Layer-specific bonuses and penalties
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronLayerModifiers
{
    GENERATED_BODY()

    /** Movement speed modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float MovementSpeedMultiplier;

    /** Damage modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float DamageMultiplier;

    /** Defense modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float DefenseMultiplier;

    /** Ability range modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float AbilityRangeMultiplier;

    /** Ability cooldown modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float AbilityCooldownMultiplier;

    /** Visibility modifier in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float VisibilityMultiplier;

    /** Stealth bonus in this layer */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    float StealthBonus;

    FAuracronLayerModifiers()
    {
        MovementSpeedMultiplier = 1.0f;
        DamageMultiplier = 1.0f;
        DefenseMultiplier = 1.0f;
        AbilityRangeMultiplier = 1.0f;
        AbilityCooldownMultiplier = 1.0f;
        VisibilityMultiplier = 1.0f;
        StealthBonus = 0.0f;
    }
};

/**
 * Auracron Layer Component
 * 
 * Component that manages an actor's relationship with the dynamic realm system:
 * - Tracks which layer the actor belongs to
 * - Handles layer transitions
 * - Applies layer-specific modifiers
 * - Manages cross-layer interactions
 * - Optimizes rendering based on layer visibility
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Auracron), meta=(BlueprintSpawnableComponent))
class AURACRONDYNAMICREALMBRIDGE_API UAuracronLayerComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronLayerComponent();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
    // Layer management
    UFUNCTION(BlueprintCallable, Category = "Layer Management")
    void SetCurrentLayer(EAuracronRealmLayer NewLayer);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Layer Management")
    EAuracronRealmLayer GetCurrentLayer() const { return CurrentLayer; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Layer Management")
    bool IsInLayer(EAuracronRealmLayer Layer) const { return CurrentLayer == Layer; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Layer Management")
    bool CanTransitionToLayer(EAuracronRealmLayer TargetLayer) const;

    // Transition management
    UFUNCTION(BlueprintCallable, Category = "Transitions")
    bool RequestLayerTransition(EAuracronRealmLayer TargetLayer, ERealmTransitionType TransitionType = ERealmTransitionType::Gradual);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Transitions")
    bool IsInTransition() const { return bIsTransitioning; }

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Transitions")
    float GetTransitionProgress() const { return TransitionProgress; }

    UFUNCTION(BlueprintCallable, Category = "Transitions")
    void CancelTransition();

    // Layer modifiers
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Layer Modifiers")
    FAuracronLayerModifiers GetCurrentLayerModifiers() const;

    UFUNCTION(BlueprintCallable, Category = "Layer Modifiers")
    void ApplyLayerModifiers();

    UFUNCTION(BlueprintCallable, Category = "Layer Modifiers")
    void RemoveLayerModifiers();

    // Cross-layer interactions
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Cross-Layer")
    bool CanAffectLayer(EAuracronRealmLayer TargetLayer) const;

    UFUNCTION(BlueprintCallable, Category = "Cross-Layer")
    float CalculateCrossLayerDamage(float BaseDamage, EAuracronRealmLayer TargetLayer) const;

    UFUNCTION(BlueprintCallable, Category = "Cross-Layer")
    float CalculateCrossLayerRange(float BaseRange, EAuracronRealmLayer TargetLayer) const;

    // Visibility and rendering
    UFUNCTION(BlueprintCallable, Category = "Rendering")
    void UpdateLayerVisibility();

    UFUNCTION(BlueprintCallable, Category = "Rendering")
    void SetLayerRenderingEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Rendering")
    bool IsLayerVisible() const { return bIsLayerVisible; }

    // Events
    UFUNCTION(BlueprintImplementableEvent, Category = "Layer Events")
    void OnLayerChanged(EAuracronRealmLayer OldLayer, EAuracronRealmLayer NewLayer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Layer Events")
    void OnTransitionStarted(EAuracronRealmLayer TargetLayer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Layer Events")
    void OnTransitionCompleted(EAuracronRealmLayer NewLayer);

    UFUNCTION(BlueprintImplementableEvent, Category = "Layer Events")
    void OnLayerModifiersApplied(const FAuracronLayerModifiers& Modifiers);

protected:
    // Core layer data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Configuration")
    EAuracronRealmLayer CurrentLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Configuration")
    EAuracronRealmLayer PreviousLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Configuration")
    FAuracronLayerInteraction InteractionSettings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Configuration")
    FAuracronLayerMovement MovementSettings;

    // Layer-specific modifiers
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Modifiers")
    TMap<EAuracronRealmLayer, FAuracronLayerModifiers> LayerModifiers;

    // Transition state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    bool bIsTransitioning;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    EAuracronRealmLayer TransitionTargetLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    ERealmTransitionType CurrentTransitionType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    float TransitionProgress;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    float TransitionDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition State")
    float TransitionStartTime;

    // Rendering state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    bool bIsLayerVisible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    bool bRenderingEnabled;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rendering")
    int32 CurrentLODLevel;

private:
    // Internal functions
    void InitializeLayerModifiers();
    void UpdateTransition(float DeltaTime);
    void CompleteTransition();
    void ApplyTransitionEffects();
    void UpdateLayerSpecificBehavior(float DeltaTime);
    
    // Layer behavior updates
    void UpdateTerrestrialBehavior(float DeltaTime);
    void UpdateCelestialBehavior(float DeltaTime);
    void UpdateAbyssalBehavior(float DeltaTime);
    
    // Utility functions
    UAuracronDynamicRealmSubsystem* GetRealmSubsystem() const;
    bool ValidateTransition(EAuracronRealmLayer TargetLayer) const;
    void LogComponentStatus() const;
    
    // Cached references
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    UPROPERTY()
    TObjectPtr<UAuracronRealmTransitionComponent> TransitionComponent;
};
