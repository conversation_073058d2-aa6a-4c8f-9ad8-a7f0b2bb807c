#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Animation/Skeleton.h"
#include "Materials/Material.h"
#include "Engine/SkeletalMesh.h"
#include "HAL/CriticalSection.h"
#include "AuracronHairSystem.generated.h"

// Forward declarations
class UGroomAsset;
class UGroomComponent;
class UGroomBindingAsset;
class USkeletalMeshComponent;
class AActor;

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronHairSystem, Log, All);

// ========================================
// Hair Generation Parameters
// ========================================

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairSystemGenerationParameters
{
    GENERATED_BODY()

    // Basic hair properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FString HairName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    USkeleton* TargetSkeleton;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    int32 StrandCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float StrandLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float StrandWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float RootScale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float TipScale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float Clumping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    float Roughness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    FLinearColor HairColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Generation")
    UMaterial* HairMaterial;

    // Physics properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    bool bEnablePhysics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    bool bEnableCollision;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float PhysicsStiffness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Physics")
    float PhysicsDamping;

    // Advanced properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Advanced")
    bool bEnableLOD;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Advanced")
    int32 LODCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Advanced")
    bool bEnableOptimizations;

    FHairSystemGenerationParameters()
        : HairName()
        , TargetSkeleton(nullptr)
        , StrandCount(10000)
        , StrandLength(10.0f)
        , StrandWidth(0.02f)
        , RootScale(1.0f)
        , TipScale(0.1f)
        , Clumping(0.5f)
        , Roughness(0.3f)
        , HairColor(FLinearColor::Black)
        , HairMaterial(nullptr)
        , bEnablePhysics(true)
        , bEnableCollision(true)
        , PhysicsStiffness(0.8f)
        , PhysicsDamping(0.1f)
        , bEnableLOD(true)
        , LODCount(3)
        , bEnableOptimizations(true)
    {
    }
};

// ========================================
// Hair Binding Parameters
// ========================================

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairBindingParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Binding")
    FString BindingName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Binding")
    int32 InterpolationPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Binding")
    int32 MatchingSection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Binding")
    bool bUseUniqueGuides;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Binding")
    float BindingQuality;

    FHairBindingParameters()
        : InterpolationPoints(100)
        , MatchingSection(0)
        , bUseUniqueGuides(true)
        , BindingQuality(1.0f)
    {
    }
};

// ========================================
// Hair Attachment Parameters
// ========================================

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FHairAttachmentParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    FString ComponentName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    FName AttachmentSocket;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    bool bVisible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    bool bCastShadows;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    bool bReceiveDecals;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    bool bEnablePhysics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hair Attachment")
    FTransform RelativeTransform;

    FHairAttachmentParameters()
        : ComponentName(TEXT("GroomComponent"))
        , AttachmentSocket(NAME_None)
        , bVisible(true)
        , bCastShadows(true)
        , bReceiveDecals(true)
        , bEnablePhysics(true)
        , RelativeTransform(FTransform::Identity)
    {
    }
};

// ========================================
// Hair System Class
// ========================================

/**
 * Advanced Hair System for MetaHuman integration using UE 5.6 Hair/Groom system
 * Provides robust hair generation, binding, and attachment functionality
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronHairSystem
{
public:
    FAuracronHairSystem();
    ~FAuracronHairSystem();

    // ========================================
    // Core Hair Generation Methods
    // ========================================

    /**
     * Generate a new hair asset using UE 5.6 Groom system
     * @param Parameters Hair generation parameters
     * @return Generated Groom Asset or nullptr if failed
     */
    UGroomAsset* GenerateHairAsset(const FHairSystemGenerationParameters& Parameters);

    /**
     * Create hair binding between Groom Asset and Skeletal Mesh
     * @param GroomAsset Source Groom Asset
     * @param TargetMesh Target Skeletal Mesh
     * @param Parameters Binding parameters
     * @return Created Groom Binding Asset or nullptr if failed
     */
    UGroomBindingAsset* CreateHairBinding(UGroomAsset* GroomAsset, USkeletalMesh* TargetMesh, const FHairBindingParameters& Parameters);

    /**
     * Attach hair to character using UE 5.6 Component system
     * @param Character Target character
     * @param GroomAsset Hair asset to attach
     * @param BindingAsset Optional binding asset
     * @param Parameters Attachment parameters
     * @return Created Groom Component or nullptr if failed
     */
    UGroomComponent* AttachHairToCharacter(AActor* Character, UGroomAsset* GroomAsset, UGroomBindingAsset* BindingAsset, const FHairAttachmentParameters& Parameters);

    // ========================================
    // Utility Methods
    // ========================================

    /**
     * Validate hair generation parameters
     * @param Parameters Parameters to validate
     * @param OutError Error message if validation fails
     * @return True if parameters are valid
     */
    bool ValidateHairGenerationParameters(const FHairSystemGenerationParameters& Parameters, FString& OutError);

    /**
     * Calculate hash for hair generation parameters (for caching)
     * @param Parameters Parameters to hash
     * @return Hash string
     */
    FString CalculateHairGenerationHash(const FHairSystemGenerationParameters& Parameters);

    /**
     * Clear hair cache
     */
    void ClearHairCache();

    // ========================================
    // Statistics and Monitoring
    // ========================================

    /**
     * Get total hair generation time
     * @return Total time in seconds
     */
    float GetTotalHairGenerationTime() const { return TotalHairGenerationTime; }

    /**
     * Get hair cache memory usage
     * @return Memory usage in bytes
     */
    int32 GetHairCacheMemoryUsage() const { return HairCacheMemoryUsage; }

private:
    // ========================================
    // Internal Methods
    // ========================================

    void InitializeHairSystemDefaults();
    bool ConfigureGroomAssetProperties(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters);
    bool GenerateHairStrands(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters);
    bool ApplyHairMaterials(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters);
    bool BuildGroomAsset(UGroomAsset* GroomAsset);

    // ========================================
    // Member Variables
    // ========================================

    // Default parameters
    FHairSystemGenerationParameters DefaultHairParameters;

    // Caching system
    TMap<FString, TWeakObjectPtr<UGroomAsset>> HairCache;
    FCriticalSection HairGenerationMutex;
    int32 HairCacheMemoryUsage;

    // Statistics
    float TotalHairGenerationTime;
};
