{"fusion_20_archetypes": {"total_combinations": 150, "generation_formula": "5 Aegis × 5 Ruin × 6 Vesper = 150 unique archetypes", "archetype_categories": {"guardian": "Defense-focused archetypes with high survivability", "destroyer": "Damage-focused archetypes with high offensive power", "assassin": "Burst damage archetypes with mobility", "healer": "Support archetypes focused on healing and protection", "controller": "Utility archetypes with crowd control", "hybrid": "Balanced archetypes with mixed capabilities"}}, "legendary_archetypes": {"destruidor_absoluto": {"combination": ["Aegis.Absoluto", "<PERSON><PERSON><PERSON>", "Vesper.<PERSON>"], "name": "Destruidor Absoluto", "description": "O arquétipo mais poderoso - invulnerabilidade, dano massivo e manipulação temporal", "category": "Destroyer", "rarity": "Legendary", "required_level": 20, "power_multiplier": 3.0, "cooldown_reduction": 0.5, "energy_efficiency": 2.0, "unique_effects": ["Temporal Invulnerability: 5 seconds of complete immunity", "Annihilation Wave: Massive AoE damage", "Time Freeze: Stops time for 3 seconds"], "fusion_ability": "Apocalypse Protocol", "primary_color": "#8B0000", "secondary_color": "#FFD700"}, "mestre_temporal": {"combination": ["Aegis.Temporal", "<PERSON><PERSON><PERSON>", "Vesper.<PERSON>"], "name": "Me<PERSON><PERSON>", "description": "Controle absoluto sobre o tempo e espaço", "category": "Controller", "rarity": "Legendary", "required_level": 18, "power_multiplier": 2.5, "cooldown_reduction": 0.4, "energy_efficiency": 1.8, "unique_effects": ["Chronos Field: Manipulates time in large area", "Shadow Step: Teleport through shadows", "Temporal Echo: Creates time-delayed duplicates"], "fusion_ability": "Chronos Dominion", "primary_color": "#9370DB", "secondary_color": "#2F2F2F"}, "guardiao_cristalino": {"combination": ["Aegis.Cristalino", "<PERSON><PERSON><PERSON>", "Vesper.Curativo"], "name": "Guardião Cristalino", "description": "Protetor supremo com capacidades de cura e contra-ataque", "category": "Guardian", "rarity": "Epic", "required_level": 15, "power_multiplier": 2.2, "cooldown_reduction": 0.3, "energy_efficiency": 1.6, "unique_effects": ["Crystal Fortress: Reflects all damage for 5 seconds", "Healing Flames: Fire that heals allies", "Prismatic Shield: Multi-layered protection"], "fusion_ability": "Crystal Sanctuary", "primary_color": "#FFD700", "secondary_color": "#00FF7F"}, "assassino_sombrio": {"combination": ["Aegis.Espectral", "<PERSON><PERSON><PERSON>", "Vesper.Teleporte"], "name": "Assassino Sombrio", "description": "Assassino furtivo com capacidades espectrais", "category": "Assassin", "rarity": "Epic", "required_level": 16, "power_multiplier": 2.8, "cooldown_reduction": 0.35, "energy_efficiency": 1.4, "unique_effects": ["Shadow Merge: Become untargetable for 3 seconds", "Spectral Strike: Ignores armor and shields", "Shadow Network: Teleport between shadows"], "fusion_ability": "Umbral Assassination", "primary_color": "#2F2F2F", "secondary_color": "#800080"}}, "epic_archetypes": {"lamina_glacial": {"combination": ["Aegis.Cristalino", "<PERSON><PERSON><PERSON>", "Vesper.Velocidade"], "name": "Lâmina Glacial", "description": "Guerreiro veloz com ataques de gelo e defesas cristalinas", "category": "Hybrid", "rarity": "Epic", "required_level": 12, "power_multiplier": 2.0, "cooldown_reduction": 0.25, "energy_efficiency": 1.5, "unique_effects": ["Frost Rush: High-speed ice attacks", "Crystal Armor: Reflects and slows attackers", "Glacial Trail: Leaves freezing path"], "fusion_ability": "Blizzard Charge", "primary_color": "#00BFFF", "secondary_color": "#FFD700"}, "curandeiro_divino": {"combination": ["Aegis.Absoluto", "<PERSON><PERSON><PERSON>", "Vesper.Curativo"], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Curandeiro supremo com proteção divina e chamas purificadoras", "category": "He<PERSON>r", "rarity": "Epic", "required_level": 14, "power_multiplier": 1.8, "cooldown_reduction": 0.3, "energy_efficiency": 1.7, "unique_effects": ["Divine Sanctuary: Area of complete protection", "Purifying Flames: Fire that heals allies", "Resurrection: Revive fallen allies"], "fusion_ability": "Divine Intervention", "primary_color": "#FFD700", "secondary_color": "#FF4500"}}, "rare_archetypes": {"sentinela_corrosiva": {"combination": ["Aegis.Primordial", "Ruin.<PERSON>", "Vesper.Energetico"], "name": "Sentinela Corrosiva", "description": "Defensor resistente com ataques que enfraquecem inimigos", "category": "Guardian", "rarity": "Rare", "required_level": 10, "power_multiplier": 1.6, "cooldown_reduction": 0.2, "energy_efficiency": 1.3, "unique_effects": ["Corrosive Aura: Weakens nearby enemies", "Energy Shield: Converts mana to shield", "Acid Rain: Area corrosive damage"], "fusion_ability": "Toxic Fortress", "primary_color": "#32CD32", "secondary_color": "#1E90FF"}, "explorador_espectral": {"combination": ["Aegis.Espectral", "<PERSON><PERSON><PERSON>", "Vesper.Visao"], "name": "Explorador Espectral", "description": "Scout especializado em reconhecimento e ataques furtivos", "category": "Assassin", "rarity": "Rare", "required_level": 8, "power_multiplier": 1.5, "cooldown_reduction": 0.15, "energy_efficiency": 1.2, "unique_effects": ["Spectral Vision: See through walls", "Shadow Cloak: Temporary invisibility", "Spirit Walk: Phase through enemies"], "fusion_ability": "Phantom Reconnaissance", "primary_color": "#00FFFF", "secondary_color": "#2F2F2F"}}, "generation_rules": {"naming_algorithm": {"step_1": "Identify dominant element from combination", "step_2": "Determine archetype category based on sigil balance", "step_3": "Apply naming pattern from category", "step_4": "Add elemental modifier", "step_5": "Ensure uniqueness across all 150 combinations"}, "power_calculation": {"base_power": 1.0, "aegis_contribution": 0.3, "ruin_contribution": 0.4, "vesper_contribution": 0.3, "synergy_bonus": 0.25, "rarity_multiplier": {"common": 1.0, "uncommon": 1.2, "rare": 1.5, "epic": 2.0, "legendary": 3.0}}, "effect_inheritance": {"aegis_effects": "Defensive capabilities and damage mitigation", "ruin_effects": "Offensive capabilities and damage dealing", "vesper_effects": "Utility capabilities and support functions", "fusion_effects": "Unique combinations that transcend individual sigil limitations"}}, "balance_matrix": {"archetype_distribution": {"guardian": 30, "destroyer": 25, "assassin": 20, "healer": 25, "controller": 25, "hybrid": 25}, "rarity_distribution": {"common": 60, "uncommon": 45, "rare": 30, "epic": 12, "legendary": 3}, "power_level_distribution": {"tier_1": "Levels 1-5: Basic combinations", "tier_2": "Levels 6-10: Intermediate combinations", "tier_3": "Levels 11-15: Advanced combinations", "tier_4": "Levels 16-20: Master combinations"}}}