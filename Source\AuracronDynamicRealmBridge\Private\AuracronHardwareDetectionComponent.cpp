#include "AuracronHardwareDetectionComponent.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

UAuracronHardwareDetectionComponent::UAuracronHardwareDetectionComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
    
    // Configurações padrão
    bAutoDetectOnBeginPlay = true;
    bAutoApplySettings = true;
    bVerboseLogging = true;
    MinimumQualityLevel = 0;
    MaximumQualityLevel = 4;
    QualityMultiplier = 1.0f;
    bHardwareDetected = false;
    
    // Criar sistema de detecção
    HardwareDetectionSystem = CreateDefaultSubobject<UAuracronSimpleHardwareDetection>(TEXT("HardwareDetectionSystem"));
}

void UAuracronHardwareDetectionComponent::BeginPlay()
{
    Super::BeginPlay();
    
    if (bAutoDetectOnBeginPlay)
    {
        // Executar detecção após um pequeno delay para garantir que tudo esteja inicializado
        FTimerHandle TimerHandle;
        GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &UAuracronHardwareDetectionComponent::RunFullHardwareDetection, 1.0f, false);
    }
}

FSimpleHardwareInfo UAuracronHardwareDetectionComponent::DetectHardware()
{
    if (!HardwareDetectionSystem)
    {
        UE_LOG(LogTemp, Error, TEXT("HardwareDetectionSystem is null"));
        return FSimpleHardwareInfo();
    }
    
    FSimpleHardwareInfo HardwareInfo = HardwareDetectionSystem->DetectHardware();
    LastDetectedHardware = HardwareInfo;
    bHardwareDetected = true;
    
    if (bVerboseLogging)
    {
        LogHardwareInfo(HardwareInfo);
    }
    
    // Disparar evento
    OnHardwareDetected.Broadcast(HardwareInfo);
    
    return HardwareInfo;
}

FSimpleQualitySettings UAuracronHardwareDetectionComponent::CalculateRecommendedSettings(const FSimpleHardwareInfo& HardwareInfo)
{
    if (!HardwareDetectionSystem)
    {
        UE_LOG(LogTemp, Error, TEXT("HardwareDetectionSystem is null"));
        return FSimpleQualitySettings();
    }
    
    FSimpleQualitySettings Settings = HardwareDetectionSystem->CalculateRecommendedSettings(HardwareInfo);
    
    // Aplicar multiplicador de qualidade
    if (QualityMultiplier != 1.0f)
    {
        Settings.OverallQuality = FMath::RoundToInt(Settings.OverallQuality * QualityMultiplier);
        Settings.TextureQuality = FMath::RoundToInt(Settings.TextureQuality * QualityMultiplier);
        Settings.ShadowQuality = FMath::RoundToInt(Settings.ShadowQuality * QualityMultiplier);
        Settings.PostProcessQuality = FMath::RoundToInt(Settings.PostProcessQuality * QualityMultiplier);
        Settings.AntiAliasingQuality = FMath::RoundToInt(Settings.AntiAliasingQuality * QualityMultiplier);
        Settings.ViewDistanceQuality = FMath::RoundToInt(Settings.ViewDistanceQuality * QualityMultiplier);
        Settings.FoliageQuality = FMath::RoundToInt(Settings.FoliageQuality * QualityMultiplier);
        Settings.ShadingQuality = FMath::RoundToInt(Settings.ShadingQuality * QualityMultiplier);
    }
    
    // Aplicar limitações
    Settings = ApplyQualityLimits(Settings);
    
    return Settings;
}

void UAuracronHardwareDetectionComponent::ApplyQualitySettings(const FSimpleQualitySettings& Settings)
{
    if (!HardwareDetectionSystem)
    {
        UE_LOG(LogTemp, Error, TEXT("HardwareDetectionSystem is null"));
        return;
    }
    
    FSimpleQualitySettings FinalSettings = ApplyQualityLimits(Settings);
    
    HardwareDetectionSystem->ApplyQualitySettings(FinalSettings);
    LastAppliedSettings = FinalSettings;
    
    if (bVerboseLogging)
    {
        LogQualitySettings(FinalSettings);
    }
    
    // Disparar evento
    OnQualitySettingsApplied.Broadcast(FinalSettings);
    
    UE_LOG(LogTemp, Log, TEXT("Quality settings applied successfully"));
}

void UAuracronHardwareDetectionComponent::RunFullHardwareDetection()
{
    UE_LOG(LogTemp, Log, TEXT("Starting full hardware detection..."));
    
    // Detectar hardware
    FSimpleHardwareInfo HardwareInfo = DetectHardware();
    
    if (bAutoApplySettings)
    {
        // Calcular e aplicar configurações
        FSimpleQualitySettings RecommendedSettings = CalculateRecommendedSettings(HardwareInfo);
        ApplyQualitySettings(RecommendedSettings);
    }
    
    UE_LOG(LogTemp, Log, TEXT("Full hardware detection completed"));
}

void UAuracronHardwareDetectionComponent::SaveCustomSettings(const FSimpleQualitySettings& CustomSettings)
{
    // Salvar configurações no GameUserSettings
    UGameUserSettings* GameSettings = UGameUserSettings::GetGameUserSettings();
    if (GameSettings)
    {
        // Aplicar configurações
        HardwareDetectionSystem->ApplyQualitySettings(CustomSettings);
        LastAppliedSettings = CustomSettings;
        
        UE_LOG(LogTemp, Log, TEXT("Custom settings saved and applied"));
    }
}

FSimpleQualitySettings UAuracronHardwareDetectionComponent::LoadCustomSettings()
{
    // Carregar configurações do GameUserSettings
    UGameUserSettings* GameSettings = UGameUserSettings::GetGameUserSettings();
    if (GameSettings)
    {
        FSimpleQualitySettings Settings;
        Settings.OverallQuality = GameSettings->GetOverallScalabilityLevel();
        Settings.TextureQuality = GameSettings->GetTextureQuality();
        Settings.ShadowQuality = GameSettings->GetShadowQuality();
        Settings.PostProcessQuality = GameSettings->GetPostProcessingQuality();
        Settings.AntiAliasingQuality = GameSettings->GetAntiAliasingQuality();
        Settings.ViewDistanceQuality = GameSettings->GetViewDistanceQuality();
        Settings.FoliageQuality = GameSettings->GetFoliageQuality();
        Settings.ShadingQuality = GameSettings->GetShadingQuality();
        Settings.ResolutionScale = GameSettings->GetResolutionScaleNormalized();
        Settings.TargetFPS = GameSettings->GetFrameRateLimit();
        
        return Settings;
    }
    
    return FSimpleQualitySettings();
}

void UAuracronHardwareDetectionComponent::ResetToDefaultSettings()
{
    FSimpleQualitySettings DefaultSettings;
    ApplyQualitySettings(DefaultSettings);
    
    UE_LOG(LogTemp, Log, TEXT("Reset to default settings"));
}

FSimpleQualitySettings UAuracronHardwareDetectionComponent::ApplyQualityLimits(const FSimpleQualitySettings& Settings)
{
    FSimpleQualitySettings LimitedSettings = Settings;
    
    // Aplicar limites mínimos e máximos
    LimitedSettings.OverallQuality = FMath::Clamp(LimitedSettings.OverallQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.TextureQuality = FMath::Clamp(LimitedSettings.TextureQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.ShadowQuality = FMath::Clamp(LimitedSettings.ShadowQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.PostProcessQuality = FMath::Clamp(LimitedSettings.PostProcessQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.AntiAliasingQuality = FMath::Clamp(LimitedSettings.AntiAliasingQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.ViewDistanceQuality = FMath::Clamp(LimitedSettings.ViewDistanceQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.FoliageQuality = FMath::Clamp(LimitedSettings.FoliageQuality, MinimumQualityLevel, MaximumQualityLevel);
    LimitedSettings.ShadingQuality = FMath::Clamp(LimitedSettings.ShadingQuality, MinimumQualityLevel, MaximumQualityLevel);
    
    return LimitedSettings;
}

void UAuracronHardwareDetectionComponent::LogHardwareInfo(const FSimpleHardwareInfo& HardwareInfo)
{
    UE_LOG(LogTemp, Log, TEXT("=== HARDWARE DETECTION RESULTS ==="));
    UE_LOG(LogTemp, Log, TEXT("GPU: %s"), *HardwareInfo.GPUName);
    UE_LOG(LogTemp, Log, TEXT("Video Memory: %d MB"), HardwareInfo.VideoMemoryMB);
    UE_LOG(LogTemp, Log, TEXT("Total RAM: %.1f GB"), HardwareInfo.TotalRAMGB);
    UE_LOG(LogTemp, Log, TEXT("Available RAM: %.1f GB"), HardwareInfo.AvailableRAMGB);
    UE_LOG(LogTemp, Log, TEXT("CPU Cores: %d"), HardwareInfo.CPUCores);
    UE_LOG(LogTemp, Log, TEXT("Ray Tracing Support: %s"), HardwareInfo.bSupportsRayTracing ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Log, TEXT("Mesh Shader Support: %s"), HardwareInfo.bSupportsMeshShaders ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Log, TEXT("==================================="));
}

void UAuracronHardwareDetectionComponent::LogQualitySettings(const FSimpleQualitySettings& Settings)
{
    UE_LOG(LogTemp, Log, TEXT("=== APPLIED QUALITY SETTINGS ==="));
    UE_LOG(LogTemp, Log, TEXT("Overall Quality: %d"), Settings.OverallQuality);
    UE_LOG(LogTemp, Log, TEXT("Texture Quality: %d"), Settings.TextureQuality);
    UE_LOG(LogTemp, Log, TEXT("Shadow Quality: %d"), Settings.ShadowQuality);
    UE_LOG(LogTemp, Log, TEXT("Post Process Quality: %d"), Settings.PostProcessQuality);
    UE_LOG(LogTemp, Log, TEXT("Anti-Aliasing Quality: %d"), Settings.AntiAliasingQuality);
    UE_LOG(LogTemp, Log, TEXT("View Distance Quality: %d"), Settings.ViewDistanceQuality);
    UE_LOG(LogTemp, Log, TEXT("Foliage Quality: %d"), Settings.FoliageQuality);
    UE_LOG(LogTemp, Log, TEXT("Shading Quality: %d"), Settings.ShadingQuality);
    UE_LOG(LogTemp, Log, TEXT("Resolution Scale: %.2f"), Settings.ResolutionScale);
    UE_LOG(LogTemp, Log, TEXT("Target FPS: %d"), Settings.TargetFPS);
    UE_LOG(LogTemp, Log, TEXT("Lumen Enabled: %s"), Settings.bEnableLumen ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Log, TEXT("Nanite Enabled: %s"), Settings.bEnableNanite ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Log, TEXT("Ray Tracing Enabled: %s"), Settings.bEnableRayTracing ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Log, TEXT("================================="));
}
