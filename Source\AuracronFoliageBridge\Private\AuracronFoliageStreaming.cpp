// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Streaming Integration Implementation
// Bridge 4.10: Foliage - Streaming Integration

#include "AuracronFoliageStreaming.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
// Forward declaration instead of include to avoid circular dependency
// #include "AuracronWorldPartitionStreaming.h"
// #include "AuracronWorldPartitionDataLayers.h" // Will be available when WorldPartitionBridge is compiled
#include "AuracronFoliageBridge.h"

// UE5.6 Streaming includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Foliage streaming includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"
#include "ProceduralFoliageComponent.h"

// Memory management includes
#include "HAL/PlatformMemory.h"
#include "HAL/MemoryBase.h"
#include "Containers/LockFreeList.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "Engine/StreamableManager.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Box.h"

// =============================================================================
// FOLIAGE STREAMING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageStreamingManager* UAuracronFoliageStreamingManager::Instance = nullptr;

UAuracronFoliageStreamingManager* UAuracronFoliageStreamingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageStreamingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageStreamingManager::Initialize(const FAuracronFoliageStreamingConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage Streaming Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    FoliageChunks.Empty();
    StreamingRequests.Empty();
    MemoryPools.Empty();

    // Clear queues
    while (!PendingLoadRequests.IsEmpty())
    {
        FString DummyRequest;
        PendingLoadRequests.Dequeue(DummyRequest);
    }
    while (!PendingUnloadRequests.IsEmpty())
    {
        FString DummyRequest;
        PendingUnloadRequests.Dequeue(DummyRequest);
    }
    ActiveAsyncOperations.Empty();

    // Initialize performance data
    PerformanceData = FAuracronFoliageStreamingPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastStreamingUpdate = 0.0f;
    LastMemoryUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    // Initialize memory pools if enabled
    if (Configuration.bEnableInstancePooling)
    {
        InitializeMemoryPools();
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming Manager initialized with strategy: %s, max memory: %.1f MB"), 
                              *UEnum::GetValueAsString(Configuration.StreamingStrategy),
                              Configuration.MaxFoliageMemoryMB);
}

void UAuracronFoliageStreamingManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel all pending operations
    while (!PendingLoadRequests.IsEmpty())
    {
        FString RequestId;
        PendingLoadRequests.Dequeue(RequestId);
    }
    while (!PendingUnloadRequests.IsEmpty())
    {
        FString RequestId;
        PendingUnloadRequests.Dequeue(RequestId);
    }

    // Clear all collections
    FoliageChunks.Empty();
    StreamingRequests.Empty();
    MemoryPools.Empty();
    ActiveAsyncOperations.Empty();

    // Reset references
    ManagedWorld.Reset();
    WorldPartitionManager.Reset();
    DataLayerManager.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming Manager shutdown completed"));
}

bool UAuracronFoliageStreamingManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageStreamingManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update streaming operations
    LastStreamingUpdate += DeltaTime;
    if (LastStreamingUpdate >= Configuration.StreamingUpdateInterval)
    {
        UpdateStreamingInternal(DeltaTime);
        LastStreamingUpdate = 0.0f;
    }

    // Process streaming requests
    ProcessStreamingRequests();

    // Update async loading operations
    if (Configuration.bEnableAsyncFoliageLoading)
    {
        UpdateAsyncLoadingOperations(DeltaTime);
    }

    // Update memory management
    LastMemoryUpdate += DeltaTime;
    if (LastMemoryUpdate >= 1.0f) // Update every second
    {
        UpdateMemoryManagement();
        LastMemoryUpdate = 0.0f;
    }

    // Update data layer based streaming
    if (Configuration.bEnableDataLayerIntegration)
    {
        UpdateDataLayerBasedStreaming();
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageStreamingManager::SetConfiguration(const FAuracronFoliageStreamingConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming configuration updated"));
}

FAuracronFoliageStreamingConfiguration UAuracronFoliageStreamingManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageStreamingManager::IntegrateWithWorldPartition(UObject* InWorldPartitionManager)
{
    if (!InWorldPartitionManager)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid World Partition Manager"));
        return;
    }

    WorldPartitionManager = InWorldPartitionManager;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming integrated with World Partition"));
}

void UAuracronFoliageStreamingManager::IntegrateWithDataLayers(UObject* InDataLayerManager)
{
    if (!InDataLayerManager)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid Data Layer Manager"));
        return;
    }

    DataLayerManager = InDataLayerManager;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Streaming integrated with Data Layers"));
}

FString UAuracronFoliageStreamingManager::CreateFoliageChunk(const FBox& ChunkBounds, const FString& CellId, const FString& DataLayerId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    FString ChunkId = GenerateChunkId();

    FAuracronFoliageChunkData NewChunkData;
    NewChunkData.ChunkId = ChunkId;
    NewChunkData.CellId = CellId;
    NewChunkData.DataLayerId = DataLayerId;
    NewChunkData.ChunkBounds = ChunkBounds;
    NewChunkData.StreamingState = EAuracronFoliageStreamingState::Unloaded;
    NewChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
    NewChunkData.LastAccessTime = FPlatformTime::Seconds();

    FoliageChunks.Add(ChunkId, NewChunkData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk created: %s (Cell: %s, DataLayer: %s)"), 
                              *ChunkId, *CellId, *DataLayerId);

    return ChunkId;
}

void UAuracronFoliageStreamingManager::ValidateConfiguration()
{
    // Validate streaming distances
    Configuration.FoliageStreamingDistance = FMath::Max(1000.0f, Configuration.FoliageStreamingDistance);
    Configuration.FoliageUnloadingDistance = FMath::Max(Configuration.FoliageStreamingDistance + 500.0f, Configuration.FoliageUnloadingDistance);
    Configuration.FoliagePreloadDistance = FMath::Max(Configuration.FoliageUnloadingDistance + 500.0f, Configuration.FoliagePreloadDistance);

    // Validate cell streaming
    Configuration.CellStreamingBuffer = FMath::Max(100.0f, Configuration.CellStreamingBuffer);

    // Validate memory settings
    Configuration.MaxFoliageMemoryMB = FMath::Max(64.0f, Configuration.MaxFoliageMemoryMB);
    Configuration.MemoryPressureThreshold = FMath::Clamp(Configuration.MemoryPressureThreshold, 0.5f, 0.95f);
    Configuration.InstancePoolSize = FMath::Max(1000, Configuration.InstancePoolSize);

    // Validate async settings
    Configuration.MaxConcurrentFoliageLoads = FMath::Max(1, Configuration.MaxConcurrentFoliageLoads);
    Configuration.FoliageLoadingTimeSliceMs = FMath::Max(1.0f, Configuration.FoliageLoadingTimeSliceMs);
    Configuration.AsyncWorkerThreads = FMath::Max(1, Configuration.AsyncWorkerThreads);

    // Validate performance settings
    Configuration.MaxFoliageInstancesPerFrame = FMath::Max(100, Configuration.MaxFoliageInstancesPerFrame);
    Configuration.StreamingUpdateInterval = FMath::Max(0.01f, Configuration.StreamingUpdateInterval);

    // Validate caching settings
    Configuration.CacheRetentionTime = FMath::Max(5.0f, Configuration.CacheRetentionTime);
    Configuration.MaxCachedFoliageChunks = FMath::Max(1, Configuration.MaxCachedFoliageChunks);
}

FString UAuracronFoliageStreamingManager::GenerateChunkId() const
{
    return FString::Printf(TEXT("FoliageChunk_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageStreamingManager::GenerateRequestId() const
{
    return FString::Printf(TEXT("StreamingRequest_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageStreamingManager::GeneratePoolId() const
{
    return FString::Printf(TEXT("MemoryPool_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageStreamingManager::UpdateStreamingInternal(float DeltaTime)
{
    if (!ManagedWorld.IsValid())
    {
        return;
    }

    // Get streaming sources (players, cameras, etc.)
    TArray<FVector> StreamingSources;

    // Add player locations as streaming sources
    UWorld* World = ManagedWorld.Get();
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        if (APlayerController* PC = Iterator->Get())
        {
            if (APawn* Pawn = PC->GetPawn())
            {
                StreamingSources.Add(Pawn->GetActorLocation());
            }
        }
    }

    // Update distance-based streaming
    if (Configuration.StreamingStrategy == EAuracronFoliageStreamingStrategy::DistanceBased ||
        Configuration.StreamingStrategy == EAuracronFoliageStreamingStrategy::Hybrid)
    {
        UpdateDistanceBasedStreaming(StreamingSources);
    }

    // Update chunk priorities
    UpdateChunkPriorities(StreamingSources);
}

void UAuracronFoliageStreamingManager::UpdateDistanceBasedStreaming(const TArray<FVector>& StreamingSources)
{
    FScopeLock Lock(&StreamingLock);

    for (auto& ChunkPair : FoliageChunks)
    {
        FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        // Check if chunk should be loaded
        if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloaded)
        {
            if (ShouldLoadChunk(ChunkData, StreamingSources))
            {
                RequestFoliageChunkLoading(ChunkData.ChunkId, ChunkData.LoadingPriority);
            }
        }
        // Check if chunk should be unloaded
        else if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded)
        {
            if (ShouldUnloadChunk(ChunkData, StreamingSources))
            {
                RequestFoliageChunkUnloading(ChunkData.ChunkId);
            }
        }
    }
}

void UAuracronFoliageStreamingManager::UpdateChunkPriorities(const TArray<FVector>& StreamingSources)
{
    FScopeLock Lock(&StreamingLock);

    for (auto& ChunkPair : FoliageChunks)
    {
        FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        // Calculate priority based on distance to nearest streaming source
        float MinDistance = FLT_MAX;
        for (const FVector& Source : StreamingSources)
        {
            float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
            MinDistance = FMath::Min(MinDistance, Distance);
        }

        // Update loading priority based on distance
        if (MinDistance < Configuration.FoliageStreamingDistance * 0.5f)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::VeryHigh;
        }
        else if (MinDistance < Configuration.FoliageStreamingDistance * 0.75f)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::High;
        }
        else if (MinDistance < Configuration.FoliageStreamingDistance)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
        }
        else if (MinDistance < Configuration.FoliagePreloadDistance)
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Low;
        }
        else
        {
            ChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::VeryLow;
        }
    }
}

bool UAuracronFoliageStreamingManager::ShouldLoadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const
{
    // Check distance to streaming sources
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
        if (Distance <= Configuration.FoliageStreamingDistance)
        {
            return true;
        }
    }

    return false;
}

bool UAuracronFoliageStreamingManager::ShouldUnloadChunk(const FAuracronFoliageChunkData& ChunkData, const TArray<FVector>& StreamingSources) const
{
    // Check distance to streaming sources
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist(Source, ChunkData.ChunkBounds.GetCenter());
        if (Distance <= Configuration.FoliageUnloadingDistance)
        {
            return false; // Don't unload if within unloading distance
        }
    }

    return true; // Unload if all sources are beyond unloading distance
}

FString UAuracronFoliageStreamingManager::RequestFoliageChunkLoading(const FString& ChunkId, EAuracronFoliageLoadingPriority Priority)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    // Check if chunk exists
    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return FString();
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Check if already loaded or loading
    if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded ||
        ChunkData.StreamingState == EAuracronFoliageStreamingState::Loading)
    {
        return FString(); // Already loaded or loading
    }

    FString RequestId = GenerateRequestId();

    FAuracronFoliageStreamingRequest NewRequest;
    NewRequest.RequestId = RequestId;
    NewRequest.ChunkId = ChunkId;
    NewRequest.bIsLoadRequest = true;
    NewRequest.Priority = Priority;
    NewRequest.RequestTime = FPlatformTime::Seconds();

    StreamingRequests.Add(RequestId, NewRequest);
    PendingLoadRequests.Enqueue(RequestId);

    // Update chunk state
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Loading;
    ChunkData.LoadingPriority = Priority;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk loading requested: %s (Priority: %s)"),
                              *ChunkId,
                              *UEnum::GetValueAsString(Priority));

    return RequestId;
}

FString UAuracronFoliageStreamingManager::RequestFoliageChunkUnloading(const FString& ChunkId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&StreamingLock);

    // Check if chunk exists
    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return FString();
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Check if already unloaded or unloading
    if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloaded ||
        ChunkData.StreamingState == EAuracronFoliageStreamingState::Unloading)
    {
        return FString(); // Already unloaded or unloading
    }

    FString RequestId = GenerateRequestId();

    FAuracronFoliageStreamingRequest NewRequest;
    NewRequest.RequestId = RequestId;
    NewRequest.ChunkId = ChunkId;
    NewRequest.bIsLoadRequest = false;
    NewRequest.Priority = EAuracronFoliageLoadingPriority::Normal;
    NewRequest.RequestTime = FPlatformTime::Seconds();

    StreamingRequests.Add(RequestId, NewRequest);
    PendingUnloadRequests.Enqueue(RequestId);

    // Update chunk state
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Unloading;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage chunk unloading requested: %s"), *ChunkId);

    return RequestId;
}

// ========================================
// Missing Function Implementations
// ========================================

void UAuracronFoliageStreamingManager::RegisterFoliageWithCell(const FString& CellId, const FString& FoliageTypeId, const TArray<FTransform>& Instances)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return;
    }

    FScopeLock Lock(&StreamingLock);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Registering %d foliage instances of type %s with cell %s"),
                              Instances.Num(), *FoliageTypeId, *CellId);

    // Create or update foliage chunk for this cell
    FString ChunkId = FString::Printf(TEXT("%s_%s"), *CellId, *FoliageTypeId);

    if (!FoliageChunks.Contains(ChunkId))
    {
        // Calculate bounds from instances
        FBox ChunkBounds(ForceInit);
        for (const FTransform& Transform : Instances)
        {
            ChunkBounds += Transform.GetLocation();
        }
        ChunkBounds = ChunkBounds.ExpandBy(100.0f); // Add some padding

        // Create new chunk
        FAuracronFoliageChunkData NewChunkData;
        NewChunkData.ChunkId = ChunkId;
        NewChunkData.CellId = CellId;
        NewChunkData.ChunkBounds = ChunkBounds;
        NewChunkData.StreamingState = EAuracronFoliageStreamingState::Unloaded;
        NewChunkData.LoadingPriority = EAuracronFoliageLoadingPriority::Normal;
        NewChunkData.LastAccessTime = FPlatformTime::Seconds();
        NewChunkData.TotalInstanceCount = Instances.Num();

        FoliageChunks.Add(ChunkId, NewChunkData);
    }
    else
    {
        // Update existing chunk
        FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];
        ChunkData.TotalInstanceCount += Instances.Num();
        ChunkData.LastAccessTime = FPlatformTime::Seconds();
    }
}

void UAuracronFoliageStreamingManager::UnregisterFoliageFromCell(const FString& CellId, const FString& FoliageTypeId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return;
    }

    FScopeLock Lock(&StreamingLock);

    FString ChunkId = FString::Printf(TEXT("%s_%s"), *CellId, *FoliageTypeId);

    if (FoliageChunks.Contains(ChunkId))
    {
        FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

        // If chunk is loaded, unload it first
        if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded)
        {
            RequestFoliageChunkUnloading(ChunkId);
        }

        // Remove chunk
        FoliageChunks.Remove(ChunkId);

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Unregistered foliage type %s from cell %s"), *FoliageTypeId, *CellId);
    }
}

bool UAuracronFoliageStreamingManager::UpdateFoliageChunk(const FString& ChunkId, const FAuracronFoliageChunkData& ChunkData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&StreamingLock);

    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return false;
    }

    FoliageChunks[ChunkId] = ChunkData;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Updated foliage chunk: %s"), *ChunkId);

    return true;
}

bool UAuracronFoliageStreamingManager::RemoveFoliageChunk(const FString& ChunkId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&StreamingLock);

    if (!FoliageChunks.Contains(ChunkId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage chunk not found: %s"), *ChunkId);
        return false;
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // If chunk is loaded, unload it first
    if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded)
    {
        RequestFoliageChunkUnloading(ChunkId);
    }

    FoliageChunks.Remove(ChunkId);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Removed foliage chunk: %s"), *ChunkId);

    return true;
}

FAuracronFoliageChunkData UAuracronFoliageStreamingManager::GetFoliageChunk(const FString& ChunkId) const
{
    FScopeLock Lock(&StreamingLock);

    if (FoliageChunks.Contains(ChunkId))
    {
        return FoliageChunks[ChunkId];
    }

    // Return empty chunk data if not found
    return FAuracronFoliageChunkData();
}

TArray<FAuracronFoliageChunkData> UAuracronFoliageStreamingManager::GetAllFoliageChunks() const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FAuracronFoliageChunkData> AllChunks;
    for (const auto& ChunkPair : FoliageChunks)
    {
        AllChunks.Add(ChunkPair.Value);
    }

    return AllChunks;
}

TArray<FString> UAuracronFoliageStreamingManager::GetChunksInRange(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FString> ChunksInRange;

    for (const auto& ChunkPair : FoliageChunks)
    {
        const FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;
        float Distance = FVector::Dist(Location, ChunkData.ChunkBounds.GetCenter());

        if (Distance <= Radius)
        {
            ChunksInRange.Add(ChunkData.ChunkId);
        }
    }

    return ChunksInRange;
}

bool UAuracronFoliageStreamingManager::CancelFoliageStreamingRequest(const FString& RequestId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&StreamingLock);

    if (!StreamingRequests.Contains(RequestId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Streaming request not found: %s"), *RequestId);
        return false;
    }

    StreamingRequests.Remove(RequestId);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cancelled streaming request: %s"), *RequestId);

    return true;
}

void UAuracronFoliageStreamingManager::ProcessStreamingRequests()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Process load requests
    FString LoadRequestId;
    while (PendingLoadRequests.Dequeue(LoadRequestId))
    {
        ProcessSingleStreamingRequest(LoadRequestId);
    }

    // Process unload requests
    FString UnloadRequestId;
    while (PendingUnloadRequests.Dequeue(UnloadRequestId))
    {
        ProcessSingleStreamingRequest(UnloadRequestId);
    }
}

void UAuracronFoliageStreamingManager::EnableAsyncFoliageLoading(bool bEnabled)
{
    Configuration.bEnableAsyncFoliageLoading = bEnabled;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Async foliage loading %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageStreamingManager::IsAsyncFoliageLoadingEnabled() const
{
    return Configuration.bEnableAsyncFoliageLoading;
}

void UAuracronFoliageStreamingManager::UpdateAsyncLoadingOperations(float DeltaTime)
{
    if (!Configuration.bEnableAsyncFoliageLoading)
    {
        return;
    }

    // Update active async operations
    for (int32 i = ActiveAsyncOperations.Num() - 1; i >= 0; --i)
    {
        const FString& OperationId = ActiveAsyncOperations[i];

        // Check if operation is complete (simplified implementation)
        // In a real implementation, this would check actual async task status
        if (FMath::RandBool()) // Random completion for demonstration
        {
            ActiveAsyncOperations.RemoveAt(i);
            AURACRON_FOLIAGE_LOG_INFO(TEXT("Async operation completed: %s"), *OperationId);
        }
    }
}

int32 UAuracronFoliageStreamingManager::GetActiveAsyncLoadingCount() const
{
    return ActiveAsyncOperations.Num();
}

void UAuracronFoliageStreamingManager::InitializeMemoryPools()
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return;
    }

    MemoryPools.Empty();

    // Create default memory pools
    CreateMemoryPool(TEXT("DefaultFoliagePool"), Configuration.InstancePoolSize, Configuration.MaxFoliageMemoryMB * 0.5f);
    CreateMemoryPool(TEXT("HighPriorityPool"), Configuration.InstancePoolSize / 2, Configuration.MaxFoliageMemoryMB * 0.3f);
    CreateMemoryPool(TEXT("CachePool"), Configuration.InstancePoolSize / 4, Configuration.MaxFoliageMemoryMB * 0.2f);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory pools initialized"));
}

FAuracronFoliageMemoryPool UAuracronFoliageStreamingManager::CreateMemoryPool(const FString& PoolId, int32 PoolSize, float MaxMemoryMB)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return FAuracronFoliageMemoryPool();
    }

    FScopeLock Lock(&StreamingLock);

    FAuracronFoliageMemoryPool NewPool;
    NewPool.PoolId = PoolId;
    NewPool.PoolSize = PoolSize;
    NewPool.MaxMemoryMB = MaxMemoryMB;
    NewPool.UsedInstances = 0;
    NewPool.MemoryUsageMB = 0.0f;
    NewPool.bIsActive = true;

    MemoryPools.Add(PoolId, NewPool);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Created memory pool: %s (Size: %d, Memory: %.1f MB)"),
                              *PoolId, PoolSize, MaxMemoryMB);

    return NewPool;
}

bool UAuracronFoliageStreamingManager::UpdateMemoryPool(const FString& PoolId, const FAuracronFoliageMemoryPool& PoolData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Streaming Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&StreamingLock);

    if (!MemoryPools.Contains(PoolId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Memory pool not found: %s"), *PoolId);
        return false;
    }

    MemoryPools[PoolId] = PoolData;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Updated memory pool: %s"), *PoolId);

    return true;
}

void UAuracronFoliageStreamingManager::UpdateMemoryManagement()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&StreamingLock);

    // Update memory usage statistics
    float TotalMemoryUsage = 0.0f;
    for (const auto& PoolPair : MemoryPools)
    {
        TotalMemoryUsage += PoolPair.Value.MemoryUsageMB;
    }

    PerformanceData.FoliageMemoryUsageMB = TotalMemoryUsage;

    // Check for memory pressure
    if (IsMemoryPressureHigh())
    {
        CleanupUnusedMemory();
        OnFoliageMemoryPressure.Broadcast(TotalMemoryUsage / Configuration.MaxFoliageMemoryMB);
    }
}

bool UAuracronFoliageStreamingManager::IsMemoryPressureHigh() const
{
    float TotalMemoryUsage = 0.0f;
    for (const auto& PoolPair : MemoryPools)
    {
        TotalMemoryUsage += PoolPair.Value.MemoryUsageMB;
    }

    return (TotalMemoryUsage / Configuration.MaxFoliageMemoryMB) > Configuration.MemoryPressureThreshold;
}

void UAuracronFoliageStreamingManager::CleanupUnusedMemory()
{
    FScopeLock Lock(&StreamingLock);

    // Clean up expired cache entries
    CleanupExpiredCacheEntries();

    // Unload low-priority chunks if memory pressure is high
    TArray<FString> ChunksToUnload;
    for (const auto& ChunkPair : FoliageChunks)
    {
        const FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Loaded &&
            ChunkData.LoadingPriority == EAuracronFoliageLoadingPriority::VeryLow)
        {
            ChunksToUnload.Add(ChunkData.ChunkId);
        }
    }

    for (const FString& ChunkId : ChunksToUnload)
    {
        RequestFoliageChunkUnloading(ChunkId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleaned up unused memory, unloaded %d chunks"), ChunksToUnload.Num());
}

void UAuracronFoliageStreamingManager::OnDataLayerStateChanged(const FString& DataLayerId, bool bIsLoaded)
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&StreamingLock);

    // Find chunks associated with this data layer
    TArray<FString> AffectedChunks;
    for (const auto& ChunkPair : FoliageChunks)
    {
        if (ChunkPair.Value.DataLayerId == DataLayerId)
        {
            AffectedChunks.Add(ChunkPair.Key);
        }
    }

    // Load or unload chunks based on data layer state
    for (const FString& ChunkId : AffectedChunks)
    {
        if (bIsLoaded)
        {
            RequestFoliageChunkLoading(ChunkId, EAuracronFoliageLoadingPriority::Normal);
        }
        else
        {
            RequestFoliageChunkUnloading(ChunkId);
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Data layer %s state changed to %s, affected %d chunks"),
                              *DataLayerId, bIsLoaded ? TEXT("loaded") : TEXT("unloaded"), AffectedChunks.Num());
}

void UAuracronFoliageStreamingManager::UpdateDataLayerBasedStreaming()
{
    if (!Configuration.bEnableDataLayerIntegration || !DataLayerManager.IsValid())
    {
        return;
    }

    // This would integrate with the actual data layer system
    // For now, just log that we're updating
    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Updating data layer based streaming"));
}

TArray<FString> UAuracronFoliageStreamingManager::GetFoliageChunksInDataLayer(const FString& DataLayerId) const
{
    FScopeLock Lock(&StreamingLock);

    TArray<FString> ChunksInDataLayer;
    for (const auto& ChunkPair : FoliageChunks)
    {
        if (ChunkPair.Value.DataLayerId == DataLayerId)
        {
            ChunksInDataLayer.Add(ChunkPair.Key);
        }
    }

    return ChunksInDataLayer;
}

FAuracronFoliageStreamingPerformanceData UAuracronFoliageStreamingManager::GetPerformanceData() const
{
    return PerformanceData;
}

void UAuracronFoliageStreamingManager::UpdatePerformanceMetrics()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&StreamingLock);

    // Update chunk counts
    PerformanceData.TotalFoliageChunks = FoliageChunks.Num();
    PerformanceData.LoadedChunks = 0;
    PerformanceData.LoadingChunks = 0;
    PerformanceData.CachedChunks = 0;
    PerformanceData.TotalFoliageInstances = 0;
    PerformanceData.StreamedInstances = 0;

    for (const auto& ChunkPair : FoliageChunks)
    {
        const FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        switch (ChunkData.StreamingState)
        {
            case EAuracronFoliageStreamingState::Loaded:
                PerformanceData.LoadedChunks++;
                PerformanceData.StreamedInstances += ChunkData.TotalInstanceCount;
                break;
            case EAuracronFoliageStreamingState::Loading:
                PerformanceData.LoadingChunks++;
                break;
            case EAuracronFoliageStreamingState::Cached:
                PerformanceData.CachedChunks++;
                break;
        }

        PerformanceData.TotalFoliageInstances += ChunkData.TotalInstanceCount;
    }

    // Update request counts
    PerformanceData.PendingRequests = StreamingRequests.Num();
    PerformanceData.CompletedRequests++; // Simplified tracking
}

int32 UAuracronFoliageStreamingManager::GetLoadedChunkCount() const
{
    return PerformanceData.LoadedChunks;
}

float UAuracronFoliageStreamingManager::GetFoliageMemoryUsage() const
{
    return PerformanceData.FoliageMemoryUsageMB;
}

void UAuracronFoliageStreamingManager::EnableDebugVisualization(bool bEnabled)
{
    Configuration.bEnableDebugVisualization = bEnabled;
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug visualization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageStreamingManager::IsDebugVisualizationEnabled() const
{
    return Configuration.bEnableDebugVisualization;
}

void UAuracronFoliageStreamingManager::DrawDebugStreamingInfo(UWorld* World) const
{
    if (!Configuration.bEnableDebugVisualization || !World)
    {
        return;
    }

    // Draw debug information for foliage chunks
    for (const auto& ChunkPair : FoliageChunks)
    {
        const FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        FColor DebugColor = FColor::White;
        switch (ChunkData.StreamingState)
        {
            case EAuracronFoliageStreamingState::Loaded:
                DebugColor = FColor::Green;
                break;
            case EAuracronFoliageStreamingState::Loading:
                DebugColor = FColor::Yellow;
                break;
            case EAuracronFoliageStreamingState::Unloading:
                DebugColor = FColor::Orange;
                break;
            case EAuracronFoliageStreamingState::Unloaded:
                DebugColor = FColor::Red;
                break;
            case EAuracronFoliageStreamingState::Cached:
                DebugColor = FColor::Blue;
                break;
        }

        DrawDebugBox(World, ChunkData.ChunkBounds.GetCenter(), ChunkData.ChunkBounds.GetExtent(),
                     DebugColor, false, -1.0f, 0, 2.0f);
    }
}

void UAuracronFoliageStreamingManager::LogStreamingStatistics() const
{
    AURACRON_FOLIAGE_LOG_INFO(TEXT("=== Foliage Streaming Statistics ==="));
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Total Chunks: %d"), PerformanceData.TotalFoliageChunks);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Loaded Chunks: %d"), PerformanceData.LoadedChunks);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Loading Chunks: %d"), PerformanceData.LoadingChunks);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cached Chunks: %d"), PerformanceData.CachedChunks);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Total Instances: %d"), PerformanceData.TotalFoliageInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Streamed Instances: %d"), PerformanceData.StreamedInstances);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory Usage: %.1f MB"), PerformanceData.FoliageMemoryUsageMB);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Pending Requests: %d"), PerformanceData.PendingRequests);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("====================================="));
}

// ========================================
// Internal Helper Functions
// ========================================

void UAuracronFoliageStreamingManager::ProcessSingleStreamingRequest(const FString& RequestId)
{
    if (!StreamingRequests.Contains(RequestId))
    {
        return;
    }

    const FAuracronFoliageStreamingRequest& Request = StreamingRequests[RequestId];

    if (Request.bIsLoadRequest)
    {
        LoadFoliageChunkInternal(Request.ChunkId);
    }
    else
    {
        UnloadFoliageChunkInternal(Request.ChunkId);
    }

    // Remove completed request
    StreamingRequests.Remove(RequestId);
}

void UAuracronFoliageStreamingManager::LoadFoliageChunkInternal(const FString& ChunkId)
{
    if (!FoliageChunks.Contains(ChunkId))
    {
        return;
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Simulate loading process
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Loaded;
    ChunkData.LastAccessTime = FPlatformTime::Seconds();

    // Broadcast event
    OnFoliageChunkLoaded.Broadcast(ChunkId, ChunkData.TotalInstanceCount);
    OnFoliageStreamingStateChanged.Broadcast(ChunkId, ChunkData.StreamingState);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Loaded foliage chunk: %s (%d instances)"), *ChunkId, ChunkData.TotalInstanceCount);
}

void UAuracronFoliageStreamingManager::UnloadFoliageChunkInternal(const FString& ChunkId)
{
    if (!FoliageChunks.Contains(ChunkId))
    {
        return;
    }

    FAuracronFoliageChunkData& ChunkData = FoliageChunks[ChunkId];

    // Simulate unloading process
    ChunkData.StreamingState = EAuracronFoliageStreamingState::Unloaded;

    // Broadcast event
    OnFoliageChunkUnloaded.Broadcast(ChunkId);
    OnFoliageStreamingStateChanged.Broadcast(ChunkId, ChunkData.StreamingState);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Unloaded foliage chunk: %s"), *ChunkId);
}

float UAuracronFoliageStreamingManager::CalculateChunkPriority(const FAuracronFoliageChunkData& ChunkData, const FVector& SourceLocation) const
{
    float Distance = FVector::Dist(SourceLocation, ChunkData.ChunkBounds.GetCenter());

    // Priority decreases with distance
    float Priority = 1.0f - (Distance / Configuration.FoliagePreloadDistance);
    Priority = FMath::Clamp(Priority, 0.0f, 1.0f);

    // Boost priority for high-priority chunks
    switch (ChunkData.LoadingPriority)
    {
        case EAuracronFoliageLoadingPriority::VeryHigh:
            Priority *= 2.0f;
            break;
        case EAuracronFoliageLoadingPriority::High:
            Priority *= 1.5f;
            break;
        case EAuracronFoliageLoadingPriority::Low:
            Priority *= 0.7f;
            break;
        case EAuracronFoliageLoadingPriority::VeryLow:
            Priority *= 0.5f;
            break;
    }

    return FMath::Clamp(Priority, 0.0f, 2.0f);
}

void UAuracronFoliageStreamingManager::CleanupExpiredCacheEntries()
{
    double CurrentTime = FPlatformTime::Seconds();

    TArray<FString> ExpiredChunks;
    for (const auto& ChunkPair : FoliageChunks)
    {
        const FAuracronFoliageChunkData& ChunkData = ChunkPair.Value;

        if (ChunkData.StreamingState == EAuracronFoliageStreamingState::Cached)
        {
            double TimeSinceAccess = CurrentTime - ChunkData.LastAccessTime;
            if (TimeSinceAccess > Configuration.CacheRetentionTime)
            {
                ExpiredChunks.Add(ChunkData.ChunkId);
            }
        }
    }

    for (const FString& ChunkId : ExpiredChunks)
    {
        RemoveFoliageChunk(ChunkId);
    }

    if (ExpiredChunks.Num() > 0)
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleaned up %d expired cache entries"), ExpiredChunks.Num());
    }
}

TArray<FString> UAuracronFoliageStreamingManager::GetChunksForCell(const FString& CellId) const
{
    TArray<FString> CellChunks;

    for (const auto& ChunkPair : FoliageChunks)
    {
        if (ChunkPair.Value.CellId == CellId)
        {
            CellChunks.Add(ChunkPair.Key);
        }
    }

    return CellChunks;
}

void UAuracronFoliageStreamingManager::OnWorldPartitionCellLoaded(const FString& CellId)
{
    TArray<FString> CellChunks = GetChunksForCell(CellId);

    for (const FString& ChunkId : CellChunks)
    {
        RequestFoliageChunkLoading(ChunkId, EAuracronFoliageLoadingPriority::Normal);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("World partition cell loaded: %s, loading %d foliage chunks"),
                              *CellId, CellChunks.Num());
}

void UAuracronFoliageStreamingManager::OnWorldPartitionCellUnloaded(const FString& CellId)
{
    TArray<FString> CellChunks = GetChunksForCell(CellId);

    for (const FString& ChunkId : CellChunks)
    {
        RequestFoliageChunkUnloading(ChunkId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("World partition cell unloaded: %s, unloading %d foliage chunks"),
                              *CellId, CellChunks.Num());
}
