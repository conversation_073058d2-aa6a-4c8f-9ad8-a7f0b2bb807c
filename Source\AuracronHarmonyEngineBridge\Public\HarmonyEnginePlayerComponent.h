#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "GameplayTagContainer.h"
#include "Net/UnrealNetwork.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEnginePlayerComponent.generated.h"

class UHarmonyEngineSubsystem;
class APlayerController;
class UAbilitySystemComponent;

/**
 * Player Component for Harmony Engine Integration
 * Attach this to PlayerState or PlayerController for automatic Harmony Engine integration
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(HarmonyEngine), meta=(BlueprintSpawnableComponent))
class AURACRONHARMONYENGINEBRIDGE_API UHarmonyEnginePlayerComponent : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UHarmonyEnginePlayerComponent(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    // UActorComponent interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // UGameFrameworkComponent interface
    virtual void OnRegister() override;
    virtual void OnUnregister() override;

    // Networking
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    virtual bool IsSupportedForNetworking() const override { return true; }

    // Player Behavior Tracking
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void ReportPlayerAction(const FString& ActionType, bool bIsPositive, float Intensity = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void ReportChatMessage(const FString& Message);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void ReportEmotionalState(EEmotionalState NewEmotionalState);

    // Server RPC methods
    UFUNCTION(Server, Reliable, Category = "Harmony Engine")
    void ProcessChatMessageOnServer(const FString& Message);

    UFUNCTION(Server, Reliable, Category = "Harmony Engine")
    void ProcessEmotionalStateOnServer(EEmotionalState NewEmotionalState);

    // Client RPC methods
    UFUNCTION(Client, Reliable, Category = "Harmony Engine")
    void ClientUpdateHarmonyData(float ToxicityScore, float PositivityScore, EEmotionalState EmotionalState, int32 KindnessPoints);

    // Analysis methods
    float AnalyzeMessageToxicity(const FString& Message);
    float AnalyzeMessagePositivity(const FString& Message);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void ReportTeamworkAction(const FString& TeamworkType, const TArray<FString>& InvolvedPlayerIDs);

    // Player Status Queries
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    float GetCurrentToxicityScore() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    float GetCurrentPositivityScore() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    EEmotionalState GetCurrentEmotionalState() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    int32 GetKindnessPoints() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    bool IsUnderIntervention() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    bool IsInHealingSession() const;

    // Intervention Response
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void RespondToIntervention(const FString& InterventionID, bool bAccepted);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void RequestCommunitySupport(const FString& Reason);

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    void VolunteerAsHealer(const TArray<EHealingSessionType>& Specializations);

    // Rewards and Progression
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    TArray<FHarmonyReward> GetAvailableRewards();

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine")
    bool ClaimReward(const FString& RewardID);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine")
    float GetProgressToNextTier() const;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnBehaviorDetected OnPlayerBehaviorDetected;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnInterventionTriggered OnPlayerInterventionTriggered;

    UPROPERTY(BlueprintAssignable, Category = "Harmony Engine Events")
    FOnKindnessReward OnPlayerKindnessReward;

protected:
    // Replicated Properties
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    float CurrentToxicityScore;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    float CurrentPositivityScore;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    EEmotionalState CurrentEmotionalState;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    int32 TotalKindnessPoints;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    bool bIsUnderIntervention;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Harmony Engine")
    bool bIsInHealingSession;

    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableAutomaticReporting;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    float BehaviorUpdateInterval;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableEmotionalTracking;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Harmony Engine Config")
    bool bEnableRewardNotifications;

    // Runtime Data
    FString PlayerID;

    // Cached subsystem reference
    UPROPERTY()
    TObjectPtr<UHarmonyEngineSubsystem> HarmonyEngineSubsystem;

    UPROPERTY()
    TArray<FString> RecentActions;

    UPROPERTY()
    TArray<FString> RecentChatMessages;

    UPROPERTY()
    FDateTime LastBehaviorUpdate;

    UPROPERTY()
    int32 ActionsThisSession;

    // Timers
    FTimerHandle BehaviorUpdateTimer;
    FTimerHandle EmotionalCheckTimer;

private:
    // Initialization
    void InitializeHarmonyEngine();
    void RegisterWithHarmonyEngine();
    void UnregisterFromHarmonyEngine();
    
    // Behavior tracking
    void UpdateBehaviorMetrics();
    void AnalyzeRecentBehavior();
    void ProcessActionHistory();

    // Behavior analysis methods
    int32 CountPositiveActions() const;
    int32 CountNegativeActions() const;
    float CalculateToxicityFromActions() const;
    float CalculatePositivityFromActions() const;

    // Server processing
    void ProcessPlayerActionOnServer(const FString& ActionType, bool bIsPositive, float Intensity);
    
    // Event handlers
    UFUNCTION()
    void OnBehaviorDetectedInternal(const FString& DetectedPlayerID, const FPlayerBehaviorSnapshot& BehaviorData);

    UFUNCTION()
    void OnInterventionTriggeredInternal(const FString& DetectedPlayerID, const FHarmonyInterventionData& InterventionData);

    UFUNCTION()
    void OnKindnessRewardInternal(const FString& DetectedPlayerID, const FKindnessReward& Reward);

    // Network functions
    UFUNCTION(Server, Reliable)
    void ServerReportPlayerAction(const FString& ActionType, bool bIsPositive, float Intensity);

    UFUNCTION(Server, Reliable)
    void ServerReportChatMessage(const FString& Message);

    UFUNCTION(Server, Reliable)
    void ServerReportEmotionalState(EEmotionalState NewEmotionalState);

    UFUNCTION(Client, Reliable)
    void ClientShowIntervention(const FString& InterventionMessage, EInterventionType InterventionType);

    UFUNCTION(Client, Reliable)
    void ClientShowRewardNotification(const FKindnessReward& Reward);

    // Utility functions
    APlayerController* GetOwningPlayerController() const;
    bool IsLocalPlayerController() const;
    void ValidateConfiguration();
    void CleanupOldData();
};
