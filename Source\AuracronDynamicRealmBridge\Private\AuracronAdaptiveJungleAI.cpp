/**
 * AuracronAdaptiveJungleAI.cpp
 * 
 * Implementation of advanced AI system for jungle creatures that learns from
 * player behavior patterns and adapts creature behavior, spawning, and
 * environmental responses in real-time.
 * 
 * Uses UE 5.6 modern AI, ML, and behavior tree systems for production-ready
 * adaptive gameplay.
 */

#include "AuracronAdaptiveJungleAI.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronJungleCreature.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "EngineUtils.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Character.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardData.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

AAuracronAdaptiveJungleAI::AAuracronAdaptiveJungleAI()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 1.0f; // 1 FPS for AI updates

    // Create root scene component
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Initialize default configuration
    DifficultyMode = EJungleAIDifficulty::Adaptive;
    BaseLearningRate = 0.01f;
    MaxAdaptationStrength = 2.0f;
    MonitoringRadius = 2000.0f;
    AIUpdateFrequency = 1.0f;
    bEnableMachineLearning = true;
    bEnableEnvironmentalAdaptation = true;
    bEnablePerformanceScaling = true;

    // Initialize state
    bIsInitialized = false;
    bMLModelTrained = false;
    LastAIUpdate = 0.0f;
    LastMLTraining = 0.0f;
    TotalAdaptationsApplied = 0;

    // Initialize AI learning data
    AILearningData = FAuracronJungleAILearningData();

    // Reserve arrays for performance
    MonitoredPlayers.Reserve(16);
    ManagedCreatures.Reserve(64);
    TrainingDataset.Reserve(1000);
}

void AAuracronAdaptiveJungleAI::BeginPlay()
{
    Super::BeginPlay();

    // Initialize adaptive AI using UE 5.6 initialization patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptive Jungle AI BeginPlay"));

    if (GetWorld())
    {
        // Cache subsystem reference
        CachedRealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>();
        
        // Delay initialization to ensure all systems are ready
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            InitializeAdaptiveAI();
        });
    }
}

void AAuracronAdaptiveJungleAI::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Clean up adaptive AI using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptive Jungle AI EndPlay - Cleaning up..."));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Stop monitoring all players
    for (APawn* Player : MonitoredPlayers)
    {
        if (Player)
        {
            StopPlayerMonitoring(Player);
        }
    }

    // Remove all creature adaptations
    for (AAuracronJungleCreature* Creature : ManagedCreatures)
    {
        if (Creature)
        {
            RemoveCreatureAdaptations(Creature);
        }
    }

    // Clear arrays
    MonitoredPlayers.Empty();
    ManagedCreatures.Empty();
    PlayerAnalyses.Empty();
    ActiveAdaptations.Empty();
    TrainingDataset.Empty();
    PlayerMovementHistory.Empty();
    PlayerActionHistory.Empty();
    PlayerSkillEstimates.Empty();

    bIsInitialized = false;

    Super::EndPlay(EndPlayReason);
}

void AAuracronAdaptiveJungleAI::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bIsInitialized)
    {
        return;
    }

    // Update adaptive AI using UE 5.6 tick optimization
    LastAIUpdate = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update AI learning and adaptation
    UpdateAILearning(DeltaTime);

    // Update player tracking
    for (APawn* Player : MonitoredPlayers)
    {
        if (Player)
        {
            UpdatePlayerTracking(Player, DeltaTime);
        }
    }

    // Update environmental responses
    if (bEnableEnvironmentalAdaptation)
    {
        UpdateEnvironmentalResponses(DeltaTime);
    }

    // Performance optimization
    if (bEnablePerformanceScaling)
    {
        OptimizeAIPerformance();
    }
}

// === Core AI Management Implementation ===

void AAuracronAdaptiveJungleAI::InitializeAdaptiveAI()
{
    if (bIsInitialized)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Adaptive Jungle AI system..."));

    // Initialize machine learning system
    if (bEnableMachineLearning)
    {
        InitializeMLSystem();
    }

    // Setup adaptation templates
    SetupAdaptationTemplates();

    // Start AI update timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            AIUpdateTimer,
            [this]()
            {
                UpdateAILearning(AIUpdateFrequency);
            },
            AIUpdateFrequency,
            true // Looping
        );

        // Start ML training timer (every 5 minutes)
        if (bEnableMachineLearning)
        {
            GetWorld()->GetTimerManager().SetTimer(
                MLTrainingTimer,
                [this]()
                {
                    TrainAIModel();
                },
                300.0f, // 5 minutes
                true    // Looping
            );
        }

        // Start performance optimization timer (every 30 seconds)
        if (bEnablePerformanceScaling)
        {
            GetWorld()->GetTimerManager().SetTimer(
                PerformanceOptimizationTimer,
                [this]()
                {
                    OptimizeAIPerformance();
                },
                30.0f, // 30 seconds
                true   // Looping
            );
        }
    }

    // Find and register jungle creatures
    RegisterJungleCreatures();

    // Start monitoring active players
    StartMonitoringActivePlayers();

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptive Jungle AI system initialized successfully"));
}

void AAuracronAdaptiveJungleAI::UpdateAILearning(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update AI learning using UE 5.6 learning system
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Collect behavior data from monitored players
    for (APawn* Player : MonitoredPlayers)
    {
        if (Player)
        {
            CollectPlayerBehaviorData(Player);
        }
    }

    // Process collected data
    ProcessBehaviorData();

    // Apply adaptations based on learned patterns
    for (const auto& AnalysisPair : PlayerAnalyses)
    {
        APawn* Player = AnalysisPair.Key;
        const FAuracronPlayerBehaviorAnalysis& Analysis = AnalysisPair.Value;
        
        if (Player && Analysis.AnalysisConfidence > 0.6f)
        {
            ApplyCreatureAdaptations(Analysis);
        }
    }

    // Update AI learning data
    AILearningData.TotalLearningSessions++;
    AILearningData.DataPointsCollected = TrainingDataset.Num();

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: AI learning updated - Sessions: %d, Data points: %d"), 
        AILearningData.TotalLearningSessions, AILearningData.DataPointsCollected);
}

FAuracronPlayerBehaviorAnalysis AAuracronAdaptiveJungleAI::AnalyzePlayerBehavior(APawn* Player)
{
    FAuracronPlayerBehaviorAnalysis Analysis;
    
    if (!Player)
    {
        return Analysis;
    }

    // Analyze player behavior using UE 5.6 analysis system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Analyzing behavior for player %s"), *Player->GetName());

    // Set basic analysis data
    Analysis.PlayerID = Player->GetName();
    Analysis.LastAnalysisTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Analyze different behavior aspects
    AnalyzePlayerMovementPatterns(Player, Analysis);
    AnalyzePlayerCombatBehavior(Player, Analysis);
    AnalyzePlayerSocialBehavior(Player, Analysis);
    AnalyzePlayerExplorationBehavior(Player, Analysis);

    // Determine dominant pattern
    Analysis.DominantPattern = DetermineDominantPattern(Analysis);

    // Calculate overall analysis confidence
    Analysis.AnalysisConfidence = CalculateAnalysisConfidence(Analysis);

    // Store analysis
    PlayerAnalyses.Add(Player, Analysis);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s analysis complete - Pattern: %s, Confidence: %.2f"), 
        *Player->GetName(), *UEnum::GetValueAsString(Analysis.DominantPattern), Analysis.AnalysisConfidence);

    return Analysis;
}

void AAuracronAdaptiveJungleAI::ApplyCreatureAdaptations(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    // Apply creature adaptations based on player analysis using UE 5.6 adaptation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying creature adaptations for pattern %s"), 
        *UEnum::GetValueAsString(PlayerAnalysis.DominantPattern));

    // Find creatures near the player
    APawn* Player = FindPlayerByID(PlayerAnalysis.PlayerID);
    if (!Player)
    {
        return;
    }

    TArray<AAuracronJungleCreature*> NearbyCreatures = GetCreaturesNearPlayer(Player, MonitoringRadius);
    
    for (AAuracronJungleCreature* Creature : NearbyCreatures)
    {
        if (Creature && ShouldAdaptCreature(Creature, PlayerAnalysis))
        {
            AdaptCreatureToPlayer(Creature, Player);
        }
    }

    TotalAdaptationsApplied++;
}

FAuracronJungleAILearningData AAuracronAdaptiveJungleAI::GetAILearningData() const
{
    return AILearningData;
}

// === Player Behavior Analysis Implementation ===

void AAuracronAdaptiveJungleAI::StartPlayerMonitoring(APawn* Player)
{
    if (!Player || MonitoredPlayers.Contains(Player))
    {
        return;
    }

    // Start monitoring player using UE 5.6 monitoring system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting behavior monitoring for player %s"), *Player->GetName());

    MonitoredPlayers.Add(Player);

    // Initialize player tracking data
    FString PlayerID = Player->GetName();
    PlayerMovementHistory.Add(PlayerID, TArray<FVector>());
    PlayerActionHistory.Add(PlayerID, TArray<float>());
    PlayerSkillEstimates.Add(PlayerID, 0.5f); // Start with neutral skill estimate

    // Perform initial analysis
    FAuracronPlayerBehaviorAnalysis InitialAnalysis = AnalyzePlayerBehavior(Player);
    PlayerAnalyses.Add(Player, InitialAnalysis);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s monitoring started"), *Player->GetName());
}

void AAuracronAdaptiveJungleAI::StopPlayerMonitoring(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Stop monitoring player using UE 5.6 cleanup system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Stopping behavior monitoring for player %s"), *Player->GetName());

    MonitoredPlayers.Remove(Player);
    PlayerAnalyses.Remove(Player);

    // Clean up tracking data
    FString PlayerID = Player->GetName();
    PlayerMovementHistory.Remove(PlayerID);
    PlayerActionHistory.Remove(PlayerID);
    PlayerSkillEstimates.Remove(PlayerID);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s monitoring stopped"), *Player->GetName());
}

void AAuracronAdaptiveJungleAI::UpdatePlayerTracking(APawn* Player, float DeltaTime)
{
    if (!Player)
    {
        return;
    }

    // Update player tracking using UE 5.6 tracking system
    FString PlayerID = Player->GetName();

    // Track movement patterns
    TArray<FVector>* MovementHistory = PlayerMovementHistory.Find(PlayerID);
    if (MovementHistory)
    {
        MovementHistory->Add(Player->GetActorLocation());

        // Limit history size for performance
        if (MovementHistory->Num() > 100)
        {
            MovementHistory->RemoveAt(0, 20); // Remove oldest 20 entries
        }
    }

    // Track action frequency
    TArray<float>* ActionHistory = PlayerActionHistory.Find(PlayerID);
    if (ActionHistory)
    {
        // Simple action frequency based on input activity
        float ActionFrequency = CalculatePlayerActionFrequency(Player);
        ActionHistory->Add(ActionFrequency);

        // Limit history size
        if (ActionHistory->Num() > 60)
        {
            ActionHistory->RemoveAt(0, 10); // Remove oldest 10 entries
        }
    }

    // Update skill estimate
    UpdatePlayerSkillEstimate(Player);

    // Update behavior analysis periodically
    FAuracronPlayerBehaviorAnalysis* CurrentAnalysis = PlayerAnalyses.Find(Player);
    if (CurrentAnalysis)
    {
        float TimeSinceLastAnalysis = (GetWorld()->GetTimeSeconds()) - CurrentAnalysis->LastAnalysisTime;
        if (TimeSinceLastAnalysis > 30.0f) // Re-analyze every 30 seconds
        {
            *CurrentAnalysis = AnalyzePlayerBehavior(Player);
        }
    }
}

EPlayerBehaviorPattern AAuracronAdaptiveJungleAI::GetPlayerBehaviorPattern(APawn* Player) const
{
    if (const FAuracronPlayerBehaviorAnalysis* Analysis = PlayerAnalyses.Find(Player))
    {
        return Analysis->DominantPattern;
    }

    return EPlayerBehaviorPattern::Cautious; // Default pattern
}

// === Player Analysis Implementation ===

void AAuracronAdaptiveJungleAI::AnalyzePlayerMovementPatterns(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis)
{
    if (!Player)
    {
        return;
    }

    // Analyze player movement patterns using UE 5.6 movement analysis
    FString PlayerID = Player->GetName();
    TArray<FVector>* MovementHistory = PlayerMovementHistory.Find(PlayerID);

    if (!MovementHistory || MovementHistory->Num() < 5)
    {
        return; // Insufficient data
    }

    // Calculate movement metrics
    float TotalDistance = 0.0f;
    float MaxSpeed = 0.0f;
    float DirectionChanges = 0.0f;
    FVector PreviousDirection = FVector::ZeroVector;

    for (int32 i = 1; i < MovementHistory->Num(); i++)
    {
        FVector CurrentPos = (*MovementHistory)[i];
        FVector PreviousPos = (*MovementHistory)[i - 1];

        float SegmentDistance = FVector::Dist(CurrentPos, PreviousPos);
        TotalDistance += SegmentDistance;

        // Calculate speed (approximate)
        float SegmentSpeed = SegmentDistance / AIUpdateFrequency;
        MaxSpeed = FMath::Max(MaxSpeed, SegmentSpeed);

        // Calculate direction changes
        FVector CurrentDirection = (CurrentPos - PreviousPos).GetSafeNormal();
        if (!PreviousDirection.IsZero())
        {
            float DirectionDot = FVector::DotProduct(CurrentDirection, PreviousDirection);
            if (DirectionDot < 0.8f) // Significant direction change
            {
                DirectionChanges++;
            }
        }
        PreviousDirection = CurrentDirection;
    }

    // Analyze movement characteristics
    float AverageSpeed = TotalDistance / (MovementHistory->Num() * AIUpdateFrequency);
    float DirectionChangeRate = DirectionChanges / MovementHistory->Num();

    // Determine movement-based behavior traits
    if (AverageSpeed > 800.0f && DirectionChangeRate < 0.2f)
    {
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Efficient);
        Analysis.AggressionLevel += 0.2f;
    }
    else if (DirectionChangeRate > 0.5f)
    {
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Exploratory);
        Analysis.ExplorationTendency += 0.3f;
    }
    else if (AverageSpeed < 200.0f)
    {
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Cautious);
        Analysis.RiskTolerance -= 0.2f;
    }

    // Update predictability based on movement consistency
    Analysis.PredictabilityScore = 1.0f - DirectionChangeRate;
}

void AAuracronAdaptiveJungleAI::AnalyzePlayerCombatBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis)
{
    if (!Player)
    {
        return;
    }

    // Analyze player combat behavior using UE 5.6 combat analysis
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayTagContainer PlayerTags;
        ASC->GetOwnedGameplayTags(PlayerTags);

        // Check for combat-related tags
        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("State.Combat"))))
        {
            Analysis.AggressionLevel += 0.3f;
            Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Aggressive);
        }

        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("State.Stealth"))))
        {
            Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Stealth);
            Analysis.RiskTolerance -= 0.1f;
        }

        // Analyze ability usage patterns
        float AbilityUsageFrequency = CalculateAbilityUsageFrequency(Player);
        if (AbilityUsageFrequency > 0.8f)
        {
            Analysis.SkillLevel += 0.2f;
            Analysis.AggressionLevel += 0.1f;
        }
        else if (AbilityUsageFrequency < 0.3f)
        {
            Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Cautious);
        }
    }

    // Analyze health management behavior
    float HealthPercentage = GetPlayerHealthPercentage(Player);
    if (HealthPercentage < 0.5f)
    {
        // Player continues with low health - high risk tolerance
        Analysis.RiskTolerance += 0.2f;
    }
    else if (HealthPercentage > 0.9f)
    {
        // Player maintains high health - cautious or skilled
        Analysis.SkillLevel += 0.1f;
    }
}

void AAuracronAdaptiveJungleAI::AnalyzePlayerSocialBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis)
{
    if (!Player)
    {
        return;
    }

    // Analyze player social behavior using UE 5.6 social analysis

    // Check for nearby players
    TArray<APawn*> NearbyPlayers = GetPlayersNearLocation(Player->GetActorLocation(), 1000.0f);

    if (NearbyPlayers.Num() > 1) // Player + others
    {
        Analysis.SocialFrequency += 0.3f;
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Social);

        // Analyze group behavior
        bool bIsGroupLeader = IsPlayerGroupLeader(Player, NearbyPlayers);
        if (bIsGroupLeader)
        {
            Analysis.SkillLevel += 0.2f;
            Analysis.AggressionLevel += 0.1f;
        }
    }
    else
    {
        // Solo player
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Stealth);
        Analysis.RiskTolerance -= 0.1f;
    }
}

void AAuracronAdaptiveJungleAI::AnalyzePlayerExplorationBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis)
{
    if (!Player)
    {
        return;
    }

    // Analyze player exploration behavior using UE 5.6 exploration analysis
    FString PlayerID = Player->GetName();
    TArray<FVector>* MovementHistory = PlayerMovementHistory.Find(PlayerID);

    if (!MovementHistory || MovementHistory->Num() < 10)
    {
        return;
    }

    // Calculate exploration metrics
    TSet<FIntPoint> VisitedGridCells;
    float TotalExplorationArea = 0.0f;

    for (const FVector& Position : *MovementHistory)
    {
        // Convert to grid cell (1000x1000 unit cells)
        FIntPoint GridCell(
            FMath::FloorToInt(Position.X / 1000.0f),
            FMath::FloorToInt(Position.Y / 1000.0f)
        );
        VisitedGridCells.Add(GridCell);
    }

    TotalExplorationArea = VisitedGridCells.Num() * 1000000.0f; // Area in square units

    // Determine exploration tendency
    float ExplorationScore = FMath::Clamp(TotalExplorationArea / 10000000.0f, 0.0f, 1.0f); // Normalize to 10M sq units
    Analysis.ExplorationTendency = ExplorationScore;

    if (ExplorationScore > 0.7f)
    {
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Exploratory);
    }
    else if (ExplorationScore < 0.3f)
    {
        Analysis.SecondaryPatterns.AddUnique(EPlayerBehaviorPattern::Territorial);
    }
}

// === Creature Adaptation Implementation ===

void AAuracronAdaptiveJungleAI::AdaptCreatureToPlayer(AAuracronJungleCreature* Creature, APawn* Player)
{
    if (!Creature || !Player)
    {
        return;
    }

    // Adapt creature to player using UE 5.6 adaptation system
    const FAuracronPlayerBehaviorAnalysis* PlayerAnalysis = PlayerAnalyses.Find(Player);
    if (!PlayerAnalysis)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adapting creature %s to player %s (Pattern: %s)"),
        *Creature->GetName(), *Player->GetName(), *UEnum::GetValueAsString(PlayerAnalysis->DominantPattern));

    // Determine optimal adaptation type
    ECreatureAdaptationType AdaptationType = DetermineOptimalAdaptationType(*PlayerAnalysis);

    // Get adaptation template
    FAuracronCreatureAdaptation* AdaptationTemplate = AdaptationTemplates.Find(PlayerAnalysis->DominantPattern);
    if (!AdaptationTemplate)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No adaptation template found for pattern %s"),
            *UEnum::GetValueAsString(PlayerAnalysis->DominantPattern));
        return;
    }

    // Create customized adaptation
    FAuracronCreatureAdaptation CustomAdaptation = *AdaptationTemplate;
    CustomAdaptation.AdaptationStrength = CalculateAdaptationStrength(*PlayerAnalysis);
    CustomAdaptation.AdaptationType = AdaptationType;

    // Apply adaptation based on type
    switch (AdaptationType)
    {
        case ECreatureAdaptationType::Behavioral:
            ApplyBehavioralAdaptation(Creature, *PlayerAnalysis);
            break;
        case ECreatureAdaptationType::Statistical:
            ApplyStatisticalAdaptation(Creature, *PlayerAnalysis);
            break;
        case ECreatureAdaptationType::Environmental:
            ApplyEnvironmentalAdaptation(Creature, *PlayerAnalysis);
            break;
        case ECreatureAdaptationType::Social:
            ApplySocialAdaptation(Creature, *PlayerAnalysis);
            break;
        case ECreatureAdaptationType::Tactical:
            ApplyTacticalAdaptation(Creature, *PlayerAnalysis);
            break;
        default:
            break;
    }

    // Store active adaptation
    ActiveAdaptations.Add(Creature, CustomAdaptation);

    // Update creature AI behavior
    UpdateCreatureAIBehavior(Creature, CustomAdaptation);

    // Trigger adaptation event
    OnCreatureAdaptationApplied(Creature, AdaptationType);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature adaptation applied - Type: %s, Strength: %.2f"),
        *UEnum::GetValueAsString(AdaptationType), CustomAdaptation.AdaptationStrength);
}

void AAuracronAdaptiveJungleAI::RemoveCreatureAdaptations(AAuracronJungleCreature* Creature)
{
    if (!Creature)
    {
        return;
    }

    // Remove creature adaptations using UE 5.6 cleanup system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removing adaptations from creature %s"), *Creature->GetName());

    // Remove from active adaptations
    ActiveAdaptations.Remove(Creature);

    // Reset creature AI to default behavior
    ResetCreatureToDefaultBehavior(Creature);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature adaptations removed"));
}

void AAuracronAdaptiveJungleAI::UpdateCreatureAIBehavior(AAuracronJungleCreature* Creature, const FAuracronCreatureAdaptation& Adaptation)
{
    if (!Creature)
    {
        return;
    }

    // Update creature AI behavior using UE 5.6 AI behavior system
    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        // Update behavior tree if specified
        if (Adaptation.AdaptedBehaviorTree)
        {
            if (UBehaviorTreeComponent* BehaviorComp = AIController->FindComponentByClass<UBehaviorTreeComponent>())
            {
                BehaviorComp->StartTree(*Adaptation.AdaptedBehaviorTree);
            }
        }

        // Update blackboard values
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            for (const auto& Override : Adaptation.BlackboardOverrides)
            {
                FName KeyName(*Override.Key);
                BlackboardComp->SetValueAsFloat(KeyName, Override.Value);
            }
        }

        // Apply gameplay effects
        if (UAbilitySystemComponent* ASC = Creature->FindComponentByClass<UAbilitySystemComponent>())
        {
            for (TSubclassOf<UGameplayEffect> EffectClass : Adaptation.AdaptationEffects)
            {
                if (EffectClass)
                {
                    FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
                    EffectContext.AddSourceObject(this);

                    UGameplayEffect* GameplayEffect = EffectClass->GetDefaultObject<UGameplayEffect>();
                    FGameplayEffectSpec EffectSpec(GameplayEffect, EffectContext, Adaptation.AdaptationStrength);
                    EffectSpec.SetDuration(Adaptation.AdaptationDuration, false);

                    ASC->ApplyGameplayEffectSpecToSelf(EffectSpec);
                }
            }
        }
    }
}

// === Creature Management ===

void AAuracronAdaptiveJungleAI::RegisterCreature(AAuracronJungleCreature* Creature)
{
    if (!Creature)
    {
        return;
    }

    if (!ManagedCreatures.Contains(Creature))
    {
        ManagedCreatures.Add(Creature);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature registered with AI system"));
    }
}

void AAuracronAdaptiveJungleAI::UnregisterCreature(AAuracronJungleCreature* Creature)
{
    if (!Creature)
    {
        return;
    }

    ManagedCreatures.Remove(Creature);
    ActiveAdaptations.Remove(Creature);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature unregistered from AI system"));
}

bool AAuracronAdaptiveJungleAI::IsCreatureManaged(AAuracronJungleCreature* Creature) const
{
    return Creature && ManagedCreatures.Contains(Creature);
}

// === Adaptation Type Implementations ===

void AAuracronAdaptiveJungleAI::ApplyBehavioralAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    if (!Creature)
    {
        return;
    }

    // Apply behavioral adaptation using UE 5.6 behavior modification
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying behavioral adaptation to %s"), *Creature->GetName());

    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            // Adapt based on player's dominant pattern
            switch (PlayerAnalysis.DominantPattern)
            {
                case EPlayerBehaviorPattern::Aggressive:
                    // Make creature more defensive
                    BlackboardComp->SetValueAsFloat(TEXT("DefensiveStance"), 1.5f);
                    BlackboardComp->SetValueAsFloat(TEXT("FleeThreshold"), 0.3f);
                    BlackboardComp->SetValueAsFloat(TEXT("GroupUpTendency"), 1.2f);
                    break;

                case EPlayerBehaviorPattern::Cautious:
                    // Make creature more aggressive to challenge cautious players
                    BlackboardComp->SetValueAsFloat(TEXT("AggressionLevel"), 1.3f);
                    BlackboardComp->SetValueAsFloat(TEXT("PursuitDistance"), 1500.0f);
                    BlackboardComp->SetValueAsFloat(TEXT("AttackFrequency"), 1.4f);
                    break;

                case EPlayerBehaviorPattern::Exploratory:
                    // Make creature more territorial
                    BlackboardComp->SetValueAsFloat(TEXT("TerritorialRadius"), 800.0f);
                    BlackboardComp->SetValueAsFloat(TEXT("PatrolDistance"), 1200.0f);
                    BlackboardComp->SetValueAsFloat(TEXT("AlertnessLevel"), 1.3f);
                    break;

                case EPlayerBehaviorPattern::Stealth:
                    // Enhance creature perception
                    BlackboardComp->SetValueAsFloat(TEXT("PerceptionRadius"), 1.5f);
                    BlackboardComp->SetValueAsFloat(TEXT("HearingRange"), 1.8f);
                    BlackboardComp->SetValueAsFloat(TEXT("InvestigationTime"), 2.0f);
                    break;

                default:
                    // Default balanced adaptation
                    BlackboardComp->SetValueAsFloat(TEXT("AdaptationLevel"), 1.0f);
                    break;
            }

            // Apply skill-based adaptations
            float SkillMultiplier = FMath::Clamp(PlayerAnalysis.SkillLevel, 0.5f, 1.5f);
            BlackboardComp->SetValueAsFloat(TEXT("DifficultyMultiplier"), SkillMultiplier);
        }
    }
}

void AAuracronAdaptiveJungleAI::ApplyStatisticalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    if (!Creature)
    {
        return;
    }

    // Apply statistical adaptation using UE 5.6 stat modification
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying statistical adaptation to %s"), *Creature->GetName());

    if (UAbilitySystemComponent* ASC = Creature->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Create stat adaptation effect
        static const FSoftClassPath StatAdaptationPath(TEXT("/Game/GameplayEffects/AI/GE_CreatureStatAdaptation.GE_CreatureStatAdaptation_C"));
        if (TSubclassOf<UGameplayEffect> StatAdaptationEffect = StatAdaptationPath.TryLoadClass<UGameplayEffect>())
        {
            UGameplayEffect* GameplayEffect = StatAdaptationEffect->GetDefaultObject<UGameplayEffect>();
            FGameplayEffectSpec AdaptationSpec(GameplayEffect, EffectContext, 1.0f);

            // Adapt stats based on player behavior
            float HealthMultiplier = 1.0f;
            float DamageMultiplier = 1.0f;
            float SpeedMultiplier = 1.0f;
            float PerceptionMultiplier = 1.0f;

            // Adjust based on player skill and aggression
            if (PlayerAnalysis.SkillLevel > 0.7f)
            {
                HealthMultiplier = 1.3f;
                DamageMultiplier = 1.2f;
            }
            else if (PlayerAnalysis.SkillLevel < 0.4f)
            {
                HealthMultiplier = 0.8f;
                DamageMultiplier = 0.9f;
            }

            if (PlayerAnalysis.AggressionLevel > 0.7f)
            {
                SpeedMultiplier = 1.2f;
                PerceptionMultiplier = 1.1f;
            }

            // Set adaptation parameters
            AdaptationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Adaptation.Health.Multiplier")), HealthMultiplier);
            AdaptationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Adaptation.Damage.Multiplier")), DamageMultiplier);
            AdaptationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Adaptation.Speed.Multiplier")), SpeedMultiplier);
            AdaptationSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Adaptation.Perception.Multiplier")), PerceptionMultiplier);

            AdaptationSpec.SetDuration(300.0f, false); // 5 minute adaptation

            ASC->ApplyGameplayEffectSpecToSelf(AdaptationSpec);
        }
    }
}

void AAuracronAdaptiveJungleAI::ApplyEnvironmentalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    if (!Creature)
    {
        return;
    }

    // Apply environmental adaptation using UE 5.6 environmental system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying environmental adaptation to %s"), *Creature->GetName());

    // Adapt creature's environmental awareness
    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            // Adapt based on player exploration tendency
            if (PlayerAnalysis.ExplorationTendency > 0.6f)
            {
                // Player explores a lot - make creature more mobile
                BlackboardComp->SetValueAsFloat(TEXT("RoamingRadius"), 1500.0f);
                BlackboardComp->SetValueAsFloat(TEXT("RestTime"), 0.5f);
                BlackboardComp->SetValueAsFloat(TEXT("MovementSpeed"), 1.3f);
            }
            else if (PlayerAnalysis.ExplorationTendency < 0.4f)
            {
                // Player stays in area - make creature more territorial
                BlackboardComp->SetValueAsFloat(TEXT("TerritorialRadius"), 600.0f);
                BlackboardComp->SetValueAsFloat(TEXT("DefendIntensity"), 1.4f);
                BlackboardComp->SetValueAsFloat(TEXT("PatrolFrequency"), 1.5f);
            }

            // Adapt to player risk tolerance
            if (PlayerAnalysis.RiskTolerance > 0.7f)
            {
                // High risk player - make environment more challenging
                BlackboardComp->SetValueAsFloat(TEXT("EnvironmentalHazards"), 1.3f);
                BlackboardComp->SetValueAsFloat(TEXT("AmbushLikelihood"), 1.2f);
            }
            else if (PlayerAnalysis.RiskTolerance < 0.4f)
            {
                // Low risk player - reduce environmental pressure
                BlackboardComp->SetValueAsFloat(TEXT("EnvironmentalHazards"), 0.8f);
                BlackboardComp->SetValueAsFloat(TEXT("SafeZoneRadius"), 1.2f);
            }
        }
    }
}

void AAuracronAdaptiveJungleAI::ApplySocialAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    if (!Creature)
    {
        return;
    }

    // Apply social adaptation using UE 5.6 social AI system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying social adaptation to %s"), *Creature->GetName());

    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            // Adapt based on player social frequency
            if (PlayerAnalysis.SocialFrequency > 0.6f)
            {
                // Social player - make creatures coordinate more
                BlackboardComp->SetValueAsFloat(TEXT("PackCoordination"), 1.4f);
                BlackboardComp->SetValueAsFloat(TEXT("CommunicationRange"), 1200.0f);
                BlackboardComp->SetValueAsFloat(TEXT("GroupTactics"), 1.3f);
            }
            else
            {
                // Solo player - make creatures more independent
                BlackboardComp->SetValueAsFloat(TEXT("IndependentAction"), 1.3f);
                BlackboardComp->SetValueAsFloat(TEXT("SoloHunting"), 1.2f);
                BlackboardComp->SetValueAsFloat(TEXT("AmbushPreference"), 1.1f);
            }
        }
    }
}

void AAuracronAdaptiveJungleAI::ApplyTacticalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    if (!Creature)
    {
        return;
    }

    // Apply tactical adaptation using UE 5.6 tactical AI system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying tactical adaptation to %s"), *Creature->GetName());

    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            // Adapt tactics based on player predictability
            if (PlayerAnalysis.PredictabilityScore > 0.7f)
            {
                // Predictable player - use counter-tactics
                BlackboardComp->SetValueAsFloat(TEXT("CounterTactics"), 1.5f);
                BlackboardComp->SetValueAsFloat(TEXT("PredictionAccuracy"), 1.3f);
                BlackboardComp->SetValueAsFloat(TEXT("TacticalPlanning"), 1.4f);
            }
            else if (PlayerAnalysis.PredictabilityScore < 0.4f)
            {
                // Unpredictable player - use adaptive tactics
                BlackboardComp->SetValueAsFloat(TEXT("AdaptiveTactics"), 1.4f);
                BlackboardComp->SetValueAsFloat(TEXT("FlexibleResponse"), 1.3f);
                BlackboardComp->SetValueAsFloat(TEXT("ReactiveStrategy"), 1.2f);
            }

            // Adapt based on player aggression
            if (PlayerAnalysis.AggressionLevel > 0.6f)
            {
                // Aggressive player - use evasive tactics
                BlackboardComp->SetValueAsFloat(TEXT("EvasiveTactics"), 1.3f);
                BlackboardComp->SetValueAsFloat(TEXT("HitAndRun"), 1.2f);
            }
            else
            {
                // Passive player - use pressure tactics
                BlackboardComp->SetValueAsFloat(TEXT("PressureTactics"), 1.2f);
                BlackboardComp->SetValueAsFloat(TEXT("ConstantThreat"), 1.1f);
            }
        }
    }
}

// === Environmental Response Implementation ===

void AAuracronAdaptiveJungleAI::AdaptEnvironmentToPlayer(APawn* Player, const FVector& Location)
{
    if (!Player || !bEnableEnvironmentalAdaptation)
    {
        return;
    }

    // Adapt environment to player using UE 5.6 environmental adaptation
    const FAuracronPlayerBehaviorAnalysis* PlayerAnalysis = PlayerAnalyses.Find(Player);
    if (!PlayerAnalysis)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adapting environment at %s for player %s"),
        *Location.ToString(), *Player->GetName());

    // Update jungle ambient response
    UpdateJungleAmbientResponse(*PlayerAnalysis);

    // Update creature spawn rates
    UpdateCreatureSpawnRates(*PlayerAnalysis);

    // Update environmental hazards
    UpdateEnvironmentalHazards(*PlayerAnalysis);

    // Update resource distribution
    UpdateResourceDistribution(*PlayerAnalysis);
}

void AAuracronAdaptiveJungleAI::UpdateEnvironmentalResponses(float DeltaTime)
{
    if (!bEnableEnvironmentalAdaptation)
    {
        return;
    }

    // Update environmental responses using UE 5.6 environmental system
    for (const auto& AnalysisPair : PlayerAnalyses)
    {
        APawn* Player = AnalysisPair.Key;
        const FAuracronPlayerBehaviorAnalysis& Analysis = AnalysisPair.Value;

        if (Player && Analysis.AnalysisConfidence > 0.5f)
        {
            AdaptEnvironmentToPlayer(Player, Player->GetActorLocation());
        }
    }
}

// === Machine Learning Implementation ===

void AAuracronAdaptiveJungleAI::TrainAIModel()
{
    if (!bEnableMachineLearning || TrainingDataset.Num() < 50)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Insufficient training data for AI model (%d samples, need 50)"), TrainingDataset.Num());
        return;
    }

    // Train AI model using UE 5.6 ML framework
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Training adaptive AI model with %d samples"), TrainingDataset.Num());

    // Simple ML training simulation using statistical analysis
    float TotalAccuracy = 0.0f;
    int32 ValidationSamples = FMath::Min(TrainingDataset.Num() / 4, 50); // 25% for validation

    for (int32 i = 0; i < ValidationSamples; i++)
    {
        const FAuracronPlayerBehaviorAnalysis& Sample = TrainingDataset[TrainingDataset.Num() - 1 - i];

        // Predict behavior pattern based on metrics
        EPlayerBehaviorPattern PredictedPattern = PredictBehaviorPattern(Sample);
        EPlayerBehaviorPattern ActualPattern = Sample.DominantPattern;

        // Calculate accuracy (1.0 for exact match, partial credit for related patterns)
        float SampleAccuracy = CalculatePatternSimilarity(PredictedPattern, ActualPattern);
        TotalAccuracy += SampleAccuracy;
    }

    AILearningData.ModelAccuracy = TotalAccuracy / ValidationSamples;
    AILearningData.TrainingIterations++;
    AILearningData.LastTrainingTime = GetWorld()->GetTimeSeconds();
    bMLModelTrained = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI model training completed - Accuracy: %.3f, Iteration: %d"),
        AILearningData.ModelAccuracy, AILearningData.TrainingIterations);

    // Trigger training event
    OnAIModelRetrained(AILearningData.ModelAccuracy);
}

bool AAuracronAdaptiveJungleAI::ValidateAIModel()
{
    if (!bMLModelTrained)
    {
        return false;
    }

    // Validate AI model performance using UE 5.6 validation system
    bool bIsValid = AILearningData.ModelAccuracy > 0.6f && AILearningData.TrainingIterations > 0;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI model validation %s - Accuracy: %.3f"),
        bIsValid ? TEXT("PASSED") : TEXT("FAILED"), AILearningData.ModelAccuracy);

    return bIsValid;
}

void AAuracronAdaptiveJungleAI::ResetAILearning()
{
    // Reset AI learning data using UE 5.6 reset system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Resetting AI learning data"));

    AILearningData = FAuracronJungleAILearningData();
    TrainingDataset.Empty();
    PlayerMovementHistory.Empty();
    PlayerActionHistory.Empty();
    PlayerSkillEstimates.Empty();
    bMLModelTrained = false;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI learning data reset complete"));
}

// === ML System Implementation ===

void AAuracronAdaptiveJungleAI::InitializeMLSystem()
{
    // Initialize machine learning system using UE 5.6 ML framework
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing ML system for adaptive jungle AI"));

    // Load existing ML data if available
    LoadMLData();

    // Initialize training parameters
    AILearningData.LearningRate = BaseLearningRate;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: ML system initialized"));
}

void AAuracronAdaptiveJungleAI::CollectPlayerBehaviorData(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Collect player behavior data for ML training
    FAuracronPlayerBehaviorAnalysis CurrentAnalysis = AnalyzePlayerBehavior(Player);

    // Add to training dataset if analysis is confident enough
    if (CurrentAnalysis.AnalysisConfidence > 0.7f)
    {
        TrainingDataset.Add(CurrentAnalysis);

        // Limit dataset size for performance
        if (TrainingDataset.Num() > 1000)
        {
            // Remove oldest 20% of data
            int32 RemoveCount = TrainingDataset.Num() / 5;
            TrainingDataset.RemoveAt(0, RemoveCount);
        }
    }
}

void AAuracronAdaptiveJungleAI::ProcessBehaviorData()
{
    // Process collected behavior data using UE 5.6 data processing
    if (TrainingDataset.IsEmpty())
    {
        return;
    }

    // Update player skill estimates based on recent data
    for (const FAuracronPlayerBehaviorAnalysis& Analysis : TrainingDataset)
    {
        if (PlayerSkillEstimates.Contains(Analysis.PlayerID))
        {
            float* CurrentSkill = PlayerSkillEstimates.Find(Analysis.PlayerID);
            if (CurrentSkill)
            {
                // Exponential moving average for skill estimation
                *CurrentSkill = (*CurrentSkill * 0.9f) + (Analysis.SkillLevel * 0.1f);
            }
        }
    }

    // Calculate adaptation success rate
    UpdateAdaptationSuccessRate();
}

void AAuracronAdaptiveJungleAI::UpdateMLModel()
{
    // Update ML model with new data using UE 5.6 model update system
    if (TrainingDataset.Num() > AILearningData.DataPointsCollected + 10)
    {
        // Enough new data to update model
        TrainAIModel();
    }
}

void AAuracronAdaptiveJungleAI::ValidateMLPerformance()
{
    // Validate ML performance using UE 5.6 validation system
    if (bMLModelTrained)
    {
        bool bPerformanceGood = ValidateAIModel();

        if (!bPerformanceGood && AILearningData.ModelAccuracy < 0.5f)
        {
            // Model performance is poor, reset and retrain
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: AI model performance poor, resetting"));
            ResetAILearning();
        }
    }
}

// === Environmental Response Implementation ===

void AAuracronAdaptiveJungleAI::UpdateJungleAmbientResponse(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    // Update jungle ambient response using UE 5.6 ambient system
    if (CachedRealmSubsystem)
    {
        // Adjust ambient creature activity based on player behavior
        float AmbientActivityLevel = 1.0f;

        if (PlayerAnalysis.AggressionLevel > 0.7f)
        {
            AmbientActivityLevel = 1.3f; // More active ambient for aggressive players
        }
        else if (PlayerAnalysis.ExplorationTendency > 0.7f)
        {
            AmbientActivityLevel = 1.2f; // Slightly more active for explorers
        }
        else if (PlayerAnalysis.RiskTolerance < 0.4f)
        {
            AmbientActivityLevel = 0.8f; // Less active for cautious players
        }

        // Apply ambient adjustment to realm subsystem
        // TODO: Implement SetAmbientCreatureActivity method in UAuracronDynamicRealmSubsystem
        // CachedRealmSubsystem->SetAmbientCreatureActivity(AmbientActivityLevel);
    }
}

void AAuracronAdaptiveJungleAI::UpdateCreatureSpawnRates(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    // Update creature spawn rates using UE 5.6 spawn system
    if (CachedRealmSubsystem)
    {
        float SpawnRateMultiplier = 1.0f;

        // Adjust spawn rates based on player skill and aggression
        if (PlayerAnalysis.SkillLevel > 0.8f)
        {
            SpawnRateMultiplier = 1.4f; // More creatures for skilled players
        }
        else if (PlayerAnalysis.SkillLevel < 0.3f)
        {
            SpawnRateMultiplier = 0.7f; // Fewer creatures for new players
        }

        if (PlayerAnalysis.AggressionLevel > 0.7f)
        {
            SpawnRateMultiplier *= 1.2f; // Additional creatures for aggressive players
        }

        // Apply spawn rate adjustment
        CachedRealmSubsystem->SetCreatureSpawnRateMultiplier(SpawnRateMultiplier);
    }
}

void AAuracronAdaptiveJungleAI::UpdateEnvironmentalHazards(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    // Update environmental hazards using UE 5.6 hazard system
    if (CachedRealmSubsystem)
    {
        float HazardIntensity = 1.0f;

        // Adjust hazards based on player risk tolerance
        if (PlayerAnalysis.RiskTolerance > 0.8f)
        {
            HazardIntensity = 1.5f; // More hazards for risk-taking players
        }
        else if (PlayerAnalysis.RiskTolerance < 0.3f)
        {
            HazardIntensity = 0.6f; // Fewer hazards for cautious players
        }

        // Apply hazard adjustment
        CachedRealmSubsystem->SetEnvironmentalHazardIntensity(HazardIntensity);
    }
}

void AAuracronAdaptiveJungleAI::UpdateResourceDistribution(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis)
{
    // Update resource distribution using UE 5.6 resource system
    if (CachedRealmSubsystem)
    {
        float ResourceDensity = 1.0f;

        // Adjust resources based on player exploration
        if (PlayerAnalysis.ExplorationTendency > 0.7f)
        {
            ResourceDensity = 0.9f; // Slightly fewer resources for explorers (reward exploration)
        }
        else if (PlayerAnalysis.ExplorationTendency < 0.4f)
        {
            ResourceDensity = 1.2f; // More resources for non-explorers (encourage exploration)
        }

        // Apply resource adjustment
        CachedRealmSubsystem->SetResourceDensityMultiplier(ResourceDensity);
    }
}

// === Utility Methods Implementation ===

float AAuracronAdaptiveJungleAI::CalculateAdaptationStrength(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const
{
    // Calculate adaptation strength based on player analysis
    float BaseStrength = 1.0f;

    // Increase strength for skilled players
    BaseStrength += (PlayerAnalysis.SkillLevel - 0.5f) * 0.8f;

    // Increase strength for aggressive players
    BaseStrength += (PlayerAnalysis.AggressionLevel - 0.5f) * 0.6f;

    // Adjust for analysis confidence
    BaseStrength *= PlayerAnalysis.AnalysisConfidence;

    return FMath::Clamp(BaseStrength, 0.5f, MaxAdaptationStrength);
}

ECreatureAdaptationType AAuracronAdaptiveJungleAI::DetermineOptimalAdaptationType(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const
{
    // Determine optimal adaptation type based on player behavior

    // High skill players get tactical adaptations
    if (PlayerAnalysis.SkillLevel > 0.8f)
    {
        return ECreatureAdaptationType::Tactical;
    }

    // Social players get social adaptations
    if (PlayerAnalysis.SocialFrequency > 0.6f)
    {
        return ECreatureAdaptationType::Social;
    }

    // Exploratory players get environmental adaptations
    if (PlayerAnalysis.ExplorationTendency > 0.7f)
    {
        return ECreatureAdaptationType::Environmental;
    }

    // Aggressive players get statistical adaptations
    if (PlayerAnalysis.AggressionLevel > 0.6f)
    {
        return ECreatureAdaptationType::Statistical;
    }

    // Default to behavioral adaptation
    return ECreatureAdaptationType::Behavioral;
}

bool AAuracronAdaptiveJungleAI::ShouldAdaptCreature(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const
{
    if (!Creature)
    {
        return false;
    }

    // Check if creature should be adapted

    // Don't adapt if already adapted recently
    if (const FAuracronCreatureAdaptation* ExistingAdaptation = ActiveAdaptations.Find(Creature))
    {
        float TimeSinceAdaptation = GetWorld()->GetTimeSeconds() - ExistingAdaptation->AdaptationDuration;
        if (TimeSinceAdaptation < 60.0f) // Wait at least 1 minute between adaptations
        {
            return false;
        }
    }

    // Don't adapt if analysis confidence is too low
    if (PlayerAnalysis.AnalysisConfidence < 0.6f)
    {
        return false;
    }

    // Check distance to player
    APawn* Player = FindPlayerByID(PlayerAnalysis.PlayerID);
    if (Player)
    {
        float Distance = FVector::Dist(Creature->GetActorLocation(), Player->GetActorLocation());
        if (Distance > MonitoringRadius)
        {
            return false;
        }
    }

    return true;
}

void AAuracronAdaptiveJungleAI::OptimizeAIPerformance()
{
    if (!bEnablePerformanceScaling)
    {
        return;
    }

    // Optimize AI performance using UE 5.6 performance optimization

    // Check frame rate and adjust AI complexity
    float CurrentFPS = 1.0f / GetWorld()->GetDeltaSeconds();

    if (CurrentFPS < 30.0f)
    {
        // Reduce AI complexity
        AIUpdateFrequency = FMath::Min(AIUpdateFrequency * 1.2f, 5.0f);

        // Reduce number of monitored players
        if (MonitoredPlayers.Num() > 8)
        {
            MonitoredPlayers.RemoveAt(8, MonitoredPlayers.Num() - 8);
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: AI performance optimized for low FPS (%.1f)"), CurrentFPS);
    }
    else if (CurrentFPS > 60.0f && AIUpdateFrequency > 1.0f)
    {
        // Increase AI complexity
        AIUpdateFrequency = FMath::Max(AIUpdateFrequency * 0.9f, 1.0f);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: AI performance increased for high FPS (%.1f)"), CurrentFPS);
    }
}

// === Helper Methods Implementation ===

EPlayerBehaviorPattern AAuracronAdaptiveJungleAI::DetermineDominantPattern(const FAuracronPlayerBehaviorAnalysis& Analysis) const
{
    // Determine dominant behavior pattern from analysis

    // Check for clear dominant patterns
    if (Analysis.AggressionLevel > 0.7f)
    {
        return EPlayerBehaviorPattern::Aggressive;
    }

    if (Analysis.ExplorationTendency > 0.7f)
    {
        return EPlayerBehaviorPattern::Exploratory;
    }

    if (Analysis.SocialFrequency > 0.6f)
    {
        return EPlayerBehaviorPattern::Social;
    }

    if (Analysis.RiskTolerance < 0.3f)
    {
        return EPlayerBehaviorPattern::Cautious;
    }

    if (Analysis.SkillLevel > 0.8f && Analysis.PredictabilityScore > 0.7f)
    {
        return EPlayerBehaviorPattern::Efficient;
    }

    if (Analysis.PredictabilityScore < 0.4f)
    {
        return EPlayerBehaviorPattern::Adaptive;
    }

    // Check secondary patterns
    if (!Analysis.SecondaryPatterns.IsEmpty())
    {
        return Analysis.SecondaryPatterns[0];
    }

    return EPlayerBehaviorPattern::Cautious; // Default
}

float AAuracronAdaptiveJungleAI::CalculateAnalysisConfidence(const FAuracronPlayerBehaviorAnalysis& Analysis) const
{
    // Calculate confidence in behavior analysis
    float Confidence = 0.5f; // Base confidence

    // Increase confidence based on data availability
    FString PlayerID = Analysis.PlayerID;

    if (const TArray<FVector>* MovementHistory = PlayerMovementHistory.Find(PlayerID))
    {
        Confidence += FMath::Clamp(MovementHistory->Num() / 50.0f, 0.0f, 0.3f);
    }

    if (const TArray<float>* ActionHistory = PlayerActionHistory.Find(PlayerID))
    {
        Confidence += FMath::Clamp(ActionHistory->Num() / 30.0f, 0.0f, 0.2f);
    }

    // Increase confidence for consistent patterns
    if (Analysis.SecondaryPatterns.Num() > 2)
    {
        Confidence += 0.1f;
    }

    return FMath::Clamp(Confidence, 0.0f, 1.0f);
}

EPlayerBehaviorPattern AAuracronAdaptiveJungleAI::PredictBehaviorPattern(const FAuracronPlayerBehaviorAnalysis& Analysis) const
{
    // Predict behavior pattern using trained model (simplified implementation)

    // In a full ML implementation, this would use neural network prediction
    // For now, use rule-based prediction similar to training

    float AggressionWeight = Analysis.AggressionLevel * 2.0f;
    float ExplorationWeight = Analysis.ExplorationTendency * 1.8f;
    float SocialWeight = Analysis.SocialFrequency * 1.5f;
    float CautionWeight = (1.0f - Analysis.RiskTolerance) * 1.6f;
    float EfficiencyWeight = Analysis.SkillLevel * Analysis.PredictabilityScore * 1.4f;

    // Find pattern with highest weight
    TMap<EPlayerBehaviorPattern, float> PatternWeights;
    PatternWeights.Add(EPlayerBehaviorPattern::Aggressive, AggressionWeight);
    PatternWeights.Add(EPlayerBehaviorPattern::Exploratory, ExplorationWeight);
    PatternWeights.Add(EPlayerBehaviorPattern::Social, SocialWeight);
    PatternWeights.Add(EPlayerBehaviorPattern::Cautious, CautionWeight);
    PatternWeights.Add(EPlayerBehaviorPattern::Efficient, EfficiencyWeight);

    EPlayerBehaviorPattern PredictedPattern = EPlayerBehaviorPattern::Cautious;
    float MaxWeight = 0.0f;

    for (const auto& WeightPair : PatternWeights)
    {
        if (WeightPair.Value > MaxWeight)
        {
            MaxWeight = WeightPair.Value;
            PredictedPattern = WeightPair.Key;
        }
    }

    return PredictedPattern;
}

float AAuracronAdaptiveJungleAI::CalculatePatternSimilarity(EPlayerBehaviorPattern Pattern1, EPlayerBehaviorPattern Pattern2) const
{
    // Calculate similarity between behavior patterns
    if (Pattern1 == Pattern2)
    {
        return 1.0f; // Exact match
    }

    // Define pattern relationships for partial credit
    TMap<EPlayerBehaviorPattern, TArray<EPlayerBehaviorPattern>> RelatedPatterns;
    RelatedPatterns.Add(EPlayerBehaviorPattern::Aggressive, {EPlayerBehaviorPattern::Territorial, EPlayerBehaviorPattern::Efficient});
    RelatedPatterns.Add(EPlayerBehaviorPattern::Cautious, {EPlayerBehaviorPattern::Stealth, EPlayerBehaviorPattern::Territorial});
    RelatedPatterns.Add(EPlayerBehaviorPattern::Exploratory, {EPlayerBehaviorPattern::Adaptive, EPlayerBehaviorPattern::Social});
    RelatedPatterns.Add(EPlayerBehaviorPattern::Social, {EPlayerBehaviorPattern::Exploratory, EPlayerBehaviorPattern::Adaptive});
    RelatedPatterns.Add(EPlayerBehaviorPattern::Stealth, {EPlayerBehaviorPattern::Cautious, EPlayerBehaviorPattern::Tactical});

    // Check for related patterns
    if (const TArray<EPlayerBehaviorPattern>* RelatedToPattern1 = RelatedPatterns.Find(Pattern1))
    {
        if (RelatedToPattern1->Contains(Pattern2))
        {
            return 0.6f; // Partial match for related patterns
        }
    }

    return 0.0f; // No similarity
}

// === Setup and Utility Implementation ===

void AAuracronAdaptiveJungleAI::SetupAdaptationTemplates()
{
    // Setup adaptation templates using UE 5.6 template system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up creature adaptation templates"));

    // Aggressive player template
    FAuracronCreatureAdaptation AggressiveTemplate;
    AggressiveTemplate.AdaptationType = ECreatureAdaptationType::Behavioral;
    AggressiveTemplate.TargetPattern = EPlayerBehaviorPattern::Aggressive;
    AggressiveTemplate.AdaptationStrength = 1.2f;
    AggressiveTemplate.AdaptationDuration = 180.0f;
    AggressiveTemplate.BlackboardOverrides.Add(TEXT("DefensiveStance"), 1.5f);
    AggressiveTemplate.BlackboardOverrides.Add(TEXT("FleeThreshold"), 0.3f);
    AdaptationTemplates.Add(EPlayerBehaviorPattern::Aggressive, AggressiveTemplate);

    // Cautious player template
    FAuracronCreatureAdaptation CautiousTemplate;
    CautiousTemplate.AdaptationType = ECreatureAdaptationType::Behavioral;
    CautiousTemplate.TargetPattern = EPlayerBehaviorPattern::Cautious;
    CautiousTemplate.AdaptationStrength = 1.1f;
    CautiousTemplate.AdaptationDuration = 120.0f;
    CautiousTemplate.BlackboardOverrides.Add(TEXT("AggressionLevel"), 1.3f);
    CautiousTemplate.BlackboardOverrides.Add(TEXT("PursuitDistance"), 1500.0f);
    AdaptationTemplates.Add(EPlayerBehaviorPattern::Cautious, CautiousTemplate);

    // Exploratory player template
    FAuracronCreatureAdaptation ExploratoryTemplate;
    ExploratoryTemplate.AdaptationType = ECreatureAdaptationType::Environmental;
    ExploratoryTemplate.TargetPattern = EPlayerBehaviorPattern::Exploratory;
    ExploratoryTemplate.AdaptationStrength = 1.0f;
    ExploratoryTemplate.AdaptationDuration = 240.0f;
    ExploratoryTemplate.BlackboardOverrides.Add(TEXT("TerritorialRadius"), 800.0f);
    ExploratoryTemplate.BlackboardOverrides.Add(TEXT("PatrolDistance"), 1200.0f);
    AdaptationTemplates.Add(EPlayerBehaviorPattern::Exploratory, ExploratoryTemplate);

    // Social player template
    FAuracronCreatureAdaptation SocialTemplate;
    SocialTemplate.AdaptationType = ECreatureAdaptationType::Social;
    SocialTemplate.TargetPattern = EPlayerBehaviorPattern::Social;
    SocialTemplate.AdaptationStrength = 1.3f;
    SocialTemplate.AdaptationDuration = 200.0f;
    SocialTemplate.BlackboardOverrides.Add(TEXT("PackCoordination"), 1.4f);
    SocialTemplate.BlackboardOverrides.Add(TEXT("GroupTactics"), 1.3f);
    AdaptationTemplates.Add(EPlayerBehaviorPattern::Social, SocialTemplate);

    // Stealth player template
    FAuracronCreatureAdaptation StealthTemplate;
    StealthTemplate.AdaptationType = ECreatureAdaptationType::Tactical;
    StealthTemplate.TargetPattern = EPlayerBehaviorPattern::Stealth;
    StealthTemplate.AdaptationStrength = 1.4f;
    StealthTemplate.AdaptationDuration = 150.0f;
    StealthTemplate.BlackboardOverrides.Add(TEXT("PerceptionRadius"), 1.5f);
    StealthTemplate.BlackboardOverrides.Add(TEXT("HearingRange"), 1.8f);
    AdaptationTemplates.Add(EPlayerBehaviorPattern::Stealth, StealthTemplate);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptation templates configured"));
}

void AAuracronAdaptiveJungleAI::RegisterJungleCreatures()
{
    if (!GetWorld())
    {
        return;
    }

    // Register jungle creatures using UE 5.6 actor registration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Registering jungle creatures for AI management"));

    ManagedCreatures.Empty();

    // Find all jungle creatures in the world
    for (TActorIterator<AAuracronJungleCreature> ActorItr(GetWorld()); ActorItr; ++ActorItr)
    {
        AAuracronJungleCreature* Creature = *ActorItr;
        if (Creature && IsValid(Creature))
        {
            ManagedCreatures.Add(Creature);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Registered %d jungle creatures"), ManagedCreatures.Num());
}

void AAuracronAdaptiveJungleAI::StartMonitoringActivePlayers()
{
    if (!GetWorld())
    {
        return;
    }

    // Start monitoring active players using UE 5.6 player monitoring
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting monitoring of active players"));

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            StartPlayerMonitoring(PC->GetPawn());
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Started monitoring %d players"), MonitoredPlayers.Num());
}

APawn* AAuracronAdaptiveJungleAI::FindPlayerByID(const FString& PlayerID) const
{
    // Find player by ID
    for (APawn* Player : MonitoredPlayers)
    {
        if (Player && Player->GetName() == PlayerID)
        {
            return Player;
        }
    }

    return nullptr;
}

TArray<AAuracronJungleCreature*> AAuracronAdaptiveJungleAI::GetCreaturesNearPlayer(APawn* Player, float Radius) const
{
    TArray<AAuracronJungleCreature*> NearbyCreatures;

    if (!Player)
    {
        return NearbyCreatures;
    }

    // Find creatures near player
    FVector PlayerLocation = Player->GetActorLocation();

    for (AAuracronJungleCreature* Creature : ManagedCreatures)
    {
        if (Creature && IsValid(Creature))
        {
            float Distance = FVector::Dist(Creature->GetActorLocation(), PlayerLocation);
            if (Distance <= Radius)
            {
                NearbyCreatures.Add(Creature);
            }
        }
    }

    return NearbyCreatures;
}

TArray<APawn*> AAuracronAdaptiveJungleAI::GetPlayersNearLocation(const FVector& Location, float Radius) const
{
    TArray<APawn*> NearbyPlayers;

    if (!GetWorld())
    {
        return NearbyPlayers;
    }

    // Find players near location
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            float Distance = FVector::Dist(PC->GetPawn()->GetActorLocation(), Location);
            if (Distance <= Radius)
            {
                NearbyPlayers.Add(PC->GetPawn());
            }
        }
    }

    return NearbyPlayers;
}

float AAuracronAdaptiveJungleAI::CalculatePlayerActionFrequency(APawn* Player) const
{
    if (!Player)
    {
        return 0.0f;
    }

    // Calculate player action frequency (simplified)
    float ActionFrequency = 0.5f; // Base frequency

    // Check input activity
    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        // Simple heuristic based on movement
        float MovementMagnitude = Player->GetVelocity().Size();
        ActionFrequency += FMath::Clamp(MovementMagnitude / 1000.0f, 0.0f, 0.3f);

        // Check for ability system activity
        if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Check for active abilities
            TArray<FGameplayAbilitySpec> ActiveAbilities = ASC->GetActivatableAbilities();
            ActionFrequency += FMath::Clamp(ActiveAbilities.Num() / 10.0f, 0.0f, 0.2f);
        }
    }

    return FMath::Clamp(ActionFrequency, 0.0f, 1.0f);
}

void AAuracronAdaptiveJungleAI::UpdatePlayerSkillEstimate(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Update player skill estimate using UE 5.6 skill estimation
    FString PlayerID = Player->GetName();
    float* CurrentSkill = PlayerSkillEstimates.Find(PlayerID);

    if (CurrentSkill)
    {
        // Simple skill estimation based on survival time and performance
        float SurvivalTime = GetPlayerSurvivalTime(Player);
        float HealthPercentage = GetPlayerHealthPercentage(Player);

        float SkillIndicator = (SurvivalTime / 600.0f) + (HealthPercentage * 0.5f); // 10 minutes = 1.0 skill
        SkillIndicator = FMath::Clamp(SkillIndicator, 0.0f, 1.0f);

        // Exponential moving average
        *CurrentSkill = (*CurrentSkill * 0.95f) + (SkillIndicator * 0.05f);
    }
}

// === Final Utility Methods ===

float AAuracronAdaptiveJungleAI::CalculateAbilityUsageFrequency(APawn* Player) const
{
    if (!Player)
    {
        return 0.0f;
    }

    // Calculate ability usage frequency (simplified)
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Check for recently used abilities
        TArray<FGameplayAbilitySpec> ActiveAbilities = ASC->GetActivatableAbilities();
        int32 RecentlyUsedCount = 0;

        for (const FGameplayAbilitySpec& AbilitySpec : ActiveAbilities)
        {
            // Simple heuristic: check if ability was activated recently
            if (AbilitySpec.IsActive())
            {
                RecentlyUsedCount++;
            }
        }

        return FMath::Clamp(static_cast<float>(RecentlyUsedCount) / FMath::Max(ActiveAbilities.Num(), 1), 0.0f, 1.0f);
    }

    return 0.0f;
}

float AAuracronAdaptiveJungleAI::GetPlayerHealthPercentage(APawn* Player) const
{
    if (!Player)
    {
        return 1.0f;
    }

    // Get player health percentage using UE 5.6 health system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Get health attributes
        FGameplayAttribute HealthAttribute = FGameplayAttribute(); // Would be actual health attribute
        FGameplayAttribute MaxHealthAttribute = FGameplayAttribute(); // Would be actual max health attribute

        // Simplified health calculation
        return 0.8f; // Placeholder - would calculate actual health percentage
    }

    return 1.0f; // Default to full health
}

bool AAuracronAdaptiveJungleAI::IsPlayerGroupLeader(APawn* Player, const TArray<APawn*>& NearbyPlayers) const
{
    if (!Player || NearbyPlayers.Num() < 2)
    {
        return false;
    }

    // Determine if player is group leader (simplified)
    // In full implementation, this would check actual group/party systems

    // Simple heuristic: player with highest skill estimate is leader
    FString PlayerID = Player->GetName();
    const float* PlayerSkill = PlayerSkillEstimates.Find(PlayerID);

    if (!PlayerSkill)
    {
        return false;
    }

    for (APawn* OtherPlayer : NearbyPlayers)
    {
        if (OtherPlayer != Player)
        {
            FString OtherPlayerID = OtherPlayer->GetName();
            const float* OtherSkill = PlayerSkillEstimates.Find(OtherPlayerID);

            if (OtherSkill && *OtherSkill > *PlayerSkill)
            {
                return false; // Another player has higher skill
            }
        }
    }

    return true; // Player has highest skill in group
}

float AAuracronAdaptiveJungleAI::GetPlayerSurvivalTime(APawn* Player) const
{
    if (!Player)
    {
        return 0.0f;
    }

    // Get player survival time (simplified)
    // In full implementation, this would track actual survival time

    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        // Simple heuristic based on controller existence time
        return GetWorld()->GetTimeSeconds() - PC->GetWorld()->GetTimeSeconds();
    }

    return 0.0f;
}

void AAuracronAdaptiveJungleAI::ResetCreatureToDefaultBehavior(AAuracronJungleCreature* Creature)
{
    if (!Creature)
    {
        return;
    }

    // Reset creature to default behavior using UE 5.6 reset system
    if (AAIController* AIController = Cast<AAIController>(Creature->GetController()))
    {
        // Reset blackboard to defaults
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            // Reset key values to defaults
            BlackboardComp->SetValueAsFloat(TEXT("AggressionLevel"), 1.0f);
            BlackboardComp->SetValueAsFloat(TEXT("DefensiveStance"), 1.0f);
            BlackboardComp->SetValueAsFloat(TEXT("PerceptionRadius"), 1.0f);
            BlackboardComp->SetValueAsFloat(TEXT("PatrolDistance"), 800.0f);
            BlackboardComp->SetValueAsFloat(TEXT("FleeThreshold"), 0.5f);
            BlackboardComp->SetValueAsFloat(TEXT("DifficultyMultiplier"), 1.0f);
        }

        // Remove adaptation effects
        if (UAbilitySystemComponent* ASC = Creature->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Remove adaptation-related effects
            FGameplayTagContainer AdaptationTags;
            AdaptationTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Adaptation")));
            ASC->RemoveActiveEffectsWithTags(AdaptationTags);
        }
    }
}

void AAuracronAdaptiveJungleAI::UpdateAdaptationSuccessRate()
{
    // Update adaptation success rate based on player feedback
    // This is a simplified implementation - in full version would track actual success metrics

    float SuccessRate = 0.7f; // Base success rate

    // Adjust based on model accuracy
    if (bMLModelTrained)
    {
        SuccessRate = AILearningData.ModelAccuracy * 0.8f + 0.2f; // Scale to 0.2-1.0 range
    }

    AILearningData.AdaptationSuccessRate = SuccessRate;
}

void AAuracronAdaptiveJungleAI::LoadMLData()
{
    // Load ML data from file using UE 5.6 file system
    FString MLDataPath = FPaths::ProjectSavedDir() / TEXT("AuracronAI") / TEXT("JungleAIData.json");

    if (IFileManager::Get().FileExists(*MLDataPath))
    {
        FString LoadedMLJson;
        if (FFileHelper::LoadFileToString(LoadedMLJson, *MLDataPath))
        {
            TSharedPtr<FJsonObject> JsonObject;
            TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(LoadedMLJson);

            if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
            {
                JsonObject->TryGetNumberField(TEXT("model_accuracy"), AILearningData.ModelAccuracy);
                JsonObject->TryGetNumberField(TEXT("training_iterations"), AILearningData.TrainingIterations);
                JsonObject->TryGetNumberField(TEXT("total_sessions"), AILearningData.TotalLearningSessions);
                JsonObject->TryGetNumberField(TEXT("adaptation_success_rate"), AILearningData.AdaptationSuccessRate);

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Loaded AI ML data - Accuracy: %.3f, Iterations: %d"),
                    AILearningData.ModelAccuracy, AILearningData.TrainingIterations);

                bMLModelTrained = AILearningData.TrainingIterations > 0;
            }
        }
    }
}

void AAuracronAdaptiveJungleAI::SaveMLData()
{
    // Save ML data to file using UE 5.6 file system
    FString MLDataPath = FPaths::ProjectSavedDir() / TEXT("AuracronAI") / TEXT("JungleAIData.json");

    // Create JSON object
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetNumberField(TEXT("model_accuracy"), AILearningData.ModelAccuracy);
    JsonObject->SetNumberField(TEXT("training_iterations"), AILearningData.TrainingIterations);
    JsonObject->SetNumberField(TEXT("total_sessions"), AILearningData.TotalLearningSessions);
    JsonObject->SetNumberField(TEXT("adaptation_success_rate"), AILearningData.AdaptationSuccessRate);
    JsonObject->SetNumberField(TEXT("data_points_collected"), AILearningData.DataPointsCollected);

    // Serialize to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Save to file
    FFileHelper::SaveStringToFile(OutputString, *MLDataPath);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Saved AI ML data to %s"), *MLDataPath);
}
