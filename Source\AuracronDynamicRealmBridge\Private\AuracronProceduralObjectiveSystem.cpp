/**
 * AuracronProceduralObjectiveSystem.cpp
 * 
 * Implementation of advanced procedural objective generation system that creates
 * dynamic objectives based on match state, player behavior, team composition,
 * and game flow.
 * 
 * Uses UE 5.6 modern gameplay frameworks for production-ready adaptive objectives.
 */

#include "AuracronProceduralObjectiveSystem.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronPrismalFlow.h"
#include "AuracronPrismalIsland.h"
#include "AuracronDynamicRail.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "GameFramework/Pawn.h"
#include "AIController.h"
#include "AbilitySystemComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/Engine.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"
#include "EngineUtils.h"
#include "Math/UnrealMathUtility.h"
#include "Engine/OverlapResult.h"
#include "Engine/HitResult.h"
#include "AbilitySystemComponent.h"
#include "GameplayTagContainer.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"

void UAuracronProceduralObjectiveSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // Initialize procedural objective system using UE 5.6 subsystem initialization
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Procedural Objective System"));

    // Initialize default configuration
    bSystemEnabled = true;
    bEnableDebugLogging = true;
    MaxObjectivesPerTeam = 3;
    ObjectiveTimeoutDuration = 300.0f; // 5 minutes

    // Initialize state
    bIsInitialized = false;
    LastGenerationTime = 0.0f;
    LastMatchAnalysisTime = 0.0f;
    LastObjectiveUpdateTime = 0.0f;
    NextObjectiveID = 1;
    TotalObjectivesGenerated = 0;
    TotalObjectivesCompleted = 0;

    // Initialize generation parameters
    GenerationParams = FAuracronObjectiveGenerationParams();

    // Reserve arrays for performance
    ActiveObjectives.Reserve(12);
    CompletedObjectives.Reserve(100);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Procedural Objective System initialized"));
}

void UAuracronProceduralObjectiveSystem::Deinitialize()
{
    // Cleanup procedural objective system using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deinitializing Procedural Objective System"));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Clear all objectives
    ActiveObjectives.Empty();
    CompletedObjectives.Empty();
    ObjectiveTemplates.Empty();
    ContextObjectiveTypes.Empty();
    ObjectiveTypeWeights.Empty();

    bIsInitialized = false;

    Super::Deinitialize();
}

// === Core Objective Management Implementation ===

void UAuracronProceduralObjectiveSystem::InitializeObjectiveSystem()
{
    if (bIsInitialized || !bSystemEnabled)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing procedural objective generation system..."));

    // Cache subsystem reference
    CachedRealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>();

    // Initialize generation system
    InitializeGenerationSystem();

    // Setup objective templates
    SetupObjectiveTemplates();

    // Start objective generation
    StartObjectiveGeneration();

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Procedural objective system initialized successfully"));
}

void UAuracronProceduralObjectiveSystem::GenerateObjectives()
{
    if (!bIsInitialized || !bSystemEnabled)
    {
        return;
    }

    // Generate objectives using UE 5.6 generation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating procedural objectives..."));

    // Update match state analysis
    UpdateMatchStateAnalysis();

    // Check if we need more objectives
    int32 CurrentObjectiveCount = ActiveObjectives.Num();
    if (CurrentObjectiveCount >= GenerationParams.MaxObjectives)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Maximum objectives reached (%d), skipping generation"), CurrentObjectiveCount);
        return;
    }

    // Determine how many objectives to generate
    int32 ObjectivesToGenerate = FMath::Min(
        GenerationParams.MinObjectives - CurrentObjectiveCount,
        GenerationParams.MaxObjectives - CurrentObjectiveCount
    );

    if (ObjectivesToGenerate <= 0)
    {
        return;
    }

    // Generate objectives based on current context
    for (int32 i = 0; i < ObjectivesToGenerate; i++)
    {
        FAuracronProceduralObjective NewObjective = GenerateObjectiveForContext(CurrentMatchState.CurrentPhase);
        
        if (ValidateObjectiveLocation(NewObjective.TargetLocation, NewObjective.ObjectiveType))
        {
            // Add to active objectives
            ActiveObjectives.Add(NewObjective);
            TotalObjectivesGenerated++;

            // Trigger generation event
            OnObjectiveGenerated(NewObjective);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Generated %s objective at %s (ID: %s)"), 
                *UEnum::GetValueAsString(NewObjective.ObjectiveType),
                *NewObjective.TargetLocation.ToString(),
                *NewObjective.ObjectiveID);
        }
    }

    LastGenerationTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
}

void UAuracronProceduralObjectiveSystem::UpdateObjectives(float DeltaTime)
{
    if (!bIsInitialized || ActiveObjectives.IsEmpty())
    {
        return;
    }

    // Update objectives using UE 5.6 update system
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    LastObjectiveUpdateTime = CurrentTime;

    // Process each active objective
    for (int32 i = ActiveObjectives.Num() - 1; i >= 0; i--)
    {
        FAuracronProceduralObjective& Objective = ActiveObjectives[i];
        
        // Check for timeout
        if (Objective.Duration > 0.0f)
        {
            float ObjectiveAge = CurrentTime - Objective.CreationTime;
            if (ObjectiveAge >= Objective.Duration)
            {
                // Objective timed out
                CancelObjective(Objective.ObjectiveID);
                continue;
            }
        }

        // Process objective based on type
        switch (Objective.ObjectiveType)
        {
            case EProceduralObjectiveType::Capture:
                ProcessCaptureObjective(Objective);
                break;
            case EProceduralObjectiveType::Defend:
                ProcessDefendObjective(Objective);
                break;
            case EProceduralObjectiveType::Eliminate:
                ProcessEliminateObjective(Objective);
                break;
            case EProceduralObjectiveType::Collect:
                ProcessCollectObjective(Objective);
                break;
            case EProceduralObjectiveType::Escort:
                ProcessEscortObjective(Objective);
                break;
            case EProceduralObjectiveType::Survive:
                ProcessSurviveObjective(Objective);
                break;
            default:
                break;
        }
    }

    // Validate all objectives
    ValidateActiveObjectives();

    // Clean up expired objectives
    CleanupExpiredObjectives();
}

void UAuracronProceduralObjectiveSystem::CompleteObjective(const FString& ObjectiveID, int32 CompletingTeam)
{
    // Complete objective using UE 5.6 completion system
    for (int32 i = 0; i < ActiveObjectives.Num(); i++)
    {
        FAuracronProceduralObjective& Objective = ActiveObjectives[i];
        
        if (Objective.ObjectiveID == ObjectiveID)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective %s completed by team %d"), *ObjectiveID, CompletingTeam);

            // Mark as completed
            Objective.bIsCompleted = true;
            Objective.bIsActive = false;

            // Award rewards
            AwardObjectiveRewards(Objective, CompletingTeam);

            // Move to completed objectives
            CompletedObjectives.Add(Objective);
            ActiveObjectives.RemoveAt(i);
            TotalObjectivesCompleted++;

            // Trigger completion event
            OnObjectiveCompleted(Objective, CompletingTeam);

            // Generate replacement objective if needed
            if (GenerationParams.bEnableAdaptiveGeneration)
            {
                GenerateReplacementObjective(Objective);
            }

            break;
        }
    }
}

void UAuracronProceduralObjectiveSystem::CancelObjective(const FString& ObjectiveID)
{
    // Cancel objective using UE 5.6 cancellation system
    for (int32 i = 0; i < ActiveObjectives.Num(); i++)
    {
        FAuracronProceduralObjective& Objective = ActiveObjectives[i];
        
        if (Objective.ObjectiveID == ObjectiveID)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective %s cancelled"), *ObjectiveID);

            // Mark as inactive
            Objective.bIsActive = false;

            // Trigger cancellation event
            OnObjectiveCancelled(Objective);

            // Remove from active objectives
            ActiveObjectives.RemoveAt(i);
            break;
        }
    }
}

// === Objective Queries Implementation ===

TArray<FAuracronProceduralObjective> UAuracronProceduralObjectiveSystem::GetActiveObjectives() const
{
    return ActiveObjectives;
}

TArray<FAuracronProceduralObjective> UAuracronProceduralObjectiveSystem::GetObjectivesForTeam(int32 TeamID) const
{
    TArray<FAuracronProceduralObjective> TeamObjectives;
    
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        if (Objective.TargetTeam == TeamID || Objective.TargetTeam == 0) // 0 = both teams
        {
            TeamObjectives.Add(Objective);
        }
    }
    
    return TeamObjectives;
}

TArray<FAuracronProceduralObjective> UAuracronProceduralObjectiveSystem::GetObjectivesByType(EProceduralObjectiveType ObjectiveType) const
{
    TArray<FAuracronProceduralObjective> TypedObjectives;
    
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveType == ObjectiveType)
        {
            TypedObjectives.Add(Objective);
        }
    }
    
    return TypedObjectives;
}

FAuracronProceduralObjective UAuracronProceduralObjectiveSystem::GetObjectiveByID(const FString& ObjectiveID) const
{
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveID == ObjectiveID)
        {
            return Objective;
        }
    }
    
    // Return empty objective if not found
    return FAuracronProceduralObjective();
}

// === Match State Analysis Implementation ===

FAuracronMatchStateAnalysis UAuracronProceduralObjectiveSystem::AnalyzeMatchState()
{
    // Analyze current match state using UE 5.6 analysis system
    FAuracronMatchStateAnalysis Analysis;
    
    if (!GetWorld())
    {
        return Analysis;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Determine match phase
    Analysis.CurrentPhase = DetermineMatchPhase();
    Analysis.MatchDuration = CurrentTime;

    // Calculate team balance
    Analysis.TeamBalanceScore = CalculateTeamBalance();

    // Calculate action intensity
    Analysis.ActionIntensity = CalculateActionIntensity();

    // Calculate objective completion rate
    if (TotalObjectivesGenerated > 0)
    {
        Analysis.ObjectiveCompletionRate = static_cast<float>(TotalObjectivesCompleted) / TotalObjectivesGenerated;
    }

    // Calculate player engagement
    Analysis.PlayerEngagementLevel = CalculatePlayerEngagement();

    // Calculate strategic diversity
    Analysis.StrategicDiversityScore = CalculateStrategicDiversity();

    // Update last major event time
    Analysis.LastMajorEventTime = GetLastMajorEventTime();

    CurrentMatchState = Analysis;
    LastMatchAnalysisTime = CurrentTime;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Match state analysis - Phase: %s, Balance: %.2f, Intensity: %.2f"), 
            *UEnum::GetValueAsString(Analysis.CurrentPhase), Analysis.TeamBalanceScore, Analysis.ActionIntensity);
    }

    return Analysis;
}

EObjectiveGenerationContext UAuracronProceduralObjectiveSystem::GetCurrentMatchPhase() const
{
    return CurrentMatchState.CurrentPhase;
}

float UAuracronProceduralObjectiveSystem::CalculateTeamBalance() const
{
    // Calculate team balance using UE 5.6 balance calculation
    int32 Team1Players = GetTeamPlayerCount(1);
    int32 Team2Players = GetTeamPlayerCount(2);
    
    if (Team1Players == 0 && Team2Players == 0)
    {
        return 0.0f; // No players
    }

    float Team1Strength = GetTeamStrength(1);
    float Team2Strength = GetTeamStrength(2);
    
    if (Team1Strength + Team2Strength == 0.0f)
    {
        return 0.0f;
    }

    // Calculate balance score (-1.0 = Team 2 dominates, 0.0 = balanced, 1.0 = Team 1 dominates)
    float BalanceScore = (Team1Strength - Team2Strength) / (Team1Strength + Team2Strength);
    
    return FMath::Clamp(BalanceScore, -1.0f, 1.0f);
}

float UAuracronProceduralObjectiveSystem::CalculateActionIntensity() const
{
    // Calculate action intensity using UE 5.6 intensity calculation
    float Intensity = 1.0f; // Base intensity
    
    if (!GetWorld())
    {
        return Intensity;
    }

    // Count active players
    int32 ActivePlayerCount = 0;
    float TotalMovementSpeed = 0.0f;
    int32 PlayersInCombat = 0;

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            ActivePlayerCount++;
            
            // Add movement speed to intensity
            float MovementSpeed = PC->GetPawn()->GetVelocity().Size();
            TotalMovementSpeed += MovementSpeed;
            
            // Check if player is in combat
            if (UAbilitySystemComponent* ASC = PC->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
            {
                if (ASC->HasMatchingGameplayTag(FGameplayTag::RequestGameplayTag(TEXT("State.Combat"))))
                {
                    PlayersInCombat++;
                }
            }
        }
    }

    if (ActivePlayerCount > 0)
    {
        // Calculate movement intensity
        float AverageMovementSpeed = TotalMovementSpeed / ActivePlayerCount;
        float MovementIntensity = FMath::Clamp(AverageMovementSpeed / 600.0f, 0.5f, 2.0f);
        
        // Calculate combat intensity
        float CombatIntensity = FMath::Clamp(static_cast<float>(PlayersInCombat) / ActivePlayerCount, 0.0f, 1.0f);
        
        // Combine intensities
        Intensity = (MovementIntensity + CombatIntensity * 2.0f) / 2.0f;
    }

    return FMath::Clamp(Intensity, 0.2f, 3.0f);
}

// === Configuration Implementation ===

void UAuracronProceduralObjectiveSystem::SetGenerationParameters(const FAuracronObjectiveGenerationParams& NewParams)
{
    GenerationParams = NewParams;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective generation parameters updated"));
    
    // Restart generation with new parameters
    if (bIsInitialized)
    {
        StartObjectiveGeneration();
    }
}

FAuracronObjectiveGenerationParams UAuracronProceduralObjectiveSystem::GetGenerationParameters() const
{
    return GenerationParams;
}

void UAuracronProceduralObjectiveSystem::SetAdaptiveGeneration(bool bEnable)
{
    GenerationParams.bEnableAdaptiveGeneration = bEnable;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adaptive generation %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

// === Core Implementation Methods ===

void UAuracronProceduralObjectiveSystem::InitializeGenerationSystem()
{
    // Initialize generation system using UE 5.6 initialization
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing objective generation system"));

    // Setup context-based objective types
    ContextObjectiveTypes.Add(EObjectiveGenerationContext::MatchStart, {
        EProceduralObjectiveType::Explore,
        EProceduralObjectiveType::Collect,
        EProceduralObjectiveType::Activate
    });

    ContextObjectiveTypes.Add(EObjectiveGenerationContext::EarlyGame, {
        EProceduralObjectiveType::Capture,
        EProceduralObjectiveType::Collect,
        EProceduralObjectiveType::Eliminate
    });

    ContextObjectiveTypes.Add(EObjectiveGenerationContext::MidGame, {
        EProceduralObjectiveType::Control,
        EProceduralObjectiveType::Defend,
        EProceduralObjectiveType::Coordinate
    });

    ContextObjectiveTypes.Add(EObjectiveGenerationContext::LateGame, {
        EProceduralObjectiveType::Destroy,
        EProceduralObjectiveType::Survive,
        EProceduralObjectiveType::Coordinate
    });

    ContextObjectiveTypes.Add(EObjectiveGenerationContext::TeamFight, {
        EProceduralObjectiveType::Eliminate,
        EProceduralObjectiveType::Survive,
        EProceduralObjectiveType::Coordinate
    });

    ContextObjectiveTypes.Add(EObjectiveGenerationContext::Stalemate, {
        EProceduralObjectiveType::Capture,
        EProceduralObjectiveType::Activate,
        EProceduralObjectiveType::Explore
    });

    // Setup objective type weights
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Capture, 1.0f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Defend, 0.8f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Eliminate, 1.2f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Collect, 0.9f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Escort, 0.7f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Survive, 0.6f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Activate, 1.1f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Destroy, 1.3f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Control, 1.0f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Explore, 0.8f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Coordinate, 0.9f);
    ObjectiveTypeWeights.Add(EProceduralObjectiveType::Adapt, 0.5f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective generation system initialized"));
}

void UAuracronProceduralObjectiveSystem::SetupObjectiveTemplates()
{
    // Setup objective templates using UE 5.6 template system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up objective templates"));

    // Capture objective template
    FAuracronProceduralObjective CaptureTemplate;
    CaptureTemplate.ObjectiveType = EProceduralObjectiveType::Capture;
    CaptureTemplate.Priority = EObjectivePriority::High;
    CaptureTemplate.ObjectiveRadius = 400.0f;
    CaptureTemplate.Duration = 180.0f; // 3 minutes
    CaptureTemplate.RewardMultiplier = 1.2f;
    CaptureTemplate.RequiredTeamSize = 2;
    CaptureTemplate.ObjectiveDescription = FText::FromString(TEXT("Capture and hold this strategic position"));
    ObjectiveTemplates.Add(EProceduralObjectiveType::Capture, CaptureTemplate);

    // Defend objective template
    FAuracronProceduralObjective DefendTemplate;
    DefendTemplate.ObjectiveType = EProceduralObjectiveType::Defend;
    DefendTemplate.Priority = EObjectivePriority::Medium;
    DefendTemplate.ObjectiveRadius = 600.0f;
    DefendTemplate.Duration = 240.0f; // 4 minutes
    DefendTemplate.RewardMultiplier = 1.0f;
    DefendTemplate.RequiredTeamSize = 1;
    DefendTemplate.ObjectiveDescription = FText::FromString(TEXT("Defend this location from enemy forces"));
    ObjectiveTemplates.Add(EProceduralObjectiveType::Defend, DefendTemplate);

    // Eliminate objective template
    FAuracronProceduralObjective EliminateTemplate;
    EliminateTemplate.ObjectiveType = EProceduralObjectiveType::Eliminate;
    EliminateTemplate.Priority = EObjectivePriority::High;
    EliminateTemplate.ObjectiveRadius = 800.0f;
    EliminateTemplate.Duration = 120.0f; // 2 minutes
    EliminateTemplate.RewardMultiplier = 1.5f;
    EliminateTemplate.RequiredTeamSize = 1;
    EliminateTemplate.ObjectiveDescription = FText::FromString(TEXT("Eliminate target creatures or players"));
    ObjectiveTemplates.Add(EProceduralObjectiveType::Eliminate, EliminateTemplate);

    // Add more templates...
    SetupAdditionalObjectiveTemplates();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective templates configured"));
}

void UAuracronProceduralObjectiveSystem::StartObjectiveGeneration()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronProceduralObjectiveSystem::StartObjectiveGeneration);

    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot start objective generation - system not initialized"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting procedural objective generation"));

    // Clear any existing generation state
    TArray<FAuracronProceduralObjective> PendingObjectives;

    // Initialize generation parameters based on current match state
    FAuracronMatchStateAnalysis MatchState = AnalyzeMatchState();

    // Determine how many objectives to generate based on match state
    int32 TargetObjectiveCount = FMath::Clamp(
        3 + FMath::RoundToInt(MatchState.PlayerEngagementLevel * 2.0f),
        1,
        8
    );

    // Generate initial set of objectives
    for (int32 i = 0; i < TargetObjectiveCount; ++i)
    {
        EObjectiveGenerationContext Context = MatchState.CurrentPhase;
        FAuracronProceduralObjective NewObjective = GenerateObjectiveForContext(Context);

        if (NewObjective.ObjectiveID.IsEmpty())
        {
            continue; // Skip invalid objectives
        }

        PendingObjectives.Add(NewObjective);
    }

    // Activate pending objectives
    for (const FAuracronProceduralObjective& PendingObjective : PendingObjectives)
    {
        ActiveObjectives.Add(PendingObjective);

        // Broadcast objective creation (if delegate exists)
        // OnObjectiveCreated.Broadcast(PendingObjective);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Generated objective '%s'"),
               *PendingObjective.ObjectiveID);
    }

    PendingObjectives.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generated %d procedural objectives"), ActiveObjectives.Num());
}

void UAuracronProceduralObjectiveSystem::UpdateMatchStateAnalysis()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronProceduralObjectiveSystem::UpdateMatchStateAnalysis);

    if (!bIsInitialized)
    {
        return;
    }

    // Update cached match state analysis
    CurrentMatchState = AnalyzeMatchState();

    // Update generation parameters based on current state
    float EngagementLevel = CurrentMatchState.PlayerEngagementLevel;
    float IntensityLevel = CurrentMatchState.ActionIntensity;

    // Adjust objective generation frequency based on engagement
    float BaseInterval = 30.0f; // Base generation interval in seconds
    float NewInterval = BaseInterval;

    if (EngagementLevel < 0.3f)
    {
        // Low engagement - increase objective frequency
        NewInterval = FMath::Max(10.0f, BaseInterval * 0.7f);
    }
    else if (EngagementLevel > 0.8f)
    {
        // High engagement - reduce objective frequency
        NewInterval = FMath::Min(60.0f, BaseInterval * 1.5f);
    }

    // Store the interval for future use
    LastGenerationTime = GetWorld()->GetTimeSeconds();

    // Update objective weights based on match state
    // This would normally update a weight map, but for now we'll just log

    // Log state changes for debugging
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Match state updated - Engagement: %.2f, Intensity: %.2f"),
           EngagementLevel, IntensityLevel);
}

void UAuracronProceduralObjectiveSystem::ValidateActiveObjectives()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronProceduralObjectiveSystem::ValidateActiveObjectives);

    if (!bIsInitialized)
    {
        return;
    }

    TArray<int32> ObjectivesToRemove;
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Validate each active objective
    for (int32 i = 0; i < ActiveObjectives.Num(); ++i)
    {
        FAuracronProceduralObjective& Objective = ActiveObjectives[i];

        // Check if objective has expired
        if (CurrentTime > Objective.ExpirationTime)
        {
            ObjectivesToRemove.Add(i);
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Objective '%s' expired"), *Objective.ObjectiveID);
            continue;
        }

        // Basic validation - more complex validation would be implemented here
        // For now, just check if the objective is still valid
        if (Objective.ObjectiveID.IsEmpty())
        {
            ObjectivesToRemove.Add(i);
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Invalid objective found"));
            continue;
        }
    }

    // Remove invalid objectives (in reverse order to maintain indices)
    ObjectivesToRemove.Sort([](const int32& A, const int32& B) { return A > B; });
    for (int32 Index : ObjectivesToRemove)
    {
        if (ActiveObjectives.IsValidIndex(Index))
        {
            // OnObjectiveExpired.Broadcast(ActiveObjectives[Index]);
            ActiveObjectives.RemoveAt(Index);
        }
    }

    if (ObjectivesToRemove.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Removed %d invalid objectives"), ObjectivesToRemove.Num());
    }
}

void UAuracronProceduralObjectiveSystem::CleanupExpiredObjectives()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronProceduralObjectiveSystem::CleanupExpiredObjectives);

    if (!bIsInitialized)
    {
        return;
    }

    TArray<int32> ExpiredObjectives;
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Find all expired objectives
    for (int32 i = 0; i < ActiveObjectives.Num(); ++i)
    {
        const FAuracronProceduralObjective& Objective = ActiveObjectives[i];

        if (CurrentTime > Objective.ExpirationTime)
        {
            ExpiredObjectives.Add(i);
        }
    }

    // Remove expired objectives and clean up resources (in reverse order)
    ExpiredObjectives.Sort([](const int32& A, const int32& B) { return A > B; });
    for (int32 Index : ExpiredObjectives)
    {
        if (ActiveObjectives.IsValidIndex(Index))
        {
            const FAuracronProceduralObjective& Objective = ActiveObjectives[Index];

            // Clean up any spawned actors or components would go here

            // Broadcast expiration event
            // OnObjectiveExpired.Broadcast(Objective);

            // Remove from active objectives
            ActiveObjectives.RemoveAt(Index);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Cleaned up expired objective '%s'"), *Objective.ObjectiveID);
        }
    }

    if (ExpiredObjectives.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Cleaned up %d expired objectives"), ExpiredObjectives.Num());
    }
}

// === Generation Implementation ===

FAuracronProceduralObjective UAuracronProceduralObjectiveSystem::GenerateObjectiveForContext(EObjectiveGenerationContext Context)
{
    // Generate objective for specific context using UE 5.6 generation system
    FAuracronProceduralObjective NewObjective;

    // Select objective type based on context
    EProceduralObjectiveType SelectedType = SelectObjectiveType(Context);

    // Get template for selected type
    if (const FAuracronProceduralObjective* Template = ObjectiveTemplates.Find(SelectedType))
    {
        NewObjective = *Template;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No template found for objective type %s"), *UEnum::GetValueAsString(SelectedType));
        return NewObjective;
    }

    // Generate unique ID
    NewObjective.ObjectiveID = GenerateUniqueObjectiveID();

    // Select location
    NewObjective.TargetLocation = SelectObjectiveLocation(SelectedType);

    // Calculate rewards
    NewObjective.RewardMultiplier = CalculateObjectiveReward(NewObjective);

    // Set creation time
    NewObjective.CreationTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Mark as active
    NewObjective.bIsActive = true;
    NewObjective.bIsCompleted = false;

    // Adapt based on match state
    AdaptObjectiveToMatchState(NewObjective);

    return NewObjective;
}

EProceduralObjectiveType UAuracronProceduralObjectiveSystem::SelectObjectiveType(EObjectiveGenerationContext Context)
{
    // Select objective type based on context using UE 5.6 selection system

    // Get available types for context
    const TArray<EProceduralObjectiveType>* AvailableTypes = ContextObjectiveTypes.Find(Context);
    if (!AvailableTypes || AvailableTypes->IsEmpty())
    {
        return EProceduralObjectiveType::Capture; // Default
    }

    // Calculate weighted selection
    TArray<float> Weights;
    float TotalWeight = 0.0f;

    for (EProceduralObjectiveType Type : *AvailableTypes)
    {
        float Weight = ObjectiveTypeWeights.FindRef(Type);

        // Adjust weight based on current match state
        Weight = AdjustWeightForMatchState(Type, Weight);

        Weights.Add(Weight);
        TotalWeight += Weight;
    }

    // Select type using weighted random
    if (TotalWeight > 0.0f)
    {
        float RandomValue = FMath::RandRange(0.0f, TotalWeight);
        float CurrentWeight = 0.0f;

        for (int32 i = 0; i < AvailableTypes->Num(); i++)
        {
            CurrentWeight += Weights[i];
            if (RandomValue <= CurrentWeight)
            {
                return (*AvailableTypes)[i];
            }
        }
    }

    // Fallback to first available type
    return (*AvailableTypes)[0];
}

FVector UAuracronProceduralObjectiveSystem::SelectObjectiveLocation(EProceduralObjectiveType ObjectiveType)
{
    // Select objective location using UE 5.6 location selection
    TArray<FVector> StrategicLocations = GetStrategicLocations();

    if (StrategicLocations.IsEmpty())
    {
        // Fallback to random location
        return FVector(
            FMath::RandRange(-5000.0f, 5000.0f),
            FMath::RandRange(-5000.0f, 5000.0f),
            0.0f
        );
    }

    // Filter locations based on objective type
    TArray<FVector> SuitableLocations;

    for (const FVector& Location : StrategicLocations)
    {
        if (IsLocationSuitableForObjective(Location, ObjectiveType))
        {
            SuitableLocations.Add(Location);
        }
    }

    if (SuitableLocations.IsEmpty())
    {
        SuitableLocations = StrategicLocations; // Use all strategic locations as fallback
    }

    // Select best location based on strategic value
    FVector BestLocation = SuitableLocations[0];
    float BestScore = CalculateLocationStrategicValue(BestLocation, ObjectiveType);

    for (int32 i = 1; i < SuitableLocations.Num(); i++)
    {
        float Score = CalculateLocationStrategicValue(SuitableLocations[i], ObjectiveType);
        if (Score > BestScore)
        {
            BestScore = Score;
            BestLocation = SuitableLocations[i];
        }
    }

    return BestLocation;
}

float UAuracronProceduralObjectiveSystem::CalculateObjectiveReward(const FAuracronProceduralObjective& Objective)
{
    // Calculate objective reward using UE 5.6 reward calculation
    float BaseReward = 1.0f;

    // Adjust based on priority
    switch (Objective.Priority)
    {
        case EObjectivePriority::Low:
            BaseReward = 0.8f;
            break;
        case EObjectivePriority::Medium:
            BaseReward = 1.0f;
            break;
        case EObjectivePriority::High:
            BaseReward = 1.3f;
            break;
        case EObjectivePriority::Critical:
            BaseReward = 1.6f;
            break;
        case EObjectivePriority::Emergency:
            BaseReward = 2.0f;
            break;
        default:
            break;
    }

    // Adjust based on match state
    if (CurrentMatchState.TeamBalanceScore != 0.0f)
    {
        // Bonus for underdog team
        float BalanceBonus = FMath::Abs(CurrentMatchState.TeamBalanceScore) * 0.3f;
        BaseReward += BalanceBonus;
    }

    // Adjust based on action intensity
    if (CurrentMatchState.ActionIntensity > 1.5f)
    {
        BaseReward *= 1.2f; // Bonus for high-intensity matches
    }
    else if (CurrentMatchState.ActionIntensity < 0.8f)
    {
        BaseReward *= 1.1f; // Slight bonus to encourage action
    }

    return FMath::Clamp(BaseReward, 0.5f, 3.0f);
}

bool UAuracronProceduralObjectiveSystem::ValidateObjectiveLocation(const FVector& Location, EProceduralObjectiveType ObjectiveType)
{
    // Validate objective location using UE 5.6 validation system

    // Check if location is safe (not in void, has ground, etc.)
    if (!IsLocationSafe(Location))
    {
        return false;
    }

    // Check if location is strategic
    if (!IsLocationStrategic(Location))
    {
        return false;
    }

    // Check for conflicts with existing objectives
    for (const FAuracronProceduralObjective& ExistingObjective : ActiveObjectives)
    {
        float Distance = FVector::Dist(Location, ExistingObjective.TargetLocation);
        float MinDistance = (ExistingObjective.ObjectiveRadius + 400.0f); // Minimum separation

        if (Distance < MinDistance)
        {
            return false; // Too close to existing objective
        }
    }

    // Type-specific validation
    switch (ObjectiveType)
    {
        case EProceduralObjectiveType::Capture:
        case EProceduralObjectiveType::Control:
            return IsLocationCapturable(Location);

        case EProceduralObjectiveType::Defend:
            return IsLocationDefensible(Location);

        case EProceduralObjectiveType::Explore:
            return IsLocationExplorable(Location);

        default:
            return true; // Generic objectives can be placed anywhere safe
    }
}

// === Match Analysis Implementation ===

EObjectiveGenerationContext UAuracronProceduralObjectiveSystem::DetermineMatchPhase()
{
    // Determine match phase using UE 5.6 phase analysis
    float MatchDuration = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Time-based phase determination
    if (MatchDuration < 300.0f) // First 5 minutes
    {
        return EObjectiveGenerationContext::EarlyGame;
    }
    else if (MatchDuration < 900.0f) // 5-15 minutes
    {
        return EObjectiveGenerationContext::MidGame;
    }
    else if (MatchDuration < 1800.0f) // 15-30 minutes
    {
        return EObjectiveGenerationContext::LateGame;
    }

    // State-based phase determination
    if (DetectStalemate())
    {
        return EObjectiveGenerationContext::Stalemate;
    }

    if (DetectComebackPotential())
    {
        return EObjectiveGenerationContext::Comeback;
    }

    if (DetectDomination())
    {
        return EObjectiveGenerationContext::Domination;
    }

    // Check for team fight conditions
    if (CurrentMatchState.ActionIntensity > 2.0f)
    {
        return EObjectiveGenerationContext::TeamFight;
    }

    return EObjectiveGenerationContext::MidGame; // Default
}

float UAuracronProceduralObjectiveSystem::CalculatePlayerEngagement()
{
    // Calculate player engagement using UE 5.6 engagement calculation
    if (!GetWorld())
    {
        return 1.0f;
    }

    float TotalEngagement = 0.0f;
    int32 PlayerCount = 0;

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            PlayerCount++;

            // Calculate individual engagement
            float PlayerEngagement = CalculateIndividualPlayerEngagement(PC->GetPawn());
            TotalEngagement += PlayerEngagement;
        }
    }

    return PlayerCount > 0 ? TotalEngagement / PlayerCount : 1.0f;
}

float UAuracronProceduralObjectiveSystem::CalculateStrategicDiversity()
{
    // Calculate strategic diversity using UE 5.6 diversity calculation
    if (CompletedObjectives.IsEmpty())
    {
        return 0.5f; // Default diversity
    }

    // Count different objective types completed
    TSet<EProceduralObjectiveType> CompletedTypes;
    for (const FAuracronProceduralObjective& Objective : CompletedObjectives)
    {
        CompletedTypes.Add(Objective.ObjectiveType);
    }

    // Calculate diversity score
    int32 TotalObjectiveTypes = static_cast<int32>(EProceduralObjectiveType::Adapt) + 1;
    float DiversityScore = static_cast<float>(CompletedTypes.Num()) / TotalObjectiveTypes;

    return FMath::Clamp(DiversityScore, 0.0f, 1.0f);
}

bool UAuracronProceduralObjectiveSystem::DetectStalemate()
{
    // Detect stalemate conditions using UE 5.6 detection system

    // Check for low action intensity over time
    if (CurrentMatchState.ActionIntensity < 0.6f && CurrentMatchState.MatchDuration > 600.0f)
    {
        return true;
    }

    // Check for balanced teams with no progress
    if (FMath::Abs(CurrentMatchState.TeamBalanceScore) < 0.2f &&
        CurrentMatchState.ObjectiveCompletionRate < 0.3f &&
        CurrentMatchState.MatchDuration > 900.0f)
    {
        return true;
    }

    return false;
}

bool UAuracronProceduralObjectiveSystem::DetectComebackPotential()
{
    // Detect comeback potential using UE 5.6 comeback detection

    // Check for significant team imbalance that could lead to comeback
    float AbsBalance = FMath::Abs(CurrentMatchState.TeamBalanceScore);

    if (AbsBalance > 0.6f && CurrentMatchState.ActionIntensity > 1.2f)
    {
        return true; // Losing team is fighting back
    }

    return false;
}

bool UAuracronProceduralObjectiveSystem::DetectDomination()
{
    // Detect domination conditions using UE 5.6 domination detection

    // Check for extreme team imbalance
    float AbsBalance = FMath::Abs(CurrentMatchState.TeamBalanceScore);

    if (AbsBalance > 0.8f && CurrentMatchState.ObjectiveCompletionRate > 0.7f)
    {
        return true; // One team is clearly dominating
    }

    return false;
}

// === Objective Processing Implementation ===

void UAuracronProceduralObjectiveSystem::ProcessCaptureObjective(FAuracronProceduralObjective& Objective)
{
    // Process capture objective using UE 5.6 capture processing
    TArray<APawn*> PlayersInArea = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);

    if (PlayersInArea.Num() >= Objective.RequiredTeamSize)
    {
        // Check if all players are from same team
        int32 DominantTeam = GetDominantTeamInArea(PlayersInArea);

        if (DominantTeam != 0 && (Objective.TargetTeam == 0 || Objective.TargetTeam == DominantTeam))
        {
            // Start capture progress
            StartObjectiveCapture(Objective, DominantTeam);
        }
    }
}

void UAuracronProceduralObjectiveSystem::ProcessDefendObjective(FAuracronProceduralObjective& Objective)
{
    // Process defend objective using UE 5.6 defense processing
    TArray<APawn*> PlayersInArea = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);

    // Check for defending team presence
    bool bDefendersPresent = false;
    bool bAttackersPresent = false;

    for (APawn* Player : PlayersInArea)
    {
        if (Player)
        {
            int32 PlayerTeam = GetPlayerTeamID(Player);

            if (PlayerTeam == Objective.TargetTeam)
            {
                bDefendersPresent = true;
            }
            else if (PlayerTeam != 0)
            {
                bAttackersPresent = true;
            }
        }
    }

    // Update objective status based on presence
    if (bDefendersPresent && !bAttackersPresent)
    {
        // Successful defense
        UpdateObjectiveProgress(Objective, 0.1f); // 10% progress per update
    }
    else if (bAttackersPresent && !bDefendersPresent)
    {
        // Defense failing
        UpdateObjectiveProgress(Objective, -0.05f); // Lose 5% progress
    }
}

void UAuracronProceduralObjectiveSystem::ProcessEliminateObjective(FAuracronProceduralObjective& Objective)
{
    // Process eliminate objective using UE 5.6 elimination processing
    TArray<APawn*> PlayersInArea = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);

    // Check for elimination targets
    int32 EliminationTargets = CountEliminationTargets(Objective.TargetLocation, Objective.ObjectiveRadius);

    if (EliminationTargets == 0)
    {
        // All targets eliminated
        CompleteObjective(Objective.ObjectiveID, GetDominantTeamInArea(PlayersInArea));
    }
}

void UAuracronProceduralObjectiveSystem::ProcessCollectObjective(FAuracronProceduralObjective& Objective)
{
    // Process collect objective using UE 5.6 collection processing
    TArray<APawn*> PlayersInArea = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);

    // Check for collection progress
    for (APawn* Player : PlayersInArea)
    {
        if (Player && HasPlayerCollectedItems(Player, Objective))
        {
            int32 PlayerTeam = GetPlayerTeamID(Player);
            CompleteObjective(Objective.ObjectiveID, PlayerTeam);
            break;
        }
    }
}

void UAuracronProceduralObjectiveSystem::ProcessEscortObjective(FAuracronProceduralObjective& Objective)
{
    // Process escort objective using UE 5.6 escort processing
    // Check if escort target has reached destination
    if (HasEscortTargetReachedDestination(Objective))
    {
        TArray<APawn*> EscortingPlayers = GetEscortingPlayers(Objective);
        if (!EscortingPlayers.IsEmpty())
        {
            int32 EscortTeam = GetPlayerTeamID(EscortingPlayers[0]);
            CompleteObjective(Objective.ObjectiveID, EscortTeam);
        }
    }
}

void UAuracronProceduralObjectiveSystem::ProcessSurviveObjective(FAuracronProceduralObjective& Objective)
{
    // Process survive objective using UE 5.6 survival processing
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float ObjectiveAge = CurrentTime - Objective.CreationTime;

    // Check if survival duration has been met
    if (ObjectiveAge >= Objective.Duration)
    {
        TArray<APawn*> SurvivingPlayers = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);

        if (!SurvivingPlayers.IsEmpty())
        {
            int32 SurvivingTeam = GetDominantTeamInArea(SurvivingPlayers);
            CompleteObjective(Objective.ObjectiveID, SurvivingTeam);
        }
    }
}

// === Utility Methods Implementation ===

FString UAuracronProceduralObjectiveSystem::GenerateUniqueObjectiveID()
{
    // Generate unique objective ID using UE 5.6 ID generation
    FString ObjectiveID = FString::Printf(TEXT("OBJ_%d_%d"),
        NextObjectiveID++,
        FMath::RandRange(1000, 9999));

    return ObjectiveID;
}

TArray<FVector> UAuracronProceduralObjectiveSystem::GetStrategicLocations()
{
    // Get strategic locations using UE 5.6 location system
    TArray<FVector> StrategicLocations;

    if (CachedRealmSubsystem)
    {
        // Get island locations
        if (AAuracronPrismalFlow* FlowActor = CachedRealmSubsystem->GetPrismalFlowActor())
        {
            for (int32 IslandTypeInt = 0; IslandTypeInt < 4; IslandTypeInt++)
            {
                EPrismalIslandType IslandType = static_cast<EPrismalIslandType>(IslandTypeInt);
                TArray<AAuracronPrismalIsland*> Islands = FlowActor->GetIslandsByType(IslandType);

                for (AAuracronPrismalIsland* Island : Islands)
                {
                    if (Island)
                    {
                        StrategicLocations.Add(Island->GetActorLocation());
                    }
                }
            }
        }

        // Get rail junction locations
        TArray<AAuracronDynamicRail*> ActiveRails = CachedRealmSubsystem->GetActiveRails();
        for (AAuracronDynamicRail* Rail : ActiveRails)
        {
            if (Rail)
            {
                // Add rail start and end points
                StrategicLocations.Add(Rail->GetRailStartLocation());
                StrategicLocations.Add(Rail->GetRailEndLocation());
            }
        }
    }

    // Add some random strategic locations if we don't have enough
    while (StrategicLocations.Num() < 8)
    {
        FVector RandomLocation = FVector(
            FMath::RandRange(-4000.0f, 4000.0f),
            FMath::RandRange(-4000.0f, 4000.0f),
            FMath::RandRange(0.0f, 1000.0f)
        );

        if (IsLocationSafe(RandomLocation))
        {
            StrategicLocations.Add(RandomLocation);
        }
    }

    return StrategicLocations;
}

TArray<APawn*> UAuracronProceduralObjectiveSystem::GetPlayersInRadius(const FVector& Location, float Radius) const
{
    TArray<APawn*> PlayersInRadius;

    if (!GetWorld())
    {
        return PlayersInRadius;
    }

    // Find players in radius using UE 5.6 spatial queries
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            float Distance = FVector::Dist(PC->GetPawn()->GetActorLocation(), Location);
            if (Distance <= Radius)
            {
                PlayersInRadius.Add(PC->GetPawn());
            }
        }
    }

    return PlayersInRadius;
}

int32 UAuracronProceduralObjectiveSystem::GetTeamPlayerCount(int32 TeamID) const
{
    // Get team player count using UE 5.6 team counting
    int32 PlayerCount = 0;

    if (!GetWorld())
    {
        return PlayerCount;
    }

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            if (GetPlayerTeamID(PC->GetPawn()) == TeamID)
            {
                PlayerCount++;
            }
        }
    }

    return PlayerCount;
}

float UAuracronProceduralObjectiveSystem::GetTeamStrength(int32 TeamID) const
{
    // Get team strength using UE 5.6 strength calculation
    float TeamStrength = 0.0f;

    if (!GetWorld())
    {
        return TeamStrength;
    }

    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn() && GetPlayerTeamID(PC->GetPawn()) == TeamID)
        {
            // Calculate individual player strength
            float PlayerStrength = CalculatePlayerStrength(PC->GetPawn());
            TeamStrength += PlayerStrength;
        }
    }

    return TeamStrength;
}

bool UAuracronProceduralObjectiveSystem::IsLocationSafe(const FVector& Location)
{
    // Check if location is safe using UE 5.6 safety validation
    if (!GetWorld())
    {
        return false;
    }

    // Check for ground collision
    FHitResult HitResult;
    FVector StartTrace = Location + FVector(0.0f, 0.0f, 1000.0f);
    FVector EndTrace = Location - FVector(0.0f, 0.0f, 1000.0f);

    bool bHitGround = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartTrace,
        EndTrace,
        ECollisionChannel::ECC_WorldStatic
    );

    if (!bHitGround)
    {
        return false; // No ground found
    }

    // Check for hazards (simplified)
    // In full implementation, would check for environmental hazards, void areas, etc.

    return true;
}

bool UAuracronProceduralObjectiveSystem::IsLocationStrategic(const FVector& Location)
{
    // Check if location is strategic using UE 5.6 strategic analysis

    // Check distance to important game elements
    if (CachedRealmSubsystem)
    {
        // Check proximity to islands
        if (AAuracronPrismalFlow* FlowActor = CachedRealmSubsystem->GetPrismalFlowActor())
        {
            for (int32 IslandTypeInt = 0; IslandTypeInt < 4; IslandTypeInt++)
            {
                EPrismalIslandType IslandType = static_cast<EPrismalIslandType>(IslandTypeInt);
                TArray<AAuracronPrismalIsland*> Islands = FlowActor->GetIslandsByType(IslandType);

                for (AAuracronPrismalIsland* Island : Islands)
                {
                    if (Island)
                    {
                        float Distance = FVector::Dist(Location, Island->GetActorLocation());
                        if (Distance < 1500.0f) // Close to strategic island
                        {
                            return true;
                        }
                    }
                }
            }
        }

        // Check proximity to rails
        TArray<AAuracronDynamicRail*> ActiveRails = CachedRealmSubsystem->GetActiveRails();
        for (AAuracronDynamicRail* Rail : ActiveRails)
        {
            if (Rail)
            {
                float DistanceToRail = Rail->GetDistanceToRail(Location);
                if (DistanceToRail < 800.0f) // Close to rail
                {
                    return true;
                }
            }
        }
    }

    return false;
}

int32 UAuracronProceduralObjectiveSystem::GetPlayerTeamID(APawn* Player) const
{
    if (!Player)
    {
        return 0;
    }

    // Get player team ID using UE 5.6 team system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayTagContainer PlayerTags;
        ASC->GetOwnedGameplayTags(PlayerTags);

        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.1"))))
        {
            return 1;
        }
        else if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.2"))))
        {
            return 2;
        }
    }

    return 0; // Neutral
}

int32 UAuracronProceduralObjectiveSystem::GetDominantTeamInArea(const TArray<APawn*>& Players) const
{
    // Get dominant team in area using UE 5.6 dominance calculation
    TMap<int32, int32> TeamCounts;

    for (APawn* Player : Players)
    {
        if (Player)
        {
            int32 PlayerTeam = GetPlayerTeamID(Player);
            TeamCounts.FindOrAdd(PlayerTeam)++;
        }
    }

    int32 DominantTeam = 0;
    int32 MaxCount = 0;

    for (const auto& TeamPair : TeamCounts)
    {
        if (TeamPair.Value > MaxCount)
        {
            MaxCount = TeamPair.Value;
            DominantTeam = TeamPair.Key;
        }
    }

    return DominantTeam;
}

float UAuracronProceduralObjectiveSystem::CalculatePlayerStrength(APawn* Player) const
{
    if (!Player)
    {
        return 0.0f;
    }

    // Calculate player strength using UE 5.6 strength calculation
    float Strength = 1.0f; // Base strength

    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Get player level/power indicators
        // This would integrate with actual player progression system

        // Simple heuristic based on health and abilities
        float HealthPercentage = GetPlayerHealthPercentage(Player);
        Strength *= HealthPercentage;

        // Check for active buffs
        FGameplayTagContainer PlayerTags;
        ASC->GetOwnedGameplayTags(PlayerTags);

        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Buff.Damage"))))
        {
            Strength *= 1.2f;
        }

        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Buff.Defense"))))
        {
            Strength *= 1.1f;
        }
    }

    return FMath::Clamp(Strength, 0.1f, 3.0f);
}

float UAuracronProceduralObjectiveSystem::GetPlayerHealthPercentage(APawn* Player) const
{
    if (!Player)
    {
        return 0.0f;
    }

    // Try to get health from Gameplay Ability System first
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Use simplified approach for UE 5.6 compatibility
        FGameplayAttribute HealthAttribute;
        FGameplayAttribute MaxHealthAttribute;

        if (ASC->HasAttributeSetForAttribute(HealthAttribute) && ASC->HasAttributeSetForAttribute(MaxHealthAttribute))
        {
            float CurrentHealth = ASC->GetNumericAttribute(HealthAttribute);
            float MaxHealth = ASC->GetNumericAttribute(MaxHealthAttribute);

            if (MaxHealth > 0.0f)
            {
                return CurrentHealth / MaxHealth;
            }
        }
    }

    // Fallback to character health if available
    if (ACharacter* Character = Cast<ACharacter>(Player))
    {
        // Use Ability System Component for health if available
        if (UAbilitySystemComponent* ASC = Character->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Try to get health attributes from the ASC
            TArray<FGameplayAttribute> Attributes;
            ASC->GetAllAttributes(Attributes);

            float CurrentHealth = 100.0f;
            float MaxHealth = 100.0f;

            // Look for health-related attributes
            for (const FGameplayAttribute& Attribute : Attributes)
            {
                if (Attribute.GetName().Contains(TEXT("Health")))
                {
                    if (Attribute.GetName().Contains(TEXT("Max")))
                    {
                        MaxHealth = ASC->GetNumericAttribute(Attribute);
                    }
                    else
                    {
                        CurrentHealth = ASC->GetNumericAttribute(Attribute);
                    }
                }
            }

            if (MaxHealth > 0.0f)
            {
                return CurrentHealth / MaxHealth;
            }
        }
    }

    // Final fallback - assume healthy
    return 1.0f;
}

void UAuracronProceduralObjectiveSystem::AwardObjectiveRewards(const FAuracronProceduralObjective& Objective, int32 CompletingTeam)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronProceduralObjectiveSystem::AwardObjectiveRewards);

    if (!bIsInitialized)
    {
        return;
    }

    // Calculate base reward based on objective type and difficulty
    float BaseReward = 100.0f;
    float DifficultyMultiplier = 1.0f;

    switch (Objective.Type)
    {
        case EProceduralObjectiveType::Capture:
            BaseReward = 150.0f;
            DifficultyMultiplier = 1.2f;
            break;
        case EProceduralObjectiveType::Defend:
            BaseReward = 120.0f;
            DifficultyMultiplier = 1.1f;
            break;
        case EProceduralObjectiveType::Eliminate:
            BaseReward = 200.0f;
            DifficultyMultiplier = 1.5f;
            break;
        case EProceduralObjectiveType::Collect:
            BaseReward = 80.0f;
            DifficultyMultiplier = 0.9f;
            break;
        case EProceduralObjectiveType::Escort:
            BaseReward = 180.0f;
            DifficultyMultiplier = 1.3f;
            break;
        default:
            BaseReward = 100.0f;
            DifficultyMultiplier = 1.0f;
            break;
    }

    // Apply difficulty and time bonus
    float TimeBonus = FMath::Max(0.0f, (Objective.ExpirationTime - GetWorld()->GetTimeSeconds()) / Objective.Duration);
    float FinalReward = BaseReward * DifficultyMultiplier * (1.0f + TimeBonus * 0.5f);

    // Award rewards to team members
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        // Simple team check - would be more sophisticated in real implementation
        int32 PlayerTeam = 0; // Default team
        if (CompletingTeam == PlayerTeam)
        {
            // Award experience points - simplified version
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Awarded %.0f XP to player %s for objective '%s'"),
                   FinalReward, *PC->GetName(), *Objective.ObjectiveID);
        }
    }
}

// === Missing Function Implementations ===

void UAuracronProceduralObjectiveSystem::GenerateReplacementObjective(const FAuracronProceduralObjective& CompletedObjective)
{
    // Generate replacement objective based on completed objective
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating replacement objective for completed objective '%s'"), *CompletedObjective.ObjectiveID);

    // Determine context for replacement
    EObjectiveGenerationContext Context = GetCurrentMatchPhase();

    // Generate new objective
    FAuracronProceduralObjective NewObjective = GenerateObjectiveForContext(Context);

    if (!NewObjective.ObjectiveID.IsEmpty())
    {
        // Add to active objectives
        ActiveObjectives.Add(NewObjective);
        TotalObjectivesGenerated++;

        // Trigger generation event
        OnObjectiveGenerated(NewObjective);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Generated replacement objective '%s'"), *NewObjective.ObjectiveID);
    }
}

float UAuracronProceduralObjectiveSystem::GetLastMajorEventTime() const
{
    // Return the time of the last major event (objective completion, team elimination, etc.)
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Find the most recent major event
    float LastEventTime = 0.0f;

    // Check last objective completion time
    if (LastObjectiveCompletionTime > LastEventTime)
    {
        LastEventTime = LastObjectiveCompletionTime;
    }

    // Check last team elimination time (if available)
    // This would be tracked by the game mode or other systems

    return LastEventTime;
}

void UAuracronProceduralObjectiveSystem::SetupAdditionalObjectiveTemplates()
{
    // Setup additional objective templates
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up additional objective templates"));

    // Collect objective template
    FAuracronProceduralObjective CollectTemplate;
    CollectTemplate.ObjectiveType = EProceduralObjectiveType::Collect;
    CollectTemplate.Priority = EObjectivePriority::Medium;
    CollectTemplate.ObjectiveRadius = 300.0f;
    CollectTemplate.Duration = 150.0f; // 2.5 minutes
    CollectTemplate.RewardMultiplier = 1.1f;
    CollectTemplate.RequiredTeamSize = 1;
    CollectTemplate.ObjectiveDescription = FText::FromString(TEXT("Collect valuable resources from this area"));
    ObjectiveTemplates.Add(EProceduralObjectiveType::Collect, CollectTemplate);

    // Escort objective template
    FAuracronProceduralObjective EscortTemplate;
    EscortTemplate.ObjectiveType = EProceduralObjectiveType::Escort;
    EscortTemplate.Priority = EObjectivePriority::High;
    EscortTemplate.ObjectiveRadius = 500.0f;
    EscortTemplate.Duration = 300.0f; // 5 minutes
    EscortTemplate.RewardMultiplier = 1.3f;
    EscortTemplate.RequiredTeamSize = 2;
    EscortTemplate.ObjectiveDescription = FText::FromString(TEXT("Escort the target safely to the destination"));
    ObjectiveTemplates.Add(EProceduralObjectiveType::Escort, EscortTemplate);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Additional objective templates configured"));
}

bool UAuracronProceduralObjectiveSystem::IsLocationSuitableForObjective(const FVector& Location, EProceduralObjectiveType ObjectiveType) const
{
    // Check if location is suitable for the given objective type

    // Basic accessibility check
    if (!IsLocationAccessible(Location))
    {
        return false;
    }

    // Type-specific suitability checks
    switch (ObjectiveType)
    {
        case EProceduralObjectiveType::Capture:
        case EProceduralObjectiveType::Control:
            return IsLocationCapturable(Location);

        case EProceduralObjectiveType::Defend:
            return IsLocationDefensible(Location);

        case EProceduralObjectiveType::Explore:
            return IsLocationExplorable(Location);

        case EProceduralObjectiveType::Eliminate:
            // Eliminate objectives can be placed anywhere accessible
            return true;

        case EProceduralObjectiveType::Collect:
            // Collect objectives need resource-rich areas
            return IsLocationResourceRich(Location);

        case EProceduralObjectiveType::Escort:
            // Escort objectives need clear paths
            return HasClearPath(Location);

        default:
            return true;
    }
}

float UAuracronProceduralObjectiveSystem::CalculateLocationStrategicValue(const FVector& Location, EProceduralObjectiveType ObjectiveType) const
{
    // Calculate strategic value of location for given objective type
    float StrategicValue = 0.0f;

    // Base strategic factors
    float CentralityScore = CalculateCentralityScore(Location);
    float AccessibilityScore = CalculateAccessibilityScore(Location);
    float VisibilityScore = CalculateVisibilityScore(Location);
    float ResourceScore = CalculateResourceScore(Location);

    // Weight factors based on objective type
    switch (ObjectiveType)
    {
        case EProceduralObjectiveType::Capture:
        case EProceduralObjectiveType::Control:
            StrategicValue = (CentralityScore * 0.4f) + (AccessibilityScore * 0.3f) + (VisibilityScore * 0.3f);
            break;

        case EProceduralObjectiveType::Defend:
            StrategicValue = (VisibilityScore * 0.5f) + (AccessibilityScore * 0.3f) + (CentralityScore * 0.2f);
            break;

        case EProceduralObjectiveType::Collect:
            StrategicValue = (ResourceScore * 0.6f) + (AccessibilityScore * 0.4f);
            break;

        case EProceduralObjectiveType::Eliminate:
            StrategicValue = (CentralityScore * 0.5f) + (AccessibilityScore * 0.5f);
            break;

        default:
            StrategicValue = (CentralityScore + AccessibilityScore + VisibilityScore) / 3.0f;
            break;
    }

    return FMath::Clamp(StrategicValue, 0.0f, 1.0f);
}

bool UAuracronProceduralObjectiveSystem::IsLocationCapturable(const FVector& Location) const
{
    // Check if location can be captured (open area, not too elevated, etc.)

    // Check if location is on solid ground
    if (!IsLocationOnGround(Location))
    {
        return false;
    }

    // Check if there's enough space for capture zone
    float RequiredRadius = 400.0f; // Minimum capture radius
    if (!HasClearSpace(Location, RequiredRadius))
    {
        return false;
    }

    // Check if location is not too elevated (should be accessible)
    float MaxElevation = 1000.0f;
    if (Location.Z > MaxElevation)
    {
        return false;
    }

    return true;
}

bool UAuracronProceduralObjectiveSystem::IsLocationDefensible(const FVector& Location) const
{
    // Check if location is defensible (elevated position, cover, etc.)

    // Check if location provides tactical advantage
    float ElevationAdvantage = CalculateElevationAdvantage(Location);
    if (ElevationAdvantage < 0.3f) // Minimum elevation advantage
    {
        return false;
    }

    // Check for cover availability
    if (!HasNearbyCovers(Location))
    {
        return false;
    }

    // Check if location has multiple escape routes
    if (!HasMultipleExits(Location))
    {
        return false;
    }

    return true;
}

bool UAuracronProceduralObjectiveSystem::IsLocationExplorable(const FVector& Location) const
{
    // Check if location is suitable for exploration objectives

    // Check if location is in unexplored area
    if (IsLocationPreviouslyExplored(Location))
    {
        return false;
    }

    // Check if location has interesting features to explore
    if (!HasExplorationFeatures(Location))
    {
        return false;
    }

    // Check if location is safely accessible
    if (!IsLocationSafelyAccessible(Location))
    {
        return false;
    }

    return true;
}

float UAuracronProceduralObjectiveSystem::CalculateIndividualPlayerEngagement(APawn* Player) const
{
    // Calculate individual player engagement level
    if (!Player)
    {
        return 0.0f;
    }

    float EngagementScore = 0.0f;

    // Factor 1: Recent activity level
    float ActivityScore = CalculatePlayerActivity(Player);
    EngagementScore += ActivityScore * 0.3f;

    // Factor 2: Objective participation
    float ObjectiveScore = CalculateObjectiveParticipation(Player);
    EngagementScore += ObjectiveScore * 0.4f;

    // Factor 3: Combat engagement
    float CombatScore = CalculateCombatEngagement(Player);
    EngagementScore += CombatScore * 0.3f;

    return FMath::Clamp(EngagementScore, 0.0f, 1.0f);
}

void UAuracronProceduralObjectiveSystem::StartObjectiveCapture(FAuracronProceduralObjective& Objective, int32 CapturingTeam)
{
    // Start the capture process for the objective
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Team %d started capturing objective '%s'"), CapturingTeam, *Objective.ObjectiveID);

    // Set capture state
    Objective.bIsBeingCaptured = true;
    Objective.CapturingTeam = CapturingTeam;
    Objective.CaptureStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    Objective.CaptureProgress = 0.0f;

    // Calculate capture rate based on team size and objective difficulty
    TArray<APawn*> CapturingPlayers = GetPlayersInRadius(Objective.TargetLocation, Objective.ObjectiveRadius);
    int32 TeamPlayersInArea = 0;

    for (APawn* Player : CapturingPlayers)
    {
        if (GetPlayerTeam(Player) == CapturingTeam)
        {
            TeamPlayersInArea++;
        }
    }

    // Base capture rate: 1.0 = 100% in 60 seconds
    float BaseCaptureRate = 1.0f / 60.0f;

    // Modify rate based on team size (more players = faster capture, but with diminishing returns)
    float TeamSizeMultiplier = 1.0f + (FMath::Sqrt(static_cast<float>(TeamPlayersInArea)) - 1.0f) * 0.3f;

    // Modify rate based on objective difficulty
    float DifficultyMultiplier = 1.0f / FMath::Max(Objective.DifficultyLevel, 1.0f);

    Objective.CaptureRate = BaseCaptureRate * TeamSizeMultiplier * DifficultyMultiplier;

    // Broadcast capture started event
    OnObjectiveCaptureStarted.Broadcast(Objective.ObjectiveID, CapturingTeam);
}

void UAuracronProceduralObjectiveSystem::UpdateObjectiveProgress(FAuracronProceduralObjective& Objective, float ProgressDelta)
{
    // Update objective progress with the given delta
    float PreviousProgress = Objective.Progress;
    Objective.Progress = FMath::Clamp(Objective.Progress + ProgressDelta, 0.0f, 1.0f);

    // Update capture progress if this is a capture objective
    if (Objective.bIsBeingCaptured)
    {
        Objective.CaptureProgress = FMath::Clamp(Objective.CaptureProgress + ProgressDelta, 0.0f, 1.0f);

        // Check if capture is complete
        if (Objective.CaptureProgress >= 1.0f)
        {
            CompleteObjective(Objective.ObjectiveID, Objective.CapturingTeam);
            return;
        }

        // Check if capture was interrupted (negative progress)
        if (ProgressDelta < 0.0f && Objective.CaptureProgress <= 0.0f)
        {
            // Reset capture state
            Objective.bIsBeingCaptured = false;
            Objective.CapturingTeam = 0;
            Objective.CaptureProgress = 0.0f;

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Capture of objective '%s' was interrupted"), *Objective.ObjectiveID);
            OnObjectiveCaptureInterrupted.Broadcast(Objective.ObjectiveID);
        }
    }

    // Log significant progress changes
    if (FMath::Abs(ProgressDelta) > 0.05f) // 5% threshold
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Objective '%s' progress: %.1f%% -> %.1f%% (Δ%.1f%%)"),
               *Objective.ObjectiveID, PreviousProgress * 100.0f, Objective.Progress * 100.0f, ProgressDelta * 100.0f);
    }

    // Broadcast progress update
    OnObjectiveProgressUpdated.Broadcast(Objective.ObjectiveID, Objective.Progress);
}

int32 UAuracronProceduralObjectiveSystem::CountEliminationTargets(const FVector& Location, float Radius) const
{
    // Count elimination targets in the specified area
    int32 TargetCount = 0;

    UWorld* World = GetWorld();
    if (!World)
    {
        return TargetCount;
    }

    // Search for AI creatures/enemies in the area
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (!Pawn || !IsValid(Pawn))
        {
            continue;
        }

        // Check if pawn is within radius
        float Distance = FVector::Dist(Pawn->GetActorLocation(), Location);
        if (Distance > Radius)
        {
            continue;
        }

        // Check if this is a valid elimination target
        if (IsValidEliminationTarget(Pawn))
        {
            TargetCount++;
        }
    }

    return TargetCount;
}

bool UAuracronProceduralObjectiveSystem::HasPlayerCollectedItems(APawn* Player, const FAuracronProceduralObjective& Objective) const
{
    // Check if player has collected required items for the objective
    if (!Player)
    {
        return false;
    }

    // Get player's inventory component
    UActorComponent* InventoryComponent = Player->GetComponentByClass(UActorComponent::StaticClass());
    if (!InventoryComponent)
    {
        // Try to find inventory through player controller
        if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
        {
            // Check if player has required items through game mode or player state
            if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
            {
                // Use player state to track collected items
                // This is a simplified implementation - in production, you'd have a proper inventory system

                // Check if objective has collection requirements
                if (Objective.RequiredItems.Num() > 0)
                {
                    // For now, simulate collection based on time spent in area
                    float TimeInArea = GetWorld() ? GetWorld()->GetTimeSeconds() - Objective.CreationTime : 0.0f;
                    float RequiredTime = 30.0f; // 30 seconds to "collect" items

                    return TimeInArea >= RequiredTime;
                }
            }
        }
    }

    // If no specific requirements, consider collection complete if player is in area
    float Distance = FVector::Dist(Player->GetActorLocation(), Objective.TargetLocation);
    return Distance <= Objective.ObjectiveRadius;
}

bool UAuracronProceduralObjectiveSystem::HasEscortTargetReachedDestination(const FAuracronProceduralObjective& Objective) const
{
    // Check if escort target has reached the destination

    // Find the escort target (could be an NPC, vehicle, or special object)
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Look for escort target near the objective location
    AActor* EscortTarget = nullptr;

    // Search for potential escort targets
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (!Pawn || !IsValid(Pawn))
        {
            continue;
        }

        // Check if this pawn is tagged as an escort target
        if (Pawn->Tags.Contains(FName("EscortTarget")) ||
            Pawn->Tags.Contains(FName(*FString::Printf(TEXT("EscortTarget_%s"), *Objective.ObjectiveID))))
        {
            EscortTarget = Pawn;
            break;
        }
    }

    if (!EscortTarget)
    {
        // If no specific target found, check if any friendly units are at destination
        TArray<APawn*> PlayersAtDestination = GetPlayersInRadius(Objective.DestinationLocation, 200.0f);
        return !PlayersAtDestination.IsEmpty();
    }

    // Check if escort target is at destination
    float DistanceToDestination = FVector::Dist(EscortTarget->GetActorLocation(), Objective.DestinationLocation);
    return DistanceToDestination <= 300.0f; // 3 meter tolerance
}

TArray<APawn*> UAuracronProceduralObjectiveSystem::GetEscortingPlayers(const FAuracronProceduralObjective& Objective) const
{
    // Get players who are currently escorting (near the escort target or objective area)
    TArray<APawn*> EscortingPlayers;

    UWorld* World = GetWorld();
    if (!World)
    {
        return EscortingPlayers;
    }

    // Find escort target first
    AActor* EscortTarget = nullptr;
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (!Pawn || !IsValid(Pawn))
        {
            continue;
        }

        if (Pawn->Tags.Contains(FName("EscortTarget")) ||
            Pawn->Tags.Contains(FName(*FString::Printf(TEXT("EscortTarget_%s"), *Objective.ObjectiveID))))
        {
            EscortTarget = Pawn;
            break;
        }
    }

    // Get all players in the world
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Player = *ActorIterator;
        if (!Player || !IsValid(Player))
        {
            continue;
        }

        // Check if this is a player-controlled pawn
        if (!Player->GetController() || !Player->GetController()->IsA<APlayerController>())
        {
            continue;
        }

        bool bIsEscorting = false;

        if (EscortTarget)
        {
            // Check if player is near escort target
            float DistanceToTarget = FVector::Dist(Player->GetActorLocation(), EscortTarget->GetActorLocation());
            if (DistanceToTarget <= 500.0f) // 5 meter escort radius
            {
                bIsEscorting = true;
            }
        }
        else
        {
            // Check if player is in objective area
            float DistanceToObjective = FVector::Dist(Player->GetActorLocation(), Objective.TargetLocation);
            if (DistanceToObjective <= Objective.ObjectiveRadius)
            {
                bIsEscorting = true;
            }
        }

        if (bIsEscorting)
        {
            EscortingPlayers.Add(Player);
        }
    }

    return EscortingPlayers;
}

void UAuracronProceduralObjectiveSystem::AdaptObjectiveToMatchState(FAuracronProceduralObjective& Objective)
{
    // Adapt objective parameters based on current match state
    FAuracronMatchStateAnalysis MatchState = CurrentMatchState;

    // Adjust objective duration based on match phase
    switch (MatchState.CurrentPhase)
    {
        case EObjectiveGenerationContext::EarlyGame:
            // Longer objectives in early game to allow exploration
            Objective.Duration *= 1.3f;
            break;

        case EObjectiveGenerationContext::MidGame:
            // Standard duration
            break;

        case EObjectiveGenerationContext::LateGame:
            // Shorter, more intense objectives
            Objective.Duration *= 0.7f;
            break;

        case EObjectiveGenerationContext::Stalemate:
            // High-reward objectives to break stalemate
            Objective.RewardMultiplier *= 1.5f;
            Objective.Priority = EObjectivePriority::Critical;
            break;

        case EObjectiveGenerationContext::Comeback:
            // Balanced objectives for comeback potential
            Objective.RewardMultiplier *= 1.2f;
            break;
    }

    // Adjust based on team balance
    if (MatchState.TeamBalanceScore < 0.3f) // Heavily imbalanced
    {
        // Provide catch-up opportunities for losing team
        Objective.RewardMultiplier *= 1.4f;
        Objective.RequiredTeamSize = FMath::Max(1, Objective.RequiredTeamSize - 1);
    }
    else if (MatchState.TeamBalanceScore > 0.8f) // Very balanced
    {
        // Increase challenge for balanced matches
        Objective.DifficultyLevel *= 1.2f;
    }

    // Adjust based on action intensity
    if (MatchState.ActionIntensity < 0.3f) // Low action
    {
        // Create more engaging objectives
        Objective.ObjectiveRadius *= 1.2f; // Larger area for more encounters
        Objective.RewardMultiplier *= 1.3f; // Higher rewards to incentivize participation
    }
    else if (MatchState.ActionIntensity > 0.8f) // High action
    {
        // Smaller, more focused objectives
        Objective.ObjectiveRadius *= 0.8f;
        Objective.Duration *= 0.9f; // Slightly shorter to maintain pace
    }

    // Adjust based on player engagement
    if (MatchState.PlayerEngagementLevel < 0.4f) // Low engagement
    {
        // Make objectives more accessible and rewarding
        Objective.RequiredTeamSize = FMath::Max(1, Objective.RequiredTeamSize - 1);
        Objective.RewardMultiplier *= 1.5f;
        Objective.DifficultyLevel *= 0.8f;
    }

    // Ensure values stay within reasonable bounds
    Objective.Duration = FMath::Clamp(Objective.Duration, 30.0f, 600.0f); // 30 seconds to 10 minutes
    Objective.RewardMultiplier = FMath::Clamp(Objective.RewardMultiplier, 0.5f, 3.0f);
    Objective.DifficultyLevel = FMath::Clamp(Objective.DifficultyLevel, 0.5f, 3.0f);
    Objective.ObjectiveRadius = FMath::Clamp(Objective.ObjectiveRadius, 200.0f, 1500.0f);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Adapted objective '%s' - Duration: %.1fs, Reward: %.2fx, Difficulty: %.2f"),
           *Objective.ObjectiveID, Objective.Duration, Objective.RewardMultiplier, Objective.DifficultyLevel);
}

float UAuracronProceduralObjectiveSystem::AdjustWeightForMatchState(EProceduralObjectiveType Type, float BaseWeight) const
{
    // Adjust objective type weight based on current match state
    FAuracronMatchStateAnalysis MatchState = CurrentMatchState;
    float AdjustedWeight = BaseWeight;

    // Adjust based on match phase
    switch (MatchState.CurrentPhase)
    {
        case EObjectiveGenerationContext::EarlyGame:
            // Favor exploration and collection in early game
            if (Type == EProceduralObjectiveType::Explore || Type == EProceduralObjectiveType::Collect)
            {
                AdjustedWeight *= 1.5f;
            }
            else if (Type == EProceduralObjectiveType::Eliminate)
            {
                AdjustedWeight *= 0.7f; // Less combat focus early
            }
            break;

        case EObjectiveGenerationContext::MidGame:
            // Balanced mix, favor capture and defend
            if (Type == EProceduralObjectiveType::Capture || Type == EProceduralObjectiveType::Defend)
            {
                AdjustedWeight *= 1.3f;
            }
            break;

        case EObjectiveGenerationContext::LateGame:
            // High-stakes objectives
            if (Type == EProceduralObjectiveType::Eliminate || Type == EProceduralObjectiveType::Control)
            {
                AdjustedWeight *= 1.4f;
            }
            else if (Type == EProceduralObjectiveType::Collect)
            {
                AdjustedWeight *= 0.6f; // Less collection focus late game
            }
            break;

        case EObjectiveGenerationContext::Stalemate:
            // Favor objectives that force engagement
            if (Type == EProceduralObjectiveType::Capture || Type == EProceduralObjectiveType::Eliminate)
            {
                AdjustedWeight *= 2.0f;
            }
            else if (Type == EProceduralObjectiveType::Defend)
            {
                AdjustedWeight *= 0.5f; // Discourage defensive play during stalemate
            }
            break;

        case EObjectiveGenerationContext::Comeback:
            // Favor objectives that allow skill expression
            if (Type == EProceduralObjectiveType::Eliminate || Type == EProceduralObjectiveType::Escort)
            {
                AdjustedWeight *= 1.3f;
            }
            break;
    }

    // Adjust based on team balance
    if (MatchState.TeamBalanceScore < 0.4f) // Imbalanced
    {
        // Favor objectives that can help losing team
        if (Type == EProceduralObjectiveType::Collect || Type == EProceduralObjectiveType::Explore)
        {
            AdjustedWeight *= 1.4f; // Solo-friendly objectives
        }
        else if (Type == EProceduralObjectiveType::Control)
        {
            AdjustedWeight *= 0.7f; // Avoid objectives that favor larger teams
        }
    }

    // Adjust based on action intensity
    if (MatchState.ActionIntensity < 0.3f) // Low action
    {
        // Encourage more dynamic objectives
        if (Type == EProceduralObjectiveType::Eliminate || Type == EProceduralObjectiveType::Escort)
        {
            AdjustedWeight *= 1.6f;
        }
    }
    else if (MatchState.ActionIntensity > 0.8f) // High action
    {
        // Balance with some calmer objectives
        if (Type == EProceduralObjectiveType::Collect || Type == EProceduralObjectiveType::Defend)
        {
            AdjustedWeight *= 1.2f;
        }
    }

    // Adjust based on recent objective history to ensure variety
    int32 RecentCount = GetRecentObjectiveCount(Type, 300.0f); // Last 5 minutes
    if (RecentCount > 2)
    {
        AdjustedWeight *= 0.5f; // Reduce weight for overused types
    }
    else if (RecentCount == 0)
    {
        AdjustedWeight *= 1.2f; // Boost weight for unused types
    }

    return FMath::Max(AdjustedWeight, 0.1f); // Ensure minimum weight
}

// === Helper Function Implementations ===

bool UAuracronProceduralObjectiveSystem::IsLocationAccessible(const FVector& Location) const
{
    // Check if location is accessible to players
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Perform line trace to check if location is reachable
    FHitResult HitResult;
    FVector StartLocation = Location + FVector(0, 0, 1000.0f); // Start from above
    FVector EndLocation = Location - FVector(0, 0, 100.0f); // Trace down

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    bool bHit = World->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        ECC_WorldStatic,
        QueryParams
    );

    // Location is accessible if we hit solid ground
    return bHit && HitResult.bBlockingHit;
}

bool UAuracronProceduralObjectiveSystem::HasClearSpace(const FVector& Location, float Radius) const
{
    // Check if there's enough clear space around the location
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Perform sphere overlap to check for obstacles
    TArray<FOverlapResult> OverlapResults;
    FCollisionShape SphereShape = FCollisionShape::MakeSphere(Radius);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;

    bool bHasOverlap = World->OverlapMultiByChannel(
        OverlapResults,
        Location,
        FQuat::Identity,
        ECC_WorldStatic,
        SphereShape,
        QueryParams
    );

    // Check if overlaps are acceptable (small objects are OK)
    if (bHasOverlap)
    {
        int32 SignificantOverlaps = 0;
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (Overlap.GetActor())
            {
                FVector Origin, BoxExtent;
                Overlap.GetActor()->GetActorBounds(false, Origin, BoxExtent, false);
                float SphereRadius = BoxExtent.Size();
                if (SphereRadius > 50.0f)
                {
                    SignificantOverlaps++;
                }
            }
        }

        // Allow some small overlaps but not too many large ones
        return SignificantOverlaps <= 2;
    }

    return true; // No overlaps, space is clear
}

bool UAuracronProceduralObjectiveSystem::IsLocationOnGround(const FVector& Location) const
{
    // Check if location is on solid ground
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    FHitResult HitResult;
    FVector StartLocation = Location + FVector(0, 0, 10.0f);
    FVector EndLocation = Location - FVector(0, 0, 200.0f);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;

    bool bHit = World->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        ECC_WorldStatic,
        QueryParams
    );

    if (bHit)
    {
        // Check if the hit surface is relatively flat (not too steep)
        FVector HitNormal = HitResult.Normal;
        float SlopeAngle = FMath::Acos(FVector::DotProduct(HitNormal, FVector::UpVector));
        float MaxSlopeRadians = FMath::DegreesToRadians(45.0f); // 45 degree max slope

        return SlopeAngle <= MaxSlopeRadians;
    }

    return false;
}

bool UAuracronProceduralObjectiveSystem::IsValidEliminationTarget(APawn* Pawn) const
{
    // Check if pawn is a valid elimination target
    if (!Pawn || !IsValid(Pawn))
    {
        return false;
    }

    // Don't target player pawns (unless specifically configured)
    if (Pawn->GetController() && Pawn->GetController()->IsA<APlayerController>())
    {
        return false; // Players are not elimination targets
    }

    // Check if pawn is an AI-controlled enemy
    if (Pawn->GetController() && Pawn->GetController()->IsA<AAIController>())
    {
        // Check if pawn is tagged as eliminatable
        if (Pawn->Tags.Contains(FName("EliminationTarget")) ||
            Pawn->Tags.Contains(FName("Enemy")) ||
            Pawn->Tags.Contains(FName("Hostile")))
        {
            return true;
        }

        // Check if pawn is alive and has health
        if (UAbilitySystemComponent* ASC = Pawn->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Simple health check - in production you'd use proper attribute system
            return true; // Assume AI pawns with ASC are valid targets
        }
    }

    return false;
}

int32 UAuracronProceduralObjectiveSystem::GetPlayerTeam(APawn* Player) const
{
    // Get player's team ID
    if (!Player)
    {
        return 0;
    }

    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            // Use player state team ID if available
            // Use player ID as team assignment (simplified)
            return PS->GetPlayerId() % 2 + 1;
        }
    }

    // Fallback: use simple team assignment based on player index
    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        // Simple team assignment: even players team 1, odd players team 2
        int32 PlayerIndex = PC->GetLocalPlayer() ? PC->GetLocalPlayer()->GetControllerId() : 0;
        return (PlayerIndex % 2) + 1;
    }

    return 0; // No team
}

float UAuracronProceduralObjectiveSystem::CalculateElevationAdvantage(const FVector& Location) const
{
    // Calculate elevation advantage of location
    UWorld* World = GetWorld();
    if (!World)
    {
        return 0.0f;
    }

    float LocationHeight = Location.Z;
    float AverageHeight = 0.0f;
    int32 SampleCount = 0;

    // Sample heights in surrounding area
    float SampleRadius = 1000.0f;
    int32 NumSamples = 8;

    for (int32 i = 0; i < NumSamples; i++)
    {
        float Angle = (2.0f * PI * i) / NumSamples;
        FVector SampleLocation = Location + FVector(
            FMath::Cos(Angle) * SampleRadius,
            FMath::Sin(Angle) * SampleRadius,
            0.0f
        );

        FHitResult HitResult;
        FVector StartLocation = SampleLocation + FVector(0, 0, 1000.0f);
        FVector EndLocation = SampleLocation - FVector(0, 0, 1000.0f);

        if (World->LineTraceSingleByChannel(HitResult, StartLocation, EndLocation, ECC_WorldStatic))
        {
            AverageHeight += HitResult.Location.Z;
            SampleCount++;
        }
    }

    if (SampleCount > 0)
    {
        AverageHeight /= SampleCount;
        float HeightDifference = LocationHeight - AverageHeight;
        return FMath::Clamp(HeightDifference / 500.0f, 0.0f, 1.0f); // Normalize to 0-1
    }

    return 0.5f; // Default moderate advantage
}

bool UAuracronProceduralObjectiveSystem::HasNearbyCovers(const FVector& Location) const
{
    // Check for nearby cover objects
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    TArray<FOverlapResult> OverlapResults;
    FCollisionShape SphereShape = FCollisionShape::MakeSphere(300.0f); // 3 meter radius

    bool bHasOverlap = World->OverlapMultiByChannel(
        OverlapResults,
        Location,
        FQuat::Identity,
        ECC_WorldStatic,
        SphereShape
    );

    if (bHasOverlap)
    {
        // Count objects that could provide cover
        int32 CoverObjects = 0;
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Actor = Overlap.GetActor())
            {
                // Check if object is large enough to provide cover
                FVector Origin, BoxExtent;
                Actor->GetActorBounds(false, Origin, BoxExtent);

                if (BoxExtent.Size() > 100.0f) // Minimum size for cover
                {
                    CoverObjects++;
                }
            }
        }

        return CoverObjects >= 2; // Need at least 2 cover objects
    }

    return false;
}

bool UAuracronProceduralObjectiveSystem::HasMultipleExits(const FVector& Location) const
{
    // Check for multiple escape routes from location
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    int32 ClearPaths = 0;
    int32 NumDirections = 8;
    float CheckDistance = 500.0f;

    for (int32 i = 0; i < NumDirections; i++)
    {
        float Angle = (2.0f * PI * i) / NumDirections;
        FVector Direction = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f);
        FVector EndLocation = Location + (Direction * CheckDistance);

        FHitResult HitResult;
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = false;

        bool bBlocked = World->LineTraceSingleByChannel(
            HitResult,
            Location,
            EndLocation,
            ECC_WorldStatic,
            QueryParams
        );

        if (!bBlocked || HitResult.Distance > CheckDistance * 0.7f)
        {
            ClearPaths++;
        }
    }

    return ClearPaths >= 3; // Need at least 3 clear directions
}

bool UAuracronProceduralObjectiveSystem::IsLocationPreviouslyExplored(const FVector& Location) const
{
    // Check if location has been explored recently
    // In production, this would check against a persistent exploration map

    // For now, use a simple distance check against recent objectives
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveType == EProceduralObjectiveType::Explore)
        {
            float Distance = FVector::Dist(Location, Objective.TargetLocation);
            if (Distance < 1000.0f) // 10 meter radius
            {
                return true; // Too close to existing explore objective
            }
        }
    }

    // Check against completed objectives (simplified)
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    // In production, you'd maintain a list of recently completed exploration areas

    return false; // Assume unexplored for now
}

bool UAuracronProceduralObjectiveSystem::HasExplorationFeatures(const FVector& Location) const
{
    // Check if location has interesting features to explore
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    int32 InterestingFeatures = 0;
    float SearchRadius = 200.0f;

    // Look for interesting actors nearby
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        float Distance = FVector::Dist(Actor->GetActorLocation(), Location);
        if (Distance > SearchRadius)
        {
            continue;
        }

        // Check for exploration-worthy features
        if (Actor->Tags.Contains(FName("Landmark")) ||
            Actor->Tags.Contains(FName("Secret")) ||
            Actor->Tags.Contains(FName("Collectible")) ||
            Actor->Tags.Contains(FName("Lore")))
        {
            InterestingFeatures++;
        }

        // Check for unique geometry or structures
        FVector Origin, BoxExtent;
        Actor->GetActorBounds(false, Origin, BoxExtent);
        if (BoxExtent.Size() > 500.0f) // Large structures are interesting
        {
            InterestingFeatures++;
        }
    }

    return InterestingFeatures >= 1; // Need at least one interesting feature
}

bool UAuracronProceduralObjectiveSystem::IsLocationSafelyAccessible(const FVector& Location) const
{
    // Check if location can be safely accessed (not in hazardous area)
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Check for hazardous areas
    TArray<FOverlapResult> OverlapResults;
    FCollisionShape SphereShape = FCollisionShape::MakeSphere(100.0f);

    bool bHasOverlap = World->OverlapMultiByChannel(
        OverlapResults,
        Location,
        FQuat::Identity,
        ECC_WorldDynamic,
        SphereShape
    );

    if (bHasOverlap)
    {
        for (const FOverlapResult& Overlap : OverlapResults)
        {
            if (AActor* Actor = Overlap.GetActor())
            {
                // Check for hazard tags
                if (Actor->Tags.Contains(FName("Hazard")) ||
                    Actor->Tags.Contains(FName("Danger")) ||
                    Actor->Tags.Contains(FName("Trap")))
                {
                    return false;
                }
            }
        }
    }

    // Check if location is not too high or in void
    if (Location.Z > 5000.0f || Location.Z < -1000.0f)
    {
        return false;
    }

    return IsLocationAccessible(Location);
}

bool UAuracronProceduralObjectiveSystem::IsLocationResourceRich(const FVector& Location) const
{
    // Check if location has valuable resources for collection objectives
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    int32 ResourceCount = 0;
    float SearchRadius = 300.0f;

    // Look for resource actors nearby
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        float Distance = FVector::Dist(Actor->GetActorLocation(), Location);
        if (Distance > SearchRadius)
        {
            continue;
        }

        // Check for resource tags
        if (Actor->Tags.Contains(FName("Resource")) ||
            Actor->Tags.Contains(FName("Collectible")) ||
            Actor->Tags.Contains(FName("Mineral")) ||
            Actor->Tags.Contains(FName("Treasure")) ||
            Actor->Tags.Contains(FName("Pickup")))
        {
            ResourceCount++;
        }
    }

    return ResourceCount >= 3; // Need at least 3 resources in area
}

bool UAuracronProceduralObjectiveSystem::HasClearPath(const FVector& Location) const
{
    // Check if location has clear paths for escort objectives
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Check paths in multiple directions
    int32 ClearPaths = 0;
    int32 NumDirections = 4; // Check cardinal directions
    float PathLength = 800.0f;

    for (int32 i = 0; i < NumDirections; i++)
    {
        float Angle = (PI * 0.5f * i); // 90 degree increments
        FVector Direction = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f);

        // Check path by sampling multiple points along the direction
        bool bPathClear = true;
        int32 NumSamples = 8;

        for (int32 j = 1; j <= NumSamples; j++)
        {
            float SampleDistance = (PathLength * j) / NumSamples;
            FVector SampleLocation = Location + (Direction * SampleDistance);

            // Check if sample point is accessible
            if (!IsLocationAccessible(SampleLocation))
            {
                bPathClear = false;
                break;
            }

            // Check for obstacles
            if (!HasClearSpace(SampleLocation, 150.0f))
            {
                bPathClear = false;
                break;
            }
        }

        if (bPathClear)
        {
            ClearPaths++;
        }
    }

    return ClearPaths >= 2; // Need at least 2 clear paths
}

float UAuracronProceduralObjectiveSystem::CalculateCentralityScore(const FVector& Location) const
{
    // Calculate how central the location is relative to player activity
    UWorld* World = GetWorld();
    if (!World)
    {
        return 0.5f;
    }

    TArray<FVector> PlayerLocations;

    // Collect all player locations
    for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (!Pawn || !IsValid(Pawn))
        {
            continue;
        }

        if (Pawn->GetController() && Pawn->GetController()->IsA<APlayerController>())
        {
            PlayerLocations.Add(Pawn->GetActorLocation());
        }
    }

    if (PlayerLocations.Num() == 0)
    {
        return 0.5f; // Default centrality if no players
    }

    // Calculate average distance to all players
    float TotalDistance = 0.0f;
    for (const FVector& PlayerLocation : PlayerLocations)
    {
        TotalDistance += FVector::Dist(Location, PlayerLocation);
    }

    float AverageDistance = TotalDistance / PlayerLocations.Num();

    // Convert to centrality score (closer = higher score)
    float MaxDistance = 10000.0f; // Maximum expected distance
    float CentralityScore = 1.0f - FMath::Clamp(AverageDistance / MaxDistance, 0.0f, 1.0f);

    return CentralityScore;
}

float UAuracronProceduralObjectiveSystem::CalculateAccessibilityScore(const FVector& Location) const
{
    // Calculate how accessible the location is
    float AccessibilityScore = 0.0f;

    // Base accessibility from ground check
    if (IsLocationOnGround(Location))
    {
        AccessibilityScore += 0.4f;
    }

    // Accessibility from clear space
    if (HasClearSpace(Location, 200.0f))
    {
        AccessibilityScore += 0.3f;
    }

    // Accessibility from multiple approaches
    if (HasMultipleExits(Location))
    {
        AccessibilityScore += 0.3f;
    }

    return FMath::Clamp(AccessibilityScore, 0.0f, 1.0f);
}

float UAuracronProceduralObjectiveSystem::CalculateVisibilityScore(const FVector& Location) const
{
    // Calculate visibility/line of sight from location
    UWorld* World = GetWorld();
    if (!World)
    {
        return 0.5f;
    }

    int32 VisibleDirections = 0;
    int32 NumDirections = 8;
    float ViewDistance = 1000.0f;

    for (int32 i = 0; i < NumDirections; i++)
    {
        float Angle = (2.0f * PI * i) / NumDirections;
        FVector Direction = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f);
        FVector EndLocation = Location + (Direction * ViewDistance);

        FHitResult HitResult;
        bool bBlocked = World->LineTraceSingleByChannel(
            HitResult,
            Location + FVector(0, 0, 100.0f), // Slightly elevated start
            EndLocation + FVector(0, 0, 100.0f),
            ECC_WorldStatic
        );

        if (!bBlocked || HitResult.Distance > ViewDistance * 0.7f)
        {
            VisibleDirections++;
        }
    }

    return static_cast<float>(VisibleDirections) / NumDirections;
}

float UAuracronProceduralObjectiveSystem::CalculateResourceScore(const FVector& Location) const
{
    // Calculate resource richness score
    if (IsLocationResourceRich(Location))
    {
        return 1.0f;
    }

    // Partial score based on nearby resources
    UWorld* World = GetWorld();
    if (!World)
    {
        return 0.0f;
    }

    int32 NearbyResources = 0;
    float SearchRadius = 500.0f;

    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        float Distance = FVector::Dist(Actor->GetActorLocation(), Location);
        if (Distance > SearchRadius)
        {
            continue;
        }

        if (Actor->Tags.Contains(FName("Resource")) ||
            Actor->Tags.Contains(FName("Collectible")))
        {
            NearbyResources++;
        }
    }

    return FMath::Clamp(static_cast<float>(NearbyResources) / 5.0f, 0.0f, 1.0f);
}

float UAuracronProceduralObjectiveSystem::CalculatePlayerActivity(APawn* Player) const
{
    // Calculate player activity level
    if (!Player)
    {
        return 0.0f;
    }

    float ActivityScore = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Factor 1: Movement activity
    FVector Velocity = Player->GetVelocity();
    float Speed = Velocity.Size();
    float NormalizedSpeed = FMath::Clamp(Speed / 600.0f, 0.0f, 1.0f); // Normalize to typical running speed
    ActivityScore += NormalizedSpeed * 0.3f;

    // Factor 2: Recent input activity (simplified)
    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        // In production, you'd track input timestamps
        // For now, assume active if player is moving
        if (Speed > 50.0f)
        {
            ActivityScore += 0.4f;
        }
    }

    // Factor 3: Recent damage dealt/taken (simplified)
    // In production, you'd track damage events with timestamps
    ActivityScore += 0.3f; // Assume some base activity

    return FMath::Clamp(ActivityScore, 0.0f, 1.0f);
}

float UAuracronProceduralObjectiveSystem::CalculateObjectiveParticipation(APawn* Player) const
{
    // Calculate player's objective participation level
    if (!Player)
    {
        return 0.0f;
    }

    float ParticipationScore = 0.0f;
    int32 ParticipatedObjectives = 0;
    int32 TotalRecentObjectives = 0;

    // Check participation in recent objectives
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        TotalRecentObjectives++;

        // Check if player is currently participating
        float Distance = FVector::Dist(Player->GetActorLocation(), Objective.TargetLocation);
        if (Distance <= Objective.ObjectiveRadius)
        {
            ParticipatedObjectives++;
        }
    }

    // Calculate participation ratio
    if (TotalRecentObjectives > 0)
    {
        ParticipationScore = static_cast<float>(ParticipatedObjectives) / TotalRecentObjectives;
    }
    else
    {
        ParticipationScore = 0.5f; // Default if no recent objectives
    }

    return FMath::Clamp(ParticipationScore, 0.0f, 1.0f);
}

float UAuracronProceduralObjectiveSystem::CalculateCombatEngagement(APawn* Player) const
{
    // Calculate player's combat engagement level
    if (!Player)
    {
        return 0.0f;
    }

    float CombatScore = 0.0f;

    // Factor 1: Recent weapon usage
    // In production, you'd track weapon fire events
    // For now, check if player has weapons equipped
    if (Player->FindComponentByClass<USkeletalMeshComponent>())
    {
        CombatScore += 0.3f; // Assume armed player has some combat engagement
    }

    // Factor 2: Health status (damaged players likely in combat)
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // In production, you'd check actual health percentage
        CombatScore += 0.4f; // Assume some combat activity
    }

    // Factor 3: Proximity to other players (potential combat)
    UWorld* World = GetWorld();
    if (World)
    {
        int32 NearbyPlayers = 0;
        for (TActorIterator<APawn> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            APawn* OtherPlayer = *ActorIterator;
            if (!OtherPlayer || OtherPlayer == Player || !IsValid(OtherPlayer))
            {
                continue;
            }

            if (OtherPlayer->GetController() && OtherPlayer->GetController()->IsA<APlayerController>())
            {
                float Distance = FVector::Dist(Player->GetActorLocation(), OtherPlayer->GetActorLocation());
                if (Distance <= 1000.0f) // 10 meter combat range
                {
                    NearbyPlayers++;
                }
            }
        }

        if (NearbyPlayers > 0)
        {
            CombatScore += 0.3f;
        }
    }

    return FMath::Clamp(CombatScore, 0.0f, 1.0f);
}

int32 UAuracronProceduralObjectiveSystem::GetRecentObjectiveCount(EProceduralObjectiveType Type, float TimeWindow) const
{
    // Count objectives of given type within time window
    int32 Count = 0;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Check active objectives
    for (const FAuracronProceduralObjective& Objective : ActiveObjectives)
    {
        if (Objective.ObjectiveType == Type)
        {
            float ObjectiveAge = CurrentTime - Objective.CreationTime;
            if (ObjectiveAge <= TimeWindow)
            {
                Count++;
            }
        }
    }

    // In production, you'd also check recently completed objectives
    // For now, just return active count
    return Count;
}
