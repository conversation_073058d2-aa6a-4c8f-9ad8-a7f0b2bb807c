#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "GameFramework/GameUserSettings.h"
#include "HAL/PlatformMemory.h"
#include "RHI.h"
#include "RenderCore.h"
#include "AuracronSimpleHardwareDetection.generated.h"

/**
 * Informações básicas de hardware
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FSimpleHardwareInfo
{
    GENERATED_BODY()

    /** Nome da GPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    FString GPUName;

    /** Memória de vídeo em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    int32 VideoMemoryMB;

    /** Memória RAM total em GB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    float TotalRAMGB;

    /** Memória RAM disponível em GB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    float AvailableRAMGB;

    /** Número de cores da CPU */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    int32 CPUCores;

    /** Suporte a Ray Tracing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    bool bSupportsRayTracing;

    /** Suporte a Mesh Shaders */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware")
    bool bSupportsMeshShaders;

    FSimpleHardwareInfo()
    {
        GPUName = TEXT("Unknown");
        VideoMemoryMB = 0;
        TotalRAMGB = 0.0f;
        AvailableRAMGB = 0.0f;
        CPUCores = 1;
        bSupportsRayTracing = false;
        bSupportsMeshShaders = false;
    }
};

/**
 * Configurações de qualidade recomendadas simplificadas
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FSimpleQualitySettings
{
    GENERATED_BODY()

    /** Nível de qualidade geral (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 OverallQuality;

    /** Qualidade de texturas (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 TextureQuality;

    /** Qualidade de sombras (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 ShadowQuality;

    /** Qualidade de pós-processamento (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 PostProcessQuality;

    /** Qualidade de anti-aliasing (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 AntiAliasingQuality;

    /** Qualidade de distância de visão (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 ViewDistanceQuality;

    /** Qualidade de folhagem (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 FoliageQuality;

    /** Qualidade de shading (0-4) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 ShadingQuality;

    /** Escala de resolução */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float ResolutionScale;

    /** FPS alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 TargetFPS;

    /** Habilitar Lumen */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bEnableLumen;

    /** Habilitar Nanite */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bEnableNanite;

    /** Habilitar Ray Tracing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bEnableRayTracing;

    FSimpleQualitySettings()
    {
        OverallQuality = 2;
        TextureQuality = 2;
        ShadowQuality = 2;
        PostProcessQuality = 2;
        AntiAliasingQuality = 2;
        ViewDistanceQuality = 2;
        FoliageQuality = 2;
        ShadingQuality = 2;
        ResolutionScale = 1.0f;
        TargetFPS = 60;
        bEnableLumen = false;
        bEnableNanite = false;
        bEnableRayTracing = false;
    }
};

/**
 * Sistema simplificado de detecção de hardware para UE 5.6
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronSimpleHardwareDetection : public UObject
{
    GENERATED_BODY()

public:
    UAuracronSimpleHardwareDetection();

    /** Detecta informações de hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    FSimpleHardwareInfo DetectHardware();

    /** Calcula configurações de qualidade recomendadas */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    FSimpleQualitySettings CalculateRecommendedSettings(const FSimpleHardwareInfo& HardwareInfo);

    /** Aplica configurações de qualidade */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void ApplyQualitySettings(const FSimpleQualitySettings& Settings);

    /** Detecta e aplica configurações automaticamente */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void AutoDetectAndApplySettings();

private:
    /** Detecta informações da GPU */
    FString DetectGPUName();
    int32 DetectVideoMemory();
    
    /** Detecta informações da CPU */
    int32 DetectCPUCores();
    
    /** Detecta informações de memória */
    void DetectMemoryInfo(float& TotalRAM, float& AvailableRAM);
    
    /** Detecta suporte a recursos avançados */
    bool DetectRayTracingSupport();
    bool DetectMeshShaderSupport();
    
    /** Calcula qualidade baseada no hardware */
    int32 CalculateQualityLevel(const FSimpleHardwareInfo& HardwareInfo);
};
