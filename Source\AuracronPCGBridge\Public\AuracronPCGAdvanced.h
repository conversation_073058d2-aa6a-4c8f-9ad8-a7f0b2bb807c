// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Advanced Systems Header
// Bridge 2.2: PCG Framework - Advanced Features

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGContext.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronPCGBase.h"
#include "AuracronPCGAdvanced.generated.h"

// Forward declarations
class UAuracronPCGBridgeAPI;
class UAuracronFoliageBridge;
class UAuracronDynamicRealmSubsystem;
class UAuracronWorldPartitionBridgeAPI;

// ========================================
// ADVANCED PCG DATA STRUCTURES
// ========================================

/**
 * Biome configuration data
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronBiomeConfig
{
    GENERATED_BODY()

    /** Biome identifier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FString BiomeID = TEXT("Forest");

    /** Biome display name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FString BiomeName = TEXT("Temperate Forest");

    /** Temperature range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    FVector2D TemperatureRange = FVector2D(15.0f, 25.0f);

    /** Humidity range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    FVector2D HumidityRange = FVector2D(0.4f, 0.8f);

    /** Elevation range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geography")
    FVector2D ElevationRange = FVector2D(0.0f, 1000.0f);

    /** Dominant vegetation types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    TArray<FString> VegetationTypes;

    /** Resource types found in this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resources")
    TArray<FString> ResourceTypes;

    /** Enemy types that spawn in this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    TArray<FString> EnemyTypes;

    /** Biome transition rules */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transitions")
    TArray<FString> AllowedTransitions;

    /** Gameplay tags for this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
    FGameplayTagContainer BiomeTags;

    FAuracronBiomeConfig()
    {
        VegetationTypes.Add(TEXT("Oak"));
        VegetationTypes.Add(TEXT("Pine"));
        ResourceTypes.Add(TEXT("Wood"));
        ResourceTypes.Add(TEXT("Stone"));
        EnemyTypes.Add(TEXT("Wolf"));
        EnemyTypes.Add(TEXT("Bear"));
    }
};

/**
 * Terrain generation parameters
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronTerrainParams
{
    GENERATED_BODY()

    /** Base height */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height")
    float BaseHeight = 0.0f;

    /** Height variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height")
    float HeightVariation = 1000.0f;

    /** Noise frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float NoiseFrequency = 0.01f;

    /** Noise amplitude */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float NoiseAmplitude = 1.0f;

    /** Number of octaves */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    int32 Octaves = 4;

    /** Persistence */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Persistence = 0.5f;

    /** Lacunarity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Lacunarity = 2.0f;

    /** Terrain seed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 Seed = 12345;
};

/**
 * Structure placement rules
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronStructurePlacementRule
{
    GENERATED_BODY()

    /** Structure type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Structure")
    FString StructureType = TEXT("House");

    /** Minimum slope for placement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MinSlope = 0.0f;

    /** Maximum slope for placement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MaxSlope = 15.0f;

    /** Minimum distance from water */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MinWaterDistance = 100.0f;

    /** Maximum distance from water */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
    float MaxWaterDistance = 1000.0f;

    /** Required biome types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TArray<FString> RequiredBiomes;

    /** Forbidden biome types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TArray<FString> ForbiddenBiomes;

    /** Minimum distance from other structures */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacing")
    float MinStructureDistance = 500.0f;

    /** Placement probability */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Probability", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PlacementProbability = 0.1f;
};

/**
 * Resource distribution data
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronResourceDistribution
{
    GENERATED_BODY()

    /** Resource type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
    FString ResourceType = TEXT("Iron");

    /** Base rarity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rarity", meta = (ClampMin = "0.001", ClampMax = "1.0"))
    float BaseRarity = 0.1f;

    /** Depth preference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Depth")
    FVector2D DepthRange = FVector2D(-100.0f, -1000.0f);

    /** Biome modifiers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    TMap<FString, float> BiomeModifiers;

    /** Cluster parameters */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering")
    int32 ClusterSize = 3;

    /** Cluster radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering")
    float ClusterRadius = 200.0f;

    /** Quality range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    FVector2D QualityRange = FVector2D(0.5f, 1.5f);

    /** Respawn time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    float RespawnTime = 300.0f;

    FAuracronResourceDistribution()
    {
        BiomeModifiers.Add(TEXT("Mountain"), 2.0f);
        BiomeModifiers.Add(TEXT("Desert"), 0.5f);
        BiomeModifiers.Add(TEXT("Forest"), 1.0f);
    }
};

// ========================================
// ADVANCED PCG SETTINGS CLASSES
// ========================================

/**
 * Auracron Master PCG Settings
 * Coordinates all PCG generation systems
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Master")
class AURACRONPCGBRIDGE_API UAuracronMasterPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronMasterPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** World generation seed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Generation")
    int32 WorldSeed = 42;

    /** World size in kilometers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Generation", meta = (ClampMin = "1.0", ClampMax = "100.0"))
    float WorldSize = 10.0f;

    /** Biome configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biomes")
    TArray<FAuracronBiomeConfig> BiomeConfigs;

    /** Terrain generation parameters */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    FAuracronTerrainParams TerrainParams;

    /** Structure placement rules */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Structures")
    TArray<FAuracronStructurePlacementRule> StructureRules;

    /** Resource distributions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resources")
    TArray<FAuracronResourceDistribution> ResourceDistributions;

    /** Enable performance optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableOptimization = true;

    /** LOD system integration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseLODSystem = true;

    /** Streaming integration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseStreaming = true;

    /** Quality level (1-5) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (ClampMin = "1", ClampMax = "5"))
    int32 QualityLevel = 3;

    /** Integration with all bridges */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bIntegrateAllBridges = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Biome Transition PCG Settings
 * Handles smooth transitions between different biomes
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Biome")
class AURACRONPCGBRIDGE_API UAuracronBiomeTransitionPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronBiomeTransitionPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Transition width in meters */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition", meta = (ClampMin = "50.0", ClampMax = "2000.0"))
    float TransitionWidth = 500.0f;

    /** Transition smoothness */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float TransitionSmoothness = 1.0f;

    /** Source biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biomes")
    FString SourceBiome = TEXT("Forest");

    /** Target biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biomes")
    FString TargetBiome = TEXT("Desert");

    /** Blend vegetation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    bool bBlendVegetation = true;

    /** Blend terrain */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    bool bBlendTerrain = true;

    /** Blend resources */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    bool bBlendResources = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Quest Integration PCG Settings
 * Generates quest-related content and objectives
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Quest")
class AURACRONPCGBRIDGE_API UAuracronQuestPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronQuestPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Quest type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest")
    FString QuestType = TEXT("Exploration");

    /** Difficulty level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quest", meta = (ClampMin = "1", ClampMax = "10"))
    int32 DifficultyLevel = 5;

    /** Objective count */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objectives", meta = (ClampMin = "1", ClampMax = "20"))
    int32 ObjectiveCount = 3;

    /** Reward tier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards", meta = (ClampMin = "1", ClampMax = "5"))
    int32 RewardTier = 2;

    /** Required biomes for quest */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
    TArray<FString> RequiredBiomes;

    /** Quest duration in minutes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration", meta = (ClampMin = "5.0", ClampMax = "180.0"))
    float QuestDuration = 30.0f;

    /** Integration with Progression Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseProgressionBridge = true;

    /** Integration with Lore Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseLoreBridge = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Performance Optimization PCG Settings
 * Optimizes PCG generation for performance
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Performance")
class AURACRONPCGBRIDGE_API UAuracronPerformancePCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronPerformancePCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Enable GPU acceleration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU")
    bool bUseGPUAcceleration = true;

    /** Maximum generation time per frame (ms) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1.0", ClampMax = "33.0"))
    float MaxGenerationTimePerFrame = 16.0f;

    /** LOD distance multipliers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    TArray<float> LODDistances = {500.0f, 1000.0f, 2000.0f, 5000.0f};

    /** Culling distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling", meta = (ClampMin = "1000.0", ClampMax = "50000.0"))
    float CullingDistance = 10000.0f;

    /** Streaming chunk size */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float StreamingChunkSize = 2000.0f;

    /** Cache size in MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache", meta = (ClampMin = "100", ClampMax = "2048"))
    int32 CacheSizeMB = 512;

    /** Enable multithreading */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading")
    bool bUseMultithreading = true;

    /** Thread count (0 = auto) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threading", meta = (ClampMin = "0", ClampMax = "32"))
    int32 ThreadCount = 0;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

// ========================================
// ADVANCED PCG ELEMENTS
// ========================================

/**
 * Auracron Master PCG Element
 * Coordinates all PCG generation
 */
class AURACRONPCGBRIDGE_API FAuracronMasterPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronMasterPCGElement() = default;
    virtual ~FAuracronMasterPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void InitializeWorldGeneration(FPCGContext* Context, const UAuracronMasterPCGSettings* Settings) const;
    void GenerateBiomeMap(FPCGContext* Context, const UAuracronMasterPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void CoordinateSubSystems(FPCGContext* Context, const UAuracronMasterPCGSettings* Settings) const;
    void OptimizeGeneration(FPCGContext* Context, const UAuracronMasterPCGSettings* Settings) const;
};

/**
 * Auracron Biome Transition PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronBiomeTransitionPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronBiomeTransitionPCGElement() = default;
    virtual ~FAuracronBiomeTransitionPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void CreateTransitionZone(FPCGContext* Context, const UAuracronBiomeTransitionPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void BlendBiomeProperties(const FVector& Location, const UAuracronBiomeTransitionPCGSettings* Settings, float& BlendFactor) const;
    void ApplyTransitionEffects(FPCGContext* Context, const UAuracronBiomeTransitionPCGSettings* Settings) const;
};

/**
 * Auracron Quest PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronQuestPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronQuestPCGElement() = default;
    virtual ~FAuracronQuestPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateQuestObjectives(FPCGContext* Context, const UAuracronQuestPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void PlaceQuestItems(FPCGContext* Context, const UAuracronQuestPCGSettings* Settings) const;
    void IntegrateWithProgressionBridge(FPCGContext* Context, const UAuracronQuestPCGSettings* Settings) const;
    void IntegrateWithLoreBridge(FPCGContext* Context, const UAuracronQuestPCGSettings* Settings) const;
};

/**
 * Auracron Performance PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronPerformancePCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronPerformancePCGElement() = default;
    virtual ~FAuracronPerformancePCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void OptimizeGeneration(FPCGContext* Context, const UAuracronPerformancePCGSettings* Settings) const;
    void SetupLODSystem(FPCGContext* Context, const UAuracronPerformancePCGSettings* Settings) const;
    void ConfigureStreaming(FPCGContext* Context, const UAuracronPerformancePCGSettings* Settings) const;
    void EnableGPUAcceleration(FPCGContext* Context, const UAuracronPerformancePCGSettings* Settings) const;
};
