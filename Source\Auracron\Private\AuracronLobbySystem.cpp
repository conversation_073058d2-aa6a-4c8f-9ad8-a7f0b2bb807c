/**
 * AuracronLobbySystem.cpp
 * 
 * Implementação do sistema de lobby e matchmaking do Auracron
 */

#include "AuracronLobbySystem.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Math/UnrealMathUtility.h"

// Includes dos bridges - temporariamente removidos para compilação inicial
// #include "AuracronHarmonyEngineBridge/Public/HarmonyEngineSubsystem.h"
// #include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"

void UAuracronLobbySystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Inicializando sistema de lobby"));
    }

    // Inicializar configurações padrão
    MatchmakingConfig = FAuracronMatchmakingConfig();
    CurrentLobby = FAuracronLobbyData();
    LocalPlayerData = FAuracronPlayerMatchmakingData();

    // Inicializar ponteiros dos bridges
    HarmonyEngineSubsystem = nullptr;

    // Inicializar referências dos bridges
    InitializeBridgeReferences();

    // Configurar timer de atualização de matchmaking
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            MatchmakingUpdateTimer,
            this,
            &UAuracronLobbySystem::ProcessMatchmakingQueue,
            1.0f, // 1 segundo
            true  // repetir
        );
    }

    bSystemInitialized = true;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Sistema inicializado com sucesso"));
    }
}

void UAuracronLobbySystem::Deinitialize()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Desinicializando sistema"));
    }

    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(MatchmakingUpdateTimer);
    }

    // Cancelar matchmaking se ativo
    if (bIsSearching)
    {
        CancelMatchmaking();
    }

    // Limpar dados
    ActiveLobbies.Empty();
    MatchmakingQueue.Empty();
    bSystemInitialized = false;

    Super::Deinitialize();

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Sistema desinicializado"));
    }
}

FString UAuracronLobbySystem::CreateLobby(const FAuracronLobbyData& LobbyData)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronLobbySystem: Sistema não inicializado"));
        return TEXT("");
    }

    // Gerar ID único para o lobby
    FString LobbyID = GenerateUniqueID();

    // Criar dados do lobby
    FAuracronLobbyData NewLobby = LobbyData;
    NewLobby.LobbyID = LobbyID;
    NewLobby.CreationTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    NewLobby.CurrentState = EAuracronLobbyState::Idle;

    // Adicionar aos lobbies ativos
    ActiveLobbies.Add(LobbyID, NewLobby);

    // Definir como lobby atual
    CurrentLobby = NewLobby;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Lobby criado - ID: %s, Nome: %s"), 
               *LobbyID, *NewLobby.LobbyName);
    }

    // Disparar evento
    OnLobbyCreated.Broadcast(NewLobby);

    return LobbyID;
}

bool UAuracronLobbySystem::JoinLobby(const FString& LobbyID, const FAuracronPlayerMatchmakingData& PlayerData, const FString& Password)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronLobbySystem: Sistema não inicializado"));
        return false;
    }

    // Verificar se lobby existe
    FAuracronLobbyData* LobbyPtr = ActiveLobbies.Find(LobbyID);
    if (!LobbyPtr)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Lobby não encontrado: %s"), *LobbyID);
        return false;
    }

    FAuracronLobbyData& Lobby = *LobbyPtr;

    // Verificar senha se lobby for privado
    if (Lobby.bIsPrivate && Lobby.Password != Password)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Senha incorreta para lobby: %s"), *LobbyID);
        return false;
    }

    // Verificar se lobby está cheio
    if (Lobby.Players.Num() >= Lobby.MaxPlayers)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Lobby cheio: %s"), *LobbyID);
        return false;
    }

    // Verificar se jogador já está no lobby
    for (const FAuracronPlayerMatchmakingData& ExistingPlayer : Lobby.Players)
    {
        if (ExistingPlayer.PlayerID == PlayerData.PlayerID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador já está no lobby: %s"), *PlayerData.PlayerID);
            return false;
        }
    }

    // Validar jogador para o lobby
    if (!ValidatePlayerForMatch(PlayerData))
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador não atende critérios: %s"), *PlayerData.PlayerID);
        return false;
    }

    // Adicionar jogador ao lobby
    Lobby.Players.Add(PlayerData);

    // Atualizar lobby atual se for o mesmo
    if (CurrentLobby.LobbyID == LobbyID)
    {
        CurrentLobby = Lobby;
        LocalPlayerData = PlayerData;
    }

    // Atualizar lobby nos dados ativos
    ActiveLobbies[LobbyID] = Lobby;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Jogador entrou no lobby - Player: %s, Lobby: %s (%d/%d)"), 
               *PlayerData.PlayerName, *LobbyID, Lobby.Players.Num(), Lobby.MaxPlayers);
    }

    // Disparar evento
    OnPlayerJoinedLobby.Broadcast(PlayerData.PlayerID, PlayerData);

    // Verificar se lobby está cheio para iniciar partida
    if (Lobby.Players.Num() >= Lobby.MaxPlayers)
    {
        // Iniciar seleção de campeões
        StartChampionSelection();
    }

    return true;
}

void UAuracronLobbySystem::LeaveLobby(const FString& PlayerID)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronLobbySystem: Sistema não inicializado"));
        return;
    }

    // Encontrar e remover jogador do lobby atual
    bool bPlayerFound = false;
    for (int32 i = CurrentLobby.Players.Num() - 1; i >= 0; i--)
    {
        if (CurrentLobby.Players[i].PlayerID == PlayerID)
        {
            CurrentLobby.Players.RemoveAt(i);
            bPlayerFound = true;
            break;
        }
    }

    if (bPlayerFound)
    {
        // Atualizar lobby nos dados ativos
        if (!CurrentLobby.LobbyID.IsEmpty())
        {
            ActiveLobbies[CurrentLobby.LobbyID] = CurrentLobby;
        }

        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Jogador saiu do lobby - Player: %s (%d/%d)"), 
                   *PlayerID, CurrentLobby.Players.Num(), CurrentLobby.MaxPlayers);
        }

        // Disparar evento
        OnPlayerLeftLobby.Broadcast(PlayerID);

        // Se era o jogador local, limpar dados
        if (LocalPlayerData.PlayerID == PlayerID)
        {
            LocalPlayerData = FAuracronPlayerMatchmakingData();
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador não encontrado no lobby: %s"), *PlayerID);
    }
}

void UAuracronLobbySystem::StartMatchmaking(const FAuracronPlayerMatchmakingData& PlayerData, EAuracronMatchmakingType MatchmakingType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronLobbySystem: Sistema não inicializado"));
        return;
    }

    if (bIsSearching)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Já está buscando partida"));
        return;
    }

    // Validar dados do jogador
    if (!ValidatePlayerForMatch(PlayerData))
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador não atende critérios para matchmaking"));
        return;
    }

    // Configurar busca
    LocalPlayerData = PlayerData;
    bIsSearching = true;
    SearchStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Adicionar à fila de matchmaking
    MatchmakingQueue.Add(PlayerData);

    // Criar lobby temporário para busca
    FAuracronLobbyData SearchLobby;
    SearchLobby.LobbyID = GenerateUniqueID();
    SearchLobby.LobbyName = TEXT("Searching...");
    SearchLobby.MatchmakingType = MatchmakingType;
    SearchLobby.CurrentState = EAuracronLobbyState::Searching;
    SearchLobby.Players.Add(PlayerData);

    CurrentLobby = SearchLobby;
    ChangeLobbyState(EAuracronLobbyState::Searching);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Iniciando matchmaking - Player: %s, Tipo: %d"), 
               *PlayerData.PlayerName, (int32)MatchmakingType);
    }

    // Notificar Harmony Engine
    if (HarmonyEngineSubsystem)
    {
        // HarmonyEngineSubsystem->OnPlayerStartedMatchmaking(PlayerData);
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Notificando Harmony Engine sobre início de matchmaking"));
        }
    }
}

void UAuracronLobbySystem::CancelMatchmaking()
{
    if (!bIsSearching)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Não está buscando partida"));
        return;
    }

    // Remover da fila de matchmaking
    MatchmakingQueue.RemoveAll([this](const FAuracronPlayerMatchmakingData& Player)
    {
        return Player.PlayerID == LocalPlayerData.PlayerID;
    });

    bIsSearching = false;
    SearchStartTime = 0.0f;

    // Limpar lobby atual
    CurrentLobby = FAuracronLobbyData();
    ChangeLobbyState(EAuracronLobbyState::Idle);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Matchmaking cancelado - Player: %s"), 
               *LocalPlayerData.PlayerName);
    }

    // Notificar Harmony Engine
    if (HarmonyEngineSubsystem)
    {
        // HarmonyEngineSubsystem->OnPlayerCancelledMatchmaking(LocalPlayerData);
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Notificando Harmony Engine sobre cancelamento de matchmaking"));
        }
    }
}

void UAuracronLobbySystem::UpdateMatchmakingConfig(const FAuracronMatchmakingConfig& NewConfig)
{
    MatchmakingConfig = NewConfig;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Configurações de matchmaking atualizadas"));
    }
}

void UAuracronLobbySystem::StartChampionSelection()
{
    if (CurrentLobby.CurrentState != EAuracronLobbyState::FoundMatch &&
        CurrentLobby.CurrentState != EAuracronLobbyState::Idle)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Tentativa de iniciar seleção em estado inválido"));
        return;
    }

    if (CurrentLobby.Players.Num() < CurrentLobby.MaxPlayers)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Não há jogadores suficientes para seleção"));
        return;
    }

    // Balancear equipes
    TArray<FAuracronPlayerMatchmakingData> Players = CurrentLobby.Players;
    BalanceTeams(Players);
    CurrentLobby.Players = Players;

    // Mudar estado para seleção de campeões
    ChangeLobbyState(EAuracronLobbyState::ChampionSelect);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Seleção de campeões iniciada"));
    }

    // Notificar sistema de sígilos
    // Note: SigilosBridge é um ActorComponent, será obtido via GetComponentByClass quando necessário
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Iniciando seleção de campeões - SigilosBridge será notificado via PlayerController"));
    }

    // Disparar evento
    OnChampionSelectionStarted.Broadcast();
}

bool UAuracronLobbySystem::SelectChampion(const FString& PlayerID, int32 ChampionID)
{
    if (CurrentLobby.CurrentState != EAuracronLobbyState::ChampionSelect)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Seleção não está ativa"));
        return false;
    }

    if (ChampionID < 0 || ChampionID >= 50) // 50 campeões disponíveis
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: ID de campeão inválido: %d"), ChampionID);
        return false;
    }

    // Verificar se campeão já foi selecionado
    for (const FAuracronPlayerMatchmakingData& Player : CurrentLobby.Players)
    {
        if (Player.FavoriteChampions.Contains(ChampionID) && Player.PlayerID != PlayerID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Campeão já selecionado: %d"), ChampionID);
            return false;
        }
    }

    // Encontrar jogador e atualizar seleção
    for (FAuracronPlayerMatchmakingData& Player : CurrentLobby.Players)
    {
        if (Player.PlayerID == PlayerID)
        {
            // Limpar seleções anteriores e adicionar nova
            Player.FavoriteChampions.Empty();
            Player.FavoriteChampions.Add(ChampionID);

            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Campeão selecionado - Player: %s, Champion: %d"),
                       *Player.PlayerName, ChampionID);
            }

            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador não encontrado: %s"), *PlayerID);
    return false;
}

bool UAuracronLobbySystem::BanChampion(const FString& PlayerID, int32 ChampionID)
{
    if (CurrentLobby.CurrentState != EAuracronLobbyState::ChampionSelect)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Seleção não está ativa"));
        return false;
    }

    if (ChampionID < 0 || ChampionID >= 50)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: ID de campeão inválido para ban: %d"), ChampionID);
        return false;
    }

    // Implementar sistema de bans
    // Por enquanto, apenas log
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Campeão banido - Player: %s, Champion: %d"),
               *PlayerID, ChampionID);
    }

    return true;
}

void UAuracronLobbySystem::ConfirmSelection(const FString& PlayerID)
{
    if (CurrentLobby.CurrentState != EAuracronLobbyState::ChampionSelect)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Seleção não está ativa"));
        return;
    }

    // Encontrar jogador e marcar como confirmado
    for (FAuracronPlayerMatchmakingData& Player : CurrentLobby.Players)
    {
        if (Player.PlayerID == PlayerID)
        {
            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Seleção confirmada - Player: %s"),
                       *Player.PlayerName);
            }

            // Verificar se todos confirmaram
            bool bAllConfirmed = true;
            for (const FAuracronPlayerMatchmakingData& CheckPlayer : CurrentLobby.Players)
            {
                if (CheckPlayer.FavoriteChampions.Num() == 0)
                {
                    bAllConfirmed = false;
                    break;
                }
            }

            if (bAllConfirmed)
            {
                // Iniciar carregamento do jogo
                ChangeLobbyState(EAuracronLobbyState::LoadingGame);
            }

            return;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Jogador não encontrado para confirmação: %s"), *PlayerID);
}

// === Private Implementation ===

void UAuracronLobbySystem::ProcessMatchmakingQueue()
{
    if (!bSystemInitialized || MatchmakingQueue.Num() == 0)
    {
        return;
    }

    // Verificar timeout de busca
    if (bIsSearching && GetWorld())
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        float SearchDuration = CurrentTime - SearchStartTime;

        if (SearchDuration > LocalPlayerData.MaxSearchTime)
        {
            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Timeout de busca atingido"));
            }
            CancelMatchmaking();
            return;
        }
    }

    // Tentar formar partidas
    if (MatchmakingQueue.Num() >= 10) // Mínimo para uma partida 5v5
    {
        TArray<FAuracronPlayerMatchmakingData> MatchPlayers;

        // Selecionar os 10 primeiros jogadores da fila
        for (int32 i = 0; i < 10 && i < MatchmakingQueue.Num(); i++)
        {
            MatchPlayers.Add(MatchmakingQueue[i]);
        }

        // Calcular qualidade da partida
        float MatchQuality = CalculateMatchQuality(MatchPlayers);

        if (MatchQuality >= 0.7f) // 70% de qualidade mínima
        {
            // Criar lobby para a partida
            FAuracronLobbyData MatchLobby;
            MatchLobby.LobbyID = GenerateUniqueID();
            MatchLobby.LobbyName = TEXT("Match Found");
            MatchLobby.MatchmakingType = EAuracronMatchmakingType::Casual;
            MatchLobby.Players = MatchPlayers;
            MatchLobby.MaxPlayers = 10;
            MatchLobby.CurrentState = EAuracronLobbyState::FoundMatch;

            // Remover jogadores da fila
            for (const FAuracronPlayerMatchmakingData& Player : MatchPlayers)
            {
                MatchmakingQueue.RemoveAll([&Player](const FAuracronPlayerMatchmakingData& QueuePlayer)
                {
                    return QueuePlayer.PlayerID == Player.PlayerID;
                });
            }

            // Definir como lobby atual se jogador local está na partida
            for (const FAuracronPlayerMatchmakingData& Player : MatchPlayers)
            {
                if (Player.PlayerID == LocalPlayerData.PlayerID)
                {
                    CurrentLobby = MatchLobby;
                    bIsSearching = false;
                    ChangeLobbyState(EAuracronLobbyState::FoundMatch);
                    break;
                }
            }

            // Adicionar aos lobbies ativos
            ActiveLobbies.Add(MatchLobby.LobbyID, MatchLobby);

            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Partida encontrada - Qualidade: %.2f"), MatchQuality);
            }

            // Disparar evento
            OnMatchFound.Broadcast(MatchLobby);
        }
    }
}

bool UAuracronLobbySystem::ValidatePlayerForMatch(const FAuracronPlayerMatchmakingData& PlayerData)
{
    // Verificar score de comportamento
    if (PlayerData.BehaviorScore < MatchmakingConfig.MinBehaviorScore)
    {
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Score de comportamento muito baixo: %.2f"),
                   PlayerData.BehaviorScore);
        }
        return false;
    }

    // Verificar ping
    if (PlayerData.AveragePing > MatchmakingConfig.MaxPing)
    {
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Ping muito alto: %.2f"),
                   PlayerData.AveragePing);
        }
        return false;
    }

    // Verificar nível mínimo
    if (PlayerData.PlayerLevel < 1)
    {
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Nível muito baixo: %d"),
                   PlayerData.PlayerLevel);
        }
        return false;
    }

    return true;
}

float UAuracronLobbySystem::CalculateMatchQuality(const TArray<FAuracronPlayerMatchmakingData>& Players)
{
    if (Players.Num() != 10)
    {
        return 0.0f;
    }

    float TotalQuality = 0.0f;
    int32 QualityChecks = 0;

    // Calcular diferença de skill rating
    float MinSkill = Players[0].SkillRating;
    float MaxSkill = Players[0].SkillRating;
    float TotalSkill = 0.0f;

    for (const FAuracronPlayerMatchmakingData& Player : Players)
    {
        MinSkill = FMath::Min(MinSkill, Player.SkillRating);
        MaxSkill = FMath::Max(MaxSkill, Player.SkillRating);
        TotalSkill += Player.SkillRating;
    }

    float SkillDifference = MaxSkill - MinSkill;
    float SkillQuality = FMath::Clamp(1.0f - (SkillDifference / MatchmakingConfig.MaxSkillDifference), 0.0f, 1.0f);
    TotalQuality += SkillQuality;
    QualityChecks++;

    // Calcular qualidade de ping
    float TotalPing = 0.0f;
    for (const FAuracronPlayerMatchmakingData& Player : Players)
    {
        TotalPing += Player.AveragePing;
    }
    float AveragePing = TotalPing / Players.Num();
    float PingQuality = FMath::Clamp(1.0f - (AveragePing / MatchmakingConfig.MaxPing), 0.0f, 1.0f);
    TotalQuality += PingQuality;
    QualityChecks++;

    // Calcular qualidade de comportamento
    float TotalBehavior = 0.0f;
    for (const FAuracronPlayerMatchmakingData& Player : Players)
    {
        TotalBehavior += Player.BehaviorScore;
    }
    float AverageBehavior = TotalBehavior / Players.Num();
    float BehaviorQuality = AverageBehavior / 100.0f; // Normalizar para 0-1
    TotalQuality += BehaviorQuality * MatchmakingConfig.BehaviorWeight;
    QualityChecks++;

    return QualityChecks > 0 ? TotalQuality / QualityChecks : 0.0f;
}

void UAuracronLobbySystem::BalanceTeams(TArray<FAuracronPlayerMatchmakingData>& Players)
{
    if (Players.Num() != 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronLobbySystem: Número incorreto de jogadores para balanceamento: %d"),
               Players.Num());
        return;
    }

    // Ordenar jogadores por skill rating
    Players.Sort([](const FAuracronPlayerMatchmakingData& A, const FAuracronPlayerMatchmakingData& B)
    {
        return A.SkillRating > B.SkillRating;
    });

    // Algoritmo de balanceamento simples: alternar entre equipes
    TArray<FAuracronPlayerMatchmakingData> Team1;
    TArray<FAuracronPlayerMatchmakingData> Team2;

    for (int32 i = 0; i < Players.Num(); i++)
    {
        if (i % 2 == 0)
        {
            Team1.Add(Players[i]);
        }
        else
        {
            Team2.Add(Players[i]);
        }
    }

    // Calcular skill médio das equipes
    float Team1Skill = 0.0f;
    float Team2Skill = 0.0f;

    for (const FAuracronPlayerMatchmakingData& Player : Team1)
    {
        Team1Skill += Player.SkillRating;
    }
    Team1Skill /= Team1.Num();

    for (const FAuracronPlayerMatchmakingData& Player : Team2)
    {
        Team2Skill += Player.SkillRating;
    }
    Team2Skill /= Team2.Num();

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Equipes balanceadas - Team1: %.2f, Team2: %.2f"),
               Team1Skill, Team2Skill);
    }

    // Reorganizar array com equipes balanceadas
    Players.Empty();
    Players.Append(Team1);
    Players.Append(Team2);
}

void UAuracronLobbySystem::InitializeBridgeReferences()
{
    if (UWorld* World = GetWorld())
    {
        // Buscar por HarmonyEngineSubsystem usando reflexão
        TArray<UWorldSubsystem*> WorldSubsystems = World->GetSubsystemArrayCopy<UWorldSubsystem>();
        for (UWorldSubsystem* Subsystem : WorldSubsystems)
        {
            if (Subsystem && Subsystem->GetClass()->GetName().Contains(TEXT("HarmonyEngine")))
            {
                HarmonyEngineSubsystem = Subsystem;
                break;
            }
        }

        // Note: SigilosBridge é um ActorComponent, será obtido via GetComponentByClass quando necessário

        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Bridge references initialized - HarmonyEngine: %s"),
                HarmonyEngineSubsystem ? TEXT("Found") : TEXT("Not Available"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronLobbySystem: World não disponível para inicializar bridges"));
    }
}

void UAuracronLobbySystem::ChangeLobbyState(EAuracronLobbyState NewState)
{
    if (CurrentLobby.CurrentState == NewState)
    {
        return;
    }

    EAuracronLobbyState OldState = CurrentLobby.CurrentState;
    CurrentLobby.CurrentState = NewState;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronLobbySystem: Estado do lobby mudou de %d para %d"),
               (int32)OldState, (int32)NewState);
    }

    // Atualizar lobby nos dados ativos
    if (!CurrentLobby.LobbyID.IsEmpty())
    {
        ActiveLobbies[CurrentLobby.LobbyID] = CurrentLobby;
    }
}

FString UAuracronLobbySystem::GenerateUniqueID()
{
    // Gerar ID único baseado em timestamp e número aleatório
    int64 Timestamp = FDateTime::Now().GetTicks();
    int32 RandomNumber = FMath::RandRange(1000, 9999);

    return FString::Printf(TEXT("LOBBY_%lld_%d"), Timestamp, RandomNumber);
}
