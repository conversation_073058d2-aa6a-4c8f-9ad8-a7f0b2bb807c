#!/usr/bin/env python3
"""
AURACRON - Planície Radiante Base Terrain Creation Script
Creates the terrestrial realm base terrain using UE5.6 Python API
Uses AuracronDynamicRealmBridge C++ bridge
Production-ready implementation with complete error handling
"""

import unreal
import sys
import os
import math

class PlanicieRadianteCreator:
    """Production-ready Planície Radiante terrain creator"""
    
    def __init__(self):
        """Initialize the terrain creator with UE5.6 subsystems"""
        try:
            # Initialize UE5.6 subsystems
            self.editor_level_lib = unreal.EditorLevelLibrary  # Deprecated - keeping for compatibility
            self.editor_loading_saving = unreal.EditorLoadingAndSavingUtils  # Modern replacement
            self.editor_asset_lib = unreal.EditorAssetLibrary
            self.editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
            self.curve_factory = unreal.CurveFloatFactory()
            
            # WorldPartitionSubsystem is accessed through the world, not as EditorSubsystem
            self.world_partition_subsystem = None  # Will be initialized when world is available
            
            # Initialize AuracronDynamicRealmSubsystem (if available)
            try:
                # Get current world first
                editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
                world = editor_subsystem.get_editor_world()
                if world:
                    try:
                        # Try multiple methods to get the AuracronDynamicRealmSubsystem
                        self.realm_bridge = None

                        # Method 1: Direct subsystem access via world
                        try:
                            self.realm_bridge = world.get_subsystem(unreal.AuracronDynamicRealmSubsystem)
                            if self.realm_bridge:
                                unreal.log("✅ AuracronDynamicRealmSubsystem accessed via world.get_subsystem")
                        except Exception:
                            pass

                        # Method 2: Using USubsystemBlueprintLibrary
                        if not self.realm_bridge:
                            try:
                                self.realm_bridge = unreal.SubsystemBlueprintLibrary.get_world_subsystem(
                                    world, unreal.AuracronDynamicRealmSubsystem
                                )
                                if self.realm_bridge:
                                    unreal.log("✅ AuracronDynamicRealmSubsystem accessed via SubsystemBlueprintLibrary")
                            except Exception:
                                pass

                        # Method 3: Direct class instantiation
                        if not self.realm_bridge:
                            try:
                                self.realm_bridge = unreal.AuracronDynamicRealmSubsystem()
                                if self.realm_bridge:
                                    unreal.log("✅ AuracronDynamicRealmSubsystem created via direct instantiation")
                            except Exception:
                                pass

                        if not self.realm_bridge:
                            unreal.log_warning("⚠️ AuracronDynamicRealmSubsystem not accessible via any method")

                    except Exception as subsys_error:
                        unreal.log_warning(f"⚠️ AuracronDynamicRealmSubsystem not available: {str(subsys_error)}")
                        self.realm_bridge = None
                else:
                    unreal.log_warning("⚠️ No editor world available")
                    self.realm_bridge = None
            except Exception as e:
                self.realm_bridge = None
                unreal.log_warning(f"⚠️ AuracronDynamicRealmSubsystem not available: {str(e)}")
            
            # Terrain configuration from requirements
            self.realm_config = {
                'name': 'PlanicieRadiante',
                'type': 'TERRESTRIAL',
                'elevation': 0.0,
                'size': {'x': 12000, 'y': 12000, 'z': 2000},
                'color_palette': {
                    'primary': [(0.4, 0.8, 0.2, 1.0), (0.6, 0.4, 0.2, 1.0)],  # Verde esmeralda, Marrom terra
                    'secondary': [(0.8, 0.6, 0.2, 1.0), (0.2, 0.4, 0.8, 1.0)],  # Dourado cristal, Azul água
                    'accent': [(1.0, 0.8, 0.6, 1.0)]  # Branco luz solar
                }
            }
            
            unreal.log("✅ PlanicieRadianteCreator initialized successfully")
            
        except Exception as e:
            unreal.log_error(f"❌ Failed to initialize PlanicieRadianteCreator: {str(e)}")
            raise
    
    def verify_ue56_compatibility(self):
        """Verify UE5.6 compatibility and required APIs"""
        try:
            # Check engine version
            engine_version = unreal.SystemLibrary.get_engine_version()
            unreal.log(f"🔍 Engine Version: {engine_version}")
            
            # Verify required APIs are available
            required_apis = [
                'LandscapeProxy',
                'StaticMeshActor', 
                'WorldPartitionSubsystem',
                'EditorLevelLibrary',
                'EditorAssetLibrary'
            ]
            
            for api in required_apis:
                if hasattr(unreal, api):
                    unreal.log(f"✅ {api} API available")
                else:
                    unreal.log_error(f"❌ {api} API not available")
                    return False
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ UE5.6 compatibility check failed: {str(e)}")
            return False
    
    def create_level_structure(self):
        """Create the level structure for Planície Radiante"""
        try:
            # Create level path
            level_path = "/Game/Levels/Realms/PlanicieRadiante"
            
            # Check if level already exists
            if self.editor_asset_lib.does_asset_exist(level_path):
                unreal.log(f"⚠️ Level {level_path} already exists, loading...")
                # Use modern EditorLoadingAndSavingUtils instead of deprecated EditorLevelLibrary
                world = self.editor_loading_saving.load_map(level_path + ".umap")
                if not world:
                    unreal.log_error(f"❌ Failed to load existing level: {level_path}")
                    return False
            else:
                # Create new level using modern API
                unreal.log(f"🏗️ Creating new level: {level_path}")
                world = self.editor_loading_saving.new_blank_map(save_existing_map=True)
                if not world:
                    unreal.log_error(f"❌ Failed to create new level: {level_path}")
                    return False
                # Save the new map to the specified path
                if not self.editor_loading_saving.save_map(world, level_path):
                    unreal.log_error(f"❌ Failed to save new level: {level_path}")
                    return False
            
            # Get current world
            editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
            world = editor_subsystem.get_editor_world()
            if not world:
                unreal.log_error("❌ Failed to get editor world")
                return False
            
            # WorldPartitionSubsystem is not accessible via Python in UE 5.6
            # We'll use the AuracronWorldPartitionBridge instead if available
            self.world_partition_subsystem = None
            unreal.log("ℹ️ WorldPartitionSubsystem not accessible via Python in UE 5.6")

            # Try to use AuracronWorldPartitionBridge for world partition functionality
            try:
                # Check if AuracronWorldPartitionBridge is available
                if hasattr(unreal, 'AuracronWorldPartitionBridge'):
                    self.world_partition_bridge = unreal.AuracronWorldPartitionBridge()
                    if self.world_partition_bridge:
                        unreal.log("✅ AuracronWorldPartitionBridge available")
                    else:
                        self.world_partition_bridge = None
                        unreal.log_warning("⚠️ AuracronWorldPartitionBridge not available")
                else:
                    self.world_partition_bridge = None
                    unreal.log("ℹ️ AuracronWorldPartitionBridge not found, continuing without world partition features")
            except Exception as e:
                self.world_partition_bridge = None
                unreal.log_warning(f"⚠️ World Partition bridge initialization failed: {str(e)}")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Level structure creation failed: {str(e)}")
            return False
    
    def create_base_landscape(self):
        """Create the base landscape terrain using ULandscapeProxy"""
        try:
            unreal.log("🌱 Creating base landscape terrain...")
            
            # Landscape configuration
            landscape_config = {
                'location': unreal.Vector(0, 0, 0),
                'scale': unreal.Vector(
                    self.realm_config['size']['x'] / 100,  # Convert to UE units
                    self.realm_config['size']['y'] / 100,
                    self.realm_config['size']['z'] / 100
                ),
                'component_count_x': 8,
                'component_count_y': 8,
                'section_size': 63,  # Standard UE landscape section size
                'sections_per_component': 1
            }
            
            # Create landscape using UE5.6 API
            # Create LandscapeLayerInfoObject using C++ bridge
            unreal.log("🌱 Creating landscape layer info objects...")
            
            # Get the landscape manager from the bridge
            try:
                # Try multiple methods to get the landscape manager
                landscape_manager = None

                # Method 1: Static GetInstance method (C++ style)
                try:
                    landscape_manager = unreal.AuracronWorldPartitionLandscapeManager.get_instance()
                    if landscape_manager:
                        unreal.log("✅ AuracronWorldPartitionLandscapeManager accessed via get_instance()")
                except Exception:
                    pass

                # Method 2: Try GetInstance with capital G
                if not landscape_manager:
                    try:
                        landscape_manager = unreal.AuracronWorldPartitionLandscapeManager.GetInstance()
                        if landscape_manager:
                            unreal.log("✅ AuracronWorldPartitionLandscapeManager accessed via GetInstance()")
                    except Exception:
                        pass

                # Method 3: Direct instantiation
                if not landscape_manager:
                    try:
                        landscape_manager = unreal.AuracronWorldPartitionLandscapeManager()
                        if landscape_manager:
                            unreal.log("✅ AuracronWorldPartitionLandscapeManager created via direct instantiation")
                    except Exception:
                        pass

                if landscape_manager:
                    # Initialize the landscape manager with proper configuration using CORRECT properties
                    landscape_config_struct = unreal.AuracronLandscapeConfiguration()

                    # Set properties using exact C++ names
                    try:
                        landscape_config_struct.landscape_streaming_distance = 5000.0
                        unreal.log("✅ Set landscape_streaming_distance")
                    except AttributeError:
                        try:
                            landscape_config_struct.LandscapeStreamingDistance = 5000.0
                            unreal.log("✅ Set LandscapeStreamingDistance")
                        except AttributeError:
                            unreal.log_warning("⚠️ Could not set landscape streaming distance")

                    # Try different property name variations for landscape streaming
                    try:
                        landscape_config_struct.b_enable_landscape_streaming = True
                        unreal.log("✅ Set b_enable_landscape_streaming")
                    except AttributeError:
                        try:
                            landscape_config_struct.bEnableLandscapeStreaming = True
                            unreal.log("✅ Set bEnableLandscapeStreaming")
                        except AttributeError:
                            try:
                                landscape_config_struct.enable_landscape_streaming = True
                                unreal.log("✅ Set enable_landscape_streaming")
                            except AttributeError:
                                unreal.log_warning("⚠️ Could not set landscape streaming property")

                    # Set heightmap resolution
                    try:
                        landscape_config_struct.heightmap_resolution = 512
                        unreal.log("✅ Set heightmap_resolution")
                    except AttributeError:
                        try:
                            landscape_config_struct.HeightmapResolution = 512
                            unreal.log("✅ Set HeightmapResolution")
                        except AttributeError:
                            unreal.log_warning("⚠️ Could not set heightmap resolution")

                    # Set component size
                    try:
                        landscape_config_struct.component_size = 127
                        unreal.log("✅ Set component_size")
                    except AttributeError:
                        try:
                            landscape_config_struct.ComponentSize = 127
                            unreal.log("✅ Set ComponentSize")
                        except AttributeError:
                            unreal.log_warning("⚠️ Could not set component size")

                    # Initialize the manager
                    landscape_manager.initialize(landscape_config_struct)

                    if landscape_manager.is_initialized():
                        unreal.log("✅ AuracronWorldPartitionLandscapeManager connected and initialized successfully")
                    else:
                        unreal.log_warning("⚠️ AuracronWorldPartitionLandscapeManager failed to initialize")
                        landscape_manager = None
                else:
                    unreal.log_warning("⚠️ AuracronWorldPartitionLandscapeManager not available, using standard UE landscape creation")
                    landscape_manager = None
            except Exception as e:
                unreal.log_warning(f"⚠️ AuracronWorldPartitionLandscapeManager not available: {str(e)}")
                landscape_manager = None
            
            # Create layer info objects for different terrain types
            layer_info_objects = {}
            layer_types = ['Grass', 'Rock', 'Dirt', 'Sand']
            
            for layer_type in layer_types:
                asset_name = f"LayerInfo_{layer_type}"
                package_path = "/Game/Landscapes/LayerInfos"
                
                try:
                    # Use bridge method to create LandscapeLayerInfoObject - THIS IS THE CORRECT WAY!
                    if landscape_manager and landscape_manager.is_initialized():
                        unreal.log(f"🔧 Using AuracronWorldPartitionLandscapeManager to create {asset_name}")
                        layer_info_path = landscape_manager.create_landscape_layer_info_object(asset_name, package_path)
                        if layer_info_path:
                            layer_info_asset = unreal.EditorAssetLibrary.load_asset(layer_info_path)
                            unreal.log(f"✅ Bridge created layer info object: {layer_info_path}")
                        else:
                            unreal.log_error(f"❌ Bridge failed to create layer info object: {asset_name}")
                            layer_info_asset = None
                    else:
                        # Fallback: Create layer info object using standard UE5.6 API
                        unreal.log(f"🔄 Using standard UE5.6 API to create {asset_name}")
                        layer_info_asset = self.create_layer_info_object_fallback(asset_name, package_path)
                    
                    if layer_info_asset:
                        # Configure the layer info object
                        # LayerName is read-only in UE 5.6, it's set automatically from the asset name
                        # Just save the asset using EditorAssetLibrary (UE 5.6 compatible)
                        asset_path = f"{package_path}/{asset_name}"
                        if unreal.EditorAssetLibrary.save_asset(asset_path):
                            layer_info_objects[layer_type] = layer_info_asset
                            unreal.log(f"✅ Created layer info object: {layer_type}")
                        else:
                            unreal.log_error(f"❌ Failed to save layer info object: {layer_type}")
                    else:
                        unreal.log_error(f"❌ Failed to create layer info object: {layer_type}")
                        return None
                except Exception as e:
                    unreal.log_error(f"❌ Error creating layer info object {layer_type}: {e}")
                    return None
            
            # Use the first layer info object for the base landscape
            landscape_info = layer_info_objects.get('Grass')
            
            # Create landscape using bridge or fallback to standard creation
            landscape_actor = None
            
            if landscape_manager and landscape_manager.is_initialized():
                # Use Auracron bridge for landscape creation - THIS IS THE CORRECT WAY!
                try:
                    unreal.log("🔧 Using AuracronWorldPartitionLandscapeManager to create landscape")
                    landscape_id = landscape_manager.create_landscape(
                        location=unreal.Vector(x=0, y=0, z=0),
                        component_count_x=landscape_config['component_count_x'],
                        component_count_y=landscape_config['component_count_y'],
                        heightmap_resolution=512
                    )

                    if landscape_id:
                        unreal.log(f"✅ Landscape created with Auracron bridge, ID: {landscape_id}")

                        # Try to load the landscape to make it active
                        try:
                            if landscape_manager.load_landscape(landscape_id):
                                unreal.log("✅ Bridge landscape loaded successfully")
                                # Create a placeholder actor for the script to continue
                                landscape_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                                    unreal.Actor,  # Placeholder - the real landscape is managed by the bridge
                                    landscape_config['location'],
                                    unreal.Rotator(0, 0, 0)
                                )
                            else:
                                unreal.log_warning("⚠️ Bridge created landscape but failed to load, using fallback")
                                landscape_actor = None  # Will trigger fallback creation
                        except Exception as load_error:
                            unreal.log_warning(f"⚠️ Bridge landscape loading error: {str(load_error)}, using fallback")
                            landscape_actor = None  # Will trigger fallback creation
                    else:
                        unreal.log_warning("⚠️ Failed to create landscape through Auracron bridge, falling back to standard creation")
                        landscape_actor = None  # Will trigger fallback creation
                except Exception as e:
                    unreal.log_warning(f"⚠️ Auracron bridge error: {str(e)}, falling back to standard creation")
            
            # Fallback to standard UE5 landscape creation if bridge failed or unavailable
            if not landscape_actor:
                unreal.log("🔄 Using standard UE5 landscape creation")
                landscape_actor = self.create_landscape_fallback(landscape_config, layer_info_objects)

            if landscape_actor:
                # Configure landscape properties
                landscape_actor.set_actor_scale3d(landscape_config['scale'])

                # Configure landscape material parameters if the method exists
                try:
                    if hasattr(landscape_actor, 'set_landscape_material_scalar_parameter_value'):
                        landscape_actor.set_landscape_material_scalar_parameter_value(
                            "TerrainComplexity", 1.0  # High complexity as per requirements
                        )
                        unreal.log("✅ Landscape material parameters configured")
                    elif hasattr(landscape_actor, 'landscape_material'):
                        # Try to load a default landscape material
                        default_material_path = "/Engine/EngineMaterials/DefaultLandscapeMaterial"
                        if self.editor_asset_lib.does_asset_exist(default_material_path):
                            default_material = self.editor_asset_lib.load_asset(default_material_path)
                            if default_material:
                                landscape_actor.landscape_material = default_material
                                unreal.log("✅ Default landscape material set")
                    else:
                        unreal.log("ℹ️ Landscape material configuration not available on this actor type")
                except Exception as material_error:
                    unreal.log_warning(f"⚠️ Landscape material configuration failed: {str(material_error)}")

                unreal.log("✅ Base landscape created successfully")
                return landscape_actor
            else:
                unreal.log_error("❌ Failed to create landscape actor")
                return None
                
        except Exception as e:
            unreal.log_error(f"❌ Base landscape creation failed: {str(e)}")
            return None

    def create_landscape_fallback(self, landscape_config, layer_info_objects):
        """Create landscape using standard UE5.6 APIs as fallback"""
        try:
            unreal.log("🔄 Creating landscape using standard UE5.6 APIs")

            # Skip LandscapeEditorUtils as it's not available in UE 5.6 Python API
            unreal.log("ℹ️ Skipping LandscapeEditorUtils (not available in UE 5.6 Python API)")

            # Fallback: Try creating a basic Landscape actor
            try:
                landscape_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                    unreal.Landscape,
                    landscape_config['location'],
                    unreal.Rotator(0, 0, 0)
                )

                if landscape_actor:
                    # Configure basic landscape properties
                    landscape_actor.set_actor_scale3d(landscape_config['scale'])

                    # Try to set basic landscape properties if available
                    try:
                        if hasattr(landscape_actor, 'set_landscape_guid'):
                            import uuid
                            landscape_actor.set_landscape_guid(str(uuid.uuid4()))
                    except Exception as guid_error:
                        unreal.log_warning(f"⚠️ Could not set landscape GUID: {str(guid_error)}")

                    unreal.log("✅ Basic Landscape actor created")
                    return landscape_actor

            except Exception as landscape_error:
                unreal.log_warning(f"⚠️ Failed to create Landscape actor: {str(landscape_error)}")

            # Final fallback: Create LandscapeProxy
            try:
                landscape_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                    unreal.LandscapeProxy,
                    landscape_config['location'],
                    unreal.Rotator(0, 0, 0)
                )

                if landscape_actor:
                    landscape_actor.set_actor_scale3d(landscape_config['scale'])
                    unreal.log("✅ LandscapeProxy actor created as final fallback")
                    return landscape_actor

            except Exception as proxy_error:
                unreal.log_error(f"❌ Failed to create LandscapeProxy: {str(proxy_error)}")

            # Ultimate fallback: Create a simple StaticMeshActor as terrain placeholder
            try:
                unreal.log("🔄 Creating terrain placeholder using StaticMeshActor")

                # Try to load a basic cube mesh
                cube_mesh_path = "/Engine/BasicShapes/Cube"
                if self.editor_asset_lib.does_asset_exist(cube_mesh_path):
                    cube_mesh = self.editor_asset_lib.load_asset(cube_mesh_path)

                    terrain_placeholder = self.editor_actor_subsystem.spawn_actor_from_class(
                        unreal.StaticMeshActor,
                        landscape_config['location'],
                        unreal.Rotator(0, 0, 0)
                    )

                    if terrain_placeholder and cube_mesh:
                        # Set the mesh and scale it to represent terrain
                        mesh_component = terrain_placeholder.get_static_mesh_component()
                        if mesh_component:
                            mesh_component.set_static_mesh(cube_mesh)
                            # Scale to represent terrain area
                            terrain_scale = unreal.Vector(
                                landscape_config['scale'].x * 10,  # Make it larger
                                landscape_config['scale'].y * 10,
                                landscape_config['scale'].z * 0.1   # Make it flat like terrain
                            )
                            terrain_placeholder.set_actor_scale3d(terrain_scale)
                            unreal.log("✅ Terrain placeholder created using StaticMeshActor")
                            return terrain_placeholder

            except Exception as placeholder_error:
                unreal.log_error(f"❌ Failed to create terrain placeholder: {str(placeholder_error)}")

            return None

        except Exception as e:
            unreal.log_error(f"❌ Landscape fallback creation failed: {str(e)}")
            return None
    
    def configure_lighting_system(self):
        """Configure natural lighting system for terrestrial realm"""
        try:
            unreal.log("☀️ Configuring natural lighting system...")
            
            # Create directional light for sun
            sun_light = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.DirectionalLight,
                unreal.Vector(0, 0, 1000),
                unreal.Rotator(-45, 45, 0)  # 45-degree angle for natural lighting
            )
            
            if sun_light:
                # Configure sun light properties using UE 5.6 correct API
                try:
                    # Try different methods to get light component
                    light_component = None
                    if hasattr(sun_light, 'light_component'):
                        light_component = sun_light.light_component
                    elif hasattr(sun_light, 'get_component_by_class'):
                        light_component = sun_light.get_component_by_class(unreal.DirectionalLightComponent)
                    elif hasattr(sun_light, 'directional_light_component'):
                        light_component = sun_light.directional_light_component

                    if light_component:
                        light_component.set_intensity(3.0)  # Natural sunlight intensity
                        light_component.set_light_color(unreal.LinearColor(1.0, 0.95, 0.8, 1.0))  # Warm sunlight
                        light_component.set_cast_shadows(True)
                        if hasattr(light_component, 'set_cast_volumetric_shadows'):
                            light_component.set_cast_volumetric_shadows(True)
                        unreal.log("✅ Sun light configured")
                    else:
                        unreal.log_warning("⚠️ Could not access directional light component")
                except Exception as light_error:
                    unreal.log_warning(f"⚠️ Sun light configuration failed: {str(light_error)}")
            
            # Create sky light for ambient lighting
            sky_light = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.SkyLight,
                unreal.Vector(0, 0, 2000),
                unreal.Rotator(0, 0, 0)
            )
            
            if sky_light:
                # Configure sky light properties using UE 5.6 correct API
                try:
                    # Try different methods to get sky light component
                    sky_component = None
                    if hasattr(sky_light, 'light_component'):
                        sky_component = sky_light.light_component
                    elif hasattr(sky_light, 'get_component_by_class'):
                        sky_component = sky_light.get_component_by_class(unreal.SkyLightComponent)
                    elif hasattr(sky_light, 'sky_light_component'):
                        sky_component = sky_light.sky_light_component

                    if sky_component:
                        sky_component.set_intensity(1.0)
                        sky_component.set_light_color(unreal.LinearColor(0.4, 0.8, 1.0, 1.0))  # Sky blue
                        if hasattr(sky_component, 'set_source_type'):
                            sky_component.set_source_type(unreal.SkyLightSourceType.SLS_CAPTURED_SCENE)
                        unreal.log("✅ Sky light configured")
                    else:
                        unreal.log_warning("⚠️ Could not access sky light component")
                except Exception as sky_error:
                    unreal.log_warning(f"⚠️ Sky light configuration failed: {str(sky_error)}")
            
            # Create atmospheric fog
            atmospheric_fog = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.AtmosphericFog,
                unreal.Vector(0, 0, 0),
                unreal.Rotator(0, 0, 0)
            )
            
            if atmospheric_fog:
                # Configure atmospheric properties for terrestrial realm using UE 5.6 correct API
                try:
                    # Try different methods to get atmospheric fog component
                    fog_component = None
                    if hasattr(atmospheric_fog, 'atmospheric_fog_component'):
                        fog_component = atmospheric_fog.atmospheric_fog_component
                    elif hasattr(atmospheric_fog, 'get_component_by_class'):
                        fog_component = atmospheric_fog.get_component_by_class(unreal.AtmosphericFogComponent)

                    if fog_component:
                        if hasattr(fog_component, 'set_fog_density'):
                            fog_component.set_fog_density(0.02)  # Light fog for natural atmosphere
                        if hasattr(fog_component, 'set_fog_height_falloff'):
                            fog_component.set_fog_height_falloff(0.2)
                        unreal.log("✅ Atmospheric fog configured")
                    else:
                        unreal.log_warning("⚠️ Could not access atmospheric fog component")
                except Exception as fog_error:
                    unreal.log_warning(f"⚠️ Atmospheric fog configuration failed: {str(fog_error)}")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Lighting system configuration failed: {str(e)}")
            return False
    
    def apply_color_palette(self, landscape_actor):
        """Apply the terrestrial color palette to the landscape"""
        try:
            unreal.log("🎨 Applying terrestrial color palette...")
            
            if not landscape_actor:
                unreal.log_error("❌ No landscape actor provided")
                return False
            
            # Create material instance for terrestrial realm
            material_path = "/Game/Materials/Realms/M_PlanicieRadiante"
            
            # Check if material exists, create if not
            if not self.editor_asset_lib.does_asset_exist(material_path):
                unreal.log("🎨 Creating terrestrial material...")
                
                # Create material instance dynamic using correct UE 5.6 API
                try:
                    # Try to load a base material first
                    base_material_path = "/Engine/EngineMaterials/DefaultMaterial"
                    base_material = None
                    if self.editor_asset_lib.does_asset_exist(base_material_path):
                        base_material = self.editor_asset_lib.load_asset(base_material_path)

                    # Create material instance dynamic using correct UE 5.6 method
                    if base_material:
                        material_instance = unreal.KismetMaterialLibrary.create_dynamic_material_instance(
                            landscape_actor,  # World context object
                            base_material     # Parent material
                        )
                    else:
                        # Try without base material
                        material_instance = unreal.KismetMaterialLibrary.create_dynamic_material_instance(
                            landscape_actor,  # World context object
                            None              # No parent material
                        )
                except Exception as material_error:
                    unreal.log_warning(f"⚠️ Failed to create MaterialInstanceDynamic: {str(material_error)}")
                    material_instance = None
                
                if material_instance:
                    # Apply color palette from requirements
                    primary_colors = self.realm_config['color_palette']['primary']
                    secondary_colors = self.realm_config['color_palette']['secondary']
                    accent_colors = self.realm_config['color_palette']['accent']
                    
                    # Set material parameters
                    material_instance.set_vector_parameter_value(
                        "PrimaryColor1", 
                        unreal.LinearColor(primary_colors[0][0], primary_colors[0][1], primary_colors[0][2], primary_colors[0][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "PrimaryColor2",
                        unreal.LinearColor(primary_colors[1][0], primary_colors[1][1], primary_colors[1][2], primary_colors[1][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "SecondaryColor1",
                        unreal.LinearColor(secondary_colors[0][0], secondary_colors[0][1], secondary_colors[0][2], secondary_colors[0][3])
                    )
                    material_instance.set_vector_parameter_value(
                        "AccentColor",
                        unreal.LinearColor(accent_colors[0][0], accent_colors[0][1], accent_colors[0][2], accent_colors[0][3])
                    )
                    
                    # Apply material to landscape
                    landscape_actor.set_landscape_material(material_instance)
                    
                    unreal.log("✅ Color palette applied successfully")
                    return True
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Color palette application failed: {str(e)}")
            return False
    
    def configure_physics_properties(self):
        """Configure physics properties for terrestrial realm"""
        try:
            unreal.log("⚖️ Configuring terrestrial physics properties...")
            
            # Get current world
            editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
            world = editor_subsystem.get_editor_world()
            if not world:
                unreal.log_error("❌ Failed to get world for physics configuration")
                return False
            
            # Configure world physics settings using UE 5.6 correct API
            world_settings = world.get_world_settings()
            if world_settings:
                try:
                    # Set standard gravity for terrestrial realm using correct property names
                    if hasattr(world_settings, 'set_gravity_z'):
                        world_settings.set_gravity_z(-980.0)
                        unreal.log("✅ Gravity set via set_gravity_z")
                    elif hasattr(world_settings, 'gravity_z'):
                        world_settings.gravity_z = -980.0
                        unreal.log("✅ Gravity set via gravity_z property")
                    elif hasattr(world_settings, 'default_gravity_z'):
                        world_settings.default_gravity_z = -980.0
                        unreal.log("✅ Gravity set via default_gravity_z property")
                    else:
                        unreal.log_warning("⚠️ Could not set gravity - property not found")

                    # Configure physics simulation settings
                    try:
                        if hasattr(world_settings, 'set_enable_world_composition'):
                            world_settings.set_enable_world_composition(True)
                        elif hasattr(world_settings, 'enable_world_composition'):
                            world_settings.enable_world_composition = True
                        elif hasattr(world_settings, 'b_enable_world_composition'):
                            world_settings.b_enable_world_composition = True

                        if hasattr(world_settings, 'set_enable_world_bounds_checks'):
                            world_settings.set_enable_world_bounds_checks(True)
                        elif hasattr(world_settings, 'enable_world_bounds_checks'):
                            world_settings.enable_world_bounds_checks = True
                        elif hasattr(world_settings, 'b_enable_world_bounds_checks'):
                            world_settings.b_enable_world_bounds_checks = True

                    except Exception as physics_error:
                        unreal.log_warning(f"⚠️ Physics simulation settings error: {str(physics_error)}")

                    unreal.log("✅ Physics properties configured")
                    return True

                except Exception as settings_error:
                    unreal.log_warning(f"⚠️ World settings configuration error: {str(settings_error)}")
                    return True  # Continue even if physics settings fail
            else:
                unreal.log_error("❌ Failed to get world settings")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Physics configuration failed: {str(e)}")
            return False
    
    def save_level(self):
        """Save the created level"""
        try:
            unreal.log("💾 Saving Planície Radiante level...")
            
            # Save current level
            if self.editor_level_lib.save_current_level():
                unreal.log("✅ Level saved successfully")
                return True
            else:
                unreal.log_error("❌ Failed to save level")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Level saving failed: {str(e)}")
            return False
    
    def create_missing_curve_assets(self):
        """Create missing curve assets for Vertical Connectors"""
        try:
            unreal.log("🎯 Checking and creating missing curve assets...")
            
            curves_to_create = [
                {
                    'path': '/Game/Curves/VerticalConnectors/Curve_PortalAnima_Movement',
                    'name': 'Curve_PortalAnima_Movement',
                    'type': 'smooth'
                },
                {
                    'path': '/Game/Curves/VerticalConnectors/Curve_FendaFluxo_Movement',
                    'name': 'Curve_FendaFluxo_Movement', 
                    'type': 'ease_in_out'
                },
                {
                    'path': '/Game/Curves/VerticalConnectors/Curve_CipoAstria_Movement',
                    'name': 'Curve_CipoAstria_Movement',
                    'type': 'smooth'
                },
                {
                    'path': '/Game/Curves/VerticalConnectors/Curve_ElevadorVortice_Movement',
                    'name': 'Curve_ElevadorVortice_Movement',
                    'type': 'linear'
                },
                {
                    'path': '/Game/Curves/VerticalConnectors/Curve_RespiradoroGeotermal_Movement',
                    'name': 'Curve_RespiradoroGeotermal_Movement',
                    'type': 'ease_in_out'
                }
            ]
            
            success_count = 0
            for curve_info in curves_to_create:
                if self.create_movement_curve(
                    curve_info['path'],
                    curve_info['name'],
                    curve_info['type']
                ):
                    success_count += 1
                    
            unreal.log(f"✅ Created/verified {success_count}/{len(curves_to_create)} curve assets")
            return success_count > 0
            
        except Exception as e:
            unreal.log_error(f"❌ Error creating curve assets: {str(e)}")
            return False
            
    def create_movement_curve(self, asset_path, curve_name, curve_type="smooth"):
        """Create a movement curve asset"""
        try:
            # Check if asset already exists
            if self.editor_asset_lib.does_asset_exist(asset_path):
                unreal.log(f"✅ Curve asset {asset_path} already exists")
                return True
                
            # Get AssetTools for creating the asset
            asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
            
            # Create the curve asset
            curve_asset = asset_tools.create_asset(
                asset_name=curve_name,
                package_path=asset_path.rsplit('/', 1)[0],
                asset_class=unreal.CurveFloat,
                factory=self.curve_factory
            )
            
            if not curve_asset:
                unreal.log_error(f"❌ Failed to create curve asset: {asset_path}")
                return False
                
            # Configure curve points based on type
            curve_data = self._get_curve_data(curve_type)
            
            # Configure curve points using UE5.6 API
            # CurveFloat in UE5.6 uses different API structure
            try:
                # Access the curve data directly through the curve asset
                # In UE 5.6, CurveFloat has a direct curve property
                curve_keys = []
                for time, value, tangent_in, tangent_out in curve_data:
                    key = unreal.RichCurveKey()
                    key.time = time
                    key.value = value

                    # Use correct property names for UE 5.6
                    try:
                        key.arrive_tangent = tangent_in
                        key.leave_tangent = tangent_out
                    except AttributeError:
                        try:
                            key.in_tangent = tangent_in
                            key.out_tangent = tangent_out
                        except AttributeError:
                            # Skip tangent setting if not available
                            pass

                    try:
                        key.interp_mode = unreal.RichCurveInterpMode.RCIM_CUBIC
                    except AttributeError:
                        # Skip interp mode if not available
                        pass

                    curve_keys.append(key)

                # Set the keys directly on the curve asset
                if hasattr(curve_asset, 'curve'):
                    curve_asset.curve.keys = curve_keys
                elif hasattr(curve_asset, 'float_keys'):
                    curve_asset.float_keys = curve_keys
                else:
                    unreal.log_warning(f"⚠️ Could not find curve keys property for {asset_path}")

            except Exception as curve_error:
                unreal.log_warning(f"⚠️ Error configuring curve data: {str(curve_error)}")
                # Fallback: just save the basic curve asset
                pass
                
            # Save the asset
            if self.editor_asset_lib.save_asset(asset_path):
                unreal.log(f"✅ Created curve asset: {asset_path}")
                return True
            else:
                unreal.log_error(f"❌ Failed to save curve asset: {asset_path}")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Error creating curve {asset_path}: {str(e)}")
            return False
            
    def _get_curve_data(self, curve_type):
        """Get curve data based on type"""
        if curve_type == "smooth":
            return [
                (0.0, 0.0, 0.0, 0.5),
                (0.25, 0.3, 0.8, 0.8),
                (0.5, 0.7, 0.5, 0.5),
                (0.75, 0.9, 0.3, 0.3),
                (1.0, 1.0, 0.0, 0.0)
            ]
        elif curve_type == "linear":
            return [
                (0.0, 0.0, 1.0, 1.0),
                (1.0, 1.0, 1.0, 1.0)
            ]
        elif curve_type == "ease_in_out":
            return [
                (0.0, 0.0, 0.0, 2.0),
                (0.5, 0.5, 0.0, 0.0),
                (1.0, 1.0, 2.0, 0.0)
            ]
        else:
            return self._get_curve_data("smooth")
    
    def load_asset_with_fallback(self, asset_path, asset_type="StaticMesh"):
        """Load asset with fallback to default if not found"""
        try:
            # Check if asset exists first
            if not self.editor_asset_lib.does_asset_exist(asset_path):
                unreal.log_warning(f"⚠️ Asset does not exist: {asset_path}")
                # Try to create the asset if it's a curve
                if "Curve_" in asset_path and asset_type == "CurveFloat":
                    curve_name = asset_path.split('/')[-1]
                    if self.create_movement_curve(asset_path, curve_name, "smooth"):
                        unreal.log(f"✅ Created missing curve asset: {asset_path}")
                    else:
                        unreal.log_error(f"❌ Failed to create curve asset: {asset_path}")
                        return self.get_fallback_asset(asset_type)
                else:
                    return self.get_fallback_asset(asset_type)
                    
            asset = self.editor_asset_lib.load_asset(asset_path)
            if asset:
                return asset
            else:
                unreal.log_warning(f"⚠️ Asset failed to load: {asset_path}, using fallback")
                return self.get_fallback_asset(asset_type)
                
        except Exception as e:
            unreal.log_error(f"❌ Error loading asset {asset_path}: {str(e)}")
            return self.get_fallback_asset(asset_type)
    
    def create_layer_info_object_fallback(self, asset_name, package_path):
        """Create LandscapeLayerInfoObject using standard UE5.6 API as fallback"""
        try:
            # Create the full asset path
            full_asset_path = f"{package_path}/{asset_name}"

            # Check if asset already exists
            if self.editor_asset_lib.does_asset_exist(full_asset_path):
                unreal.log(f"✅ Layer info asset already exists: {full_asset_path}")
                return self.editor_asset_lib.load_asset(full_asset_path)

            # Create LandscapeLayerInfoObject using AssetTools
            asset_tools = unreal.AssetToolsHelpers.get_asset_tools()

            # Create LandscapeLayerInfoObject factory
            factory = unreal.LandscapeLayerInfoObjectFactory()

            # Create the asset
            layer_info_asset = asset_tools.create_asset(
                asset_name=asset_name,
                package_path=package_path,
                asset_class=unreal.LandscapeLayerInfoObject,
                factory=factory
            )

            if layer_info_asset:
                # Save the asset
                if self.editor_asset_lib.save_asset(full_asset_path):
                    unreal.log(f"✅ Created layer info object using fallback: {full_asset_path}")
                    return layer_info_asset
                else:
                    unreal.log_error(f"❌ Failed to save layer info object: {full_asset_path}")
                    return None
            else:
                unreal.log_error(f"❌ Failed to create layer info object: {asset_name}")
                return None

        except Exception as e:
            unreal.log_error(f"❌ Error in layer info object fallback creation: {str(e)}")
            return None

    def get_fallback_asset(self, asset_type):
        """Get fallback asset for missing assets"""
        fallbacks = {
            "StaticMesh": "/Engine/BasicShapes/Cube",
            "Material": "/Engine/BasicShapes/BasicShapeMaterial",
            "Texture2D": "/Engine/EngineResources/DefaultTexture",
            "CurveFloat": None  # Will create a simple linear curve
        }

        if asset_type == "CurveFloat":
            # Create a simple fallback curve
            try:
                fallback_curve = unreal.CurveFloat()
                fallback_curve.float_curve.add_key(unreal.RichCurveKey(time=0.0, value=0.0))
                fallback_curve.float_curve.add_key(unreal.RichCurveKey(time=1.0, value=1.0))
                return fallback_curve
            except Exception as e:
                unreal.log_error(f"❌ Failed to create fallback curve: {str(e)}")
                return None

        fallback_path = fallbacks.get(asset_type, "/Engine/BasicShapes/Cube")
        try:
            if self.editor_asset_lib.does_asset_exist(fallback_path):
                return self.editor_asset_lib.load_asset(fallback_path)
            else:
                unreal.log_error(f"❌ Fallback asset does not exist: {fallback_path}")
                return None
        except Exception as e:
            unreal.log_error(f"❌ Failed to load fallback asset {fallback_path}: {str(e)}")
            return None
    
    def create_planicie_radiante_complete(self):
        """Main function to create complete Planície Radiante base terrain"""
        try:
            unreal.log("🌟 Starting Planície Radiante creation...")
            
            # Step 1: Verify UE5.6 compatibility
            if not self.verify_ue56_compatibility():
                unreal.log_error("❌ UE5.6 compatibility check failed")
                return False
                
            # Create missing curve assets
            self.create_missing_curve_assets()
            
            # Step 2: Create level structure
            if not self.create_level_structure():
                unreal.log_error("❌ Level structure creation failed")
                return False
            
            # Step 3: Create base landscape
            landscape_actor = self.create_base_landscape()
            if not landscape_actor:
                unreal.log_error("❌ Base landscape creation failed")
                return False
            
            # Step 4: Configure lighting system
            if not self.configure_lighting_system():
                unreal.log_error("❌ Lighting system configuration failed")
                return False
            
            # Step 5: Apply color palette
            if not self.apply_color_palette(landscape_actor):
                unreal.log_error("❌ Color palette application failed")
                return False
            
            # Step 6: Configure physics properties
            if not self.configure_physics_properties():
                unreal.log_error("❌ Physics configuration failed")
                return False
            
            # Step 7: Save level
            if not self.save_level():
                unreal.log_error("❌ Level saving failed")
                return False
            
            unreal.log("🎉 Planície Radiante base terrain created successfully!")
            unreal.log("📊 Terrain Statistics:")
            unreal.log(f"   - Size: {self.realm_config['size']['x']}x{self.realm_config['size']['y']}x{self.realm_config['size']['z']} units")
            unreal.log(f"   - Elevation: {self.realm_config['elevation']} units")
            unreal.log(f"   - Type: {self.realm_config['type']}")
            unreal.log("   - Features: Natural lighting, atmospheric fog, terrestrial color palette")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Planície Radiante creation failed: {str(e)}")
            return False

def main():
    """Main execution function"""
    try:
        # Create Planície Radiante terrain creator
        creator = PlanicieRadianteCreator()
        
        # Execute complete creation process
        success = creator.create_planicie_radiante_complete()
        
        if success:
            unreal.log("✅ AURACRON Planície Radiante base terrain creation completed successfully!")
            return 0
        else:
            unreal.log_error("❌ AURACRON Planície Radiante creation failed!")
            return 1
            
    except Exception as e:
        unreal.log_error(f"❌ Critical error in main execution: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)