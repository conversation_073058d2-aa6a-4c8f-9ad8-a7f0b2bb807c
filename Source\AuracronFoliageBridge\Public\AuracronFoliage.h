// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Core Infrastructure Header
// Bridge 4.1: Foliage - Core Infrastructure

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"

// Foliage system includes for UE5.6
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"
#include "ProceduralFoliageSpawner.h"
#include "ProceduralFoliageComponent.h"

// Engine includes
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/RandomStream.h"

#include "AuracronFoliage.generated.h"

// Forward declarations
class UAuracronFoliageManager;
class UAuracronFoliageType;

// =============================================================================
// FOLIAGE LOGGING MACROS
// =============================================================================

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronFoliage, Log, All);

#define AURACRON_FOLIAGE_LOG_INFO(Format, ...) \
    UE_LOG(LogAuracronFoliage, Log, TEXT("[AURACRON FOLIAGE] ") Format, ##__VA_ARGS__)

#define AURACRON_FOLIAGE_LOG_WARNING(Format, ...) \
    UE_LOG(LogAuracronFoliage, Warning, TEXT("[AURACRON FOLIAGE] ") Format, ##__VA_ARGS__)

#define AURACRON_FOLIAGE_LOG_ERROR(Format, ...) \
    UE_LOG(LogAuracronFoliage, Error, TEXT("[AURACRON FOLIAGE] ") Format, ##__VA_ARGS__)

#define AURACRON_FOLIAGE_LOG_VERBOSE(Format, ...) \
    UE_LOG(LogAuracronFoliage, Verbose, TEXT("[AURACRON FOLIAGE] ") Format, ##__VA_ARGS__)

// =============================================================================
// FOLIAGE TYPES AND ENUMS
// =============================================================================

// Foliage placement modes
UENUM(BlueprintType)
enum class EAuracronFoliagePlacementMode : uint8
{
    Manual                  UMETA(DisplayName = "Manual"),
    Procedural              UMETA(DisplayName = "Procedural"),
    Hybrid                  UMETA(DisplayName = "Hybrid"),
    PCGDriven               UMETA(DisplayName = "PCG Driven")
};

// Foliage density modes
UENUM(BlueprintType)
enum class EAuracronFoliageDensityMode : uint8
{
    Uniform                 UMETA(DisplayName = "Uniform"),
    Noise                   UMETA(DisplayName = "Noise Based"),
    Texture                 UMETA(DisplayName = "Texture Based"),
    Biome                   UMETA(DisplayName = "Biome Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Foliage scaling modes
UENUM(BlueprintType)
enum class EAuracronFoliageScalingMode : uint8
{
    Uniform                 UMETA(DisplayName = "Uniform"),
    Free                    UMETA(DisplayName = "Free"),
    LockXY                  UMETA(DisplayName = "Lock XY"),
    LockXZ                  UMETA(DisplayName = "Lock XZ"),
    LockYZ                  UMETA(DisplayName = "Lock YZ")
};

// Foliage instance states
UENUM(BlueprintType)
enum class EAuracronFoliageInstanceState : uint8
{
    Active                  UMETA(DisplayName = "Active"),
    Hidden                  UMETA(DisplayName = "Hidden"),
    Culled                  UMETA(DisplayName = "Culled"),
    Destroyed               UMETA(DisplayName = "Destroyed"),
    Pending                 UMETA(DisplayName = "Pending")
};

// Foliage LOD modes
UENUM(BlueprintType)
enum class EAuracronFoliageLODMode : uint8
{
    Distance                UMETA(DisplayName = "Distance Based"),
    ScreenSize              UMETA(DisplayName = "Screen Size Based"),
    Performance             UMETA(DisplayName = "Performance Based"),
    Quality                 UMETA(DisplayName = "Quality Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// FOLIAGE CONFIGURATION
// =============================================================================

/**
 * Foliage Configuration
 * Configuration settings for the Auracron Foliage System
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    bool bEnableFoliageSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    bool bEnableProceduralGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    bool bEnableLODSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    bool bEnableCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    bool bEnableBiomeValidation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    EAuracronFoliagePlacementMode DefaultPlacementMode = EAuracronFoliagePlacementMode::Procedural;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    EAuracronFoliageDensityMode DefaultDensityMode = EAuracronFoliageDensityMode::Uniform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    EAuracronFoliageScalingMode DefaultScalingMode = EAuracronFoliageScalingMode::Free;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage")
    EAuracronFoliageLODMode DefaultLODMode = EAuracronFoliageLODMode::Distance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxInstancesPerType = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentSpawns = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CullingDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance0 = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance1 = 3000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance2 = 6000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float DefaultDensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MinSpacing = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MaxSpacing = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FVector MinScale = FVector(0.8f, 0.8f, 0.8f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FVector MaxScale = FVector(1.2f, 1.2f, 1.2f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float MaxRotationAngle = 360.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bAlignToNormal = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float AlignToNormalStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableBatching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableOcclusion = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    int32 InstanceBatchSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowInstanceBounds = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDensityMap = false;

    FAuracronFoliageConfiguration()
    {
        bEnableFoliageSystem = true;
        bEnableProceduralGeneration = true;
        bEnableLODSystem = true;
        bEnableCulling = true;
        DefaultPlacementMode = EAuracronFoliagePlacementMode::Procedural;
        DefaultDensityMode = EAuracronFoliageDensityMode::Uniform;
        DefaultScalingMode = EAuracronFoliageScalingMode::Free;
        DefaultLODMode = EAuracronFoliageLODMode::Distance;
        MaxInstancesPerType = 10000;
        MaxConcurrentSpawns = 100;
        CullingDistance = 10000.0f;
        LODDistance0 = 1000.0f;
        LODDistance1 = 3000.0f;
        LODDistance2 = 6000.0f;
        DefaultDensity = 1.0f;
        MinSpacing = 100.0f;
        MaxSpacing = 500.0f;
        MinScale = FVector(0.8f, 0.8f, 0.8f);
        MaxScale = FVector(1.2f, 1.2f, 1.2f);
        MaxRotationAngle = 360.0f;
        bAlignToNormal = true;
        AlignToNormalStrength = 1.0f;
        bEnableInstancing = true;
        bEnableBatching = true;
        bEnableOcclusion = true;
        InstanceBatchSize = 1000;
        bEnableDebugVisualization = false;
        bShowInstanceBounds = false;
        bShowDensityMap = false;
    }
};

// =============================================================================
// FOLIAGE INSTANCE DATA
// =============================================================================

/**
 * Foliage Instance Data
 * Data structure for individual foliage instances
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageInstanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FString TypeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FTransform Transform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    EAuracronFoliageInstanceState State = EAuracronFoliageInstanceState::Active;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    int32 LODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    float DistanceToViewer = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    bool bIsVisible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    bool bIsCulled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance")
    TMap<FString, FString> CustomData;

    FAuracronFoliageInstanceData()
    {
        State = EAuracronFoliageInstanceState::Active;
        LODLevel = 0;
        DistanceToViewer = 0.0f;
        bIsVisible = true;
        bIsCulled = false;
        CreationTime = FDateTime::Now();
    }
};

// =============================================================================
// FOLIAGE TYPE DATA
// =============================================================================

/**
 * Foliage Type Data
 * Extended data structure for foliage types
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageTypeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FString TypeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FString TypeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    TSoftObjectPtr<UStaticMesh> StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    TSoftObjectPtr<UFoliageType> FoliageType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliagePlacementMode PlacementMode = EAuracronFoliagePlacementMode::Procedural;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliageDensityMode DensityMode = EAuracronFoliageDensityMode::Uniform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float Density = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float TypeMinSpacing = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float TypeMaxSpacing = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FVector TypeMinScale = FVector(0.8f, 0.8f, 0.8f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FVector TypeMaxScale = FVector(1.2f, 1.2f, 1.2f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FVector MinScale = FVector(0.8f, 0.8f, 0.8f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    FVector MaxScale = FVector(1.2f, 1.2f, 1.2f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float MaxSlopeAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float MinHeight = -1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float MaxHeight = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float MinInstanceDistance = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    TArray<FString> CompatibleBiomes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    float RandomYawRange = 180.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    bool bEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    int32 MaxInstances = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Type")
    TArray<FAuracronFoliageInstanceData> Instances;

    FAuracronFoliageTypeData()
    {
        PlacementMode = EAuracronFoliagePlacementMode::Procedural;
        DensityMode = EAuracronFoliageDensityMode::Uniform;
        Density = 1.0f;
        TypeMinSpacing = 100.0f;
        TypeMaxSpacing = 500.0f;
        TypeMinScale = FVector(0.8f, 0.8f, 0.8f);
        TypeMaxScale = FVector(1.2f, 1.2f, 1.2f);
        MinScale = FVector(0.8f, 0.8f, 0.8f);
        MaxScale = FVector(1.2f, 1.2f, 1.2f);
        MaxSlopeAngle = 45.0f;
        MinHeight = -1000.0f;
        MaxHeight = 1000.0f;
        MinInstanceDistance = 50.0f;
        RandomYawRange = 180.0f;
        bEnabled = true;
        MaxInstances = 10000;
    }
};

/**
 * Instance tracking data for component management
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronInstanceTrackingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance Tracking")
    TWeakObjectPtr<UFoliageInstancedStaticMeshComponent> Component;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instance Tracking")
    TArray<FString> InstanceIds;

    FAuracronInstanceTrackingData()
    {
        Component = nullptr;
    }
};

// =============================================================================
// FOLIAGE MANAGER
// =============================================================================

/**
 * Auracron Foliage Manager
 * Central manager for the Auracron Foliage System
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    static UAuracronFoliageManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void Initialize(const FAuracronFoliageConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void Tick(float DeltaTime);

    // Foliage type management
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool RegisterFoliageType(const FAuracronFoliageTypeData& FoliageTypeData);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool UnregisterFoliageType(const FString& TypeId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    FAuracronFoliageTypeData GetFoliageType(const FString& TypeId) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    TArray<FAuracronFoliageTypeData> GetAllFoliageTypes() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool UpdateFoliageType(const FAuracronFoliageTypeData& FoliageTypeData);

    // Instance management
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    FString SpawnFoliageInstance(const FString& TypeId, const FTransform& Transform);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool RemoveFoliageInstance(const FString& InstanceId);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    FAuracronFoliageInstanceData GetFoliageInstance(const FString& InstanceId) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    TArray<FAuracronFoliageInstanceData> GetInstancesOfType(const FString& TypeId) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    TArray<FAuracronFoliageInstanceData> GetInstancesInRadius(const FVector& Center, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool UpdateFoliageInstance(const FAuracronFoliageInstanceData& InstanceData);

    // Procedural generation
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    int32 GenerateFoliageInArea(const FString& TypeId, const FBox& Area, float Density = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    int32 GenerateFoliageOnSurface(const FString& TypeId, UPrimitiveComponent* Surface, float Density = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void ClearFoliageInArea(const FBox& Area);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void ClearAllFoliage();

    // LOD and culling
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void UpdateLOD(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void UpdateCulling(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void SetInstanceLOD(const FString& InstanceId, int32 LODLevel);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void SetInstanceVisibility(const FString& InstanceId, bool bVisible);

    // Performance optimization
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void OptimizeInstances();

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void BatchInstances();

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void RebuildInstancedComponents();

    // Statistics and debugging
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    int32 GetTotalInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    int32 GetInstanceCountForType(const FString& TypeId) const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    int32 GetVisibleInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    float GetMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    TMap<FString, int32> GetInstanceStatistics() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void SetConfiguration(const FAuracronFoliageConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    FAuracronFoliageConfiguration GetConfiguration() const;

    // Debug visualization
    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Foliage Manager")
    void DrawDebugVisualization(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageInstanceSpawned, FString, TypeId, FString, InstanceId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageInstanceAdded, FString, TypeId, FString, InstanceId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageInstanceRemoved, FString, TypeId, FString, InstanceId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageTypeRegistered, FString, TypeId, FAuracronFoliageTypeData, TypeData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFoliageOptimized, int32, OptimizedInstances);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageInstanceSpawned OnFoliageInstanceSpawned;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageInstanceAdded OnFoliageInstanceAdded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageInstanceRemoved OnFoliageInstanceRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageTypeRegistered OnFoliageTypeRegistered;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageOptimized OnFoliageOptimized;

private:
    static UAuracronFoliageManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliageConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    UPROPERTY()
    TWeakObjectPtr<UWorld> TargetWorld;

    // Foliage data
    TMap<FString, FAuracronFoliageTypeData> FoliageTypes;
    TMap<FString, FAuracronFoliageInstanceData> FoliageInstances;
    TMap<FString, TWeakObjectPtr<UFoliageInstancedStaticMeshComponent>> InstancedComponents;

    // Instance tracking for components
    TMap<FString, FAuracronInstanceTrackingData> InstanceTracking;

    // Performance tracking
    int32 TotalInstanceCount = 0;
    int32 VisibleInstanceCount = 0;
    float LastLODUpdateTime = 0.0f;
    float LastCullingUpdateTime = 0.0f;
    float LastOptimizationTime = 0.0f;

    // Random generation
    FRandomStream RandomStream;

    // Thread safety
    mutable FCriticalSection FoliageLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateInstanceId() const;
    bool IsValidPlacement(const FVector& Location, const FString& TypeId) const;
    FTransform GenerateRandomTransform(const FVector& BaseLocation, const FAuracronFoliageTypeData& TypeData) const;
    UFoliageInstancedStaticMeshComponent* GetOrCreateInstancedComponent(const FString& TypeId);
    UFoliageInstancedStaticMeshComponent* CreateRealInstancedComponent(const FString& TypeId);
    void UpdateInstancedComponent(const FString& TypeId);
    void PerformLODUpdate(const FVector& ViewerLocation);
    void PerformCullingUpdate(const FVector& ViewerLocation);
    void CleanupInvalidInstances();
    void LogFoliageStatistics() const;

    // Helper methods
    void RemoveFromInstancedComponent(const FString& TypeId, const FString& InstanceId);
    int32 FindInstanceIndexInComponent(UFoliageInstancedStaticMeshComponent* Component, const FString& InstanceId);
    FString AddFoliageInstance(const FString& TypeId, const FTransform& Transform);
    int32 GenerateRealFoliageOnSurface(const FString& TypeId, UPrimitiveComponent* Surface, float Density);
    void PerformRealInstanceBatching();
    void PerformSpatialClustering(const TArray<FString>& InstanceIds, float ClusterDistance, TArray<TArray<FString>>& OutClusters);
    FString SampleBiomeAtLocation(const FVector& Location) const;
    void UpdateInstanceTracking(const FString& TypeId, int32 InstanceIndex, bool bIsRemoval);
};
