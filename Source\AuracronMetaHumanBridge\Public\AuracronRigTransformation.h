#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Transform.h"
#include "Math/Rotator.h"
#include "AuracronPhysicsBridge/Public/AuracronPhysicsBridge.h"
#include "Engine/SkeletalMesh.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimInstance.h"
#include "ControlRig/Public/ControlRig.h"
#include "ControlRig/Public/ControlRigComponent.h"
#include "ControlRig/Public/Rigs/RigHierarchy.h"
#include "ControlRig/Public/Units/RigUnit.h"

// Forward declarations for UE 5.6 compatibility
class UIKRigDefinition;
class UIKRetargeter;

#include "AuracronRigTransformation.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronRigTransformation, Log, All);

// Enums for rig transformation operations
UENUM(BlueprintType)
enum class EBoneScalingType : uint8
{
    Uniform         UMETA(DisplayName = "Uniform"),
    NonUniform      UMETA(DisplayName = "Non-Uniform"),
    Proportional    UMETA(DisplayName = "Proportional"),
    Hierarchical    UMETA(DisplayName = "Hierarchical")
};

UENUM(BlueprintType)
enum class EAuracronRigConstraintType : uint8
{
    Position        UMETA(DisplayName = "Position"),
    Rotation        UMETA(DisplayName = "Rotation"),
    Scale           UMETA(DisplayName = "Scale"),
    Parent          UMETA(DisplayName = "Parent"),
    LookAt          UMETA(DisplayName = "Look At"),
    TwoBoneIK       UMETA(DisplayName = "Two Bone IK"),
    FABRIK          UMETA(DisplayName = "FABRIK"),
    CCDIK           UMETA(DisplayName = "CCD IK")
};

UENUM(BlueprintType)
enum class EIKSolverType : uint8
{
    TwoBone         UMETA(DisplayName = "Two Bone"),
    FABRIK          UMETA(DisplayName = "FABRIK"),
    CCDIK           UMETA(DisplayName = "CCD IK"),
    FullBody        UMETA(DisplayName = "Full Body")
};

UENUM(BlueprintType)
enum class ERigValidationType : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    Hierarchy       UMETA(DisplayName = "Hierarchy"),
    Constraints     UMETA(DisplayName = "Constraints"),
    IKChains        UMETA(DisplayName = "IK Chains"),
    Comprehensive   UMETA(DisplayName = "Comprehensive")
};

// Structures for rig transformation data
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FBoneScalingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bone Scaling")
    int32 BoneIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bone Scaling")
    FVector ScaleFactor = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bone Scaling")
    EBoneScalingType ScalingType = EBoneScalingType::Uniform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bone Scaling")
    bool bPropagateToChildren = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bone Scaling")
    TArray<int32> ExcludedBones;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAuracronConstraintData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    EAuracronConstraintType ConstraintType = EAuracronConstraintType::Fixed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    int32 SourceBoneIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    int32 TargetBoneIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    FVector PositionOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    FRotator RotationOffset = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Constraint")
    FVector ScaleOffset = FVector::OneVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FIKChainData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    FString ChainName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    TArray<int32> BoneChain;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    EIKSolverType SolverType = EIKSolverType::TwoBone;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    FVector TargetPosition = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    FRotator TargetRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    int32 MaxIterations = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Chain")
    float Precision = 0.001f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FRetargetingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    int32 SourceSkeletonIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    int32 TargetSkeletonIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    TMap<FString, FString> BoneMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    bool bRetargetTranslation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    bool bRetargetRotation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    bool bRetargetScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Retargeting")
    float ScaleFactor = 1.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FRigValidationResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    bool bIsValid = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    TArray<FString> Errors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    TArray<FString> Warnings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    int32 BoneCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    int32 ConstraintCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    int32 IKChainCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    float PerformanceScore = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rig Validation")
    float CompatibilityScore = 100.0f;
};

/**
 * Rig transformation system for MetaHuman Bridge
 * Provides advanced rig manipulation capabilities with UE5.6 Control Rig APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronRigTransformation
{
public:
    FAuracronRigTransformation();
    ~FAuracronRigTransformation();

    // Bone scaling operations
    bool SetBoneScale(int32 BoneIndex, const FVector& ScaleFactor, EBoneScalingType ScalingType, bool bPropagateToChildren);
    FVector GetBoneScale(int32 BoneIndex) const;
    bool ApplyBoneScalingData(const FBoneScalingData& ScalingData);
    bool ApplyMultipleBoneScaling(const TArray<FBoneScalingData>& ScalingDataArray);
    bool ResetBoneScale(int32 BoneIndex, bool bResetChildren);
    bool ScaleBoneHierarchy(int32 RootBoneIndex, const FVector& ScaleFactor, const TArray<int32>& ExcludedBones);

    // Constraint operations
    bool CreateConstraint(const FConstraintData& ConstraintData);
    bool ModifyConstraint(int32 ConstraintIndex, const FConstraintData& NewConstraintData);
    bool RemoveConstraint(int32 ConstraintIndex);
    FAuracronConstraintData GetConstraintData(int32 ConstraintIndex) const;
    bool SetConstraintWeight(int32 ConstraintIndex, float Weight);
    bool ToggleConstraint(int32 ConstraintIndex, bool bIsActive);

    // IK chain operations
    bool CreateIKChain(const FIKChainData& IKChainData);
    bool ModifyIKChain(int32 ChainIndex, const FIKChainData& NewIKChainData);
    bool RemoveIKChain(int32 ChainIndex);
    FIKChainData GetIKChainData(int32 ChainIndex) const;
    bool SetIKTarget(int32 ChainIndex, const FVector& TargetPosition, const FRotator& TargetRotation);
    bool SolveIKChain(int32 ChainIndex);

    // Retargeting operations
    bool SetupRetargeting(const FRetargetingData& RetargetingData);
    bool ApplyRetargeting(int32 RetargetingIndex, const TArray<FTransform>& SourceTransforms, TArray<FTransform>& OutTargetTransforms);
    bool ValidateRetargeting(int32 RetargetingIndex) const;

    // Bone hierarchy operations
    int32 GetBoneParent(int32 BoneIndex) const;
    TArray<int32> GetBoneChildren(int32 BoneIndex) const;
    FString GetBoneName(int32 BoneIndex) const;
    int32 FindBoneByName(const FString& BoneName) const;
    bool IsBoneInChain(int32 BoneIndex, const TArray<int32>& BoneChain) const;
    float CalculateBoneChainLength(const TArray<int32>& BoneChain) const;

    // Validation and analysis
    FRigValidationResult ValidateRigIntegrity(ERigValidationType ValidationType);
    float CalculateRigPerformanceScore() const;
    float CalculateRigCompatibilityScore() const;
    bool DetectCircularDependencies() const;

    // Control Rig integration
    bool CreateControlRig(USkeletalMesh* SkeletalMesh, UControlRig*& OutControlRig);
    bool SetupControlRigHierarchy(UControlRig* ControlRig);
    bool AddControlRigControl(UControlRig* ControlRig, const FString& ControlName, const FTransform& Transform);
    bool SetControlRigControlValue(UControlRig* ControlRig, const FString& ControlName, const FRigControlValue& Value);

    // Thread safety
    mutable FCriticalSection RigTransformationMutex;

private:
    // State tracking
    FThreadSafeBool bRigTransformationCacheValid;
    mutable TMap<int32, FConstraintData> ConstraintCache;
    mutable TMap<int32, FIKChainData> IKChainCache;
    mutable TMap<int32, FRetargetingData> RetargetingCache;
    mutable TMap<int32, FVector> OriginalBoneScales;

    // Skeletal mesh and retargeter storage for UE 5.6
    mutable TArray<TWeakObjectPtr<USkeletalMesh>> SkeletalMeshes;
    mutable TArray<TWeakObjectPtr<UIKRetargeter>> IKRetargeters;
    mutable TWeakObjectPtr<USkeletalMesh> CurrentSkeletalMesh;

    // Internal helper methods
    bool IsValidBoneIndex(int32 BoneIndex) const;
    bool IsValidConstraintIndex(int32 ConstraintIndex) const;
    bool IsValidIKChainIndex(int32 ChainIndex) const;
    bool IsValidBoneScalingData(const FBoneScalingData& ScalingData) const;
    bool IsValidConstraintData(const FConstraintData& ConstraintData) const;
    bool IsValidIKChainData(const FIKChainData& IKChainData) const;
    bool IsValidRetargetingData(const FRetargetingData& RetargetingData) const;

    void InvalidateRigTransformationCache();
    void BuildRigTransformationCache() const;

    FVector CalculateProportionalScale(const FVector& OriginalScale, const FVector& TargetScale, EBoneScalingType ScalingType) const;
    bool ApplyBoneScaleRecursive(int32 BoneIndex, const FVector& ScaleFactor, const TArray<int32>& ExcludedBones) const;
    void BackupOriginalBoneScales() const;

    // Helper functions for UE 5.6 compatibility
    USkeletalMesh* GetCurrentSkeletalMesh() const;
    USkeletalMesh* GetSkeletalMeshByIndex(int32 SkeletonIndex) const;
    UControlRig* GetOrCreateControlRig() const;
    FRigElementKey GetBoneElementKey(int32 BoneIndex) const;
    UIKRigDefinition* CreateIKRigFromSkeleton(USkeleton* Skeleton) const;
    UIKRetargeter* GetRetargeterForIndex(int32 RetargetingIndex) const;
    bool HasCircularDependency(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton, TSet<int32>& VisitedBones, TArray<int32>& BoneStack) const;
    bool ValidateIKChainConnectivity(const FIKChainData& IKChain, const FReferenceSkeleton& RefSkeleton) const;

    // Additional helper functions for UE 5.6
    bool AreBonesConnected(int32 Bone1, int32 Bone2, const FReferenceSkeleton& RefSkeleton) const;
    int32 CalculateMaxBoneHierarchyDepth(const FReferenceSkeleton& RefSkeleton) const;
    int32 CalculateBoneDepth(int32 BoneIndex, const FReferenceSkeleton& RefSkeleton) const;
    bool CheckForRootMotionCompatibility(const FReferenceSkeleton& RefSkeleton) const;
    bool CheckRetargetingCompatibility(const FReferenceSkeleton& RefSkeleton) const;
    bool CheckControlRigCompatibility(const FReferenceSkeleton& RefSkeleton) const;

    // Prevent copying
    FAuracronRigTransformation(const FAuracronRigTransformation&) = delete;
    FAuracronRigTransformation& operator=(const FAuracronRigTransformation&) = delete;
};
