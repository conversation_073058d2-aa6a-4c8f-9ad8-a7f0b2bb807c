#!/usr/bin/env python3
"""
Script de teste para verificar as correções dos erros do create_planicie_radiante_base.py
Este script deve ser executado no console do Unreal Engine 5.6

Para executar no UE5.6:
1. <PERSON><PERSON> o Unreal Engine Editor
2. Abra o Output Log (Window > Developer Tools > Output Log)
3. No console, digite: py "C:/Aura/projeto/Auracron/Content/Python/test_corrections.py"
"""

import unreal

def test_api_availability():
    """Testa se as APIs corrigidas estão disponíveis no UE5.6"""
    unreal.log("🧪 Testando disponibilidade das APIs corrigidas...")
    
    tests_passed = 0
    total_tests = 0
    
    # Teste 1: CurveFloat API
    total_tests += 1
    try:
        curve_factory = unreal.CurveFloatFactory()
        if hasattr(curve_factory, 'create_new_asset'):
            unreal.log("✅ CurveFloatFactory API disponível")
            tests_passed += 1
        else:
            unreal.log_warning("⚠️ CurveFloatFactory.create_new_asset não encontrado")
    except Exception as e:
        unreal.log_error(f"❌ Erro ao testar CurveFloatFactory: {str(e)}")
    
    # Teste 2: EditorAssetLibrary para LandscapeLayerInfoObject
    total_tests += 1
    try:
        editor_asset_lib = unreal.EditorAssetLibrary()
        if hasattr(editor_asset_lib, 'create_asset'):
            unreal.log("✅ EditorAssetLibrary.create_asset disponível")
            tests_passed += 1
        else:
            unreal.log_warning("⚠️ EditorAssetLibrary.create_asset não encontrado")
    except Exception as e:
        unreal.log_error(f"❌ Erro ao testar EditorAssetLibrary: {str(e)}")
    
    # Teste 3: WorldPartitionSubsystem via world.get_subsystem
    total_tests += 1
    try:
        world = unreal.EditorLevelLibrary.get_editor_world()
        if world:
            world_partition_subsystem = world.get_subsystem(unreal.WorldPartitionSubsystem)
            if world_partition_subsystem:
                unreal.log("✅ WorldPartitionSubsystem via world.get_subsystem disponível")
                tests_passed += 1
            else:
                unreal.log_warning("⚠️ WorldPartitionSubsystem não encontrado via world.get_subsystem")
        else:
            unreal.log_error("❌ Não foi possível obter o editor world")
    except Exception as e:
        unreal.log_error(f"❌ Erro ao testar WorldPartitionSubsystem: {str(e)}")
    
    # Teste 4: LandscapeLayerInfoObject
    total_tests += 1
    try:
        if hasattr(unreal, 'LandscapeLayerInfoObject'):
            unreal.log("✅ LandscapeLayerInfoObject classe disponível")
            tests_passed += 1
        else:
            unreal.log_warning("⚠️ LandscapeLayerInfoObject classe não encontrada")
    except Exception as e:
        unreal.log_error(f"❌ Erro ao testar LandscapeLayerInfoObject: {str(e)}")
    
    # Resultado final
    unreal.log("\n" + "="*50)
    unreal.log(f"📊 RESULTADO DOS TESTES: {tests_passed}/{total_tests} passaram")
    
    if tests_passed == total_tests:
        unreal.log("🎯 SUCESSO! Todas as APIs estão funcionando corretamente")
        unreal.log("✅ O script create_planicie_radiante_base.py deve funcionar agora")
        return True
    else:
        unreal.log("⚠️ ATENÇÃO! Alguns testes falharam")
        unreal.log("🔧 Verifique se o Unreal Engine 5.6 está configurado corretamente")
        return False

def test_curve_creation():
    """Testa a criação de uma curva usando a API corrigida"""
    unreal.log("\n🧪 Testando criação de curva com API corrigida...")
    
    try:
        # Criar factory
        curve_factory = unreal.CurveFloatFactory()
        asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        
        # Tentar criar um asset de teste
        test_curve_path = "/Game/Test/TestCurve"
        
        # Verificar se já existe e deletar
        if unreal.EditorAssetLibrary.does_asset_exist(test_curve_path):
            unreal.EditorAssetLibrary.delete_asset(test_curve_path)
        
        # Criar novo asset
        curve_asset = asset_tools.create_asset(
            asset_name="TestCurve",
            package_path="/Game/Test",
            asset_class=unreal.CurveFloat,
            factory=curve_factory
        )
        
        if curve_asset:
            unreal.log("✅ Curva de teste criada com sucesso")
            
            # Testar métodos da curva
            if hasattr(curve_asset, 'reset_curve'):
                unreal.log("✅ Método reset_curve disponível")
            elif hasattr(curve_asset, 'add_key'):
                unreal.log("✅ Método add_key disponível")
            else:
                unreal.log_warning("⚠️ Métodos de curva podem precisar de ajuste")
            
            # Limpar asset de teste
            unreal.EditorAssetLibrary.delete_asset(test_curve_path)
            unreal.log("🧹 Asset de teste removido")
            
            return True
        else:
            unreal.log_error("❌ Falha ao criar curva de teste")
            return False
            
    except Exception as e:
        unreal.log_error(f"❌ Erro no teste de criação de curva: {str(e)}")
        return False

def main():
    """Função principal do teste"""
    unreal.log("🚀 Iniciando testes das correções do create_planicie_radiante_base.py")
    unreal.log("="*70)
    
    # Verificar se estamos no contexto correto
    try:
        world = unreal.EditorLevelLibrary.get_editor_world()
        if not world:
            unreal.log_error("❌ Este script deve ser executado no Unreal Engine Editor")
            return False
        
        unreal.log("✅ Contexto do Unreal Engine detectado")
        
        # Executar testes
        api_test = test_api_availability()
        curve_test = test_curve_creation()
        
        # Resultado final
        unreal.log("\n" + "="*70)
        unreal.log("📊 RESULTADO FINAL DOS TESTES")
        unreal.log("="*70)
        
        if api_test and curve_test:
            unreal.log("🎯 SUCESSO TOTAL!")
            unreal.log("✅ Todas as correções estão funcionando")
            unreal.log("🚀 O script create_planicie_radiante_base.py está pronto para execução")
            return True
        else:
            unreal.log("⚠️ ALGUNS TESTES FALHARAM")
            unreal.log("🔧 Revise as correções antes de executar o script principal")
            return False
            
    except Exception as e:
        unreal.log_error(f"❌ ERRO CRÍTICO: {str(e)}")
        return False

# Executar automaticamente
if __name__ == "__main__":
    main()

# Também disponibilizar para execução direta
def run_test():
    """Função de conveniência para execução direta"""
    return main()