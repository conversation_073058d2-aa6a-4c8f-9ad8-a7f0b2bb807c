// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Advanced Combat System Example for UE 5.6
// This file demonstrates production-ready implementation using UE 5.6 APIs

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "AuracronCombatBridge.h"
#include "Engine/Engine.h"
#include "Components/AudioComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "NiagaraComponent.h"
#include "ControlRigComponent.h"
#include "ControlRig.h"
#include "Sound/SoundBase.h"
#include "NiagaraSystem.h"
#include "Animation/AnimMontage.h"

// Forward declarations for editor-only classes
class UControlRigBlueprint;

#if WITH_EDITOR
#include "ControlRigBlueprint.h"
#include "RigVMCore/RigVMExecuteContext.h"
#include "Units/RigUnit.h"
#include "Rigs/RigHierarchy.h"
#endif

#include "AuracronUE56CombatExample.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronUE56Combat, Log, All);

/**
 * Combat State Enumeration for UE 5.6
 */
UENUM(BlueprintType)
enum class EAuracronCombatState : uint8
{
    Idle        UMETA(DisplayName = "Idle"),
    Attacking   UMETA(DisplayName = "Attacking"),
    Defending   UMETA(DisplayName = "Defending"),
    Stunned     UMETA(DisplayName = "Stunned"),
    Dead        UMETA(DisplayName = "Dead")
};

/**
 * Attack Type Enumeration for UE 5.6
 */
UENUM(BlueprintType)
enum class EAuracronAttackType : uint8
{
    Melee       UMETA(DisplayName = "Melee"),
    Ranged      UMETA(DisplayName = "Ranged"),
    Magic       UMETA(DisplayName = "Magic"),
    Special     UMETA(DisplayName = "Special")
};

/**
 * Advanced Combat Example Actor for UE 5.6
 * Demonstrates integration of MetaSound, Niagara, Control Rig, and Animation systems
 * using the latest UE 5.6 APIs in a production-ready implementation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONCOMBABRIDGE_API AAuracronUE56CombatExample : public AActor
{
    GENERATED_BODY()

public:
    AAuracronUE56CombatExample();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // === COMPONENTS ===
    
    /** Skeletal Mesh Component for character representation */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USkeletalMeshComponent> SkeletalMeshComponent;
    
    /** Audio Component for MetaSound integration */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> AudioComponent;
    
    /** Niagara Component for VFX */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UNiagaraComponent> NiagaraComponent;
    
    /** Control Rig Component for advanced animation */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UControlRigComponent> ControlRigComponent;

    // === ASSETS ===
    
    /** MetaSound Source for combat audio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio", meta = (AllowedClasses = "MetaSoundSource"))
    TSoftObjectPtr<UObject> CombatMetaSoundAsset;
    
    /** Niagara System for combat VFX */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TSoftObjectPtr<UNiagaraSystem> CombatVFXAsset;
    
    /** Control Rig Blueprint for advanced animation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (AllowedClasses = "ControlRigBlueprint"))
    TSoftObjectPtr<UObject> CombatControlRigAsset;
    
    /** Attack Animation Montage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    TObjectPtr<UAnimMontage> AttackMontage;

    // === COMBAT PROPERTIES ===
    
    /** Current combat state */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
    EAuracronCombatState CombatState;
    
    /** Current health */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float Health;
    
    /** Maximum health */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float MaxHealth;
    
    /** Attack damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float AttackDamage;
    
    /** Attack range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float AttackRange;
    
    /** Attack duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float AttackDuration = 1.0f;
    
    /** Defense duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float DefenseDuration = 2.0f;
    
    /** Stun duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float StunDuration = 1.5f;
    
    /** Whether currently in combat */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
    bool bIsInCombat;

private:
    // === INTERNAL STATE ===
    
    /** Attack timer */
    float AttackTimer = 0.0f;
    
    /** Defense timer */
    float DefenseTimer = 0.0f;
    
    /** Stun timer */
    float StunTimer = 0.0f;
    
    /** Whether death sequence has started */
    bool bDeathSequenceStarted = false;

    // === INITIALIZATION METHODS ===
    
    /** Initialize audio system using UE 5.6 MetaSound APIs */
    void InitializeAudioSystem();
    
    /** Initialize VFX system using UE 5.6 Niagara APIs */
    void InitializeVFXSystem();
    
    /** Initialize Control Rig system using UE 5.6 Control Rig APIs */
    void InitializeControlRigSystem();
    
    /** Set up Control Rig controls */
    void SetupControlRigControls(UControlRig* ControlRig);

    // === UPDATE METHODS ===
    
    /** Update combat state machine */
    void UpdateCombatState(float DeltaTime);
    
    /** Update audio system parameters */
    void UpdateAudioSystem(float DeltaTime);
    
    /** Update VFX system parameters */
    void UpdateVFXSystem(float DeltaTime);

    // === STATE HANDLERS ===
    
    /** Handle idle state */
    void HandleIdleState(float DeltaTime);
    
    /** Handle attacking state */
    void HandleAttackingState(float DeltaTime);
    
    /** Handle defending state */
    void HandleDefendingState(float DeltaTime);
    
    /** Handle stunned state */
    void HandleStunnedState(float DeltaTime);
    
    /** Handle dead state */
    void HandleDeadState(float DeltaTime);

    // === UTILITY METHODS ===
    
    /** Get current combat intensity value */
    float GetCombatIntensity() const;
    
    /** Play attack animation */
    void PlayAttackAnimation();
    
    /** Trigger attack VFX */
    void TriggerAttackVFX();
    
    /** Play attack audio */
    void PlayAttackAudio();
    
    /** Execute Control Rig attack sequence */
    void ExecuteControlRigAttack();
    
    /** Trigger damage effects */
    void TriggerDamageEffects();

public:
    // === PUBLIC INTERFACE ===
    
    /** Start an attack */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void StartAttack();
    
    /** Start defense */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void StartDefense();
    
    /** Take damage - Override from AActor */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;
    
    /** Apply stun effect */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void ApplyStun(float StunDurationOverride = -1.0f);
    
    /** Get current health percentage */
    UFUNCTION(BlueprintPure, Category = "Combat")
    float GetHealthPercentage() const { return Health / MaxHealth; }
    
    /** Get current combat state */
    UFUNCTION(BlueprintPure, Category = "Combat")
    EAuracronCombatState GetCombatState() const { return CombatState; }
    
    /** Check if character is alive */
    UFUNCTION(BlueprintPure, Category = "Combat")
    bool IsAlive() const { return CombatState != EAuracronCombatState::Dead; }
};
