// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Performance Monitoring Header
// Bridge 3.11: World Partition - Performance Monitoring

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Performance monitoring includes for UE5.6
#include "Stats/Stats.h"
#include "Stats/StatsData.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformProcess.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionPerformance.generated.h"

// Forward declarations
class UAuracronWorldPartitionPerformanceManager;

// =============================================================================
// PERFORMANCE TYPES AND ENUMS
// =============================================================================

// Performance monitoring states
UENUM(BlueprintType)
enum class EAuracronPerformanceMonitoringState : uint8
{
    Disabled                UMETA(DisplayName = "Disabled"),
    Enabled                 UMETA(DisplayName = "Enabled"),
    Recording               UMETA(DisplayName = "Recording"),
    Analyzing               UMETA(DisplayName = "Analyzing"),
    Reporting               UMETA(DisplayName = "Reporting")
};

// Performance severity levels
UENUM(BlueprintType)
enum class EAuracronPerformanceSeverity : uint8
{
    Info                    UMETA(DisplayName = "Info"),
    Warning                 UMETA(DisplayName = "Warning"),
    Critical                UMETA(DisplayName = "Critical"),
    Emergency               UMETA(DisplayName = "Emergency")
};

// Performance metric types
UENUM(BlueprintType)
enum class EAuracronPerformanceMetricType : uint8
{
    Memory                  UMETA(DisplayName = "Memory"),
    CPU                     UMETA(DisplayName = "CPU"),
    GPU                     UMETA(DisplayName = "GPU"),
    Streaming               UMETA(DisplayName = "Streaming"),
    Network                 UMETA(DisplayName = "Network"),
    IO                      UMETA(DisplayName = "Input/Output")
};

// Bottleneck types
UENUM(BlueprintType)
enum class EAuracronBottleneckType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Memory                  UMETA(DisplayName = "Memory"),
    CPU                     UMETA(DisplayName = "CPU"),
    GPU                     UMETA(DisplayName = "GPU"),
    IO                      UMETA(DisplayName = "I/O"),
    Network                 UMETA(DisplayName = "Network"),
    Streaming               UMETA(DisplayName = "Streaming")
};

// =============================================================================
// PERFORMANCE CONFIGURATION
// =============================================================================

/**
 * Performance Configuration
 * Configuration settings for performance monitoring in world partition
 */
USTRUCT(BlueprintType)
struct FAuracronPerformanceConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableMemoryTracking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCPUProfiling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableGPUProfiling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableStreamingMetrics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Monitoring")
    float MonitoringUpdateInterval = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Monitoring")
    float MetricSamplingRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Monitoring")
    int32 MaxMetricHistorySize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float MemoryWarningThresholdMB = 4096.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float MemoryCriticalThresholdMB = 6144.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float CPUWarningThresholdPercent = 70.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float CPUCriticalThresholdPercent = 90.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float GPUWarningThresholdPercent = 80.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float GPUCriticalThresholdPercent = 95.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float StreamingWarningThresholdMBps = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float StreamingCriticalThresholdMBps = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAutoOptimization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableBottleneckDetection = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableOptimizationSuggestions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reporting")
    bool bEnablePerformanceReporting = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reporting")
    bool bLogPerformanceMetrics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceDebug = false;

    FAuracronPerformanceConfiguration()
    {
        bEnablePerformanceMonitoring = true;
        bEnableMemoryTracking = true;
        bEnableCPUProfiling = true;
        bEnableGPUProfiling = true;
        bEnableStreamingMetrics = true;
        MonitoringUpdateInterval = 1.0f;
        MetricSamplingRate = 0.1f;
        MaxMetricHistorySize = 1000;
        MemoryWarningThresholdMB = 4096.0f;
        MemoryCriticalThresholdMB = 6144.0f;
        CPUWarningThresholdPercent = 70.0f;
        CPUCriticalThresholdPercent = 90.0f;
        GPUWarningThresholdPercent = 80.0f;
        GPUCriticalThresholdPercent = 95.0f;
        StreamingWarningThresholdMBps = 50.0f;
        StreamingCriticalThresholdMBps = 100.0f;
        bEnableAutoOptimization = false;
        bEnableBottleneckDetection = true;
        bEnableOptimizationSuggestions = true;
        bEnablePerformanceReporting = true;
        bLogPerformanceMetrics = false;
        bEnablePerformanceDebug = false;
    }
};

// =============================================================================
// PERFORMANCE METRIC
// =============================================================================

/**
 * Performance Metric
 * Individual performance metric data point
 */
USTRUCT(BlueprintType)
struct FAuracronPerformanceMetric
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    FString MetricId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    FString MetricName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    EAuracronPerformanceMetricType MetricType = EAuracronPerformanceMetricType::Memory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    float Value = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    float MinValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    float MaxValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    float AverageValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    FString Unit;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    EAuracronPerformanceSeverity Severity = EAuracronPerformanceSeverity::Info;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    FDateTime Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metric")
    FString Description;

    FAuracronPerformanceMetric()
    {
        MetricType = EAuracronPerformanceMetricType::Memory;
        Value = 0.0f;
        MinValue = 0.0f;
        MaxValue = 0.0f;
        AverageValue = 0.0f;
        Unit = TEXT("MB");
        Severity = EAuracronPerformanceSeverity::Info;
        Timestamp = FDateTime::Now();
    }
};

// =============================================================================
// PERFORMANCE REPORT
// =============================================================================

/**
 * Performance Report
 * Comprehensive performance analysis report
 */
USTRUCT(BlueprintType)
struct FAuracronPerformanceReport
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    FString ReportId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    FDateTime GenerationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    FTimespan MonitoringDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    TArray<FAuracronPerformanceMetric> Metrics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    EAuracronBottleneckType PrimaryBottleneck = EAuracronBottleneckType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    TArray<FString> OptimizationSuggestions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    float OverallPerformanceScore = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    int32 WarningCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    int32 CriticalCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Report")
    FString Summary;

    FAuracronPerformanceReport()
    {
        GenerationTime = FDateTime::Now();
        MonitoringDuration = FTimespan::Zero();
        PrimaryBottleneck = EAuracronBottleneckType::None;
        OverallPerformanceScore = 100.0f;
        WarningCount = 0;
        CriticalCount = 0;
    }
};

// =============================================================================
// WORLD PARTITION PERFORMANCE MANAGER
// =============================================================================

/**
 * World Partition Performance Manager
 * Central manager for performance monitoring in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class UAuracronWorldPartitionPerformanceManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    static UAuracronWorldPartitionPerformanceManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void Initialize(const FAuracronPerformanceConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void Tick(float DeltaTime);

    // Monitoring control
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void StartMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void StopMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void PauseMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void ResumeMonitoring();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    EAuracronPerformanceMonitoringState GetMonitoringState() const;

    // Metric collection
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void CollectMetrics();

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    FAuracronPerformanceMetric GetCurrentMetric(EAuracronPerformanceMetricType MetricType) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FAuracronPerformanceMetric> GetMetricHistory(EAuracronPerformanceMetricType MetricType, int32 MaxSamples = 100) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FAuracronPerformanceMetric> GetAllCurrentMetrics() const;

    // Memory monitoring
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetCurrentMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetPeakMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetAvailableMemoryMB() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TMap<FString, float> GetMemoryBreakdown() const;

    // CPU monitoring
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetCurrentCPUUsagePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetAverageCPUUsagePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TMap<FString, float> GetCPUBreakdown() const;

    // GPU monitoring
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetCurrentGPUUsagePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetGPUMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TMap<FString, float> GetGPUBreakdown() const;

    // Streaming monitoring
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetStreamingBandwidthMBps() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    int32 GetActiveStreamingOperations() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TMap<FString, float> GetStreamingBreakdown() const;

    // Bottleneck detection
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    EAuracronBottleneckType DetectBottleneck() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FString> GetBottleneckDetails(EAuracronBottleneckType BottleneckType) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetBottleneckSeverity(EAuracronBottleneckType BottleneckType) const;

    // Optimization suggestions
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FString> GetOptimizationSuggestions() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FString> GetOptimizationSuggestionsForBottleneck(EAuracronBottleneckType BottleneckType) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    bool ApplyAutoOptimization();

    // Performance scoring
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float CalculatePerformanceScore() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TMap<FString, float> GetPerformanceScoreBreakdown() const;

    // Reporting
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    FAuracronPerformanceReport GeneratePerformanceReport() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    bool SavePerformanceReport(const FAuracronPerformanceReport& Report, const FString& FilePath) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    FAuracronPerformanceReport LoadPerformanceReport(const FString& FilePath) const;

    // Thresholds and alerts
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void SetThreshold(EAuracronPerformanceMetricType MetricType, EAuracronPerformanceSeverity Severity, float Threshold);

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    float GetThreshold(EAuracronPerformanceMetricType MetricType, EAuracronPerformanceSeverity Severity) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    TArray<FAuracronPerformanceMetric> GetActiveAlerts() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void SetConfiguration(const FAuracronPerformanceConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    FAuracronPerformanceConfiguration GetConfiguration() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void EnablePerformanceDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    bool IsPerformanceDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void LogPerformanceState() const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void DrawDebugPerformanceInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Performance Manager")
    void ResetMetrics();

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceAlert, FAuracronPerformanceMetric, Metric);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBottleneckDetected, EAuracronBottleneckType, BottleneckType);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceReportGenerated, FAuracronPerformanceReport, Report);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnThresholdExceeded, EAuracronPerformanceMetricType, MetricType, float, Value);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceAlert OnPerformanceAlert;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBottleneckDetected OnBottleneckDetected;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceReportGenerated OnPerformanceReportGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnThresholdExceeded OnThresholdExceeded;

private:
    static UAuracronWorldPartitionPerformanceManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronPerformanceConfiguration Configuration;

    UPROPERTY()
    EAuracronPerformanceMonitoringState MonitoringState = EAuracronPerformanceMonitoringState::Disabled;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Performance data
    TMap<EAuracronPerformanceMetricType, TArray<FAuracronPerformanceMetric>> MetricHistory;
    TMap<EAuracronPerformanceMetricType, FAuracronPerformanceMetric> CurrentMetrics;
    TMap<EAuracronPerformanceMetricType, TMap<EAuracronPerformanceSeverity, float>> Thresholds;

    // Monitoring state
    FDateTime MonitoringStartTime;
    float LastUpdateTime = 0.0f;
    float LastSampleTime = 0.0f;

    // Thread safety
    mutable FCriticalSection MetricsLock;

    // Internal functions
    void UpdateMetrics(float DeltaTime);
    void CollectMemoryMetrics();
    void CollectCPUMetrics();
    void CollectGPUMetrics();
    void CollectStreamingMetrics();
    void CheckThresholds();
    void AnalyzeBottlenecks();
    void GenerateOptimizationSuggestions();
    void ValidateConfiguration();
    FAuracronPerformanceMetric CreateMetric(EAuracronPerformanceMetricType MetricType, const FString& Name, float Value, const FString& Unit) const;
    EAuracronPerformanceSeverity CalculateMetricSeverity(EAuracronPerformanceMetricType MetricType, float Value) const;
    void TrimMetricHistory();
};
