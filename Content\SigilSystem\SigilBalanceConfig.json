{"sigil_balance_system": {"version": "1.0.0", "description": "Dynamic balance configuration for Auracron Sigil System (Fusion 2.0)", "balance_framework": "Data-driven with runtime adjustments", "update_frequency": "Real-time with server validation"}, "power_scaling_curves": {"aegis_scaling": {"base_effectiveness": 1.0, "level_multiplier": 0.08, "max_level": 20, "diminishing_returns_threshold": 15, "diminishing_factor": 0.5, "power_curve": "Logarithmic", "balance_weights": {"primordial": 1.0, "cristalino": 1.2, "temporal": 1.3, "espectral": 1.4, "absoluto": 2.0}}, "ruin_scaling": {"base_effectiveness": 1.0, "level_multiplier": 0.12, "max_level": 20, "diminishing_returns_threshold": 12, "diminishing_factor": 0.6, "power_curve": "Exponential", "balance_weights": {"flamejante": 1.1, "gelido": 1.0, "sombrio": 1.3, "corrosivo": 1.2, "aniquilador": 2.5}}, "vesper_scaling": {"base_effectiveness": 1.0, "level_multiplier": 0.06, "max_level": 20, "diminishing_returns_threshold": 18, "diminishing_factor": 0.3, "power_curve": "Linear", "balance_weights": {"curativo": 1.1, "energetico": 1.0, "velocidade": 1.2, "visao": 1.1, "teleporte": 1.4, "temporal": 1.8}}}, "fusion_20_balance": {"base_power_multiplier": 1.5, "synergy_bonus_cap": 0.8, "cooldown_penalty": 2.0, "energy_cost_multiplier": 1.8, "duration_scaling": {"base_duration": 15.0, "level_bonus": 0.5, "archetype_modifier": {"guardian": 1.2, "destroyer": 0.8, "assassin": 0.7, "healer": 1.3, "controller": 1.1, "hybrid": 1.0}}, "power_caps": {"damage_multiplier_max": 5.0, "healing_multiplier_max": 3.0, "utility_effectiveness_max": 4.0, "defensive_multiplier_max": 3.5}}, "archetype_balance_matrix": {"legendary_archetypes": {"destruidor_absoluto": {"power_multiplier": 3.0, "cooldown_penalty": 3.0, "energy_cost_multiplier": 2.5, "required_mastery": 0.9, "usage_restrictions": {"max_uses_per_match": 3, "cooldown_between_uses": 180.0, "requires_all_sigils_max_level": false, "requires_minimum_combined_level": 45}}, "mestre_temporal": {"power_multiplier": 2.5, "cooldown_penalty": 2.5, "energy_cost_multiplier": 2.2, "required_mastery": 0.85, "usage_restrictions": {"max_uses_per_match": 4, "cooldown_between_uses": 150.0, "requires_all_sigils_max_level": false, "requires_minimum_combined_level": 40}}, "guardiao_cristalino": {"power_multiplier": 2.2, "cooldown_penalty": 2.0, "energy_cost_multiplier": 1.8, "required_mastery": 0.8, "usage_restrictions": {"max_uses_per_match": 5, "cooldown_between_uses": 120.0, "requires_all_sigils_max_level": false, "requires_minimum_combined_level": 35}}, "assassino_sombrio": {"power_multiplier": 2.8, "cooldown_penalty": 2.2, "energy_cost_multiplier": 2.0, "required_mastery": 0.82, "usage_restrictions": {"max_uses_per_match": 4, "cooldown_between_uses": 135.0, "requires_all_sigils_max_level": false, "requires_minimum_combined_level": 38}}}, "epic_archetypes": {"power_multiplier_range": [1.8, 2.2], "cooldown_penalty_range": [1.5, 2.0], "energy_cost_multiplier_range": [1.4, 1.8], "required_mastery_range": [0.6, 0.8], "usage_restrictions": {"max_uses_per_match": 6, "cooldown_between_uses": 90.0, "requires_minimum_combined_level": 25}}, "rare_archetypes": {"power_multiplier_range": [1.4, 1.8], "cooldown_penalty_range": [1.2, 1.5], "energy_cost_multiplier_range": [1.2, 1.4], "required_mastery_range": [0.4, 0.6], "usage_restrictions": {"max_uses_per_match": 8, "cooldown_between_uses": 60.0, "requires_minimum_combined_level": 15}}, "common_archetypes": {"power_multiplier_range": [1.0, 1.4], "cooldown_penalty_range": [1.0, 1.2], "energy_cost_multiplier_range": [1.0, 1.2], "required_mastery_range": [0.0, 0.4], "usage_restrictions": {"max_uses_per_match": 12, "cooldown_between_uses": 30.0, "requires_minimum_combined_level": 5}}}, "pvp_balance_modifiers": {"damage_reduction": 0.7, "healing_reduction": 0.8, "utility_effectiveness": 0.9, "cooldown_increase": 1.3, "energy_cost_increase": 1.2, "duration_reduction": 0.8, "special_restrictions": {"no_instant_execution": true, "max_invulnerability_duration": 2.0, "max_time_manipulation_strength": 0.3, "max_teleport_range": 600.0}}, "pve_balance_modifiers": {"damage_bonus": 1.1, "healing_bonus": 1.0, "utility_effectiveness": 1.1, "cooldown_reduction": 0.9, "energy_cost_reduction": 0.9, "duration_bonus": 1.2, "boss_encounter_modifiers": {"damage_multiplier": 1.5, "healing_multiplier": 1.3, "cooldown_reduction_bonus": 0.2, "energy_efficiency_bonus": 0.3}}, "dynamic_balance_rules": {"usage_tracking": {"track_sigil_popularity": true, "track_archetype_winrates": true, "track_fusion_effectiveness": true, "track_level_distribution": true, "sample_size_minimum": 1000, "confidence_threshold": 0.95}, "auto_adjustment_triggers": {"winrate_deviation_threshold": 0.15, "usage_rate_deviation_threshold": 0.3, "power_level_deviation_threshold": 0.2, "adjustment_frequency": "Weekly", "max_adjustment_per_cycle": 0.1}, "balance_constraints": {"min_power_multiplier": 0.8, "max_power_multiplier": 4.0, "min_cooldown": 5.0, "max_cooldown": 300.0, "min_duration": 1.0, "max_duration": 60.0, "min_energy_cost": 10.0, "max_energy_cost": 500.0}}, "competitive_balance": {"tournament_mode": {"enabled": false, "restricted_archetypes": ["destruidor_absoluto", "mestre_temporal"], "power_cap_multiplier": 0.8, "cooldown_floor_multiplier": 1.5, "standardized_levels": true, "fixed_level": 15}, "ranked_mode": {"enabled": true, "tier_based_restrictions": {"bronze": {"max_archetype_rarity": "Rare", "power_multiplier_cap": 2.0}, "silver": {"max_archetype_rarity": "Epic", "power_multiplier_cap": 2.5}, "gold": {"max_archetype_rarity": "Epic", "power_multiplier_cap": 3.0}, "platinum": {"max_archetype_rarity": "Legendary", "power_multiplier_cap": 3.5}, "diamond": {"max_archetype_rarity": "Legendary", "power_multiplier_cap": 4.0}, "master": {"max_archetype_rarity": "Legendary", "power_multiplier_cap": 5.0}}}}, "accessibility_balance": {"simplified_mode": {"enabled": true, "reduced_complexity": true, "auto_optimal_combinations": true, "visual_indicators": true, "power_normalization": 0.9}, "assisted_mode": {"enabled": true, "auto_activation_suggestions": true, "optimal_timing_hints": true, "effect_prediction": true, "simplified_ui": true}}, "telemetry_configuration": {"data_collection": {"sigil_usage_frequency": true, "archetype_formation_rates": true, "fusion_success_rates": true, "average_session_duration": true, "player_progression_speed": true, "error_rates": true, "performance_metrics": true}, "privacy_settings": {"anonymize_player_data": true, "aggregate_only": true, "opt_out_available": true, "data_retention_days": 90, "gdpr_compliant": true}, "analytics_endpoints": {"balance_analytics": "/api/v1/sigils/balance", "usage_analytics": "/api/v1/sigils/usage", "performance_analytics": "/api/v1/sigils/performance", "error_analytics": "/api/v1/sigils/errors"}}}