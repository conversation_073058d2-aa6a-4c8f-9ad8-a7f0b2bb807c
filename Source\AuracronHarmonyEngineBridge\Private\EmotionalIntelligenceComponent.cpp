#include "EmotionalIntelligenceComponent.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemComponent.h"
#include "Engine/World.h"
#include "Engine/GameInstance.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "Math/UnrealMathUtility.h"

UEmotionalIntelligenceComponent::UEmotionalIntelligenceComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // Tick every second for emotional analysis
    
    // Default configuration
    MonitoringInterval = 5.0f;
    FrustrationThreshold = 0.6f;
    EscalationDetectionWindow = 120.0f; // 2 minutes
    MaxSupportMessagesPerSession = 3;
    bEnablePredictiveAnalysis = true;
    bEnableMLTraining = true;
    
    // Initialize ML model data
    CurrentModelAccuracy = 0.0f;
    TrainingIterations = 0;
}

void UEmotionalIntelligenceComponent::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Emotional Intelligence Component started"));
    
    // Initialize timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            EmotionalAnalysisTimer,
            this,
            &UEmotionalIntelligenceComponent::PerformEmotionalAnalysis,
            MonitoringInterval,
            true
        );
        
        if (bEnableMLTraining)
        {
            World->GetTimerManager().SetTimer(
                MLTrainingTimer,
                this,
                &UEmotionalIntelligenceComponent::TrainEmotionalPredictionModel,
                300.0f, // Train every 5 minutes
                true
            );
        }
        
        World->GetTimerManager().SetTimer(
            DataPersistenceTimer,
            this,
            &UEmotionalIntelligenceComponent::SaveEmotionalData,
            600.0f, // Save every 10 minutes
            true
        );
    }
    
    // Load existing data
    LoadEmotionalData();
    
    // Validate configuration
    if (!ValidateConfiguration())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Invalid configuration detected, using defaults"));
        InitializeDefaultConfiguration();
    }
}

void UEmotionalIntelligenceComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Update emotional states for all monitored players
    UpdateEmotionalStates();
    
    // Process emotional trends
    ProcessEmotionalTrends();
}

void UEmotionalIntelligenceComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Save all emotional data before component destruction
    SaveEmotionalData();
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(EmotionalAnalysisTimer);
        World->GetTimerManager().ClearTimer(MLTrainingTimer);
        World->GetTimerManager().ClearTimer(DataPersistenceTimer);
    }
    
    // Cleanup data
    PlayerEmotionalHistory.Empty();
    CurrentEmotionalStates.Empty();
    PlayerFrustrationLevels.Empty();
    TrainingDataset.Empty();
    
    Super::EndPlay(EndPlayReason);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Emotional Intelligence Component ended"));
}

void UEmotionalIntelligenceComponent::StartEmotionalMonitoring(const FString& PlayerID)
{
    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot start monitoring for empty PlayerID"));
        return;
    }
    
    // Initialize emotional history for player
    if (!PlayerEmotionalHistory.Contains(PlayerID))
    {
        // Initialize with a default snapshot
        FPlayerBehaviorSnapshot DefaultSnapshot;
        DefaultSnapshot.Timestamp = FDateTime::Now();
        DefaultSnapshot.EmotionalState = EEmotionalState::Neutral;

        FPlayerBehaviorHistory NewHistory;
        NewHistory.BehaviorSnapshots.Add(DefaultSnapshot);
        PlayerEmotionalHistory.Add(PlayerID, NewHistory);
    }
    
    // Set initial emotional state
    CurrentEmotionalStates.Add(PlayerID, EEmotionalState::Neutral);
    PlayerFrustrationLevels.Add(PlayerID, 0.0f);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Started emotional monitoring for player: %s"), *PlayerID);
}

void UEmotionalIntelligenceComponent::StopEmotionalMonitoring(const FString& PlayerID)
{
    if (PlayerID.IsEmpty())
    {
        return;
    }
    
    // Save final emotional data
    SavePlayerEmotionalData(PlayerID);
    
    // Remove from active monitoring
    CurrentEmotionalStates.Remove(PlayerID);
    PlayerFrustrationLevels.Remove(PlayerID);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Stopped emotional monitoring for player: %s"), *PlayerID);
}

EEmotionalState UEmotionalIntelligenceComponent::AnalyzeCurrentEmotionalState(const FString& PlayerID)
{
    if (!CurrentEmotionalStates.Contains(PlayerID))
    {
        return EEmotionalState::Neutral;
    }
    
    // Get recent behavioral data
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return EEmotionalState::Neutral;
    }
    
    // Use the latest snapshot data
    const FPlayerBehaviorSnapshot& LatestSnapshot = PlayerHistory->BehaviorSnapshots.Last();
    float RecentPositivity = LatestSnapshot.PositivityScore;
    float RecentToxicity = LatestSnapshot.ToxicityScore;
    float RecentFrustration = LatestSnapshot.FrustrationLevel;

    // Values are already from the current snapshot, no need to average
    
    // Determine emotional state based on analysis
    EEmotionalState NewState = EEmotionalState::Neutral;
    
    if (RecentPositivity > 0.7f)
    {
        NewState = EEmotionalState::Happy;
    }
    else if (RecentFrustration > 0.8f)
    {
        NewState = EEmotionalState::Angry;
    }
    else if (RecentFrustration > 0.6f)
    {
        NewState = EEmotionalState::Frustrated;
    }
    else if (RecentToxicity > 0.5f)
    {
        NewState = EEmotionalState::Angry;
    }
    else if (RecentPositivity > 0.4f)
    {
        NewState = EEmotionalState::Calm;
    }
    
    // Update current state
    CurrentEmotionalStates.Add(PlayerID, NewState);
    
    return NewState;
}

float UEmotionalIntelligenceComponent::PredictFrustrationLevel(const FString& PlayerID, float TimeHorizon)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return 0.0f; // Not enough data for prediction
    }

    // Simple prediction based on current frustration level
    const FPlayerBehaviorSnapshot& LatestSnapshot = PlayerHistory->BehaviorSnapshots.Last();
    float CurrentFrustration = LatestSnapshot.FrustrationLevel;

    // Simple prediction: if current frustration is high, predict it will remain high
    float PredictedFrustration = CurrentFrustration;
    if (CurrentFrustration > 0.7f)
    {
        PredictedFrustration = FMath::Min(1.0f, CurrentFrustration + 0.1f);
    }

    return FMath::Clamp(PredictedFrustration, 0.0f, 1.0f);
}

bool UEmotionalIntelligenceComponent::DetectEmotionalEscalation(const FString& PlayerID)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return false;
    }
    
    // Check for rapid increase in frustration or toxicity
    FDateTime CurrentTime = FDateTime::Now();
    float EscalationRate = 0.0f;
    int32 SamplesInWindow = 0;
    
    // Use the latest snapshot from the history
    const FPlayerBehaviorSnapshot& Snapshot = PlayerHistory->BehaviorSnapshots.Last();
    // With single snapshot, check if current frustration is high
    if (Snapshot.FrustrationLevel > 0.7f)
    {
        EscalationRate = 1.0f; // High frustration indicates potential escalation
        SamplesInWindow = 1;
    }
    
    if (SamplesInWindow > 0)
    {
        EscalationRate /= SamplesInWindow;
        return EscalationRate > 0.01f; // Threshold for escalation detection
    }
    
    return false;
}

void UEmotionalIntelligenceComponent::ProvideEmotionalSupport(const FString& PlayerID, const FString& SupportMessage)
{
    // Get reference to registered players from HarmonyEngineSubsystem
    UHarmonyEngineSubsystem* HarmonySubsystem = GetWorld()->GetSubsystem<UHarmonyEngineSubsystem>();
    if (!HarmonySubsystem)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("HarmonyEngineSubsystem not found"));
        return;
    }
    
    // Check support message frequency
    if (!ValidateSupportMessageFrequency(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Support message frequency limit reached for player: %s"), *PlayerID);
        return;
    }
    
    // Deliver support message
    DeliverEmotionalSupport(PlayerID, SupportMessage);
    
    // Update support message count
    int32& MessageCount = SupportMessagesCount.FindOrAdd(PlayerID);
    MessageCount++;
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Provided emotional support to player %s: %s"), *PlayerID, *SupportMessage);
}

// Implementation of missing helper functions

bool UEmotionalIntelligenceComponent::ValidateSupportMessageFrequency(const FString& PlayerID)
{
    int32 CurrentCount = SupportMessagesCount.Contains(PlayerID) ? SupportMessagesCount[PlayerID] : 0;
    return CurrentCount < MaxSupportMessagesPerSession;
}

void UEmotionalIntelligenceComponent::DeliverEmotionalSupport(const FString& PlayerID, const FString& Message)
{
    // Find the player controller to deliver the message
    UHarmonyEngineSubsystem* HarmonySubsystem = GetWorld()->GetSubsystem<UHarmonyEngineSubsystem>();
    if (!HarmonySubsystem)
    {
        return;
    }

    // In a full implementation, this would send the message through the UI system
    // For now, we log it and could integrate with chat/notification systems
    UE_LOG(LogHarmonyEngine, Log, TEXT("Delivering emotional support to %s: %s"), *PlayerID, *Message);

    // This would integrate with the game's notification/UI system
    // Example: NotificationSystem->ShowSupportMessage(PlayerID, Message);
}

void UEmotionalIntelligenceComponent::SavePlayerEmotionalData(const FString& PlayerID)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return;
    }

    // Create JSON data for player's emotional history
    FString EmotionalDataJson = TEXT("{\"emotional_history\":[");
    bool bFirstEntry = true;

    // Save last 50 entries to avoid excessive data
    // Save the latest snapshot we have
    const FPlayerBehaviorSnapshot& Snapshot = PlayerHistory->BehaviorSnapshots.Last();
    EmotionalDataJson += FString::Printf(
        TEXT("{\"emotion\":%d,\"frustration\":%.3f,\"positivity\":%.3f,\"timestamp\":\"%s\"}"),
        static_cast<int32>(Snapshot.EmotionalState),
        Snapshot.FrustrationLevel,
        Snapshot.PositivityScore,
        *Snapshot.Timestamp.ToString()
    );

    EmotionalDataJson += TEXT("]}");

    // Save to file
    FString SavePath = FPaths::ProjectSavedDir() + FString::Printf(TEXT("HarmonyEngine/Emotional/%s_emotional.json"), *PlayerID);
    FFileHelper::SaveStringToFile(EmotionalDataJson, *SavePath);

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Saved emotional data for player: %s"), *PlayerID);
}

bool UEmotionalIntelligenceComponent::ValidateConfiguration()
{
    bool bIsValid = true;

    if (MonitoringInterval <= 0.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid MonitoringInterval: %f"), MonitoringInterval);
        bIsValid = false;
    }

    if (FrustrationThreshold < 0.0f || FrustrationThreshold > 1.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid FrustrationThreshold: %f"), FrustrationThreshold);
        bIsValid = false;
    }

    if (EscalationDetectionWindow <= 0.0f)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid EscalationDetectionWindow: %f"), EscalationDetectionWindow);
        bIsValid = false;
    }

    if (MaxSupportMessagesPerSession <= 0)
    {
        UE_LOG(LogHarmonyEngine, Error, TEXT("Invalid MaxSupportMessagesPerSession: %d"), MaxSupportMessagesPerSession);
        bIsValid = false;
    }

    return bIsValid;
}

void UEmotionalIntelligenceComponent::InitializeDefaultConfiguration()
{
    MonitoringInterval = 5.0f;
    FrustrationThreshold = 0.6f;
    EscalationDetectionWindow = 120.0f;
    MaxSupportMessagesPerSession = 3;
    bEnablePredictiveAnalysis = true;
    bEnableMLTraining = true;

    UE_LOG(LogHarmonyEngine, Log, TEXT("Initialized default Emotional Intelligence configuration"));
}

void UEmotionalIntelligenceComponent::PerformEmotionalAnalysis()
{
    // Analyze emotional patterns for all monitored players
    for (const auto& StatePair : CurrentEmotionalStates)
    {
        const FString& PlayerID = StatePair.Key;
        AnalyzeCurrentEmotionalState(PlayerID);
    }
}

void UEmotionalIntelligenceComponent::UpdateEmotionalStates()
{
    // Update emotional states based on recent behavior
    for (const auto& StatePair : CurrentEmotionalStates)
    {
        const FString& PlayerID = StatePair.Key;
        EEmotionalState NewState = AnalyzeCurrentEmotionalState(PlayerID);
        CurrentEmotionalStates.Add(PlayerID, NewState);
    }
}

void UEmotionalIntelligenceComponent::ProcessEmotionalTrends()
{
    // Process long-term emotional trends for ML training
    for (const auto& HistoryPair : PlayerEmotionalHistory)
    {
        const FString& PlayerID = HistoryPair.Key;
        const FPlayerBehaviorHistory& History = HistoryPair.Value;

        if (History.BehaviorSnapshots.Num() >= 5) // Need minimum data for trend analysis
        {
            float EmotionalTrend = CalculateEmotionalTrend(PlayerID);

            // Add to training data if significant trend detected
            if (FMath::Abs(EmotionalTrend) > 0.1f && bEnableMLTraining)
            {
                FPlayerBehaviorSnapshot TrendData = History.BehaviorSnapshots.Last();
                TrainingDataset.Add(TrendData);
            }
        }
    }
}

void UEmotionalIntelligenceComponent::SaveEmotionalData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Saving emotional intelligence data"));

    // Save all player emotional data
    for (const auto& HistoryPair : PlayerEmotionalHistory)
    {
        SavePlayerEmotionalData(HistoryPair.Key);
    }

    // Save ML model data
    FString MLDataPath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/MLModel/training_data.json");
    FString MLDataJson = TEXT("{\"training_samples\":[");

    bool bFirstSample = true;
    for (const FPlayerBehaviorSnapshot& Sample : TrainingDataset)
    {
        if (!bFirstSample)
        {
            MLDataJson += TEXT(",");
        }
        bFirstSample = false;

        MLDataJson += FString::Printf(
            TEXT("{\"emotion\":%d,\"toxicity\":%.3f,\"positivity\":%.3f,\"frustration\":%.3f}"),
            static_cast<int32>(Sample.EmotionalState),
            Sample.ToxicityScore,
            Sample.PositivityScore,
            Sample.FrustrationLevel
        );
    }

    MLDataJson += FString::Printf(TEXT("],\"model_accuracy\":%.3f,\"training_iterations\":%d}"), CurrentModelAccuracy, TrainingIterations);

    FFileHelper::SaveStringToFile(MLDataJson, *MLDataPath);
}

void UEmotionalIntelligenceComponent::LoadEmotionalData()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Loading emotional intelligence data"));

    // Load ML model data
    FString MLDataPath = FPaths::ProjectSavedDir() + TEXT("HarmonyEngine/MLModel/training_data.json");
    FString LoadedMLJson;

    if (FFileHelper::LoadFileToString(LoadedMLJson, *MLDataPath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(LoadedMLJson);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            JsonObject->TryGetNumberField(TEXT("model_accuracy"), CurrentModelAccuracy);
            JsonObject->TryGetNumberField(TEXT("training_iterations"), TrainingIterations);

            UE_LOG(LogHarmonyEngine, Log, TEXT("Loaded ML model data - Accuracy: %.3f, Iterations: %d"),
                CurrentModelAccuracy, TrainingIterations);
        }
    }
}

// Additional missing function implementations

TArray<FString> UEmotionalIntelligenceComponent::GetEmotionalTriggers(const FString& PlayerID)
{
    TArray<FString> Triggers;

    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return Triggers;
    }

    // Analyze patterns that lead to negative emotional states
    for (int32 i = 1; i < PlayerHistory->BehaviorSnapshots.Num(); i++)
    {
        const FPlayerBehaviorSnapshot& Current = PlayerHistory->BehaviorSnapshots[i];
        const FPlayerBehaviorSnapshot& Previous = PlayerHistory->BehaviorSnapshots[i - 1];

        // Check for emotional state deterioration
        if (Current.FrustrationLevel > Previous.FrustrationLevel + 0.2f)
        {
            // Analyze what might have triggered this
            if (Current.NegativeActionsCount > Previous.NegativeActionsCount)
            {
                Triggers.AddUnique(TEXT("Negative player interactions"));
            }

            if (Current.SessionDuration > Previous.SessionDuration + 60.0f)
            {
                Triggers.AddUnique(TEXT("Extended play session"));
            }

            if (Current.ToxicityScore > Previous.ToxicityScore + 0.1f)
            {
                Triggers.AddUnique(TEXT("Exposure to toxic behavior"));
            }
        }
    }

    return Triggers;
}

float UEmotionalIntelligenceComponent::CalculateEmotionalStability(const FString& PlayerID)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return 0.5f; // Neutral stability for insufficient data
    }

    // Calculate variance in emotional states over time
    TArray<float> EmotionalValues;
    for (const FPlayerBehaviorSnapshot& Snapshot : PlayerHistory->BehaviorSnapshots)
    {
        // Convert emotional state to numerical value for analysis
        float EmotionalValue = 0.5f; // Neutral baseline

        switch (Snapshot.EmotionalState)
        {
            case EEmotionalState::Happy:
                EmotionalValue = 1.0f;
                break;
            case EEmotionalState::Excited:
                EmotionalValue = 0.9f;
                break;
            case EEmotionalState::Calm:
                EmotionalValue = 0.7f;
                break;
            case EEmotionalState::Frustrated:
                EmotionalValue = 0.3f;
                break;
            case EEmotionalState::Angry:
                EmotionalValue = 0.1f;
                break;
            case EEmotionalState::Sad:
                EmotionalValue = 0.2f;
                break;
            case EEmotionalState::Anxious:
                EmotionalValue = 0.4f;
                break;
            default:
                EmotionalValue = 0.5f;
                break;
        }

        EmotionalValues.Add(EmotionalValue);
    }

    // Calculate variance
    float Mean = 0.0f;
    for (float Value : EmotionalValues)
    {
        Mean += Value;
    }
    Mean /= EmotionalValues.Num();

    float Variance = 0.0f;
    for (float Value : EmotionalValues)
    {
        Variance += FMath::Pow(Value - Mean, 2.0f);
    }
    Variance /= EmotionalValues.Num();

    // Stability is inverse of variance (lower variance = higher stability)
    float Stability = 1.0f / (1.0f + Variance);

    return FMath::Clamp(Stability, 0.0f, 1.0f);
}

bool UEmotionalIntelligenceComponent::IsPlayerInOptimalState(const FString& PlayerID)
{
    EEmotionalState CurrentState = GetCurrentEmotionalState(PlayerID);
    float StabilityScore = CalculateEmotionalStability(PlayerID);
    float FrustrationLevel = GetCurrentFrustrationLevel(PlayerID);

    // Player is in optimal state if they're in positive emotional state, stable, and not frustrated
    bool bPositiveState = (CurrentState == EEmotionalState::Happy) ||
                         (CurrentState == EEmotionalState::Calm) ||
                         (CurrentState == EEmotionalState::Excited);

    bool bStable = StabilityScore > 0.6f;
    bool bNotFrustrated = FrustrationLevel < 0.4f;

    return bPositiveState && bStable && bNotFrustrated;
}

FHarmonyInterventionData UEmotionalIntelligenceComponent::RecommendIntervention(const FString& PlayerID)
{
    FHarmonyInterventionData Intervention;

    EEmotionalState CurrentState = GetCurrentEmotionalState(PlayerID);
    float FrustrationLevel = GetCurrentFrustrationLevel(PlayerID);
    float StabilityScore = CalculateEmotionalStability(PlayerID);

    // Determine intervention type based on emotional analysis
    if (CurrentState == EEmotionalState::Angry && FrustrationLevel > 0.8f)
    {
        Intervention.InterventionType = EInterventionType::Emergency;
        Intervention.InterventionMessage = TEXT("We notice you're having a really tough time. Let's take a step back and breathe together. 🌸");
        Intervention.SuggestedAction = TEXT("Take a 10-minute wellness break with guided breathing");
        Intervention.InterventionPriority = 1.0f;
        Intervention.bRequiresImmediateAction = true;
    }
    else if (CurrentState == EEmotionalState::Frustrated && FrustrationLevel > 0.6f)
    {
        Intervention.InterventionType = EInterventionType::Strong;
        Intervention.InterventionMessage = TEXT("Feeling frustrated? That's totally normal! Every champion has tough moments. 💪");
        Intervention.SuggestedAction = TEXT("Try a different strategy or ask a teammate for tips");
        Intervention.InterventionPriority = 0.8f;
        Intervention.bRequiresImmediateAction = false;
    }
    else if (StabilityScore < 0.4f)
    {
        Intervention.InterventionType = EInterventionType::Moderate;
        Intervention.InterventionMessage = TEXT("We've noticed some ups and downs in your session. Remember, consistency comes with practice! 🎯");
        Intervention.SuggestedAction = TEXT("Focus on one improvement at a time");
        Intervention.InterventionPriority = 0.6f;
        Intervention.bRequiresImmediateAction = false;
    }
    else if (FrustrationLevel > FrustrationThreshold)
    {
        Intervention.InterventionType = EInterventionType::Gentle;
        Intervention.InterventionMessage = TEXT("Hey there! Take a deep breath - you're doing great! 🌟");
        Intervention.SuggestedAction = TEXT("Try a positive affirmation or celebrate a recent success");
        Intervention.InterventionPriority = 0.4f;
        Intervention.bRequiresImmediateAction = false;
    }

    // Add relevant tags
    switch (Intervention.InterventionType)
    {
        case EInterventionType::Emergency:
            Intervention.InterventionTags.AddTag(HarmonyEngineGameplayTags::Intervention_Emergency);
            break;
        case EInterventionType::Strong:
            Intervention.InterventionTags.AddTag(HarmonyEngineGameplayTags::Intervention_Strong);
            break;
        case EInterventionType::Moderate:
            Intervention.InterventionTags.AddTag(HarmonyEngineGameplayTags::Intervention_Moderate);
            break;
        case EInterventionType::Gentle:
            Intervention.InterventionTags.AddTag(HarmonyEngineGameplayTags::Intervention_Gentle);
            break;
    }

    return Intervention;
}

TArray<FString> UEmotionalIntelligenceComponent::GenerateSupportMessages(EEmotionalState EmotionalState)
{
    TArray<FString> SupportMessages;

    switch (EmotionalState)
    {
        case EEmotionalState::Angry:
            SupportMessages.Add(TEXT("Take a deep breath. Anger is temporary, but your growth is permanent. 🌱"));
            SupportMessages.Add(TEXT("Channel that energy into determination. You've overcome challenges before! 💪"));
            SupportMessages.Add(TEXT("Remember: every pro player has bad moments. What matters is how we bounce back! 🏆"));
            break;

        case EEmotionalState::Frustrated:
            SupportMessages.Add(TEXT("Frustration means you care! That passion will drive your improvement. 🔥"));
            SupportMessages.Add(TEXT("Every expert was once a beginner. You're on the right path! 🛤️"));
            SupportMessages.Add(TEXT("Take a moment to appreciate how far you've already come. 📈"));
            break;

        case EEmotionalState::Sad:
            SupportMessages.Add(TEXT("It's okay to feel down sometimes. Your gaming community is here for you! 🤗"));
            SupportMessages.Add(TEXT("Tomorrow is a new day with new opportunities to shine! ☀️"));
            SupportMessages.Add(TEXT("Remember: you bring unique value to every team you join. 🌟"));
            break;

        case EEmotionalState::Anxious:
            SupportMessages.Add(TEXT("Feeling nervous? That shows you care about doing well! 💙"));
            SupportMessages.Add(TEXT("Take it one game at a time. You don't have to be perfect. 🎯"));
            SupportMessages.Add(TEXT("Your teammates believe in you, and so do we! 🤝"));
            break;

        case EEmotionalState::Happy:
            SupportMessages.Add(TEXT("Your positive energy is contagious! Keep spreading those good vibes! ✨"));
            SupportMessages.Add(TEXT("You're in the zone! This is what peak performance feels like! 🚀"));
            break;

        case EEmotionalState::Excited:
            SupportMessages.Add(TEXT("Love that enthusiasm! Channel it into amazing plays! ⚡"));
            SupportMessages.Add(TEXT("Your excitement lifts the whole team! Keep it up! 🎉"));
            break;

        case EEmotionalState::Calm:
            SupportMessages.Add(TEXT("Perfect mindset! Calm confidence leads to great decisions. 🧘"));
            SupportMessages.Add(TEXT("You're in control and it shows. Trust your instincts! 🎯"));
            break;

        default:
            SupportMessages.Add(TEXT("You're doing great! Keep up the positive attitude! 😊"));
            break;
    }

    return SupportMessages;
}

bool UEmotionalIntelligenceComponent::ShouldSuggestBreak(const FString& PlayerID)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return false;
    }

    const FPlayerBehaviorSnapshot& LatestSnapshot = PlayerHistory->BehaviorSnapshots.Last();

    // Suggest break if:
    // 1. Session duration is very long (>3 hours)
    // 2. Frustration level is high and increasing
    // 3. Multiple negative emotional states in recent history

    bool bLongSession = LatestSnapshot.SessionDuration > 10800.0f; // 3 hours
    bool bHighFrustration = LatestSnapshot.FrustrationLevel > 0.7f;
    bool bNegativeTrend = HasNegativeEmotionalTrend(PlayerID);

    return bLongSession || (bHighFrustration && bNegativeTrend);
}

void UEmotionalIntelligenceComponent::UpdateMLModel(const FPlayerBehaviorSnapshot& TrainingData)
{
    if (!bEnableMLTraining)
    {
        return;
    }

    // Add to training dataset
    TrainingDataset.Add(TrainingData);

    // Limit dataset size for performance
    if (TrainingDataset.Num() > 5000)
    {
        // Remove oldest 20% of data
        int32 RemoveCount = TrainingDataset.Num() / 5;
        TrainingDataset.RemoveAt(0, RemoveCount);
    }

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated ML model with new training data. Dataset size: %d"), TrainingDataset.Num());
}

void UEmotionalIntelligenceComponent::TrainEmotionalPredictionModel()
{
    if (!bEnableMLTraining || TrainingDataset.Num() < 10)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Insufficient training data for ML model training"));
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("Training emotional prediction model with %d samples"), TrainingDataset.Num());

    // Simple ML training simulation using statistical analysis
    float TotalAccuracy = 0.0f;
    int32 ValidationSamples = FMath::Min(TrainingDataset.Num() / 4, 50); // 25% for validation

    for (int32 i = 0; i < ValidationSamples; i++)
    {
        const FPlayerBehaviorSnapshot& Sample = TrainingDataset[TrainingDataset.Num() - 1 - i];

        // Predict emotional state based on behavior metrics
        EEmotionalState PredictedState = PredictEmotionalStateFromBehavior(Sample);
        EEmotionalState ActualState = Sample.EmotionalState;

        // Calculate accuracy (1.0 for exact match, partial credit for close states)
        float SampleAccuracy = CalculateEmotionalStateSimilarity(PredictedState, ActualState);
        TotalAccuracy += SampleAccuracy;
    }

    CurrentModelAccuracy = TotalAccuracy / ValidationSamples;
    TrainingIterations++;

    UE_LOG(LogHarmonyEngine, Log, TEXT("ML training completed - Accuracy: %.3f, Iteration: %d"),
        CurrentModelAccuracy, TrainingIterations);
}

float UEmotionalIntelligenceComponent::GetModelAccuracy() const
{
    return CurrentModelAccuracy;
}

// Helper function implementations

EEmotionalState UEmotionalIntelligenceComponent::GetCurrentEmotionalState(const FString& PlayerID) const
{
    if (const EEmotionalState* State = CurrentEmotionalStates.Find(PlayerID))
    {
        return *State;
    }
    return EEmotionalState::Neutral;
}

float UEmotionalIntelligenceComponent::GetCurrentFrustrationLevel(const FString& PlayerID) const
{
    if (const float* Level = PlayerFrustrationLevels.Find(PlayerID))
    {
        return *Level;
    }
    return 0.0f;
}

bool UEmotionalIntelligenceComponent::HasNegativeEmotionalTrend(const FString& PlayerID) const
{
    const FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return false;
    }

    // Check last 3 emotional states for negative trend
    int32 NegativeStates = 0;
    int32 CheckCount = FMath::Min(3, PlayerHistory->BehaviorSnapshots.Num());

    for (int32 i = PlayerHistory->BehaviorSnapshots.Num() - CheckCount; i < PlayerHistory->BehaviorSnapshots.Num(); i++)
    {
        EEmotionalState State = PlayerHistory->BehaviorSnapshots[i].EmotionalState;
        if (State == EEmotionalState::Angry || State == EEmotionalState::Frustrated || State == EEmotionalState::Sad)
        {
            NegativeStates++;
        }
    }

    return NegativeStates >= 2; // 2 out of 3 recent states are negative
}

EEmotionalState UEmotionalIntelligenceComponent::PredictEmotionalStateFromBehavior(const FPlayerBehaviorSnapshot& BehaviorData) const
{
    // Simple rule-based prediction (in full implementation, this would use trained ML model)
    if (BehaviorData.ToxicityScore > 0.7f || BehaviorData.FrustrationLevel > 0.8f)
    {
        return EEmotionalState::Angry;
    }
    else if (BehaviorData.FrustrationLevel > 0.5f)
    {
        return EEmotionalState::Frustrated;
    }
    else if (BehaviorData.PositivityScore > 0.8f)
    {
        return EEmotionalState::Happy;
    }
    else if (BehaviorData.PositivityScore > 0.6f)
    {
        return EEmotionalState::Calm;
    }
    else if (BehaviorData.ToxicityScore > 0.3f)
    {
        return EEmotionalState::Anxious;
    }

    return EEmotionalState::Neutral;
}

float UEmotionalIntelligenceComponent::CalculateEmotionalStateSimilarity(EEmotionalState Predicted, EEmotionalState Actual) const
{
    if (Predicted == Actual)
    {
        return 1.0f; // Perfect match
    }

    // Define emotional state relationships for partial credit
    TMap<EEmotionalState, TArray<EEmotionalState>> SimilarStates;
    SimilarStates.Add(EEmotionalState::Happy, {EEmotionalState::Excited, EEmotionalState::Calm});
    SimilarStates.Add(EEmotionalState::Excited, {EEmotionalState::Happy});
    SimilarStates.Add(EEmotionalState::Calm, {EEmotionalState::Happy, EEmotionalState::Neutral});
    SimilarStates.Add(EEmotionalState::Frustrated, {EEmotionalState::Angry, EEmotionalState::Anxious});
    SimilarStates.Add(EEmotionalState::Angry, {EEmotionalState::Frustrated});
    SimilarStates.Add(EEmotionalState::Sad, {EEmotionalState::Anxious});
    SimilarStates.Add(EEmotionalState::Anxious, {EEmotionalState::Frustrated, EEmotionalState::Sad});

    // Check for similar states
    if (SimilarStates.Contains(Predicted))
    {
        const TArray<EEmotionalState>& SimilarToPredicted = SimilarStates[Predicted];
        if (SimilarToPredicted.Contains(Actual))
        {
            return 0.7f; // Partial credit for similar states
        }
    }

    return 0.0f; // No similarity
}

float UEmotionalIntelligenceComponent::CalculateEmotionalTrend(const FString& PlayerID)
{
    FPlayerBehaviorHistory* PlayerHistory = PlayerEmotionalHistory.Find(PlayerID);
    if (!PlayerHistory || PlayerHistory->BehaviorSnapshots.Num() == 0)
    {
        return 0.0f;
    }

    // Calculate trend in emotional positivity over time
    TArray<float> EmotionalValues;
    TArray<float> TimeValues;

    // Use the latest snapshot to determine trend
    const FPlayerBehaviorSnapshot& LatestSnapshot = PlayerHistory->BehaviorSnapshots.Last();
    float EmotionalValue = ConvertEmotionalStateToValue(LatestSnapshot.EmotionalState);
    return EmotionalValue > 0.5f ? 1.0f : -1.0f; // Positive or negative trend


}

float UEmotionalIntelligenceComponent::ConvertEmotionalStateToValue(EEmotionalState State) const
{
    switch (State)
    {
        case EEmotionalState::Happy: return 1.0f;
        case EEmotionalState::Excited: return 0.9f;
        case EEmotionalState::Calm: return 0.7f;
        case EEmotionalState::Neutral: return 0.5f;
        case EEmotionalState::Anxious: return 0.4f;
        case EEmotionalState::Frustrated: return 0.3f;
        case EEmotionalState::Sad: return 0.2f;
        case EEmotionalState::Angry: return 0.1f;
        default: return 0.5f;
    }
}


