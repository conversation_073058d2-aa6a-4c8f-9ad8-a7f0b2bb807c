/**
 * AuracronAutomatedQABridge.Build.cs
 * 
 * Build configuration for Auracron Automated QA Bridge module.
 * Comprehensive automated QA and validation system for all Auracron systems.
 * 
 * Uses UE 5.6 modern build system for production-ready compilation.
 */

using UnrealBuildTool;

public class AuracronAutomatedQABridge : ModuleRules
{
    public AuracronAutomatedQABridge(ReadOnlyTargetRules Target) : base(Target)
    {
        // UE 5.6 PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable modern C++ features
        CppStandard = CppStandardVersion.Cpp20;
        bUseUnity = true;
        
        // Core dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities",
            "UMG",
            "Slate",
            "SlateCore",
            "InputCore",
            "EnhancedInput",
            "NetCore",
            "OnlineSubsystem",
            "OnlineSubsystemUtils",
            "Sockets",
            "Networking",
            "Json",
            "JsonUtilities",
            "HTTP",
            "AutomationController",
            "AutomationWorker",
            "AutomationMessages",
            "AutomationTest",
            "FunctionalTesting",
            "ImageWrapper",
            "Analytics",
            "AnalyticsET",
            "PerfCounters",
            "SessionServices",
            "RenderCore",
            "ApplicationCore"
        });

        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "DeveloperSettings",
            "StatusBar",
            "MainFrame",
            "LevelEditor",
            "SceneOutliner",
            "ContentBrowser",
            "AssetTools",
            "AssetRegistry",
            "EditorSubsystem",
            "UnrealEdMessages",
            "SourceControl",
            "Projects",
            "DesktopPlatform",
            "LauncherPlatform",
            "GameProjectGeneration",
            "AddContentDialog",
            "HardwareTargeting",
            "LocalizationService",
            "TranslationEditor",
            "Localization",
            "InternationalizationSettings"
            });
        }

        // Auracron bridge dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "AuracronMasterOrchestrator",
            "AuracronDynamicRealmBridge",
            "AuracronHarmonyEngineBridge",
            "AuracronSigilosBridge",
            "AuracronPCGBridge",
            "AuracronNexusCommunityBridge",
            "AuracronLivingWorldBridge",
            "AuracronAdaptiveEngagementBridge",
            "AuracronQuantumConsciousnessBridge",
            "AuracronIntelligentDocumentationBridge",
            "AuracronNetworkingBridge",
            "AuracronAnalyticsBridge",
            "AuracronUIBridge",
            "AuracronAudioBridge",
            "AuracronVFXBridge",
            "AuracronTutorialBridge",
            "AuracronAbismoUmbrioBridge"
        });

        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "RenderCore",
            "ApplicationCore",
            "AutomationTest",
            "AutomationMessages",
            "AutomationController",
            "AutomationWorker",
            "FunctionalTesting"
        });

        // Platform-specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "AudioMixerXAudio2",
                "WindowsPlatformFeatures"
            });
        }

        // Development and editor dependencies
        if (Target.bBuildDeveloperTools)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "AutomationTest",
                "SessionServices"
            });
        }

        // Optimization settings for production builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_OPTIMIZED=1");
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_DEVELOPMENT=1");
        }

        // Enable advanced features
        PublicDefinitions.Add("WITH_AURACRON_AUTOMATED_QA=1");
        PublicDefinitions.Add("WITH_AUTOMATED_TESTING=1");
        PublicDefinitions.Add("WITH_CONTINUOUS_VALIDATION=1");
        PublicDefinitions.Add("WITH_PERFORMANCE_MONITORING=1");
        PublicDefinitions.Add("WITH_REGRESSION_TESTING=1");
        PublicDefinitions.Add("WITH_QUALITY_ASSURANCE=1");

        // Version information
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_PATCH=0");
    }
}
