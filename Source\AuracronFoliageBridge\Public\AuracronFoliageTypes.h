#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"

#include "AuracronFoliageTypes.generated.h"

/**
 * Enum for foliage edit modes
 */
UENUM(BlueprintType)
enum class EFoliageEditMode : uint8
{
    Paint       UMETA(DisplayName = "Paint"),
    Erase       UMETA(DisplayName = "Erase"),
    Select      UMETA(DisplayName = "Select"),
    Reapply     UMETA(DisplayName = "Reapply")
};

/**
 * Enum for foliage edit tool types
 */
UENUM(BlueprintType)
enum class EFoliageEditToolType : uint8
{
    Brush       UMETA(DisplayName = "Brush"),
    Lasso       UMETA(DisplayName = "Lasso"),
    Spline      UMETA(DisplayName = "Spline"),
    Fill        UMETA(DisplayName = "Fill")
};

/**
 * Struct for foliage edit settings
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FFoliageEditSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float BrushSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float PaintDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    float EraseDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    bool bAlignToNormal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    bool bRandomYaw;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Edit")
    FVector2D ScaleRange;

    FFoliageEditSettings()
    {
        BrushSize = 512.0f;
        PaintDensity = 1.0f;
        EraseDensity = 1.0f;
        bAlignToNormal = true;
        bRandomYaw = true;
        ScaleRange = FVector2D(0.8f, 1.2f);
    }
};
