// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Actor Manager Header
// Bridge 3.5: World Partition - Actor Management

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// World Partition Actor includes for UE5.6
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionActorDescView.h"
// Forward declaration for WorldPartition compatibility
class UWorldPartitionActorCluster;
#include "WorldPartition/WorldPartitionStreamingSource.h"
// UE 5.6 Compatible - PartitionActor.h path updated
#include "WorldPartition/WorldPartitionActorDesc.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionActorManager.generated.h"

// Forward declarations
class UAuracronWorldPartitionActorManager;
class AActor;
class ULevel;

// =============================================================================
// ACTOR MANAGEMENT TYPES AND ENUMS
// =============================================================================

// Actor streaming states
UENUM(BlueprintType)
enum class EAuracronActorStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed"),
    Pending                 UMETA(DisplayName = "Pending")
};

// Actor placement types
UENUM(BlueprintType)
enum class EAuracronActorPlacementType : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Dynamic                 UMETA(DisplayName = "Dynamic"),
    Streaming               UMETA(DisplayName = "Streaming"),
    Persistent              UMETA(DisplayName = "Persistent"),
    Temporary               UMETA(DisplayName = "Temporary")
};

// Spatial query types
UENUM(BlueprintType)
enum class EAuracronSpatialQueryType : uint8
{
    Point                   UMETA(DisplayName = "Point"),
    Sphere                  UMETA(DisplayName = "Sphere"),
    Box                     UMETA(DisplayName = "Box"),
    Cylinder                UMETA(DisplayName = "Cylinder"),
    Cone                    UMETA(DisplayName = "Cone"),
    Frustum                 UMETA(DisplayName = "Frustum")
};

// Actor lifecycle states
UENUM(BlueprintType)
enum class EAuracronActorLifecycleState : uint8
{
    Created                 UMETA(DisplayName = "Created"),
    Initialized             UMETA(DisplayName = "Initialized"),
    Active                  UMETA(DisplayName = "Active"),
    Inactive                UMETA(DisplayName = "Inactive"),
    Destroying              UMETA(DisplayName = "Destroying"),
    Destroyed               UMETA(DisplayName = "Destroyed")
};

// =============================================================================
// ACTOR MANAGEMENT CONFIGURATION
// =============================================================================

/**
 * Actor Management Configuration
 * Configuration settings for actor management in world partition
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronActorManagementConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Management")
    bool bEnableActorManagement = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Management")
    bool bEnableActorStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Management")
    bool bEnableSpatialQueries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxActorsPerCell = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentActorOperations = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ActorStreamingDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ActorUnloadingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MaxActorMemoryUsageMB = 1024.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableActorPooling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    float SpatialQueryRadius = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spatial")
    int32 MaxSpatialQueryResults = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableActorDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogActorOperations = false;

    FAuracronActorManagementConfiguration()
    {
        bEnableActorManagement = true;
        bEnableActorStreaming = true;
        bEnableSpatialQueries = true;
        MaxActorsPerCell = 1000;
        MaxConcurrentActorOperations = 8;
        ActorStreamingDistance = 10000.0f;
        ActorUnloadingDistance = 15000.0f;
        MaxActorMemoryUsageMB = 1024.0f;
        bEnableActorPooling = true;
        SpatialQueryRadius = 5000.0f;
        MaxSpatialQueryResults = 1000;
        bEnableActorDebug = false;
        bLogActorOperations = false;
    }
};

// =============================================================================
// ACTOR DESCRIPTOR
// =============================================================================

/**
 * Actor Descriptor
 * Descriptor for actors in world partition system
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronActorDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FString ActorId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FString ActorName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FString ActorClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FBox Bounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    EAuracronActorPlacementType PlacementType = EAuracronActorPlacementType::Static;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    EAuracronActorStreamingState StreamingState = EAuracronActorStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    EAuracronActorLifecycleState LifecycleState = EAuracronActorLifecycleState::Created;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    TArray<FString> DataLayers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    TArray<FString> Tags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    TMap<FString, FString> Properties;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    bool bIsSpatiallyLoaded = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    bool bIsRuntimeOnly = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Actor Descriptor")
    FDateTime LastAccessTime;

    FAuracronActorDescriptor()
    {
        Location = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Scale = FVector::OneVector;
        PlacementType = EAuracronActorPlacementType::Static;
        StreamingState = EAuracronActorStreamingState::Unloaded;
        LifecycleState = EAuracronActorLifecycleState::Created;
        bIsSpatiallyLoaded = true;
        bIsRuntimeOnly = false;
        MemoryUsageMB = 0.0f;
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
    }
};

// =============================================================================
// SPATIAL QUERY PARAMETERS
// =============================================================================

/**
 * Spatial Query Parameters
 * Parameters for spatial queries of actors
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronSpatialQueryParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    EAuracronSpatialQueryType QueryType = EAuracronSpatialQueryType::Sphere;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FBox QueryBox = FBox(FVector(-500), FVector(500));

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryDirection = FVector::ForwardVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryAngle = 45.0f; // For cone queries

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    TArray<FString> FilterClasses;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    TArray<FString> FilterTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bIncludeUnloadedActors = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 1000;

    FAuracronSpatialQueryParameters()
    {
        QueryType = EAuracronSpatialQueryType::Sphere;
        QueryLocation = FVector::ZeroVector;
        QueryRadius = 1000.0f;
        QueryBox = FBox(FVector(-500), FVector(500));
        QueryDirection = FVector::ForwardVector;
        QueryDistance = 10000.0f;
        QueryAngle = 45.0f;
        bIncludeUnloadedActors = false;
        MaxResults = 1000;
    }
};

// =============================================================================
// SPATIAL QUERY RESULT
// =============================================================================

/**
 * Spatial Query Result
 * Result of a spatial query operation
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronSpatialQueryResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FAuracronActorDescriptor> FoundActors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> ActorIds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 TotalResults = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float QueryTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    bool bQuerySuccessful = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString ErrorMessage;

    FAuracronSpatialQueryResult()
    {
        TotalResults = 0;
        QueryTime = 0.0f;
        bQuerySuccessful = false;
    }
};

// =============================================================================
// ACTOR STATISTICS
// =============================================================================

/**
 * Actor Statistics
 * Performance and usage statistics for actor management
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronActorStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 ActiveActors = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageActorLoadTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 SpatialQueries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageQueryTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float ActorEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronActorStatistics()
    {
        TotalActors = 0;
        LoadedActors = 0;
        StreamingActors = 0;
        ActiveActors = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageActorLoadTime = 0.0f;
        SpatialQueries = 0;
        AverageQueryTime = 0.0f;
        FailedOperations = 0;
        ActorEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION ACTOR MANAGER
// =============================================================================

/**
 * World Partition Actor Manager
 * Central manager for actor management in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionActorManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    static UAuracronWorldPartitionActorManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void Initialize(const FAuracronActorManagementConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void Tick(float DeltaTime);

    // Actor placement and creation
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FString PlaceActor(const FString& ActorClass, const FVector& Location, const FRotator& Rotation = FRotator::ZeroRotator, EAuracronActorPlacementType PlacementType = EAuracronActorPlacementType::Static);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool RemoveActor(const FString& ActorId);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool MoveActor(const FString& ActorId, const FVector& NewLocation);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool SetActorRotation(const FString& ActorId, const FRotator& NewRotation);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool SetActorScale(const FString& ActorId, const FVector& NewScale);

    // Actor queries and information
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FAuracronActorDescriptor GetActorDescriptor(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FAuracronActorDescriptor> GetAllActors() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetActorIds() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool DoesActorExist(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    AActor* GetActorReference(const FString& ActorId) const;

    // Actor streaming
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool LoadActor(const FString& ActorId);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool UnloadActor(const FString& ActorId);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    EAuracronActorStreamingState GetActorStreamingState(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetLoadedActors() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetStreamingActors() const;

    // Spatial queries
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FAuracronSpatialQueryResult ExecuteSpatialQuery(const FAuracronSpatialQueryParameters& QueryParams) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FAuracronActorDescriptor> GetActorsInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FAuracronActorDescriptor> GetActorsInBox(const FBox& Box) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FAuracronActorDescriptor> GetActorsByClass(const FString& ActorClass) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FAuracronActorDescriptor> GetActorsByTag(const FString& Tag) const;

    // Cell management
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetActorsInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FString GetActorCell(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool MoveActorToCell(const FString& ActorId, const FString& CellId);

    // Cross-cell references
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool AddActorReference(const FString& ActorId, const FString& ReferencedActorId);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool RemoveActorReference(const FString& ActorId, const FString& ReferencedActorId);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetActorReferences(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    TArray<FString> GetActorReferencedBy(const FString& ActorId) const;

    // Actor lifecycle
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool SetActorLifecycleState(const FString& ActorId, EAuracronActorLifecycleState NewState);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    EAuracronActorLifecycleState GetActorLifecycleState(const FString& ActorId) const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void UpdateActorLifecycles();

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void SetConfiguration(const FAuracronActorManagementConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FAuracronActorManagementConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    FAuracronActorStatistics GetActorStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    int32 GetTotalActorCount() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    int32 GetLoadedActorCount() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void EnableActorDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    bool IsActorDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void LogActorState() const;

    UFUNCTION(BlueprintCallable, Category = "Actor Manager")
    void DrawDebugActorInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorPlaced, FString, ActorId, FVector, Location);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnActorRemoved, FString, ActorId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorMoved, FString, ActorId, FVector, NewLocation);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorStreamingStateChanged, FString, ActorId, EAuracronActorStreamingState, NewState);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnActorPlaced OnActorPlaced;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnActorRemoved OnActorRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnActorMoved OnActorMoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnActorStreamingStateChanged OnActorStreamingStateChanged;

private:
    static UAuracronWorldPartitionActorManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronActorManagementConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Actor data
    TMap<FString, FAuracronActorDescriptor> ActorDescriptors;
    TMap<FString, TWeakObjectPtr<AActor>> ActorReferences;
    TMap<FString, TSet<FString>> ActorCrossReferences; // ActorId -> Referenced ActorIds
    TMap<FString, TSet<FString>> ActorReferencedBy; // ActorId -> Actors that reference this

    // Cell mapping
    TMap<FString, FString> ActorToCellMap; // ActorId -> CellId
    TMap<FString, TSet<FString>> CellToActorsMap; // CellId -> ActorIds

    // Statistics
    mutable FAuracronActorStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection ActorLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateActorId(const FString& ActorClass) const;
    bool ValidateActorId(const FString& ActorId) const;
    void OnActorPlacedInternal(const FString& ActorId, const FVector& Location);
    void OnActorRemovedInternal(const FString& ActorId);
    void OnActorMovedInternal(const FString& ActorId, const FVector& NewLocation);
    void ValidateConfiguration();
    void UpdateActorCellMapping(const FString& ActorId);
    bool IsActorInQueryRange(const FAuracronActorDescriptor& Actor, const FAuracronSpatialQueryParameters& QueryParams) const;

    // Additional helper functions (const removed to match implementation)
    FVector CalculateActorBounds(UClass* ActorClass, const FVector& Location);
    bool IsActorInFrustum(const FAuracronActorDescriptor& Actor, const FAuracronSpatialQueryParams& QueryParams) const;
};
