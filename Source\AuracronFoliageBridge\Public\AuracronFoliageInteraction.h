// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Interaction System Header
// Bridge 4.9: Foliage - Interaction System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageLOD.h"
#include "AuracronFoliageWind.h"
#include "AuracronFoliageCollision.h"
#include "AuracronFoliageSeasonal.h"

// UE5.6 Interaction includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "InteractiveFoliageActor.h"

// Physics includes
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Player interaction includes
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Components/CapsuleComponent.h"
#include "Components/SphereComponent.h"

// Animation includes
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "Animation/AnimSequence.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math and Utilities
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Misc/DateTime.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

#include "AuracronFoliageInteraction.generated.h"

// Forward declarations
class UAuracronFoliageInteractionManager;
class UAuracronFoliageCollisionManager;

// =============================================================================
// INTERACTION TYPES AND ENUMS
// =============================================================================

// Player interaction types
UENUM(BlueprintType)
enum class EAuracronPlayerInteractionType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Touch                   UMETA(DisplayName = "Touch"),
    Push                    UMETA(DisplayName = "Push"),
    Walk                    UMETA(DisplayName = "Walk Through"),
    Run                     UMETA(DisplayName = "Run Through"),
    Jump                    UMETA(DisplayName = "Jump On"),
    Crouch                  UMETA(DisplayName = "Crouch Near"),
    Attack                  UMETA(DisplayName = "Attack"),
    Harvest                 UMETA(DisplayName = "Harvest"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Foliage interaction types
UENUM(BlueprintType)
enum class EAuracronFoliageInteractionType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Bend                    UMETA(DisplayName = "Bend"),
    Sway                    UMETA(DisplayName = "Sway"),
    Trample                 UMETA(DisplayName = "Trample"),
    Burn                    UMETA(DisplayName = "Burn"),
    Freeze                  UMETA(DisplayName = "Freeze"),
    Dissolve                UMETA(DisplayName = "Dissolve"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Foliage bending types
UENUM(BlueprintType)
enum class EAuracronFoliageBendingType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Gentle                  UMETA(DisplayName = "Gentle Bend"),
    Moderate                UMETA(DisplayName = "Moderate Bend"),
    Strong                  UMETA(DisplayName = "Strong Bend"),
    Extreme                 UMETA(DisplayName = "Extreme Bend"),
    Directional             UMETA(DisplayName = "Directional Bend"),
    Radial                  UMETA(DisplayName = "Radial Bend"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Recovery simulation types
UENUM(BlueprintType)
enum class EAuracronRecoverySimulationType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Instant                 UMETA(DisplayName = "Instant Recovery"),
    Linear                  UMETA(DisplayName = "Linear Recovery"),
    Exponential             UMETA(DisplayName = "Exponential Recovery"),
    Spring                  UMETA(DisplayName = "Spring Recovery"),
    Elastic                 UMETA(DisplayName = "Elastic Recovery"),
    Damped                  UMETA(DisplayName = "Damped Recovery"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Interaction response types
UENUM(BlueprintType)
enum class EAuracronInteractionResponseType : uint8
{
    None                    UMETA(DisplayName = "None"),
    Visual                  UMETA(DisplayName = "Visual Only"),
    Physics                 UMETA(DisplayName = "Physics Response"),
    Audio                   UMETA(DisplayName = "Audio Response"),
    Particle                UMETA(DisplayName = "Particle Effects"),
    Combined                UMETA(DisplayName = "Combined Response"),
    Gameplay                UMETA(DisplayName = "Gameplay Effect"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// INTERACTION CONFIGURATION DATA
// =============================================================================

/**
 * Foliage Interaction Configuration
 * Configuration for foliage interaction system behavior
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageInteractionConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction System")
    bool bEnableInteractionSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction System")
    EAuracronPlayerInteractionType DefaultInteractionType = EAuracronPlayerInteractionType::Walk;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction System")
    EAuracronInteractionResponseType DefaultResponseType = EAuracronInteractionResponseType::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionForce = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionSensitivity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    bool bEnableFoliageBending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    EAuracronFoliageBendingType DefaultBendingType = EAuracronFoliageBendingType::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendingStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendingRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float MaxBendAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    bool bEnableRecoverySimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    EAuracronRecoverySimulationType DefaultRecoveryType = EAuracronRecoverySimulationType::Spring;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoveryTime = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoverySpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoveryDamping = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling Effects")
    bool bEnableAdvancedTrampling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling Effects")
    float TramplingThreshold = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Trampling Effects")
    float TramplingDuration = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncInteractionUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxInteractionUpdatesPerFrame = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float InteractionUpdateInterval = 0.05f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxInteractionDistance = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnableInteractionAudio = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    float AudioVolumeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    float AudioPitchVariation = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    bool bEnableParticleEffects = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    float ParticleIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    float ParticleLifetime = 2.0f;

    FAuracronFoliageInteractionConfiguration()
    {
        bEnableInteractionSystem = true;
        DefaultInteractionType = EAuracronPlayerInteractionType::Walk;
        DefaultResponseType = EAuracronInteractionResponseType::Combined;
        InteractionRadius = 150.0f;
        InteractionForce = 100.0f;
        InteractionSensitivity = 1.0f;
        bEnableFoliageBending = true;
        DefaultBendingType = EAuracronFoliageBendingType::Moderate;
        BendingStrength = 1.0f;
        BendingRadius = 200.0f;
        MaxBendAngle = 45.0f;
        bEnableRecoverySimulation = true;
        DefaultRecoveryType = EAuracronRecoverySimulationType::Spring;
        RecoveryTime = 3.0f;
        RecoverySpeed = 1.0f;
        RecoveryDamping = 0.8f;
        bEnableAdvancedTrampling = true;
        TramplingThreshold = 50.0f;
        TramplingDuration = 10.0f;
        bEnableAsyncInteractionUpdates = true;
        MaxInteractionUpdatesPerFrame = 100;
        InteractionUpdateInterval = 0.05f;
        MaxInteractionDistance = 2000.0f;
        bEnableInteractionAudio = true;
        AudioVolumeMultiplier = 1.0f;
        AudioPitchVariation = 0.2f;
        bEnableParticleEffects = true;
        ParticleIntensity = 1.0f;
        ParticleLifetime = 2.0f;
    }
};

// =============================================================================
// PLAYER INTERACTION DATA
// =============================================================================

/**
 * Player Interaction Data
 * Data for player interactions with foliage
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronPlayerInteractionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    FString InteractionId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    TWeakObjectPtr<APawn> InteractingPlayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    EAuracronPlayerInteractionType InteractionType = EAuracronPlayerInteractionType::Walk;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    FVector InteractionLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    FVector InteractionDirection = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionForce = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float PlayerVelocity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float PlayerMass = 80.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Affected Foliage")
    TArray<FString> AffectedFoliageInstances;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime InteractionStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float InteractionDuration = 0.0f;

    // Additional fields for implementation compatibility
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    float InteractionStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Interaction")
    FVector PlayerLocation = FVector::ZeroVector;

    FAuracronPlayerInteractionData()
    {
        InteractionType = EAuracronPlayerInteractionType::Walk;
        InteractionLocation = FVector::ZeroVector;
        InteractionDirection = FVector::ZeroVector;
        InteractionForce = 100.0f;
        InteractionRadius = 150.0f;
        InteractionIntensity = 1.0f;
        PlayerVelocity = 0.0f;
        PlayerMass = 80.0f;
        bIsActive = false;
        InteractionStartTime = FDateTime::Now();
        InteractionDuration = 0.0f;
    }
};

// =============================================================================
// FOLIAGE INTERACTION DATA
// =============================================================================

/**
 * Foliage Interaction Data
 * Data for individual foliage instance interactions
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageInteractionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Interaction")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Interaction")
    EAuracronFoliageInteractionType InteractionType = EAuracronFoliageInteractionType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Interaction")
    float InteractionStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Interaction")
    FVector InteractionLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime StartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime LastInteractionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    int32 TotalInteractions = 0;

    FAuracronFoliageInteractionData()
    {
        InteractionType = EAuracronFoliageInteractionType::None;
        InteractionStrength = 1.0f;
        InteractionLocation = FVector::ZeroVector;
        bIsActive = false;
        StartTime = FDateTime::Now();
        LastInteractionTime = FDateTime::Now();
        TotalInteractions = 0;
    }
};

// =============================================================================
// FOLIAGE BENDING DATA
// =============================================================================

/**
 * Foliage Bending Data
 * Data for foliage bending effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageBendingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    FString BendingId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    FString FoliageInstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    EAuracronFoliageBendingType BendingType = EAuracronFoliageBendingType::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    FVector BendingDirection = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendingAngle = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float MaxBendingAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendingStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendingSpeed = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Original State")
    FTransform OriginalTransform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Original State")
    FRotator OriginalRotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    FTransform CurrentTransform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    float CurrentBendProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    bool bIsRecovering = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery")
    float RecoveryProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime BendingStartTime;

    // Additional fields for implementation compatibility
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    FVector BendDirection = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendAngle = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Bending")
    float BendSpeed = 2.0f;

    FAuracronFoliageBendingData()
    {
        BendingType = EAuracronFoliageBendingType::Moderate;
        BendingDirection = FVector::ZeroVector;
        BendingAngle = 0.0f;
        MaxBendingAngle = 45.0f;
        BendingStrength = 1.0f;
        BendingSpeed = 2.0f;
        OriginalTransform = FTransform::Identity;
        OriginalRotation = FRotator::ZeroRotator;
        CurrentTransform = FTransform::Identity;
        CurrentBendProgress = 0.0f;
        bIsRecovering = false;
        RecoveryProgress = 0.0f;
        bIsActive = false;
        BendingStartTime = FDateTime::Now();
    }
};

// =============================================================================
// RECOVERY SIMULATION DATA
// =============================================================================

/**
 * Recovery Simulation Data
 * Data for foliage recovery simulation
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronRecoverySimulationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    FString RecoveryId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    FString FoliageInstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    EAuracronRecoverySimulationType RecoveryType = EAuracronRecoverySimulationType::Spring;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoveryTime = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoverySpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float RecoveryDamping = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float SpringConstant = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery Simulation")
    float ElasticityFactor = 0.9f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target State")
    FTransform TargetTransform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target State")
    FRotator TargetRotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    FTransform CurrentTransform;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    float RecoveryProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    FVector Velocity = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Current State")
    FVector Acceleration = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsRecovering = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime RecoveryStartTime;

    FAuracronRecoverySimulationData()
    {
        RecoveryType = EAuracronRecoverySimulationType::Spring;
        RecoveryTime = 3.0f;
        RecoverySpeed = 1.0f;
        RecoveryDamping = 0.8f;
        SpringConstant = 5.0f;
        ElasticityFactor = 0.9f;
        TargetTransform = FTransform::Identity;
        TargetRotation = FRotator::ZeroRotator;
        CurrentTransform = FTransform::Identity;
        RecoveryProgress = 0.0f;
        Velocity = FVector::ZeroVector;
        Acceleration = FVector::ZeroVector;
        bIsRecovering = false;
        RecoveryStartTime = FDateTime::Now();
    }
};

// =============================================================================
// ADDITIONAL INTERACTION DATA STRUCTURES
// =============================================================================

/**
 * Foliage Swaying Data
 * Data for foliage swaying effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageSwayingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Swaying")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Swaying")
    FVector SwayDirection = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Swaying")
    float SwayAmplitude = 20.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Swaying")
    float SwayFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    FAuracronFoliageSwayingData()
    {
        SwayDirection = FVector::ZeroVector;
        SwayAmplitude = 20.0f;
        SwayFrequency = 1.0f;
        bIsActive = false;
    }
};

/**
 * Foliage Trampling Data
 * Data for foliage trampling effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageTramplingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Trampling")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Trampling")
    float TramplingForce = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Trampling")
    FVector TramplingLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    FAuracronFoliageTramplingData()
    {
        TramplingForce = 100.0f;
        TramplingLocation = FVector::ZeroVector;
        bIsActive = false;
    }
};

/**
 * Foliage Burning Data
 * Data for foliage burning effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageBurningData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Burning")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Burning")
    float BurnIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Burning")
    float BurnDuration = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    FAuracronFoliageBurningData()
    {
        BurnIntensity = 1.0f;
        BurnDuration = 10.0f;
        bIsActive = false;
    }
};

/**
 * Foliage Freezing Data
 * Data for foliage freezing effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageFreezingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Freezing")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Freezing")
    float FreezeIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Freezing")
    float FreezeDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    FAuracronFoliageFreezingData()
    {
        FreezeIntensity = 1.0f;
        FreezeDuration = 5.0f;
        bIsActive = false;
    }
};

/**
 * Foliage Dissolve Data
 * Data for foliage dissolve effects
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageDissolveData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Dissolve")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Foliage Dissolve")
    float DissolveSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = false;

    FAuracronFoliageDissolveData()
    {
        DissolveSpeed = 1.0f;
        bIsActive = false;
    }
};

// =============================================================================
// INTERACTION PERFORMANCE DATA
// =============================================================================

/**
 * Interaction Performance Data
 * Performance metrics for interaction system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronInteractionPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalInteractions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveInteractions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 BendingInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 RecoveringInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float InteractionUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float BendingUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float RecoveryUpdateTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 PlayerInteractions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronInteractionPerformanceData()
    {
        TotalInteractions = 0;
        ActiveInteractions = 0;
        BendingInstances = 0;
        RecoveringInstances = 0;
        InteractionUpdateTime = 0.0f;
        BendingUpdateTime = 0.0f;
        RecoveryUpdateTime = 0.0f;
        PlayerInteractions = 0;
        MemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// FOLIAGE INTERACTION MANAGER
// =============================================================================

/**
 * Foliage Interaction Manager
 * Manager for the foliage interaction system including player interaction, bending, and recovery simulation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageInteractionManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    static UAuracronFoliageInteractionManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void Initialize(const FAuracronFoliageInteractionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void SetConfiguration(const FAuracronFoliageInteractionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FAuracronFoliageInteractionConfiguration GetConfiguration() const;

    // Player interaction
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FString CreatePlayerInteraction(APawn* Player, const FVector& Location, EAuracronPlayerInteractionType InteractionType);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool UpdatePlayerInteraction(const FString& InteractionId, const FAuracronPlayerInteractionData& InteractionData);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool RemovePlayerInteraction(const FString& InteractionId);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FAuracronPlayerInteractionData GetPlayerInteraction(const FString& InteractionId) const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    TArray<FAuracronPlayerInteractionData> GetAllPlayerInteractions() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void ProcessPlayerMovement(APawn* Player, const FVector& OldLocation, const FVector& NewLocation);

    // Foliage bending
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FString CreateFoliageBending(const FString& FoliageInstanceId, const FVector& BendDirection, float BendStrength);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool UpdateFoliageBending(const FString& BendingId, const FAuracronFoliageBendingData& BendingData);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool RemoveFoliageBending(const FString& BendingId);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FAuracronFoliageBendingData GetFoliageBending(const FString& BendingId) const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    TArray<FAuracronFoliageBendingData> GetAllFoliageBending() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void ApplyBendingToFoliageInstance(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronFoliageBendingData& BendingData);

    // Recovery simulation
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FString CreateRecoverySimulation(const FString& FoliageInstanceId, EAuracronRecoverySimulationType RecoveryType);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool UpdateRecoverySimulation(const FString& RecoveryId, const FAuracronRecoverySimulationData& RecoveryData);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool RemoveRecoverySimulation(const FString& RecoveryId);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FAuracronRecoverySimulationData GetRecoverySimulation(const FString& RecoveryId) const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    TArray<FAuracronRecoverySimulationData> GetAllRecoverySimulations() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void StartRecoveryForFoliage(const FString& FoliageInstanceId);

    // Advanced trampling effects
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void ApplyAdvancedTrampling(const FVector& Location, float Radius, float Force, APawn* CausingPlayer);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void UpdateAdvancedTramplingEffects(float DeltaTime);

    // Real-time bending
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void UpdateRealTimeBending(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void SetBendingParameters(const FString& FoliageTypeId, float Stiffness, float Damping, float RestoreForce);

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void IntegrateWithCollisionSystem(UAuracronFoliageCollisionManager* CollisionManager);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void SynchronizeWithWindSystem(const FVector& WindDirection, float WindStrength);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void ApplySeasonalInteractionEffects(EAuracronSeasonType Season, float SeasonProgress);

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    FAuracronInteractionPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    int32 GetActiveInteractionCount() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    int32 GetBendingInstanceCount() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void DrawDebugInteractionInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Interaction Manager")
    void LogInteractionStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerInteractionStarted, FString, InteractionId, APawn*, Player);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerInteractionEnded, FString, InteractionId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFoliageBendingStarted, FString, BendingId, FString, FoliageInstanceId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFoliageRecoveryCompleted, FString, RecoveryId);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPlayerInteractionStarted OnPlayerInteractionStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPlayerInteractionEnded OnPlayerInteractionEnded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageBendingStarted OnFoliageBendingStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFoliageRecoveryCompleted OnFoliageRecoveryCompleted;

private:
    static UAuracronFoliageInteractionManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliageInteractionConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Interaction data
    TMap<FString, FAuracronPlayerInteractionData> PlayerInteractions;
    TMap<FString, FAuracronFoliageInteractionData> InteractionData;
    TMap<FString, FAuracronFoliageBendingData> FoliageBending;
    TMap<FString, FAuracronFoliageBendingData> BendingEffects;
    TMap<FString, FAuracronFoliageSwayingData> SwayingEffects;
    TMap<FString, FAuracronFoliageTramplingData> TramplingEffects;
    TMap<FString, FAuracronFoliageBurningData> BurningEffects;
    TMap<FString, FAuracronFoliageFreezingData> FreezingEffects;
    TMap<FString, FAuracronFoliageDissolveData> DissolveEffects;
    TMap<FString, FAuracronRecoverySimulationData> RecoverySimulations;

    // Integration with other systems
    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageCollisionManager> CollisionManager;

    // Performance data
    FAuracronInteractionPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastInteractionUpdate = 0.0f;
    float LastBendingUpdate = 0.0f;
    float LastRecoveryUpdate = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection InteractionLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateInteractionId() const;
    FString GenerateBendingId() const;
    FString GenerateRecoveryId() const;
    void UpdatePlayerInteractionInternal(FAuracronPlayerInteractionData& PlayerInteractionData, float DeltaTime);
    void UpdateFoliageBendingInternal(FAuracronFoliageBendingData& BendingData, float DeltaTime);
    void UpdateRecoverySimulationInternal(FAuracronRecoverySimulationData& RecoveryData, float DeltaTime);
    void UpdatePerformanceDataInternal();
    void ProcessPlayerInteractionWithFoliage(const FAuracronPlayerInteractionData& PlayerInteractionData);
    void CalculateBendingTransform(FAuracronFoliageBendingData& BendingData, float DeltaTime);
    void CalculateRecoveryTransform(FAuracronRecoverySimulationData& RecoveryData, float DeltaTime);
    void ApplyInteractionEffects(const FString& FoliageInstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    TArray<FString> GetFoliageInstancesInRadius(const FVector& Center, float Radius) const;
    float CalculateInteractionForce(const FAuracronPlayerInteractionData& InteractionData, float Distance) const;
    FVector CalculateBendingDirection(const FVector& InteractionLocation, const FVector& FoliageLocation) const;

    // Helper effect application functions
    void ApplyBendingEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    void ApplySwayingEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    void ApplyTramplingEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    void ApplyBurningEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    void ApplyFreezingEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
    void ApplyDissolvingEffect(const FString& InstanceId, const FAuracronPlayerInteractionData& PlayerInteractionData);
};
