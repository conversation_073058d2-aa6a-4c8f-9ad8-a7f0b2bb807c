// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Collision Streaming Header
// Bridge 3.8: World Partition - Collision Streaming

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Collision includes for UE5.6
#include "Components/StaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/StaticMeshActor.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Subsystems/WorldSubsystem.h"
#include "Physics/PhysicsInterfaceCore.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionCollision.generated.h"

// Forward declarations
class UAuracronWorldPartitionCollisionManager;
class UPrimitiveComponent;
class UStaticMeshComponent;
class UBodySetup;

// =============================================================================
// COLLISION TYPES AND ENUMS
// =============================================================================

// Collision streaming states
UENUM(BlueprintType)
enum class EAuracronCollisionStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Collision LOD levels
UENUM(BlueprintType)
enum class EAuracronCollisionLODLevel : uint8
{
    LOD0                    UMETA(DisplayName = "LOD 0 (Highest)"),
    LOD1                    UMETA(DisplayName = "LOD 1"),
    LOD2                    UMETA(DisplayName = "LOD 2"),
    LOD3                    UMETA(DisplayName = "LOD 3"),
    LOD4                    UMETA(DisplayName = "LOD 4 (Lowest)")
};

// Collision types
UENUM(BlueprintType)
enum class EAuracronCollisionType : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Dynamic                 UMETA(DisplayName = "Dynamic"),
    Kinematic               UMETA(DisplayName = "Kinematic"),
    Trigger                 UMETA(DisplayName = "Trigger"),
    Query                   UMETA(DisplayName = "Query Only")
};

// Collision complexity
UENUM(BlueprintType)
enum class EAuracronCollisionComplexity : uint8
{
    Simple                  UMETA(DisplayName = "Simple"),
    Complex                 UMETA(DisplayName = "Complex"),
    UseDefault              UMETA(DisplayName = "Use Default")
};

// =============================================================================
// COLLISION CONFIGURATION
// =============================================================================

/**
 * Collision Configuration
 * Configuration settings for collision streaming in world partition
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronCollisionConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bEnableCollisionStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bEnableCollisionLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bEnableAsyncCollisionLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float CollisionStreamingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float CollisionUnloadingDistance = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 MaxConcurrentCollisionOperations = 6;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float BaseLODDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LODDistanceMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevel = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxCollisionMemoryUsageMB = 1024.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCollisionCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCollisionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronCollisionComplexity DefaultCollisionComplexity = EAuracronCollisionComplexity::UseDefault;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bGenerateSimpleCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bGenerateComplexCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableCollisionDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogCollisionOperations = false;

    FAuracronCollisionConfiguration()
    {
        bEnableCollisionStreaming = true;
        bEnableCollisionLOD = true;
        bEnableAsyncCollisionLoading = true;
        CollisionStreamingDistance = 15000.0f;
        CollisionUnloadingDistance = 20000.0f;
        MaxConcurrentCollisionOperations = 6;
        BaseLODDistance = 1000.0f;
        LODDistanceMultiplier = 2.0f;
        MaxLODLevel = 4;
        MaxCollisionMemoryUsageMB = 1024.0f;
        bEnableCollisionCaching = true;
        bEnableCollisionCulling = true;
        DefaultCollisionComplexity = EAuracronCollisionComplexity::UseDefault;
        bGenerateSimpleCollision = true;
        bGenerateComplexCollision = false;
        bEnableCollisionDebug = false;
        bLogCollisionOperations = false;
    }
};

// =============================================================================
// COLLISION DESCRIPTOR
// =============================================================================

/**
 * Collision Descriptor
 * Descriptor for collision objects and their properties
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronCollisionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FString CollisionId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FString CollisionName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FString SourceActorId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    EAuracronCollisionType CollisionType = EAuracronCollisionType::Static;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    EAuracronCollisionComplexity CollisionComplexity = EAuracronCollisionComplexity::UseDefault;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    EAuracronCollisionStreamingState StreamingState = EAuracronCollisionStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    EAuracronCollisionLODLevel CurrentLODLevel = EAuracronCollisionLODLevel::LOD0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FBox Bounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    int32 TriangleCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    int32 ConvexCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    bool bIsEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    bool bIsVisible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Descriptor")
    FDateTime LastAccessTime;

    FAuracronCollisionDescriptor()
    {
        CollisionType = EAuracronCollisionType::Static;
        CollisionComplexity = EAuracronCollisionComplexity::UseDefault;
        StreamingState = EAuracronCollisionStreamingState::Unloaded;
        CurrentLODLevel = EAuracronCollisionLODLevel::LOD0;
        Location = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Scale = FVector::OneVector;
        TriangleCount = 0;
        ConvexCount = 0;
        MemoryUsageMB = 0.0f;
        bIsEnabled = true;
        bIsVisible = true;
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
    }
};

// =============================================================================
// COLLISION QUERY PARAMETERS
// =============================================================================

/**
 * Collision Query Parameters
 * Parameters for spatial collision queries
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronCollisionQueryParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FVector QueryExtent = FVector(100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    FRotator QueryRotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    TArray<EAuracronCollisionType> FilterTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bIncludeDisabled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    bool bIncludeInvisible = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 1000;

    FAuracronCollisionQueryParameters()
    {
        QueryLocation = FVector::ZeroVector;
        QueryExtent = FVector(100.0f);
        QueryRotation = FRotator::ZeroRotator;
        bIncludeDisabled = false;
        bIncludeInvisible = false;
        MaxResults = 1000;
    }
};

// =============================================================================
// COLLISION STATISTICS
// =============================================================================

/**
 * Collision Statistics
 * Performance and usage statistics for collision system
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronCollisionStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalCollisionObjects = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedCollisionObjects = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingCollisionObjects = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 CollisionQueries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageQueryTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LODTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float CollisionEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronCollisionStatistics()
    {
        TotalCollisionObjects = 0;
        LoadedCollisionObjects = 0;
        StreamingCollisionObjects = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        CollisionQueries = 0;
        AverageQueryTime = 0.0f;
        LODTransitions = 0;
        FailedOperations = 0;
        CollisionEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION COLLISION MANAGER
// =============================================================================

/**
 * World Partition Collision Manager
 * Central manager for collision streaming in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionCollisionManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    static UAuracronWorldPartitionCollisionManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Initialize(const FAuracronCollisionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void Tick(float DeltaTime);

    // Collision creation and management
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FString CreateCollisionObject(const FString& SourceActorId, EAuracronCollisionType CollisionType = EAuracronCollisionType::Static);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool RemoveCollisionObject(const FString& CollisionId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronCollisionDescriptor GetCollisionDescriptor(const FString& CollisionId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionDescriptor> GetAllCollisionObjects() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetCollisionIds() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool DoesCollisionObjectExist(const FString& CollisionId) const;

    // Collision streaming
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool LoadCollisionObject(const FString& CollisionId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool UnloadCollisionObject(const FString& CollisionId);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    EAuracronCollisionStreamingState GetCollisionStreamingState(const FString& CollisionId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetLoadedCollisionObjects() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetStreamingCollisionObjects() const;

    // Collision LOD
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool SetCollisionLOD(const FString& CollisionId, EAuracronCollisionLODLevel LODLevel);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    EAuracronCollisionLODLevel GetCollisionLOD(const FString& CollisionId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void UpdateDistanceBasedLODs(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    EAuracronCollisionLODLevel CalculateLODForDistance(float Distance) const;

    // Collision queries
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionDescriptor> ExecuteCollisionQuery(const FAuracronCollisionQueryParameters& QueryParams) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionDescriptor> GetCollisionObjectsInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionDescriptor> GetCollisionObjectsInBox(const FBox& Box) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FAuracronCollisionDescriptor> GetCollisionObjectsByType(EAuracronCollisionType CollisionType) const;

    // Collision state management
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool EnableCollisionObject(const FString& CollisionId, bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsCollisionObjectEnabled(const FString& CollisionId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool SetCollisionObjectVisibility(const FString& CollisionId, bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsCollisionObjectVisible(const FString& CollisionId) const;

    // Cell integration
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    TArray<FString> GetCollisionObjectsInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FString GetCollisionObjectCell(const FString& CollisionId) const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool MoveCollisionObjectToCell(const FString& CollisionId, const FString& CellId);

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void SetConfiguration(const FAuracronCollisionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronCollisionConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    FAuracronCollisionStatistics GetCollisionStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    int32 GetTotalCollisionObjectCount() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    int32 GetLoadedCollisionObjectCount() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void EnableCollisionDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    bool IsCollisionDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void LogCollisionState() const;

    UFUNCTION(BlueprintCallable, Category = "Collision Manager")
    void DrawDebugCollisionInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCollisionLoaded, FString, CollisionId, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCollisionUnloaded, FString, CollisionId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCollisionLODChanged, FString, CollisionId, EAuracronCollisionLODLevel, NewLOD);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCollisionStateChanged, FString, CollisionId, bool, bEnabled);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCollisionLoaded OnCollisionLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCollisionUnloaded OnCollisionUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCollisionLODChanged OnCollisionLODChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCollisionStateChanged OnCollisionStateChanged;

private:
    static UAuracronWorldPartitionCollisionManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronCollisionConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Collision data
    TMap<FString, FAuracronCollisionDescriptor> CollisionDescriptors;
    TMap<FString, TWeakObjectPtr<UPrimitiveComponent>> CollisionComponents;
    TMap<FString, FString> CollisionToCellMap; // CollisionId -> CellId
    TMap<FString, TSet<FString>> CellToCollisionMap; // CellId -> CollisionIds

    // Statistics
    mutable FAuracronCollisionStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection CollisionLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateCollisionId(const FString& SourceActorId) const;
    bool ValidateCollisionId(const FString& CollisionId) const;
    void OnCollisionLoadedInternal(const FString& CollisionId, bool bSuccess);
    void OnCollisionUnloadedInternal(const FString& CollisionId);
    void ValidateConfiguration();
    void UpdateCollisionCellMapping(const FString& CollisionId);
    bool CreateCollisionMesh(const FString& CollisionId, const FString& SourceActorId);
    bool IsCollisionInQueryRange(const FAuracronCollisionDescriptor& Collision, const FAuracronCollisionQueryParameters& QueryParams) const;
    FVector CalculateRealCollisionBounds(const FString& ActorId, const FVector& Location);
    UWorld* GetWorld() const;
};
