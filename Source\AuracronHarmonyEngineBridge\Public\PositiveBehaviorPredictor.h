#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "AuracronHarmonyEngineBridge.h"
#include "PositiveBehaviorPredictor.generated.h"

/**
 * Positive Behavior Predictor
 * AI system that identifies players likely to exhibit positive behavior and amplifies rewards
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UPositiveBehaviorPredictor : public UObject
{
    GENERATED_BODY()

public:
    UPositiveBehaviorPredictor();

    // Core Prediction Functions
    UFUNCTION(BlueprintCallable, Category = "Positive Behavior Prediction")
    float PredictPositiveBehaviorProbability(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Positive Behavior Prediction")
    bool IsPlayerLikelyToBePositive(const FString& PlayerID, float Threshold = 0.7f);

    UFUNCTION(BlueprintCallable, Category = "Positive Behavior Prediction")
    TArray<FString> IdentifyPotentialMentors();

    UFUNCTION(BlueprintCallable, Category = "Positive Behavior Prediction")
    TArray<FString> IdentifyPotentialCommunityLeaders();

    UFUNCTION(BlueprintCallable, Category = "Positive Behavior Prediction")
    float CalculateLeadershipPotential(const FString& PlayerID);

    // Machine Learning Functions
    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    void AddTrainingData(const FPlayerBehaviorSnapshot& BehaviorData);

    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    void TrainPredictionModel();

    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    float GetModelAccuracy() const;

    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    void ResetTrainingData();

    // Behavior Analysis
    UFUNCTION(BlueprintCallable, Category = "Behavior Analysis")
    TArray<FGameplayTag> AnalyzePositiveBehaviorPatterns(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Behavior Analysis")
    float CalculateConsistencyScore(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Behavior Analysis")
    bool HasPositiveTrend(const FString& PlayerID, float TimeWindow = 300.0f);

    // Reward Amplification
    UFUNCTION(BlueprintCallable, Category = "Reward Amplification")
    float CalculateRewardMultiplier(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Reward Amplification")
    bool ShouldAmplifyRewards(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Reward Amplification")
    int32 CalculateBonusKindnessPoints(const FString& PlayerID, int32 BasePoints);

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    float PositiveBehaviorThreshold;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    float LeadershipThreshold;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    float ConsistencyRequirement;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    int32 MinimumDataPointsForPrediction;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    float RewardAmplificationFactor;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Prediction Config")
    bool bEnableRewardAmplification;

    // ML Model Data
    UPROPERTY()
    TArray<FPlayerBehaviorSnapshot> TrainingDataset;

    UPROPERTY()
    TMap<FString, float> PlayerPositivityScores;

    UPROPERTY()
    TMap<FString, float> PlayerConsistencyScores;

    UPROPERTY()
    TMap<FString, float> PlayerLeadershipScores;

    UPROPERTY()
    float CurrentModelAccuracy;

    UPROPERTY()
    int32 TrainingIterations;

    UPROPERTY()
    bool bModelTrained;

private:
    // Prediction algorithms
    float CalculatePositivityTrend(const FString& PlayerID);
    float AnalyzeBehaviorConsistency(const FString& PlayerID);
    float PredictFutureBehavior(const FString& PlayerID, float TimeHorizon);
    
    // Pattern recognition
    bool DetectMentorshipPatterns(const FString& PlayerID);
    bool DetectLeadershipPatterns(const FString& PlayerID);
    bool DetectHelpingBehavior(const FString& PlayerID);
    
    // ML model functions
    void UpdateModelWeights();
    void ValidateModelPerformance();
    float CalculatePredictionError(const FPlayerBehaviorSnapshot& Actual, const FPlayerBehaviorSnapshot& Predicted);
    
    // Data management
    void CleanupOldTrainingData();
    void NormalizeTrainingData();
    void BalanceTrainingDataset();
    
    // Utility functions
    float CalculateWeightedAverage(const TArray<float>& Values, const TArray<float>& Weights);
    float ApplySigmoidFunction(float Input);
    float CalculateConfidenceInterval(float Prediction);
    
    // Configuration validation
    void ValidateAndFixConfiguration();
    void LogConfigurationStatus();
};
