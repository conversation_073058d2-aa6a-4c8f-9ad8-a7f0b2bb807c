#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "GameplayEffect.h"
#include "GameplayAbilitySpec.h"
#include "Subsystems/WorldSubsystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyRewardsSystem.generated.h"

class UAbilitySystemComponent;
class APlayerController;

UENUM(BlueprintType)
enum class ERewardTier : uint8
{
    Bronze              UMETA(DisplayName = "Bronze"),
    Silver              UMETA(DisplayName = "Silver"),
    Gold                UMETA(DisplayName = "Gold"),
    Platinum            UMETA(DisplayName = "Platinum"),
    Diamond             UMETA(DisplayName = "Diamond"),
    Legendary           UMETA(DisplayName = "Legendary")
};

UENUM(BlueprintType)
enum class ERewardCategory : uint8
{
    Kindness            UMETA(DisplayName = "Kindness"),
    Mentorship          UMETA(DisplayName = "Mentorship"),
    Leadership          UMETA(DisplayName = "Leadership"),
    Healing             UMETA(DisplayName = "Community Healing"),
    Consistency         UMETA(DisplayName = "Consistency"),
    Innovation          UMETA(DisplayName = "Innovation"),
    Teamwork            UMETA(DisplayName = "Teamwork"),
    Resilience          UMETA(DisplayName = "Resilience")
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHarmonyReward
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString RewardID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ERewardCategory Category;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ERewardTier Tier;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString RewardName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString Description;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 KindnessPointsRequired;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ExperienceBonus;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float CurrencyBonus;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSubclassOf<UGameplayEffect> RewardGameplayEffect;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<TSubclassOf<UGameplayAbility>> UnlockedAbilities;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer RewardTags;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsOneTimeReward;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsActive = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsRepeatable = false;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsVisible;

    FHarmonyReward()
    {
        RewardID = FGuid::NewGuid().ToString();
        Category = ERewardCategory::Kindness;
        Tier = ERewardTier::Bronze;
        RewardName = TEXT("");
        Description = TEXT("");
        KindnessPointsRequired = 0;
        ExperienceBonus = 0.0f;
        CurrencyBonus = 0.0f;
        bIsOneTimeReward = false;
        bIsVisible = true;
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FPlayerRewardProgress
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TotalKindnessPoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FString> UnlockedRewards;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TMap<ERewardCategory, int32> CategoryProgress;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ERewardTier CurrentTier;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TierProgress;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastRewardTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 RewardsThisSession;

    FPlayerRewardProgress()
    {
        PlayerID = TEXT("");
        TotalKindnessPoints = 0;
        CurrentTier = ERewardTier::Bronze;
        TierProgress = 0.0f;
        LastRewardTime = FDateTime::MinValue();
        RewardsThisSession = 0;
    }
};

/**
 * Harmony Rewards System
 * Manages positive reinforcement through rewards, achievements, and progression
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UHarmonyRewardsSystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UHarmonyRewardsSystem();

    // Core Reward Functions
    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void ProcessKindnessReward(const FString& PlayerID, const FKindnessReward& Reward);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    bool GrantReward(const FString& PlayerID, const FString& RewardID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    TArray<FHarmonyReward> GetAvailableRewards(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    TArray<FHarmonyReward> GetUnlockedRewards(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    bool IsRewardUnlocked(const FString& PlayerID, const FString& RewardID);

    // Progress Tracking
    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    FPlayerRewardProgress GetPlayerProgress(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void UpdatePlayerProgress(const FString& PlayerID, int32 KindnessPointsGained, ERewardCategory Category);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    float GetProgressToNextTier(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    ERewardTier CalculatePlayerTier(int32 TotalKindnessPoints);

    // Reward Management
    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void RegisterReward(const FHarmonyReward& Reward);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void UnregisterReward(const FString& RewardID);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    FHarmonyReward GetReward(const FString& RewardID);

    // Special Events
    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void TriggerSpecialEvent(const FString& EventName, float BonusMultiplier);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    bool IsSpecialEventActive() const;

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    float GetCurrentEventMultiplier() const;

    // Currency and Progression
    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void ApplyCurrencyBonus(const FString& PlayerID, float CurrencyBonus);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    void ApplyExperienceBonus(const FString& PlayerID, float BonusMultiplier);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    ERewardTier GetNextTier(ERewardTier CurrentTier);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    FString GetTierDisplayName(ERewardTier Tier);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    float CalculateTierCurrencyBonus(ERewardTier Tier);

    UFUNCTION(BlueprintCallable, Category = "Harmony Rewards")
    float CalculateTierExperienceBonus(ERewardTier Tier);

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rewards Config")
    int32 MaxRewardsPerSession;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rewards Config")
    float RewardCooldownTime;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rewards Config")
    bool bEnableProgressiveRewards;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rewards Config")
    bool bEnableSpecialEvents;

    // Data Tables
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Data")
    TObjectPtr<UDataTable> RewardsDataTable;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Data")
    TObjectPtr<UDataTable> TierRequirementsDataTable;

    // Runtime Data
    UPROPERTY()
    TMap<FString, FHarmonyReward> RegisteredRewards;

    UPROPERTY()
    TMap<FString, FPlayerRewardProgress> PlayerProgress;

    UPROPERTY()
    TMap<FString, FDateTime> LastRewardTimes;

    // Special Events
    UPROPERTY()
    bool bSpecialEventActive;

    UPROPERTY()
    FString CurrentEventName;

    UPROPERTY()
    float CurrentEventMultiplier;

    UPROPERTY()
    FDateTime EventEndTime;

    UPROPERTY()
    FDateTime SpecialEventStartTime;

    UPROPERTY()
    FTimespan SpecialEventDuration;

    // Tier Requirements
    UPROPERTY()
    TMap<ERewardTier, int32> TierKindnessRequirements;

private:
    // Reward processing
    void ApplyRewardEffects(const FString& PlayerID, const FKindnessReward& Reward);
    void GrantGameplayEffects(const FString& PlayerID, const FKindnessReward& Reward);
    void GrantAbilities(const FString& PlayerID, const FKindnessReward& Reward);
    
    // Progress calculation
    void CalculateTierProgress(const FString& PlayerID);
    void CheckForTierPromotion(const FString& PlayerID);
    void ProcessTierPromotion(const FString& PlayerID, ERewardTier NewTier);
    
    // Validation
    bool ValidateRewardEligibility(const FString& PlayerID, const FString& RewardID);
    bool IsPlayerOnRewardCooldown(const FString& PlayerID);
    bool HasPlayerReachedSessionLimit(const FString& PlayerID);
    
    // Special events
    void ProcessSpecialEventBonus(const FString& PlayerID, FKindnessReward& Reward);
    void UpdateSpecialEventStatus();
    void EndSpecialEvent();
    void BroadcastSpecialEventNotification(const FString& EventName, float Multiplier);
    
    // Data management
    void InitializeDefaultRewards();
    void InitializeTierRequirements();
    void SaveRewardData();
    void LoadRewardData();

    // Utility functions
    UAbilitySystemComponent* GetPlayerAbilitySystemComponent(const FString& PlayerID);
    APlayerController* GetPlayerController(const FString& PlayerID);
    void NotifyPlayerOfReward(const FString& PlayerID, const FKindnessReward& Reward);

    // Missing helper methods
    ERewardCategory DetermineRewardCategory(const FKindnessReward& Reward);
    void CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier, const FString& Name, const FString& Description);
    void CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier, const FString& Name, const FString& Description, int32 KindnessPointsRequired);

    // Robust conversion functions between reward types
    FHarmonyReward ConvertKindnessToHarmonyReward(const FKindnessReward& KindnessReward, const FString& RewardID = TEXT(""));
    FKindnessReward ConvertHarmonyToKindnessReward(const FHarmonyReward& HarmonyReward);
};
