#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "AuracronHardwareDetectionComponent.h"
#include "AuracronHardwareOptimizedGameMode.generated.h"

/**
 * GameMode que demonstra o uso do sistema de detecção de hardware
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronHardwareOptimizedGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    AAuracronHardwareOptimizedGameMode();

protected:
    virtual void BeginPlay() override;

public:
    /** Componente de detecção de hardware */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Hardware Detection")
    TObjectPtr<UAuracronHardwareDetectionComponent> HardwareDetectionComponent;

    /** Mostrar UI de configurações após detecção */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    bool bShowSettingsUIAfterDetection;

    /** Delay antes de executar detecção (em segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    float DetectionDelay;

    /** Configurações personalizadas para diferentes tipos de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hardware Detection")
    TMap<FString, FSimpleQualitySettings> CustomHardwareProfiles;

protected:
    /** Chamado quando hardware é detectado */
    UFUNCTION()
    void OnHardwareDetected(const FSimpleHardwareInfo& HardwareInfo);

    /** Chamado quando configurações são aplicadas */
    UFUNCTION()
    void OnQualitySettingsApplied(const FSimpleQualitySettings& AppliedSettings);

    /** Verifica se existe perfil personalizado para o hardware */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    bool HasCustomProfileForGPU(const FString& GPUName);

    /** Aplica perfil personalizado se existir */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    bool ApplyCustomProfileForGPU(const FString& GPUName);

    /** Cria perfil personalizado para GPU específica */
    UFUNCTION(BlueprintCallable, Category = "Hardware Detection")
    void CreateCustomProfileForGPU(const FString& GPUName, const FSimpleQualitySettings& Settings);

    /** Evento Blueprint para hardware detectado */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void OnHardwareDetectedBP(const FSimpleHardwareInfo& HardwareInfo);

    /** Evento Blueprint para configurações aplicadas */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void OnQualitySettingsAppliedBP(const FSimpleQualitySettings& AppliedSettings);

    /** Mostra UI de configurações (implementar em Blueprint) */
    UFUNCTION(BlueprintImplementableEvent, Category = "Hardware Detection")
    void ShowSettingsUI();

private:
    /** Timer para delay de detecção */
    FTimerHandle DetectionTimerHandle;

    /** Executa detecção após delay */
    void ExecuteDelayedDetection();

    /** Inicializa perfis personalizados padrão */
    void InitializeDefaultCustomProfiles();
};
