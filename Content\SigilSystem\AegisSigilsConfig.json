{"aegis_sigils": {"primordial": {"id": 1, "name": "<PERSON><PERSON><PERSON>", "description": "Escudo básico que absorve dano físico e mágico", "type": "Defense", "element": "Crystal", "rarity": "Common", "stats": {"shield_strength": 200.0, "absorption_rate": 0.8, "duration": 8.0, "cooldown": 30.0, "energy_cost": 60.0, "scaling_per_level": 0.1}, "effects": {"primary_effect": "Absorb Damage", "secondary_effect": "Damage Reduction", "passive_bonus": "+15% Max Health", "active_bonus": "80% Damage Absorption"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Aegis/AegisPrimordial_Activation", "active_vfx": "/Game/VFX/Sigils/Aegis/AegisPrimordial_Active", "shield_vfx": "/Game/VFX/Sigils/Aegis/AegisPrimordial_Shield", "primary_color": "#87CEEB", "secondary_color": "#4682B4", "particle_count": 500, "effect_scale": 1.0}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Aegis/AegisPrimordial_Activation", "active_sound": "/Game/Audio/Sigils/Aegis/AegisPrimordial_Active", "impact_sound": "/Game/Audio/Sigils/Aegis/AegisPrimordial_Impact", "volume": 0.8, "pitch": 1.0}, "gameplay_tags": ["Sigil.Type.Aegis.Primordial", "Sigil.Effect.Shield", "Sigil.Element.Crystal"]}, "cristalino": {"id": 2, "name": "<PERSON><PERSON><PERSON>", "description": "Escudo que reflete parte do dano recebido de volta ao atacante", "type": "Defense", "element": "Crystal", "rarity": "Uncommon", "stats": {"shield_strength": 150.0, "reflection_rate": 0.4, "duration": 6.0, "cooldown": 35.0, "energy_cost": 70.0, "scaling_per_level": 0.12}, "effects": {"primary_effect": "Damage Reflection", "secondary_effect": "Shield Absorption", "passive_bonus": "+10% Damage Reflection", "active_bonus": "40% Damage Reflected"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Aegis/AegisCristalino_Activation", "active_vfx": "/Game/VFX/Sigils/Aegis/AegisCristalino_Active", "reflection_vfx": "/Game/VFX/Sigils/Aegis/AegisCristalino_Reflection", "primary_color": "#FFD700", "secondary_color": "#FFA500", "particle_count": 750, "effect_scale": 1.2}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Aegis/AegisCristalino_Activation", "active_sound": "/Game/Audio/Sigils/Aegis/AegisCristalino_Active", "reflection_sound": "/Game/Audio/Sigils/Aegis/AegisCristalino_Reflection", "volume": 0.9, "pitch": 1.1}, "gameplay_tags": ["Sigil.Type.Aegis.Cristalino", "Sigil.Effect.Shield", "Sigil.Effect.Reflection", "Sigil.Element.Crystal"]}, "temporal": {"id": 3, "name": "<PERSON><PERSON><PERSON>", "description": "Escudo que reduz a velocidade de projéteis e ataques direcionados", "type": "Defense", "element": "Time", "rarity": "Rare", "stats": {"shield_strength": 180.0, "time_dilation": 0.3, "duration": 10.0, "cooldown": 40.0, "energy_cost": 85.0, "scaling_per_level": 0.08}, "effects": {"primary_effect": "Time Dilation", "secondary_effect": "Projectile Slowing", "passive_bonus": "+5% Cooldown Reduction", "active_bonus": "70% Projectile Speed Reduction"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Aegis/AegisTemporal_Activation", "active_vfx": "/Game/VFX/Sigils/Aegis/AegisTemporal_Active", "time_vfx": "/Game/VFX/Sigils/Aegis/AegisTemporal_TimeDilation", "primary_color": "#9370DB", "secondary_color": "#8A2BE2", "particle_count": 600, "effect_scale": 1.5}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Aegis/AegisTemporal_Activation", "active_sound": "/Game/Audio/Sigils/Aegis/AegisTemporal_Active", "time_sound": "/Game/Audio/Sigils/Aegis/AegisTemporal_TimeDilation", "volume": 0.7, "pitch": 0.8}, "gameplay_tags": ["Sigil.Type.Aegis.Temporal", "Sigil.Effect.Shield", "Sigil.Effect.TimeManipulation", "Sigil.Element.Time"]}, "espectral": {"id": 4, "name": "<PERSON><PERSON><PERSON>", "description": "Escudo que absorve efeitos mágicos e debuffs", "type": "Defense", "element": "Spirit", "rarity": "Epic", "stats": {"shield_strength": 120.0, "magic_absorption": 0.9, "duration": 12.0, "cooldown": 45.0, "energy_cost": 90.0, "scaling_per_level": 0.15}, "effects": {"primary_effect": "Magic Absorption", "secondary_effect": "<PERSON><PERSON><PERSON>", "passive_bonus": "+20% Magic Resistance", "active_bonus": "90% Magic Effect Absorption"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Aegis/AegisEspectral_Activation", "active_vfx": "/Game/VFX/Sigils/Aegis/AegisEspectral_Active", "absorption_vfx": "/Game/VFX/Sigils/Aegis/AegisEspectral_Absorption", "primary_color": "#00FFFF", "secondary_color": "#008B8B", "particle_count": 800, "effect_scale": 1.3}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Aegis/AegisEspectral_Activation", "active_sound": "/Game/Audio/Sigils/Aegis/AegisEspectral_Active", "absorption_sound": "/Game/Audio/Sigils/Aegis/AegisEspectral_Absorption", "volume": 0.6, "pitch": 1.3}, "gameplay_tags": ["Sigil.Type.Aegis.Espectral", "Sigil.Effect.Shield", "Sigil.Effect.MagicAbsorption", "Sigil.Element.Spirit"]}, "absoluto": {"id": 5, "name": "<PERSON><PERSON><PERSON>", "description": "Escudo que concede invulnerabilidade temporária", "type": "Defense", "element": "Divine", "rarity": "Legendary", "stats": {"shield_strength": 999.0, "invulnerability_duration": 3.0, "duration": 5.0, "cooldown": 60.0, "energy_cost": 120.0, "scaling_per_level": 0.05}, "effects": {"primary_effect": "Temporary Invulnerability", "secondary_effect": "Complete Damage Immunity", "passive_bonus": "+25% Max Health", "active_bonus": "3 seconds of Invulnerability"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Aegis/AegisAbsoluto_Activation", "active_vfx": "/Game/VFX/Sigils/Aegis/AegisAbsoluto_Active", "invulnerability_vfx": "/Game/VFX/Sigils/Aegis/AegisAbsoluto_Invulnerability", "primary_color": "#FFD700", "secondary_color": "#FFFFFF", "particle_count": 1200, "effect_scale": 2.0}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Aegis/AegisAbsoluto_Activation", "active_sound": "/Game/Audio/Sigils/Aegis/AegisAbsoluto_Active", "invulnerability_sound": "/Game/Audio/Sigils/Aegis/AegisAbsoluto_Invulnerability", "volume": 1.0, "pitch": 0.9}, "gameplay_tags": ["Sigil.Type.Aegis.Absoluto", "Sigil.Effect.Shield", "Sigil.Effect.Invulnerability", "Sigil.Element.Divine"]}}, "synergy_combinations": {"crystal_resonance": {"sigils": ["Aegis.Primordial", "Aegis.Cristalino"], "bonus": "+15% Shield Effectiveness", "description": "Crystal Sigils resonate together"}, "temporal_mastery": {"sigils": ["Aegis.Temporal", "Vesper.<PERSON>"], "bonus": "+20% Time Effect Duration", "description": "Temporal Sigils amplify each other"}, "divine_protection": {"sigils": ["Aegis.Absoluto", "Vesper.Curativo"], "bonus": "+25% Healing Effectiveness", "description": "Divine protection enhances healing"}}}