// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Ãudio MetaSounds Bridge
// IntegraÃ§Ã£o C++ para Ã¡udio 3D usando MetaSounds e APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/ActorComponent.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundCue.h"
#include "Sound/SoundWave.h"
#include "Sound/SoundClass.h"
#include "Sound/SoundMix.h"
#include "Sound/SoundAttenuation.h"
#include "Sound/SoundConcurrency.h"
// Forward declaration for MetaSound compatibility
class UMetaSoundSource;
// Forward declaration for MetaSound compatibility
class FMetasoundGeneratorHandle;
#include "Sound/SoundEffectSource.h"
#include "AudioMixerBlueprintLibrary.h"
// Forward declaration for Synthesis compatibility
class USynthComponent;
// Forward declaration for Synthesis compatibility
class USourceEffectChorus;
// Forward declaration for Synthesis compatibility
class USourceEffectDelay;
// Forward declaration for Synthesis compatibility
class USourceEffectReverb;
#include "Sound/ReverbEffect.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "AuracronAudioBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de Ã¡udio
 */
UENUM(BlueprintType)
enum class EAuracronAudioType : uint8
{
    None                UMETA(DisplayName = "None"),
    Music               UMETA(DisplayName = "Music"),
    SFX                 UMETA(DisplayName = "Sound Effects"),
    Voice               UMETA(DisplayName = "Voice"),
    Ambient             UMETA(DisplayName = "Ambient"),
    UI                  UMETA(DisplayName = "UI"),
    Ability             UMETA(DisplayName = "Ability"),
    Champion            UMETA(DisplayName = "Champion"),
    Realm               UMETA(DisplayName = "Realm"),
    Combat              UMETA(DisplayName = "Combat"),
    Notification        UMETA(DisplayName = "Notification")
};

/**
 * EnumeraÃ§Ã£o para camadas de Ã¡udio 3D
 */
UENUM(BlueprintType)
enum class EAuracronAudioLayer : uint8
{
    Surface             UMETA(DisplayName = "Surface Layer"),
    Sky                 UMETA(DisplayName = "Sky Layer"),
    Underground         UMETA(DisplayName = "Underground Layer"),
    All                 UMETA(DisplayName = "All Layers")
};

/**
 * Estrutura para configuraÃ§Ã£o de Ã¡udio 3D
 */
USTRUCT(BlueprintType)
struct AURACRONAUDIOBRIDGE_API FAuracronAudioConfiguration
{
    GENERATED_BODY()

    /** Volume master */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MasterVolume = 1.0f;

    /** Volume da mÃºsica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MusicVolume = 0.8f;

    /** Volume dos efeitos sonoros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SFXVolume = 1.0f;

    /** Volume das vozes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float VoiceVolume = 1.0f;

    /** Volume do ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AmbientVolume = 0.6f;

    /** Volume da UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float UIVolume = 0.8f;

    /** Usar Ã¡udio 3D */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUse3DAudio = true;

    /** Usar reverb dinÃ¢mico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseDynamicReverb = true;

    /** Usar oclusÃ£o de Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseAudioOcclusion = true;

    /** Usar compressÃ£o dinÃ¢mica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseDynamicRangeCompression = false;

    /** Qualidade do Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float AudioQuality = 1.0f;

    /** DistÃ¢ncia mÃ¡xima de Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "100.0", ClampMax = "10000.0"))
    float MaxAudioDistance = 5000.0f;

    /** Usar Ã¡udio binaural */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseBinauralAudio = false;

    /** Usar HRTF */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseHRTF = false;

    /** Camada de Ã¡udio atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    EAuracronAudioLayer CurrentAudioLayer = EAuracronAudioLayer::Surface;

    /** Usar transiÃ§Ãµes suaves entre camadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration")
    bool bUseSmoothLayerTransitions = true;

    /** Tempo de transiÃ§Ã£o entre camadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float LayerTransitionTime = 1.0f;
};

/**
 * Estrutura para configuraÃ§Ã£o de mÃºsica dinÃ¢mica
 */
USTRUCT(BlueprintType)
struct AURACRONAUDIOBRIDGE_API FAuracronDynamicMusicConfiguration
{
    GENERATED_BODY()

    /** MÃºsica de menu principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> MainMenuMusic;

    /** MÃºsica de seleÃ§Ã£o de campeÃµes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> ChampionSelectMusic;

    /** MÃºsica de jogo - PlanÃ­cie */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> SurfaceRealmMusic;

    /** MÃºsica de jogo - Firmamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> SkyRealmMusic;

    /** MÃºsica de jogo - Abismo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> UndergroundRealmMusic;

    /** MÃºsica de combate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> CombatMusic;

    /** MÃºsica de vitÃ³ria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> VictoryMusic;

    /** MÃºsica de derrota */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TSoftObjectPtr<USoundBase> DefeatMusic;

    /** Usar transiÃ§Ãµes adaptativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    bool bUseAdaptiveTransitions = true;

    /** Intensidade da mÃºsica baseada no combate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    bool bCombatBasedIntensity = true;

    /** Tempo de fade entre mÃºsicas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float MusicFadeTime = 2.0f;

    /** Volume da mÃºsica dinÃ¢mica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DynamicMusicVolume = 0.8f;

    /** Habilitar música dinâmica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    bool bEnableDynamicMusic = true;

    /** Tempo de transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float TransitionTime = 2.0f;

    /** Tempo de crossfade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float CrossfadeTime = 1.0f;

    /** Níveis de intensidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music", meta = (ClampMin = "1", ClampMax = "10"))
    int32 IntensityLevels = 5;

    /** Habilitar música adaptativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    bool bEnableAdaptiveMusic = true;

    /** Tracks de música por tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Music")
    TMap<FString, TSoftObjectPtr<USoundBase>> MusicTracks;
};

/**
 * Estrutura para configuraÃ§Ã£o de efeitos sonoros
 */
USTRUCT(BlueprintType)
struct AURACRONAUDIOBRIDGE_API FAuracronSFXConfiguration
{
    GENERATED_BODY()

    /** Sons de habilidades por campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TMap<FString, TSoftObjectPtr<USoundBase>> ChampionAbilitySounds;

    /** Sons de impacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TArray<TSoftObjectPtr<USoundCue>> ImpactSounds;

    /** Sons de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TArray<TSoftObjectPtr<USoundCue>> MovementSounds;

    /** Sons de UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TMap<FString, TSoftObjectPtr<USoundCue>> UISounds;

    /** Sons ambientes por realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TArray<TSoftObjectPtr<USoundBase>> RealmAmbientSounds;

    /** Sons de notificaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    TMap<FString, TSoftObjectPtr<USoundCue>> NotificationSounds;

    /** Usar variaÃ§Ãµes aleatÃ³rias */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    bool bUseRandomVariations = true;

    /** NÃºmero mÃ¡ximo de sons simultÃ¢neos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration", meta = (ClampMin = "1", ClampMax = "100"))
    int32 MaxSimultaneousSounds = 32;

    /** Usar pooling de componentes de Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    bool bUseAudioComponentPooling = true;

    /** Tamanho do pool de componentes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration", meta = (ClampMin = "5", ClampMax = "50"))
    int32 AudioComponentPoolSize = 20;

    /** Habilitar variações aleatórias */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    bool bEnableRandomVariations = true;

    /** Habilitar pooling de componentes de áudio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration")
    bool bEnableAudioComponentPooling = true;

    /** Tamanho do pool */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "SFX Configuration", meta = (ClampMin = "5", ClampMax = "100"))
    int32 PoolSize = 32;
};

/**
 * Classe principal do Bridge para Sistema de Ãudio MetaSounds
 * ResponsÃ¡vel pelo gerenciamento completo de Ã¡udio 3D e mÃºsica dinÃ¢mica
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Audio", meta = (DisplayName = "AURACRON Audio Bridge", BlueprintSpawnableComponent))
class AURACRONAUDIOBRIDGE_API UAuracronAudioBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronAudioBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Audio Management ===

    /**
     * Reproduzir som 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|3D", CallInEditor)
    bool PlaySound3D(USoundBase* Sound, const FVector& Location, float Volume = 1.0f, float Pitch = 1.0f);

    /**
     * Reproduzir MetaSound
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|MetaSounds", CallInEditor)
    bool PlayMetaSound(USoundBase* MetaSound, const FVector& Location, const TMap<FString, float>& Parameters);

    /**
     * Parar som
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Control", CallInEditor)
    bool StopSound(UAudioComponent* AudioComponent);

    /**
     * Pausar todos os sons
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Control", CallInEditor)
    bool PauseAllSounds();

    /**
     * Retomar todos os sons
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Control", CallInEditor)
    bool ResumeAllSounds();

    // === Dynamic Music ===

    /**
     * Reproduzir mÃºsica dinÃ¢mica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Music", CallInEditor)
    bool PlayDynamicMusic(const FString& MusicType, bool bLoop = true);

    /**
     * Parar mÃºsica atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Music", CallInEditor)
    bool StopCurrentMusic(bool bFadeOut = true);

    /**
     * Fazer transiÃ§Ã£o para nova mÃºsica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Music", CallInEditor)
    bool TransitionToMusic(const FString& NewMusicType, float TransitionTime = 2.0f);

    /**
     * Definir intensidade da mÃºsica
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Music", CallInEditor)
    bool SetMusicIntensity(float Intensity);

    // === Realm Audio ===

    /**
     * Mudar Ã¡udio para realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Realm", CallInEditor)
    bool ChangeToRealmAudio(int32 RealmIndex);

    /**
     * Aplicar efeitos de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Realm", CallInEditor)
    bool ApplyRealmAudioEffects(int32 RealmIndex);

    /**
     * Reproduzir som ambiente de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Realm", CallInEditor)
    bool PlayRealmAmbientSound(int32 RealmIndex, const FVector& Location);

    // === Champion Audio ===

    /**
     * Reproduzir voz de campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Champion", CallInEditor)
    bool PlayChampionVoice(const FString& ChampionID, const FString& VoiceLineType);

    /**
     * Reproduzir som de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Champion", CallInEditor)
    bool PlayAbilitySound(const FString& ChampionID, const FString& AbilitySlot, const FVector& Location);

    /**
     * Reproduzir som de movimento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Champion", CallInEditor)
    bool PlayMovementSound(const FString& MovementType, const FVector& Location);

    // === Audio Effects ===

    /**
     * Aplicar efeito de reverb
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Effects", CallInEditor)
    bool ApplyReverbEffect(const FString& ReverbType, float Intensity = 1.0f);

    /**
     * Aplicar efeito de delay
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Effects", CallInEditor)
    bool ApplyDelayEffect(float DelayTime, float Feedback = 0.3f);

    /**
     * Remover efeito de delay
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Effects", CallInEditor)
    bool RemoveDelayEffect();

    /**
     * Aplicar filtro de frequÃªncia
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Effects", CallInEditor)
    bool ApplyFrequencyFilter(float LowPassFreq, float HighPassFreq);

    // === Configuration ===

    /**
     * Aplicar configuraÃ§Ã£o de Ã¡udio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Configuration", CallInEditor)
    bool ApplyAudioConfiguration(const FAuracronAudioConfiguration& Configuration);

    /**
     * Salvar configuraÃ§Ãµes de Ã¡udio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Configuration", CallInEditor)
    bool SaveAudioSettings();

    /**
     * Carregar configuraÃ§Ãµes de Ã¡udio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Audio|Configuration", CallInEditor)
    bool LoadAudioSettings();

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de Ã¡udio */
    bool InitializeAudioSystem();
    
    /** Configurar MetaSounds */
    bool SetupMetaSounds();
    
    /** Configurar mixagem de Ã¡udio */
    bool SetupAudioMixing();
    
    /** Processar transiÃ§Ãµes de Ã¡udio */
    void ProcessAudioTransitions(float DeltaTime);
    
    /** Atualizar Ã¡udio 3D */
    void Update3DAudio(float DeltaTime);
    
    /** Validar configuraÃ§Ã£o de Ã¡udio */
    UFUNCTION(BlueprintCallable, Category = "Audio Configuration")
    bool ValidateAudioConfiguration(const FAuracronAudioConfiguration& Configuration) const;



public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ã£o geral de Ã¡udio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronAudioConfiguration AudioConfiguration;

    /** ConfiguraÃ§Ã£o de mÃºsica dinÃ¢mica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronDynamicMusicConfiguration DynamicMusicConfiguration;

    /** ConfiguraÃ§Ã£o de efeitos sonoros */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronSFXConfiguration SFXConfiguration;

    /** Componentes de Ã¡udio ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<TObjectPtr<UAudioComponent>> ActiveAudioComponents;

    /** MÃºsica atual tocando */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_CurrentMusic)
    FString CurrentMusicType;

    /** Intensidade da mÃºsica atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_MusicIntensity)
    float CurrentMusicIntensity = 0.5f;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Pool de componentes de Ã¡udio */
    TArray<TObjectPtr<UAudioComponent>> AudioComponentPool;
    
    /** Timer para transiÃ§Ãµes */
    FTimerHandle TransitionTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection AudioMutex;
    
    /** MÃºsica atual (objeto) */
    UPROPERTY()
    TObjectPtr<USoundBase> CurrentMusic;
    
    /** Componente de mÃºsica atual */
    UPROPERTY()
    TObjectPtr<UAudioComponent> CurrentMusicComponent;
    
    /** ParÃ¢metros padrÃ£o para MetaSounds */
    TMap<FString, float> DefaultMetaSoundParameters;
    
    /** ParÃ¢metros para vozes de campeÃ£o */
    TMap<FString, float> ChampionVoiceParameters;
    
    /** ParÃ¢metros para sons de habilidade */
    TMap<FString, float> AbilitySoundParameters;
    
    /** ParÃ¢metros para sons de movimento */
    TMap<FString, float> MovementSoundParameters;
    
    /** ConfiguraÃ§Ã£o de mÃºsica dinÃ¢mica (runtime) */
    FAuracronDynamicMusicConfiguration DynamicMusicConfig;
    
    /** ConfiguraÃ§Ã£o de SFX (runtime) */
    FAuracronSFXConfiguration SFXConfig;
    
    /** Efeito de reverb atual */
    UPROPERTY()
    TObjectPtr<UReverbEffect> CurrentReverbEffect;
    
    /** Tipo de reverb atual */
    FString CurrentReverbType;
    
    /** Configurações de delay simplificadas */
    struct {
        float DelayTime = 0.0f;
        float Feedback = 0.0f;
        float WetLevel = 0.3f;
        float DryLevel = 0.7f;
    } DelaySettings;
    
    /** Tempo de delay atual */
    float CurrentDelayTime = 0.0f;
    
    /** Feedback de delay atual */
    float CurrentDelayFeedback = 0.0f;
    
    /** Frequência Low Pass atual */
    float CurrentLowPassFreq = 20000.0f;
    
    /** Frequência High Pass atual */
    float CurrentHighPassFreq = 20.0f;
    
    /** Filtros habilitados */
    bool bFiltersEnabled = false;

    // === Replication Callbacks ===
    
    UFUNCTION()
    void OnRep_CurrentMusic();
    
    UFUNCTION()
    void OnRep_MusicIntensity();
    
    /** Callback para quando um componente de áudio termina de tocar */
    UFUNCTION()
    void OnAudioComponentFinished(UAudioComponent* AudioComponent);

    /** Callback moderno UE5.6 para quando áudio termina (sem parâmetros) */
    UFUNCTION()
    void OnAudioFinishedModern();

public:
    // === Delegates ===
    
    /** Delegate chamado quando mÃºsica muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMusicChanged, FString, OldMusic, FString, NewMusic);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Audio|Events")
    FOnMusicChanged OnMusicChanged;
    
    /** Delegate chamado quando camada de Ã¡udio muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAudioLayerChanged, EAuracronAudioLayer, OldLayer, EAuracronAudioLayer, NewLayer);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Audio|Events")
    FOnAudioLayerChanged OnAudioLayerChanged;
};

