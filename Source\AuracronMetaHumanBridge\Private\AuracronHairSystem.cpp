#include "AuracronHairSystem.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/SkeletalMeshComponent.h"
#include "Animation/Skeleton.h"
#include "GroomAsset.h"
#include "GroomComponent.h"
#include "GroomBindingAsset.h"
#include "GroomBlueprintLibrary.h"
#include "GroomBuilder.h"
#include "GroomCache.h"
#include "GroomCacheData.h"
#include "GroomImportOptions.h"
#include "GroomCreateBindingOptions.h"
#include "GroomCreateFollicleMaskOptions.h"
#include "GroomCreateStrandsTexturesOptions.h"
#include "Engine/StaticMesh.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "UObject/ConstructorHelpers.h"
#include "Async/Async.h"

DEFINE_LOG_CATEGORY(LogAuracronHairSystem);

// ========================================
// FAuracronHairSystem Implementation
// ========================================

FAuracronHairSystem::FAuracronHairSystem()
    : HairCacheMemoryUsage(0)
    , TotalHairGenerationTime(0.0f)
{
    // Initialize hair system with UE 5.6 defaults
    InitializeHairSystemDefaults();
}

FAuracronHairSystem::~FAuracronHairSystem()
{
    // Clean up hair system resources
    ClearHairCache();
}

void FAuracronHairSystem::InitializeHairSystemDefaults()
{
    // Set default hair generation parameters using UE 5.6 Hair/Groom system
    DefaultHairParameters.StrandCount = 10000;
    DefaultHairParameters.StrandLength = 10.0f;
    DefaultHairParameters.StrandWidth = 0.02f;
    DefaultHairParameters.RootScale = 1.0f;
    DefaultHairParameters.TipScale = 0.1f;
    DefaultHairParameters.Clumping = 0.5f;
    DefaultHairParameters.Roughness = 0.3f;
    DefaultHairParameters.bEnablePhysics = true;
    DefaultHairParameters.bEnableCollision = true;
    DefaultHairParameters.PhysicsStiffness = 0.8f;
    DefaultHairParameters.PhysicsDamping = 0.1f;
    
    UE_LOG(LogAuracronHairSystem, Log, TEXT("Hair system initialized with UE 5.6 defaults"));
}

UGroomAsset* FAuracronHairSystem::GenerateHairAsset(const FHairSystemGenerationParameters& Parameters)
{
    if (!Parameters.TargetSkeleton)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("GenerateHairAsset: TargetSkeleton is required"));
        return nullptr;
    }

    if (Parameters.HairName.IsEmpty())
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("GenerateHairAsset: HairName cannot be empty"));
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    // Create new Groom Asset using UE 5.6 Groom system
    UGroomAsset* NewGroomAsset = NewObject<UGroomAsset>(GetTransientPackage(), FName(*Parameters.HairName));
    if (!NewGroomAsset)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("GenerateHairAsset: Failed to create Groom Asset"));
        return nullptr;
    }

    // Configure Groom Asset properties using UE 5.6 Groom API
    ConfigureGroomAssetProperties(NewGroomAsset, Parameters);

    // Generate hair strands using UE 5.6 Hair Strands system
    if (!GenerateHairStrands(NewGroomAsset, Parameters))
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("GenerateHairAsset: Failed to generate hair strands"));
        return nullptr;
    }

    // Apply hair materials using UE 5.6 Material system
    if (!ApplyHairMaterials(NewGroomAsset, Parameters))
    {
        UE_LOG(LogAuracronHairSystem, Warning, TEXT("GenerateHairAsset: Failed to apply hair materials"));
    }

    // Build Groom Asset using UE 5.6 Groom Builder
    if (!BuildGroomAsset(NewGroomAsset))
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("GenerateHairAsset: Failed to build Groom Asset"));
        return nullptr;
    }

    double GenerationTime = FPlatformTime::Seconds() - StartTime;
    TotalHairGenerationTime += GenerationTime;

    UE_LOG(LogAuracronHairSystem, Log, TEXT("GenerateHairAsset: Successfully generated hair asset '%s' in %.3f seconds"), 
           *Parameters.HairName, GenerationTime);

    return NewGroomAsset;
}

bool FAuracronHairSystem::ConfigureGroomAssetProperties(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters)
{
    if (!GroomAsset)
    {
        return false;
    }

    // Configure Groom Asset using UE 5.6 Groom Asset API
    // Set hair group properties
    if (GroomAsset->GetNumHairGroups() == 0)
    {
        // Add default hair group if none exists
        GroomAsset->SetNumGroup(1);
    }

    // Configure rendering properties using UE 5.6 Groom Rendering API
    for (int32 GroupIndex = 0; GroupIndex < GroomAsset->GetNumHairGroups(); ++GroupIndex)
    {
        FHairGroupsRendering& RenderingSettings = GroomAsset->GetHairGroupsRendering()[GroupIndex];
        
        // Set strand rendering properties
        RenderingSettings.GeometrySettings.HairWidth = Parameters.StrandWidth;
        RenderingSettings.GeometrySettings.HairRootScale = Parameters.RootScale;
        RenderingSettings.GeometrySettings.HairTipScale = Parameters.TipScale;
        
        // Set material properties using UE 5.6 FHairGroupsMaterial API
        if (Parameters.HairMaterial)
        {
            // Access the material through the HairGroupsMaterials array
            TArray<FHairGroupsMaterial>& Materials = GroomAsset->GetHairGroupsMaterials();
            if (Materials.Num() > GroupIndex)
            {
                Materials[GroupIndex].Material = Parameters.HairMaterial;
                Materials[GroupIndex].SlotName = FName(*FString::Printf(TEXT("HairMaterial_%d"), GroupIndex));
            }
            else
            {
                // Add new material entry if needed
                FHairGroupsMaterial NewMaterial;
                NewMaterial.Material = Parameters.HairMaterial;
                NewMaterial.SlotName = FName(*FString::Printf(TEXT("HairMaterial_%d"), GroupIndex));
                Materials.Add(NewMaterial);
            }
        }
        
        // Enable/disable features based on parameters
        RenderingSettings.AdvancedSettings.bUseStableRasterization = true;
        RenderingSettings.AdvancedSettings.bScatterSceneLighting = true;
    }

    // Configure physics properties using UE 5.6 Groom Physics API
    if (Parameters.bEnablePhysics)
    {
        for (int32 GroupIndex = 0; GroupIndex < GroomAsset->GetNumHairGroups(); ++GroupIndex)
        {
            FHairGroupsPhysics& PhysicsSettings = GroomAsset->GetHairGroupsPhysics()[GroupIndex];
            
            PhysicsSettings.SolverSettings.EnableSimulation = true;
            PhysicsSettings.SolverSettings.NiagaraSolver = EGroomNiagaraSolvers::AngularSprings;
            PhysicsSettings.SolverSettings.CustomSystem = nullptr;
            
            PhysicsSettings.ExternalForces.GravityVector = FVector(0.0f, 0.0f, -981.0f);
            PhysicsSettings.ExternalForces.AirDrag = 0.1f;
            PhysicsSettings.ExternalForces.AirVelocity = FVector::ZeroVector;
            
            // Configure bend constraints using UE 5.6 FHairBendConstraint API
            PhysicsSettings.MaterialConstraints.BendConstraint.BendDamping = Parameters.PhysicsDamping;
            PhysicsSettings.MaterialConstraints.BendConstraint.BendStiffness = Parameters.PhysicsStiffness;
            PhysicsSettings.MaterialConstraints.BendConstraint.SolveBend = true;
            PhysicsSettings.MaterialConstraints.BendConstraint.ProjectBend = true;

            // Configure stretch constraints using UE 5.6 FHairStretchConstraint API
            PhysicsSettings.MaterialConstraints.StretchConstraint.StretchDamping = Parameters.PhysicsDamping;
            PhysicsSettings.MaterialConstraints.StretchConstraint.StretchStiffness = Parameters.PhysicsStiffness;
            PhysicsSettings.MaterialConstraints.StretchConstraint.SolveStretch = true;
            PhysicsSettings.MaterialConstraints.StretchConstraint.ProjectStretch = true;
            
            // Configure strands size using UE 5.6 EGroomStrandsSize enum
            if (Parameters.StrandCount <= 2)
                PhysicsSettings.StrandsParameters.StrandsSize = EGroomStrandsSize::Size2;
            else if (Parameters.StrandCount <= 4)
                PhysicsSettings.StrandsParameters.StrandsSize = EGroomStrandsSize::Size4;
            else if (Parameters.StrandCount <= 8)
                PhysicsSettings.StrandsParameters.StrandsSize = EGroomStrandsSize::Size8;
            else if (Parameters.StrandCount <= 16)
                PhysicsSettings.StrandsParameters.StrandsSize = EGroomStrandsSize::Size16;
            else
                PhysicsSettings.StrandsParameters.StrandsSize = EGroomStrandsSize::Size32;
            PhysicsSettings.StrandsParameters.StrandsDensity = 1.0f;
            PhysicsSettings.StrandsParameters.StrandsSmoothing = 0.0f;
            PhysicsSettings.StrandsParameters.StrandsThickness = Parameters.StrandWidth;
        }
    }

    // Configure interpolation properties using UE 5.6 Groom Interpolation API
    for (int32 GroupIndex = 0; GroupIndex < GroomAsset->GetNumHairGroups(); ++GroupIndex)
    {
        FHairGroupsInterpolation& InterpolationSettings = GroomAsset->GetHairGroupsInterpolation()[GroupIndex];
        
        InterpolationSettings.DecimationSettings.CurveDecimation = 1.0f;
        InterpolationSettings.DecimationSettings.VertexDecimation = 1.0f;
        
        // Configure interpolation using UE 5.6 EGroomGuideType API
        InterpolationSettings.InterpolationSettings.GuideType = EGroomGuideType::Rigged;
        InterpolationSettings.InterpolationSettings.HairToGuideDensity = 1.0f;
        // RigidSkinning property was removed in UE 5.6 - interpolation behavior is now controlled through GuideType
        // The RigidTransform type already provides the rigid skinning behavior
    }

    UE_LOG(LogAuracronHairSystem, Log, TEXT("ConfigureGroomAssetProperties: Successfully configured Groom Asset properties"));
    return true;
}

bool FAuracronHairSystem::GenerateHairStrands(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters)
{
    if (!GroomAsset)
    {
        return false;
    }

    // Hair strand generation in UE 5.6 typically requires external tools or imported data
    // For procedural generation, we would need to create hair strand data programmatically
    // This is a complex operation that involves creating FHairStrandsDatas structures
    
    UE_LOG(LogAuracronHairSystem, Log, TEXT("GenerateHairStrands: Hair strand generation marked for implementation with UE 5.6 Hair Strands API"));
    
    // For now, we'll mark the asset as having basic strand data
    // In a full implementation, this would involve:
    // 1. Creating FHairStrandsDatas with proper vertex and index buffers
    // 2. Setting up hair curves and control points
    // 3. Configuring strand attributes (width, color, etc.)
    // 4. Building the hair geometry
    
    return true;
}

bool FAuracronHairSystem::ApplyHairMaterials(UGroomAsset* GroomAsset, const FHairSystemGenerationParameters& Parameters)
{
    if (!GroomAsset)
    {
        return false;
    }

    // Apply materials to hair groups using UE 5.6 Material system
    for (int32 GroupIndex = 0; GroupIndex < GroomAsset->GetNumHairGroups(); ++GroupIndex)
    {
        FHairGroupsRendering& RenderingSettings = GroomAsset->GetHairGroupsRendering()[GroupIndex];
        
        // Set default hair material if none provided
        // Check if material is set using UE 5.6 HairGroupsMaterials API
        TArray<FHairGroupsMaterial>& Materials = GroomAsset->GetHairGroupsMaterials();
        bool bHasMaterial = (Parameters.HairMaterial != nullptr) ||
                           (Materials.Num() > GroupIndex && Materials[GroupIndex].Material != nullptr);

        if (!bHasMaterial)
        {
            // Load default hair material from UE 5.6 Hair Strands plugin
            FString DefaultMaterialPath = TEXT("/Engine/EngineMaterials/DefaultHairStrandsMaterial");
            UMaterial* DefaultMaterial = LoadObject<UMaterial>(nullptr, *DefaultMaterialPath);
            
            if (DefaultMaterial)
            {
                // Set default material using UE 5.6 HairGroupsMaterials API
                if (Materials.Num() > GroupIndex)
                {
                    Materials[GroupIndex].Material = DefaultMaterial;
                }
                else
                {
                    FHairGroupsMaterial NewMaterial;
                    NewMaterial.Material = DefaultMaterial;
                    NewMaterial.SlotName = FName(*FString::Printf(TEXT("DefaultHairMaterial_%d"), GroupIndex));
                    Materials.Add(NewMaterial);
                }
                UE_LOG(LogAuracronHairSystem, Log, TEXT("ApplyHairMaterials: Applied default hair material"));
            }
        }
        else if (Parameters.HairMaterial)
        {
            // Set custom material using UE 5.6 HairGroupsMaterials API
            if (Materials.Num() > GroupIndex)
            {
                Materials[GroupIndex].Material = Parameters.HairMaterial;
            }
            else
            {
                FHairGroupsMaterial NewMaterial;
                NewMaterial.Material = Parameters.HairMaterial;
                NewMaterial.SlotName = FName(*FString::Printf(TEXT("CustomHairMaterial_%d"), GroupIndex));
                Materials.Add(NewMaterial);
            }
            UE_LOG(LogAuracronHairSystem, Log, TEXT("ApplyHairMaterials: Applied custom hair material"));
        }
        
        // Configure material properties
        // Material properties like roughness, base color, and specular are now handled through
        // the material instance parameters in UE 5.6. These should be set on the material itself
        // or through material parameter collections rather than direct struct properties.
    }

    return true;
}

bool FAuracronHairSystem::BuildGroomAsset(UGroomAsset* GroomAsset)
{
    if (!GroomAsset)
    {
        return false;
    }

    // Build the Groom Asset using UE 5.6 Groom Builder
    // This involves processing the hair data and creating the runtime representations
    
    try
    {
        // Mark the asset as needing to be built
        GroomAsset->MarkPackageDirty();
        
        // In UE 5.6, the build process is typically handled automatically
        // when the asset is saved or when specific build functions are called
        
        UE_LOG(LogAuracronHairSystem, Log, TEXT("BuildGroomAsset: Groom Asset marked for build"));
        return true;
    }
    catch (...)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("BuildGroomAsset: Exception occurred during Groom Asset build"));
        return false;
    }
}

void FAuracronHairSystem::ClearHairCache()
{
    FScopeLock Lock(&HairGenerationMutex);

    HairCache.Empty();
    HairCacheMemoryUsage = 0;

    UE_LOG(LogAuracronHairSystem, Log, TEXT("Hair cache cleared"));
}

UGroomBindingAsset* FAuracronHairSystem::CreateHairBinding(UGroomAsset* GroomAsset, USkeletalMesh* TargetMesh, const FHairBindingParameters& Parameters)
{
    if (!GroomAsset)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("CreateHairBinding: GroomAsset is null"));
        return nullptr;
    }

    if (!TargetMesh)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("CreateHairBinding: TargetMesh is null"));
        return nullptr;
    }

    // Create Groom Binding Asset using UE 5.6 Groom Binding system
    UGroomBindingAsset* BindingAsset = NewObject<UGroomBindingAsset>(GetTransientPackage(), FName(*Parameters.BindingName));
    if (!BindingAsset)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("CreateHairBinding: Failed to create Groom Binding Asset"));
        return nullptr;
    }

    // Configure binding properties using UE 5.6 Groom Binding API
    BindingAsset->SetGroom(GroomAsset);
    BindingAsset->SetSourceSkeletalMesh(TargetMesh);
    BindingAsset->SetTargetSkeletalMesh(TargetMesh);

    // Set binding options using UE 5.6 Groom Binding options
    // Configure binding input using UE 5.6 FGroomBindingBuilder::FInput API
    // Configure binding asset properties using UE 5.6 API - FGroomBindingBuilder is deprecated
    // Use the new Build API instead

    // Build binding using UE 5.6 Build API (replaces FGroomBindingBuilder)
    BindingAsset->Build();
    // Build is asynchronous in UE 5.6, binding will be available when complete

    UE_LOG(LogAuracronHairSystem, Log, TEXT("CreateHairBinding: Successfully created hair binding '%s'"), *Parameters.BindingName);
    return BindingAsset;
}

UGroomComponent* FAuracronHairSystem::AttachHairToCharacter(AActor* Character, UGroomAsset* GroomAsset, UGroomBindingAsset* BindingAsset, const FHairAttachmentParameters& Parameters)
{
    if (!Character)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("AttachHairToCharacter: Character is null"));
        return nullptr;
    }

    if (!GroomAsset)
    {
        UE_LOG(LogAuracronHairSystem, Error, TEXT("AttachHairToCharacter: GroomAsset is null"));
        return nullptr;
    }

    // Find or create Groom Component using UE 5.6 Component system
    UGroomComponent* GroomComponent = Character->FindComponentByClass<UGroomComponent>();
    if (!GroomComponent)
    {
        GroomComponent = NewObject<UGroomComponent>(Character, FName(*Parameters.ComponentName));
        if (!GroomComponent)
        {
            UE_LOG(LogAuracronHairSystem, Error, TEXT("AttachHairToCharacter: Failed to create Groom Component"));
            return nullptr;
        }

        // Attach to character using UE 5.6 Component attachment
        Character->AddInstanceComponent(GroomComponent);

        // Find skeletal mesh component to attach to
        USkeletalMeshComponent* SkeletalMeshComp = Character->FindComponentByClass<USkeletalMeshComponent>();
        if (SkeletalMeshComp)
        {
            GroomComponent->AttachToComponent(SkeletalMeshComp, FAttachmentTransformRules::KeepWorldTransform, Parameters.AttachmentSocket);
        }
    }

    // Configure Groom Component using UE 5.6 Groom Component API
    GroomComponent->SetGroomAsset(GroomAsset);

    if (BindingAsset)
    {
        GroomComponent->SetBindingAsset(BindingAsset);
    }

    // Set component properties
    GroomComponent->SetVisibility(Parameters.bVisible);
    GroomComponent->SetCastShadow(Parameters.bCastShadows);
    GroomComponent->SetReceivesDecals(Parameters.bReceiveDecals);

    // Configure physics if enabled
    if (Parameters.bEnablePhysics)
    {
        GroomComponent->SetEnableSimulation(true);
    }

    // Register component with UE 5.6 Component system
    GroomComponent->RegisterComponent();

    UE_LOG(LogAuracronHairSystem, Log, TEXT("AttachHairToCharacter: Successfully attached hair to character"));
    return GroomComponent;
}

bool FAuracronHairSystem::ValidateHairGenerationParameters(const FHairSystemGenerationParameters& Parameters, FString& OutError)
{
    // Validate hair name
    if (Parameters.HairName.IsEmpty())
    {
        OutError = TEXT("Hair name cannot be empty");
        return false;
    }

    // Validate target skeleton
    if (!Parameters.TargetSkeleton)
    {
        OutError = TEXT("Target skeleton is required");
        return false;
    }

    // Validate strand parameters
    if (Parameters.StrandCount <= 0)
    {
        OutError = TEXT("Strand count must be greater than 0");
        return false;
    }

    if (Parameters.StrandLength <= 0.0f)
    {
        OutError = TEXT("Strand length must be greater than 0");
        return false;
    }

    if (Parameters.StrandWidth <= 0.0f)
    {
        OutError = TEXT("Strand width must be greater than 0");
        return false;
    }

    // Validate scale parameters
    if (Parameters.RootScale <= 0.0f || Parameters.TipScale <= 0.0f)
    {
        OutError = TEXT("Root and tip scale must be greater than 0");
        return false;
    }

    // Validate physics parameters if physics is enabled
    if (Parameters.bEnablePhysics)
    {
        if (Parameters.PhysicsStiffness < 0.0f || Parameters.PhysicsStiffness > 1.0f)
        {
            OutError = TEXT("Physics stiffness must be between 0 and 1");
            return false;
        }

        if (Parameters.PhysicsDamping < 0.0f || Parameters.PhysicsDamping > 1.0f)
        {
            OutError = TEXT("Physics damping must be between 0 and 1");
            return false;
        }
    }

    return true;
}

FString FAuracronHairSystem::CalculateHairGenerationHash(const FHairSystemGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE 5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%.2f_%.3f"),
        *Parameters.HairName,
        Parameters.TargetSkeleton ? *Parameters.TargetSkeleton->GetName() : TEXT("NoSkeleton"),
        Parameters.StrandCount,
        Parameters.StrandLength,
        Parameters.StrandWidth
    );

    // Add material hash if present
    if (Parameters.HairMaterial)
    {
        BaseKey += FString::Printf(TEXT("_mat%s"), *Parameters.HairMaterial->GetName());
    }

    // Add color hash
    BaseKey += FString::Printf(TEXT("_color%.2f%.2f%.2f"),
        Parameters.HairColor.R, Parameters.HairColor.G, Parameters.HairColor.B);

    // Add physics parameters if enabled
    if (Parameters.bEnablePhysics)
    {
        BaseKey += FString::Printf(TEXT("_phys%.2f%.2f"),
            Parameters.PhysicsStiffness, Parameters.PhysicsDamping);
    }

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}
