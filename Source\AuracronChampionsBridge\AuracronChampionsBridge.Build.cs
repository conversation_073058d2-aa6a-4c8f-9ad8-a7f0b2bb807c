﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de CampeÃµes Bridge Build Configuration
using UnrealBuildTool;
public class AuracronChampionsBridge : ModuleRules
{
    public AuracronChampionsBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        // Include paths sÃ£o gerenciados automaticamente pelos mÃ³dulos
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "EnhancedInput",
                "InputCore","NavigationSystem",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings",
                "AnimGraphRuntime",
                "AnimationCore",
                "ControlRig",
                "I<PERSON>Rig",
                "Niagara",}
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "NiagaraCore",
                "NiagaraShader",
                "NiagaraAnimNotifies",
                "ChaosCore",
                "PhysicsCore",
                "AudioMixer",
                "MetasoundFrontend",
                "MetasoundStandardNodes",
                "SignalProcessing",
                "OnlineSubsystemUtils",
                "Sockets",
                "Networking",
                "PacketHandler",
                "ReliabilityHandlerComponent",
                "MeshDescription",
                "StaticMeshDescription",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus",
                    "AnimationBlueprintLibrary",
                    "Persona",
                    "SkeletalMeshEditor",
                    "AnimationEditor",
                    "AnimationBlueprintEditor",
                    "ControlRigEditor",
                    "IKRigEditor"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {"OnlineSubsystemSteam"}
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_CHAMPIONS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_CHAMPIONS_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_CHAMPIONS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_CHAMPIONS_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_GAMEPLAY_ABILITY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_METAHUMAN_SDK=1");
        PublicDefinitions.Add("WITH_CONTROL_RIG=1");
        PublicDefinitions.Add("WITH_IK_RIG=1");
    }
}


