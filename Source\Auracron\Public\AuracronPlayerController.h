/**
 * AuracronPlayerController.h
 * 
 * PlayerController específico do Auracron que integra com todos os bridges
 * e fornece interface para o jogador interagir com os sistemas do jogo.
 * 
 * Responsabilidades:
 * - Gerenciar input do jogador usando Enhanced Input System
 * - Interface com sistema de sígilos e campeões
 * - Coordenar com trilhos dinâmicos e transições verticais
 * - Integrar com Harmony Engine para experiência positiva
 * - Gerenciar UI e HUD do jogador
 * 
 * Usa UE 5.6 PlayerController moderno com integração completa dos bridges.
 */

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "InputActionValue.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronPlayerController.generated.h"

// Forward declarations
class UInputMappingContext;
class UInputAction;
// Temporariamente removidas para compilação inicial
// class UAuracronSigilosBridge;
// class UAuracronVerticalTransitionsBridge;
// class UHarmonyEngineSubsystem;
class AAuracronGameMode;

/**
 * Estados do jogador
 */
UENUM(BlueprintType)
enum class EAuracronPlayerState : uint8
{
    Disconnected        UMETA(DisplayName = "Disconnected"),
    InLobby             UMETA(DisplayName = "In Lobby"),
    SelectingChampion   UMETA(DisplayName = "Selecting Champion"),
    InGame              UMETA(DisplayName = "In Game"),
    Spectating          UMETA(DisplayName = "Spectating"),
    PostGame            UMETA(DisplayName = "Post Game")
};

/**
 * Dados do campeão selecionado
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronChampionData
{
    GENERATED_BODY()

    /** ID do campeão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion")
    int32 ChampionID = -1;

    /** Nome do campeão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion")
    FString ChampionName;

    /** Sígilos equipados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion")
    TArray<FGameplayTag> EquippedSigils;

    /** Nível do campeão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion")
    int32 ChampionLevel = 1;

    /** Experiência do campeão */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion")
    float ChampionExperience = 0.0f;

    FAuracronChampionData()
    {
        ChampionID = -1;
        ChampionName = TEXT("");
        EquippedSigils.Empty();
        ChampionLevel = 1;
        ChampionExperience = 0.0f;
    }
};

/**
 * Configurações de input do jogador
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAuracronInputSettings
{
    GENERATED_BODY()

    /** Sensibilidade do mouse */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
    float MouseSensitivity = 1.0f;

    /** Inverter eixo Y */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
    bool bInvertYAxis = false;

    /** Habilitar auto-run */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
    bool bEnableAutoRun = false;

    /** Sensibilidade de gamepad */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
    float GamepadSensitivity = 1.0f;

    FAuracronInputSettings()
    {
        MouseSensitivity = 1.0f;
        bInvertYAxis = false;
        bEnableAutoRun = false;
        GamepadSensitivity = 1.0f;
    }
};

/**
 * PlayerController do Auracron
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRON_API AAuracronPlayerController : public APlayerController
{
    GENERATED_BODY()

public:
    AAuracronPlayerController();

    // PlayerController interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void SetupInputComponent() override;
    virtual void Tick(float DeltaTime) override;

    // === Player State Management ===
    
    /** Obter estado atual do jogador */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Player")
    EAuracronPlayerState GetCurrentPlayerState() const { return CurrentPlayerState; }

    /** Obter dados do campeão */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Player")
    FAuracronChampionData GetChampionData() const { return ChampionData; }

    /** Obter configurações de input */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron Player")
    FAuracronInputSettings GetInputSettings() const { return InputSettings; }

    // === Champion Selection ===
    
    /** Selecionar campeão */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void SelectChampion(int32 ChampionID);

    /** Equipar sígilo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void EquipSigil(FGameplayTag SigilTag);

    /** Remover sígilo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void UnequipSigil(FGameplayTag SigilTag);

    /** Confirmar seleção de campeão */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void ConfirmChampionSelection();

    // === Gameplay Actions ===
    
    /** Usar trilho dinâmico */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void UseDynamicRail();

    /** Usar transição vertical */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void UseVerticalTransition();

    /** Ativar sígilo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void ActivateSigil(int32 SigilSlot);

    /** Interagir com objetivo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void InteractWithObjective();

    // === UI Management ===
    
    /** Mostrar/esconder menu principal */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void ToggleMainMenu();

    /** Mostrar/esconder inventário */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void ToggleInventory();

    /** Mostrar/esconder configurações */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void ToggleSettings();

    /** Atualizar configurações de input */
    UFUNCTION(BlueprintCallable, Category = "Auracron Player")
    void UpdateInputSettings(const FAuracronInputSettings& NewSettings);

    // === Events ===
    
    /** Evento quando estado do jogador muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerStateChanged, EAuracronPlayerState, OldState, EAuracronPlayerState, NewState);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Player")
    FOnPlayerStateChanged OnPlayerStateChanged;

    /** Evento quando campeão é selecionado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnChampionSelected, FAuracronChampionData, ChampionData);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Player")
    FOnChampionSelected OnChampionSelected;

    /** Evento quando sígilo é equipado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSigilEquipped, FGameplayTag, SigilTag);
    UPROPERTY(BlueprintAssignable, Category = "Auracron Player")
    FOnSigilEquipped OnSigilEquipped;

protected:
    // === Enhanced Input ===
    
    /** Input Mapping Context */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputMappingContext> DefaultMappingContext;

    /** Input Actions */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> MoveAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> LookAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> JumpAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> InteractAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> UseSigil1Action;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> UseSigil2Action;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> UseSigil3Action;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> MenuAction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> InventoryAction;

    // === State ===
    
    /** Estado atual do jogador */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    EAuracronPlayerState CurrentPlayerState;

    /** Dados do campeão */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    FAuracronChampionData ChampionData;

    /** Configurações de input */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    FAuracronInputSettings InputSettings;

    /** Equipe do jogador */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    int32 PlayerTeam = -1;

    // === Bridge References ===
    // Usando UObject* para evitar problemas de linkagem até os bridges estarem prontos

    /** Harmony Engine Subsystem */
    UPROPERTY()
    TObjectPtr<UObject> HarmonyEngineSubsystem;

    /** Game Mode Reference */
    UPROPERTY()
    TObjectPtr<AAuracronGameMode> AuracronGameMode;

    // === Configuration ===
    
    /** Habilitar logs detalhados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bVerboseLogging = true;

private:
    // === Input Handlers ===
    void Move(const FInputActionValue& Value);
    void Look(const FInputActionValue& Value);
    void Jump(const FInputActionValue& Value);
    void Interact(const FInputActionValue& Value);
    void UseSigil1(const FInputActionValue& Value);
    void UseSigil2(const FInputActionValue& Value);
    void UseSigil3(const FInputActionValue& Value);
    void OpenMenu(const FInputActionValue& Value);
    void OpenInventory(const FInputActionValue& Value);

    // === Implementation ===
    void ChangePlayerState(EAuracronPlayerState NewState);
    void InitializeBridgeReferences();
    void SetupEnhancedInput();
    void UpdatePlayerLogic(float DeltaTime);
    void ValidateChampionSelection();
    void NotifyHarmonyEngine();

    /** Flag de inicialização */
    bool bPlayerInitialized = false;
};
