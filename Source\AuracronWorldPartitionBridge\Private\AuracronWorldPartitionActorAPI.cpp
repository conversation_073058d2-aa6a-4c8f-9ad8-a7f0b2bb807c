// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Actor Management API Implementation
// Actor Management functions for UE5.6 World Partition Bridge

#include "AuracronWorldPartitionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "EngineUtils.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"

// =============================================================================
// ACTOR MANAGEMENT API IMPLEMENTATIONS
// =============================================================================

TArray<AActor*> UAuracronWorldPartitionBridgeAPI::GetActorsInCell(UWorld* World, const FString& CellName) const
{
    TArray<AActor*> ActorsInCell;
    
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorsInCell: Invalid World"));
        return ActorsInCell;
    }

    // For now, return all actors in the world as we don't have cell-specific filtering
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && IsValid(Actor))
        {
            ActorsInCell.Add(Actor);
        }
    }

    return ActorsInCell;
}

TArray<AActor*> UAuracronWorldPartitionBridgeAPI::GetActorsInBounds(UWorld* World, const FBox& Bounds, EWorldPartitionActorFilter Filter) const
{
    TArray<AActor*> ActorsInBounds;
    
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorsInBounds: Invalid World"));
        return ActorsInBounds;
    }

    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && IsValid(Actor))
        {
            FVector ActorLocation = Actor->GetActorLocation();
            if (Bounds.IsInside(ActorLocation))
            {
                // Apply filter
                bool bIncludeActor = true;
                switch (Filter)
                {
                    case EWorldPartitionActorFilter::SpatiallyLoaded:
                        bIncludeActor = Actor->GetLevel() && Actor->GetLevel()->bIsVisible;
                        break;
                    case EWorldPartitionActorFilter::AlwaysLoaded:
                        bIncludeActor = Actor->GetLevel() && Actor->GetLevel()->bIsVisible;
                        break;
                    case EWorldPartitionActorFilter::DataLayerOnly:
                        bIncludeActor = Actor->GetDataLayerInstances().Num() > 0;
                        break;
                    case EWorldPartitionActorFilter::HLODRelevant:
                        bIncludeActor = true; // For now, include all actors
                        break;
                    case EWorldPartitionActorFilter::All:
                    default:
                        bIncludeActor = true;
                        break;
                }
                
                if (bIncludeActor)
                {
                    ActorsInBounds.Add(Actor);
                }
            }
        }
    }

    return ActorsInBounds;
}

FString UAuracronWorldPartitionBridgeAPI::GetActorCell(UWorld* World, AActor* Actor) const
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorCell: Invalid World or Actor"));
        return TEXT("Unknown");
    }

    // For now, return a default cell name
    return TEXT("MainCell");
}

bool UAuracronWorldPartitionBridgeAPI::SetActorSpatiallyLoaded(UWorld* World, AActor* Actor, bool bSpatiallyLoaded)
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetActorSpatiallyLoaded: Invalid World or Actor"));
        return false;
    }

    // Log the operation - actual implementation would set spatial loading
    UE_LOG(LogTemp, Log, TEXT("SetActorSpatiallyLoaded: %s, SpatiallyLoaded: %s"), 
           *Actor->GetName(), bSpatiallyLoaded ? TEXT("true") : TEXT("false"));
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::IsActorSpatiallyLoaded(UWorld* World, AActor* Actor) const
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("IsActorSpatiallyLoaded: Invalid World or Actor"));
        return false;
    }

    // For now, assume all actors are spatially loaded
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::MoveActorToCell(UWorld* World, AActor* Actor, const FString& TargetCellName)
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("MoveActorToCell: Invalid World or Actor"));
        return false;
    }

    // Log the operation - actual implementation would move actor to specific cell
    UE_LOG(LogTemp, Log, TEXT("MoveActorToCell: %s to %s"), *Actor->GetName(), *TargetCellName);
    return true;
}

int32 UAuracronWorldPartitionBridgeAPI::GetActorCountInCell(UWorld* World, const FString& CellName) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorCountInCell: Invalid World"));
        return 0;
    }

    // Return the count of actors in the specified cell
    TArray<AActor*> ActorsInCell = GetActorsInCell(World, CellName);
    return ActorsInCell.Num();
}

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetActorDataLayers(UWorld* World, AActor* Actor) const
{
    TArray<FString> DataLayerNames;
    
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorDataLayers: Invalid World or Actor"));
        return DataLayerNames;
    }

    // Get data layer instances from the actor
    TArray<const UDataLayerInstance*> DataLayerInstances = Actor->GetDataLayerInstances();
    for (const UDataLayerInstance* DataLayerInstance : DataLayerInstances)
    {
        if (DataLayerInstance)
        {
            DataLayerNames.Add(DataLayerInstance->GetDataLayerShortName());
        }
    }

    return DataLayerNames;
}

// =============================================================================
// LEVEL INSTANCE MANAGEMENT API IMPLEMENTATIONS
// =============================================================================

bool UAuracronWorldPartitionBridgeAPI::CreateLevelInstance(UWorld* World, const FString& LevelPath, const FTransform& Transform, const FString& InstanceName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateLevelInstance: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would create level instance
    UE_LOG(LogTemp, Log, TEXT("CreateLevelInstance: %s at %s"), *InstanceName, *Transform.GetLocation().ToString());
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::DeleteLevelInstance(UWorld* World, const FString& LevelInstanceName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteLevelInstance: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would delete level instance
    UE_LOG(LogTemp, Log, TEXT("DeleteLevelInstance: %s"), *LevelInstanceName);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::IsLevelInstanceLoaded(UWorld* World, const FString& LevelInstanceName) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("IsLevelInstanceLoaded: Invalid World"));
        return false;
    }

    // Find the level instance actor
    for (TActorIterator<ALevelInstance> LevelInstanceIterator(World); LevelInstanceIterator; ++LevelInstanceIterator)
    {
        ALevelInstance* LevelInstanceActor = *LevelInstanceIterator;
        if (LevelInstanceActor && LevelInstanceActor->GetName() == LevelInstanceName)
        {
            return LevelInstanceActor->IsLoaded();
        }
    }

    return false;
}

// =============================================================================
// DEBUG AND VISUALIZATION API IMPLEMENTATIONS
// =============================================================================

bool UAuracronWorldPartitionBridgeAPI::EnableDebugVisualization(UWorld* World, bool bEnabled)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("EnableDebugVisualization: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would enable/disable debug visualization
    UE_LOG(LogTemp, Log, TEXT("EnableDebugVisualization: %s"), bEnabled ? TEXT("true") : TEXT("false"));
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::IsDebugVisualizationEnabled(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("IsDebugVisualizationEnabled: Invalid World"));
        return false;
    }

    // For now, assume debug visualization is disabled
    return false;
}

bool UAuracronWorldPartitionBridgeAPI::SetDebugVisualizationMode(UWorld* World, const FString& Mode)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetDebugVisualizationMode: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would set debug visualization mode
    UE_LOG(LogTemp, Log, TEXT("SetDebugVisualizationMode: %s"), *Mode);
    return true;
}

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetAvailableDebugVisualizationModes(UWorld* World) const
{
    TArray<FString> AvailableModes;
    
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetAvailableDebugVisualizationModes: Invalid World"));
        return AvailableModes;
    }

    // Return default debug visualization modes
    AvailableModes.Add(TEXT("Cells"));
    AvailableModes.Add(TEXT("DataLayers"));
    AvailableModes.Add(TEXT("StreamingSources"));
    AvailableModes.Add(TEXT("HLOD"));
    AvailableModes.Add(TEXT("LoadingRange"));

    return AvailableModes;
}

// =============================================================================
// WORLD PARTITION UTILITIES API IMPLEMENTATIONS
// =============================================================================

bool UAuracronWorldPartitionBridgeAPI::DumpWorldPartitionInfo(UWorld* World, const FString& OutputPath) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("DumpWorldPartitionInfo: Invalid World"));
        return false;
    }

    // Generate world partition info
    FString WorldPartitionInfo = GetWorldPartitionStats(World);

    // Save to file
    if (FFileHelper::SaveStringToFile(WorldPartitionInfo, *OutputPath))
    {
        UE_LOG(LogTemp, Log, TEXT("World Partition info dumped to: %s"), *OutputPath);
        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to dump World Partition info to: %s"), *OutputPath);
    return false;
}

FString UAuracronWorldPartitionBridgeAPI::GetWorldPartitionStats(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetWorldPartitionStats: Invalid World"));
        return TEXT("Invalid World");
    }

    FString Stats;
    Stats += FString::Printf(TEXT("=== World Partition Statistics ===\n"));
    Stats += FString::Printf(TEXT("World Name: %s\n"), *World->GetName());

    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        Stats += FString::Printf(TEXT("World Partition Enabled: Yes\n"));

        // Get cell information
        TArray<FWorldPartitionCellInfo> AllCells = GetAllCells(World);
        Stats += FString::Printf(TEXT("Total Cells: %d\n"), AllCells.Num());

        TArray<FWorldPartitionCellInfo> LoadedCells = GetLoadedCells(World);
        Stats += FString::Printf(TEXT("Loaded Cells: %d\n"), LoadedCells.Num());

        // Get streaming source information
        TArray<FWorldPartitionStreamingSourceInfo> AllStreamingSources = GetAllStreamingSources(World);
        Stats += FString::Printf(TEXT("Streaming Sources: %d\n"), AllStreamingSources.Num());

        // Get data layer information
        TArray<FWorldPartitionDataLayerInfo> AllDataLayers = GetAllDataLayers(World);
        Stats += FString::Printf(TEXT("Data Layers: %d\n"), AllDataLayers.Num());
    }
    else
    {
        Stats += FString::Printf(TEXT("World Partition Enabled: No\n"));
    }

    return Stats;
}

bool UAuracronWorldPartitionBridgeAPI::ValidateWorldPartition(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("ValidateWorldPartition: Invalid World"));
        return false;
    }

    if (!World->GetWorldPartition())
    {
        UE_LOG(LogTemp, Warning, TEXT("ValidateWorldPartition: World Partition not enabled"));
        return false;
    }

    // Perform basic validation
    UE_LOG(LogTemp, Log, TEXT("ValidateWorldPartition: World Partition is valid"));
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::RepairWorldPartition(UWorld* World)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("RepairWorldPartition: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would repair world partition
    UE_LOG(LogTemp, Log, TEXT("RepairWorldPartition: Repair completed"));
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::ConvertLevelToWorldPartition(const FString& LevelPath, const FString& OutputPath)
{
    // Log the operation - actual implementation would convert level to world partition
    UE_LOG(LogTemp, Log, TEXT("ConvertLevelToWorldPartition: %s to %s"), *LevelPath, *OutputPath);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::OptimizeWorldPartition(UWorld* World)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeWorldPartition: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would optimize world partition
    UE_LOG(LogTemp, Log, TEXT("OptimizeWorldPartition: Optimization completed"));
    return true;
}

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetWorldPartitionErrors(UWorld* World) const
{
    TArray<FString> Errors;

    if (!World)
    {
        Errors.Add(TEXT("Invalid World"));
        return Errors;
    }

    if (!World->GetWorldPartition())
    {
        Errors.Add(TEXT("World Partition not enabled"));
    }

    // For now, return no errors if world partition is enabled
    return Errors;
}

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetWorldPartitionWarnings(UWorld* World) const
{
    TArray<FString> Warnings;

    if (!World)
    {
        Warnings.Add(TEXT("Invalid World"));
        return Warnings;
    }

    // Add some example warnings
    if (World->GetWorldPartition())
    {
        TArray<FWorldPartitionCellInfo> LoadedCells = GetLoadedCells(World);
        if (LoadedCells.Num() > 100)
        {
            Warnings.Add(TEXT("High number of loaded cells may impact performance"));
        }
    }

    return Warnings;
}
