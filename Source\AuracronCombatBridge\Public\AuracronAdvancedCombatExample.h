// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Advanced Combat System Example Implementation

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "AuracronCombatBridge.h"
#include "AuracronAdvancedCombatExample.generated.h"

/**
 * Example implementation showcasing advanced combat features
 * This demonstrates how to use the expanded AuracronCombatBridge with UE 5.6 features
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONCOMBABRIDGE_API AAuracronAdvancedCombatExample : public AActor
{
    GENERATED_BODY()

public:
    AAuracronAdvancedCombatExample();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    /** Combat Bridge Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
    TObjectPtr<UAuracronCombatBridge> CombatBridge;

    /** Example Enhanced Input Configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    FAuracronEnhancedInputConfig ExampleInputConfig;

    /** Example AI Combat Configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    FAuracronAICombatConfig ExampleAIConfig;

    /** Example Elemental Damage Configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental System")
    FAuracronElementalDamageConfig ExampleElementalConfig;

    /** Example Combo Configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<FAuracronComboConfig> ExampleCombos;

    /** Example Advanced Destruction Configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction")
    FAuracronAdvancedDestructionConfig ExampleDestructionConfig;

public:
    // === Enhanced Input Examples ===

    /** Initialize enhanced input system with example configuration */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Enhanced Input")
    void InitializeEnhancedInputExample();

    /** Setup example combo system */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Enhanced Input")
    void SetupExampleCombos();

    /** Execute example combo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Enhanced Input")
    void ExecuteExampleCombo(EAuracronComboType ComboType);

    // === AI Combat Examples ===

    /** Initialize AI combat with example behavior */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|AI Combat")
    void InitializeAICombatExample();

    /** Demonstrate AI behavior adaptation */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|AI Combat")
    void DemonstrateAIAdaptation();

    /** Update AI combat state with example target */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|AI Combat")
    void UpdateAICombatExample(AActor* TargetActor);

    // === Elemental System Examples ===

    /** Initialize elemental system with example configuration */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Elemental")
    void InitializeElementalSystemExample();

    /** Demonstrate elemental damage application */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Elemental")
    void DemonstrateElementalDamage(AActor* TargetActor, EAuracronElementalType ElementType);

    /** Demonstrate elemental interactions */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Elemental")
    void DemonstrateElementalInteractions(const FVector& Location);

    // === Analytics Examples ===

    /** Demonstrate combat analytics */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Analytics")
    void DemonstrateCombatAnalytics();

    /** Export example analytics data */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Analytics")
    void ExportExampleAnalytics();

    // === Advanced Destruction Examples ===

    /** Initialize advanced destruction system */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Destruction")
    void InitializeAdvancedDestructionExample();

    /** Demonstrate advanced destruction */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Destruction")
    void DemonstrateAdvancedDestruction(const FVector& Location);

    /** Demonstrate procedural damage */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Destruction")
    void DemonstrateProceduralDamage(AActor* TargetActor);

    // === Complete Combat Scenario ===

    /** Run complete advanced combat scenario */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Example|Complete")
    void RunAdvancedCombatScenario();

protected:
    // === Event Handlers ===

    UFUNCTION()
    void OnComboExecutedExample(EAuracronComboType ComboType, int32 ComboStep, float DamageMultiplier);

    UFUNCTION()
    void OnElementalDamageAppliedExample(AActor* TargetActor, EAuracronElementalType ElementType, float Damage, bool bStatusEffectApplied);

    UFUNCTION()
    void OnAICombatDecisionExample(EAuracronAICombatBehavior BehaviorType, FString DecisionType, float ConfidenceLevel);

    UFUNCTION()
    void OnAdvancedDestructionExample(FVector Location, float DestructionForce, int32 AffectedObjects);

    UFUNCTION()
    void OnCombatAnalyticsUpdatedExample(FAuracronCombatAnalytics Analytics, float EfficiencyScore);

private:
    /** Example target actor for demonstrations */
    UPROPERTY()
    TObjectPtr<AActor> ExampleTarget;

    /** Timer for running periodic examples */
    FTimerHandle ExampleTimer;

    /** Current example phase */
    int32 CurrentExamplePhase;

    /** Initialize example configurations */
    void InitializeExampleConfigurations();

    /** Create example target */
    void CreateExampleTarget();

    /** Log example results */
    void LogExampleResults(const FString& ExampleName, const FString& Results);
};
