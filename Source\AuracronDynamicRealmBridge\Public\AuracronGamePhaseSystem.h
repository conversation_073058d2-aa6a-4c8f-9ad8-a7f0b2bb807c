/**
 * AuracronGamePhaseSystem.h
 * 
 * Sistema completo de fases da partida para gerenciar a evolução temporal
 * do mapa e mecânicas de jogo conforme especificado no design document.
 * 
 * Implementa as 4 fases específicas:
 * 1. DESPERTAR (0-15 min) - Apenas Planície Radiante ativa
 * 2. CONVERGÊNCIA (15-25 min) - Firmamento Zephyr ativa
 * 3. INTENSIFICAÇÃO (25-35 min) - Abismo Umbrio ativa
 * 4. RESOLUÇÃO (35+ min) - <PERSON><PERSON> as camadas em máxima intensidade
 * 
 * Usa UE 5.6 APIs modernas para transições suaves e otimização adaptativa.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/World.h"
#include "GameFramework/GameStateBase.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "AuracronGamePhaseSystem.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class UAuracronHardwareDetectionSystem;
class AAuracronPrismalFlow;
class AAuracronDynamicRail;

/**
 * Fases da partida
 */
UENUM(BlueprintType)
enum class EGamePhase : uint8
{
    None                UMETA(DisplayName = "None"),
    Despertar           UMETA(DisplayName = "Despertar (0-15min)"),
    Convergencia        UMETA(DisplayName = "Convergência (15-25min)"),
    Intensificacao      UMETA(DisplayName = "Intensificação (25-35min)"),
    Resolucao           UMETA(DisplayName = "Resolução (35+min)")
};

/**
 * Estados de transição de fase
 */
UENUM(BlueprintType)
enum class EPhaseTransitionState : uint8
{
    Stable              UMETA(DisplayName = "Stable"),
    Preparing           UMETA(DisplayName = "Preparing"),
    Transitioning       UMETA(DisplayName = "Transitioning"),
    Completing          UMETA(DisplayName = "Completing"),
    Error               UMETA(DisplayName = "Error")
};

/**
 * Tipos de camada de realm
 */
UENUM(BlueprintType)
enum class ERealmLayerType : uint8
{
    None                UMETA(DisplayName = "None"),
    PlanicieRadiante    UMETA(DisplayName = "Planície Radiante"),
    FirmamentoZephyr    UMETA(DisplayName = "Firmamento Zephyr"),
    AbismoUmbrio        UMETA(DisplayName = "Abismo Umbrio")
};

/**
 * Configuração de fase
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FGamePhaseConfig
{
    GENERATED_BODY()

    /** Fase do jogo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    EGamePhase Phase;

    /** Duração mínima da fase em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float MinDurationSeconds;

    /** Duração máxima da fase em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float MaxDurationSeconds;

    /** Camadas ativas nesta fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    TArray<ERealmLayerType> ActiveLayers;

    /** Intensidade dos efeitos (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float EffectIntensity;

    /** Velocidade dos trilhos (multiplicador) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float RailSpeedMultiplier;

    /** Intensidade do Fluxo Prismal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float PrismalFlowIntensity;

    /** Frequência de spawn de objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float ObjectiveSpawnFrequency;

    /** Multiplicador de experiência */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float ExperienceMultiplier;

    /** Multiplicador de ouro */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    float GoldMultiplier;

    /** Efeitos visuais da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    TObjectPtr<UNiagaraSystem> PhaseVFX;

    /** Música da fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    TObjectPtr<USoundBase> PhaseMusic;

    /** Curva de transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    TObjectPtr<UCurveFloat> TransitionCurve;

    /** Adaptação baseada em hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    bool bAdaptToHardware;

    /** Configurações específicas por tier de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Config")
    TMap<int32, float> HardwareTierMultipliers;

    FGamePhaseConfig()
    {
        Phase = EGamePhase::None;
        MinDurationSeconds = 900.0f; // 15 minutes
        MaxDurationSeconds = 900.0f;
        EffectIntensity = 1.0f;
        RailSpeedMultiplier = 1.0f;
        PrismalFlowIntensity = 1.0f;
        ObjectiveSpawnFrequency = 1.0f;
        ExperienceMultiplier = 1.0f;
        GoldMultiplier = 1.0f;
        PhaseVFX = nullptr;
        PhaseMusic = nullptr;
        TransitionCurve = nullptr;
        bAdaptToHardware = true;
    }
};

/**
 * Dados de transição de fase
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPhaseTransitionData
{
    GENERATED_BODY()

    /** Fase de origem */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    EGamePhase FromPhase;

    /** Fase de destino */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    EGamePhase ToPhase;

    /** Estado da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    EPhaseTransitionState TransitionState;

    /** Progresso da transição (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float TransitionProgress;

    /** Tempo de início da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float StartTime;

    /** Duração da transição */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    float Duration;

    /** Componentes de efeito ativados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    TArray<TObjectPtr<UNiagaraComponent>> ActiveEffects;

    /** Componente de áudio ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition Data")
    TObjectPtr<UAudioComponent> ActiveAudio;

    FPhaseTransitionData()
    {
        FromPhase = EGamePhase::None;
        ToPhase = EGamePhase::None;
        TransitionState = EPhaseTransitionState::Stable;
        TransitionProgress = 0.0f;
        StartTime = 0.0f;
        Duration = 5.0f;
        ActiveAudio = nullptr;
    }
};

/**
 * Métricas de fase
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPhaseMetrics
{
    GENERATED_BODY()

    /** Tempo total na fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Metrics")
    float TimeInPhase;

    /** Número de kills na fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Metrics")
    int32 KillsInPhase;

    /** Ouro ganho na fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Metrics")
    float GoldEarnedInPhase;

    /** Objetivos completados na fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Metrics")
    int32 ObjectivesCompletedInPhase;

    /** Performance média na fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Phase Metrics")
    float AveragePerformanceInPhase;

    FPhaseMetrics()
    {
        TimeInPhase = 0.0f;
        KillsInPhase = 0;
        GoldEarnedInPhase = 0.0f;
        ObjectivesCompletedInPhase = 0;
        AveragePerformanceInPhase = 0.0f;
    }
};

/**
 * Sistema de fases da partida
 * 
 * Gerencia a evolução temporal do jogo através das 4 fases específicas
 */
UCLASS(BlueprintType)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronGamePhaseSystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    void Tick(float DeltaTime); // Custom tick method, not overriding base class
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // === Core Phase Management ===
    
    /** Inicializar sistema de fases */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void InitializePhaseSystem();

    /** Iniciar partida com fase inicial */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void StartMatch();

    /** Finalizar partida */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void EndMatch();

    /** Forçar transição para próxima fase */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void ForceNextPhase();

    /** Forçar transição para fase específica */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void ForcePhaseTransition(EGamePhase TargetPhase);

    // === Phase Information ===
    
    /** Obter fase atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    EGamePhase GetCurrentPhase() const;

    /** Obter progresso da fase atual (0.0 - 1.0) */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    float GetCurrentPhaseProgress() const;

    /** Obter tempo restante na fase atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    float GetTimeRemainingInPhase() const;

    /** Obter configuração da fase atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    FGamePhaseConfig GetCurrentPhaseConfig() const;

    /** Verificar se está em transição */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    bool IsInTransition() const;

    /** Obter dados da transição atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    FPhaseTransitionData GetCurrentTransitionData() const;

    // === Phase Metrics ===
    
    /** Obter métricas da fase atual */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    FPhaseMetrics GetCurrentPhaseMetrics() const;

    /** Obter métricas de todas as fases */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    TMap<EGamePhase, FPhaseMetrics> GetAllPhaseMetrics() const;

    /** Atualizar métricas da fase */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void UpdatePhaseMetrics(int32 Kills, float Gold, int32 Objectives);

    // === Configuration ===
    
    /** Configurar fase específica */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void ConfigurePhase(EGamePhase Phase, const FGamePhaseConfig& Config);

    /** Obter configuração de fase */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Game Phases")
    FGamePhaseConfig GetPhaseConfiguration(EGamePhase Phase) const;

    /** Aplicar configurações baseadas em hardware */
    UFUNCTION(BlueprintCallable, Category = "Game Phases")
    void ApplyHardwareBasedConfigurations();

    // === Events ===
    
    /** Evento quando fase muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "Game Phases")
    void OnPhaseChanged(EGamePhase OldPhase, EGamePhase NewPhase);

    /** Evento quando transição de fase inicia */
    UFUNCTION(BlueprintImplementableEvent, Category = "Game Phases")
    void OnPhaseTransitionStarted(EGamePhase FromPhase, EGamePhase ToPhase);

    /** Evento quando transição de fase completa */
    UFUNCTION(BlueprintImplementableEvent, Category = "Game Phases")
    void OnPhaseTransitionCompleted(EGamePhase FromPhase, EGamePhase ToPhase);

    /** Evento quando partida inicia */
    UFUNCTION(BlueprintImplementableEvent, Category = "Game Phases")
    void OnMatchStarted();

    /** Evento quando partida termina */
    UFUNCTION(BlueprintImplementableEvent, Category = "Game Phases")
    void OnMatchEnded();

protected:
    // === Configuration ===
    
    /** Configurações de todas as fases */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EGamePhase, FGamePhaseConfig> PhaseConfigurations;

    /** Duração padrão das transições */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float DefaultTransitionDuration;

    /** Adaptação automática baseada em hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bAutoAdaptToHardware;

    // === System State ===
    
    /** Fase atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    EGamePhase CurrentPhase;

    /** Dados da transição atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    FPhaseTransitionData CurrentTransition;

    /** Métricas por fase */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    TMap<EGamePhase, FPhaseMetrics> PhaseMetrics;

    /** Tempo de início da fase atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    float CurrentPhaseStartTime;

    /** Tempo total de partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    float TotalMatchTime;

    /** Sistema inicializado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    bool bSystemInitialized;

    /** Partida ativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "System State")
    bool bMatchActive;

private:
    // === Internal Implementation ===
    void InitializeDefaultConfigurations();
    void UpdatePhaseSystem(float DeltaTime);
    void CheckPhaseTransitionConditions();
    void ProcessPhaseTransition(float DeltaTime);
    void CompletePhaseTransition();
    
    // === Phase Implementation ===
    void ActivatePhase(EGamePhase Phase);
    void DeactivatePhase(EGamePhase Phase);
    void ApplyPhaseEffects(EGamePhase Phase, float Intensity);
    void UpdatePhaseIntensity(float DeltaTime);
    
    // === Transition Implementation ===
    void StartPhaseTransition(EGamePhase FromPhase, EGamePhase ToPhase);
    void UpdateTransitionEffects(float DeltaTime);
    void CleanupTransitionEffects();
    
    // === Hardware Adaptation ===
    void AdaptConfigurationToHardware(FGamePhaseConfig& Config);
    float GetHardwareMultiplier(int32 HardwareTier);
    
    // === Utility Methods ===
    bool ShouldTransitionToNextPhase() const;
    EGamePhase GetNextPhase(EGamePhase CurrentPhase) const;
    float CalculatePhaseProgress() const;
    void UpdateRealmLayers();
    void UpdateGameplayMultipliers();
    
    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;
    
    UPROPERTY()
    TObjectPtr<UAuracronHardwareDetectionSystem> CachedHardwareSystem;
    
    // === Timers ===
    FTimerHandle PhaseUpdateTimer;
    FTimerHandle TransitionTimer;
    FTimerHandle MetricsUpdateTimer;
    
    // === Performance Tracking ===
    float LastUpdateTime;
    int32 TotalPhaseTransitions;
    float AverageTransitionTime;
};
