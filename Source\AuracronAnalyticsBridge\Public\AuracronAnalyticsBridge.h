// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Telemetria e Analytics Bridge
// IntegraÃ§Ã£o C++ para analytics usando Unreal Analytics e telemetria customizada com APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/ActorComponent.h"
#include "Analytics.h"
#include "AnalyticsEventAttribute.h"
#include "Interfaces/IAnalyticsProvider.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "Templates/SharedPointer.h"
#include "HAL/ThreadSafeBool.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "AuracronAnalyticsBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de eventos analÃ­ticos
 */
UENUM(BlueprintType)
enum class EAuracronAnalyticsEventType : uint8
{
    None                    UMETA(DisplayName = "None"),
    GameplayEvent           UMETA(DisplayName = "Gameplay Event"),
    PerformanceMetric       UMETA(DisplayName = "Performance Metric"),
    UserBehavior            UMETA(DisplayName = "User Behavior"),
    BalanceData             UMETA(DisplayName = "Balance Data"),
    MonetizationEvent       UMETA(DisplayName = "Monetization Event"),
    TechnicalMetric         UMETA(DisplayName = "Technical Metric"),
    SecurityEvent           UMETA(DisplayName = "Security Event"),
    ABTestEvent             UMETA(DisplayName = "A/B Test Event"),
    CustomEvent             UMETA(DisplayName = "Custom Event")
};

/**
 * EnumeraÃ§Ã£o para categorias de mÃ©tricas de balanceamento
 */
UENUM(BlueprintType)
enum class EAuracronBalanceMetricCategory : uint8
{
    ChampionPerformance     UMETA(DisplayName = "Champion Performance"),
    AbilityUsage            UMETA(DisplayName = "Ability Usage"),
    ItemEffectiveness       UMETA(DisplayName = "Item Effectiveness"),
    SigiloImpact            UMETA(DisplayName = "Sigilo Impact"),
    RealmBalance            UMETA(DisplayName = "Realm Balance"),
    MatchDuration           UMETA(DisplayName = "Match Duration"),
    PlayerProgression       UMETA(DisplayName = "Player Progression"),
    EconomyBalance          UMETA(DisplayName = "Economy Balance"),
    TeamComposition         UMETA(DisplayName = "Team Composition"),
    ObjectiveControl        UMETA(DisplayName = "Objective Control")
};

/**
 * Estrutura para evento analÃ­tico
 */
USTRUCT(BlueprintType)
struct AURACRONANALYTICSBRIDGE_API FAuracronAnalyticsEvent
{
    GENERATED_BODY()

    /** ID Ãºnico do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString EventID;

    /** Nome do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString EventName;

    /** Tipo do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    EAuracronAnalyticsEventType EventType = EAuracronAnalyticsEventType::None;

    /** Categoria do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString EventCategory;

    /** Timestamp do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FDateTime EventTimestamp;

    /** ID do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString PlayerID;

    /** ID da sessÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString SessionID;

    /** ID da partida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FString MatchID;

    /** ParÃ¢metros do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    TMap<FString, FString> EventParameters;

    /** Valores numÃ©ricos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    TMap<FString, float> NumericValues;

    /** LocalizaÃ§Ã£o no mundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FVector WorldLocation = FVector::ZeroVector;

    /** Realm onde ocorreu */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event", meta = (ClampMin = "0", ClampMax = "2"))
    int32 RealmIndex = 0;

    /** Prioridade do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event", meta = (ClampMin = "1", ClampMax = "10"))
    int32 Priority = 5;

    /** Tags do evento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analytics Event")
    FGameplayTagContainer EventTags;
};

/**
 * Estrutura para mÃ©tricas de balanceamento
 */
USTRUCT(BlueprintType)
struct AURACRONANALYTICSBRIDGE_API FAuracronBalanceMetrics
{
    GENERATED_BODY()

    /** Categoria da mÃ©trica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics")
    EAuracronBalanceMetricCategory Category = EAuracronBalanceMetricCategory::ChampionPerformance;

    /** ID do objeto sendo analisado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics")
    FString ObjectID;

    /** Taxa de vitÃ³ria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float WinRate = 0.0f;

    /** Taxa de pick */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PickRate = 0.0f;

    /** Taxa de ban */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float BanRate = 0.0f;

    /** KDA mÃ©dio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0"))
    float AverageKDA = 0.0f;

    /** Dano mÃ©dio por minuto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0"))
    float AverageDamagePerMinute = 0.0f;

    /** Gold mÃ©dio por minuto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0"))
    float AverageGoldPerMinute = 0.0f;

    /** Tempo mÃ©dio de primeira morte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0"))
    float AverageTimeToFirstDeath = 0.0f;

    /** ParticipaÃ§Ã£o em team fights */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TeamFightParticipation = 0.0f;

    /** Efetividade em objetivos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ObjectiveEffectiveness = 0.0f;

    /** NÃºmero de amostras */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics", meta = (ClampMin = "1"))
    int32 SampleSize = 1;

    /** Ãšltima atualizaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balance Metrics")
    FDateTime LastUpdated;
};

/**
 * Estrutura para configuraÃ§Ã£o de A/B Testing
 */
USTRUCT(BlueprintType)
struct AURACRONANALYTICSBRIDGE_API FAuracronABTestConfiguration
{
    GENERATED_BODY()

    /** ID do teste A/B */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FString TestID;

    /** Nome do teste */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FString TestName;

    /** DescriÃ§Ã£o do teste */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FString TestDescription;

    /** Variante A (controle) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    TMap<FString, FString> VariantA;

    /** Variante B (teste) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    TMap<FString, FString> VariantB;

    /** Porcentagem de jogadores na variante B */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float VariantBPercentage = 0.5f;

    /** Teste estÃ¡ ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    bool bIsActive = false;

    /** Data de inÃ­cio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FDateTime StartDate;

    /** Data de fim */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FDateTime EndDate;

    /** MÃ©tricas a serem coletadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    TArray<FString> MetricsToTrack;

    /** CritÃ©rio de sucesso */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration")
    FString SuccessCriteria;

    /** Tamanho mÃ­nimo da amostra */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration", meta = (ClampMin = "100"))
    int32 MinimumSampleSize = 1000;

    /** NÃ­vel de confianÃ§a estatÃ­stica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "A/B Test Configuration", meta = (ClampMin = "0.8", ClampMax = "0.99"))
    float ConfidenceLevel = 0.95f;
};

/**
 * Classe principal do Bridge para Sistema de Telemetria e Analytics
 * ResponsÃ¡vel pela coleta completa de dados para balanceamento e otimizaÃ§Ã£o
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Analytics", meta = (DisplayName = "AURACRON Analytics Bridge", BlueprintSpawnableComponent))
class AURACRONANALYTICSBRIDGE_API UAuracronAnalyticsBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronAnalyticsBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Analytics ===

    /**
     * Registrar evento analÃ­tico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Events", CallInEditor)
    bool RecordAnalyticsEvent(const FAuracronAnalyticsEvent& Event);

    /**
     * Registrar evento de gameplay
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Events", CallInEditor)
    bool RecordGameplayEvent(const FString& EventName, const TMap<FString, FString>& Parameters);

    /**
     * Registrar mÃ©trica de performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Performance", CallInEditor)
    bool RecordPerformanceMetric(const FString& MetricName, float Value, const FString& Context = TEXT(""));

    /**
     * Registrar dados de balanceamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Balance", CallInEditor)
    bool RecordBalanceData(const FAuracronBalanceMetrics& BalanceData);

    // === A/B Testing ===

    /**
     * Iniciar teste A/B
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|ABTesting", CallInEditor)
    bool StartABTest(const FAuracronABTestConfiguration& TestConfig);

    /**
     * Parar teste A/B
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|ABTesting", CallInEditor)
    bool StopABTest(const FString& TestID);

    /**
     * Obter variante do jogador para teste
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|ABTesting", CallInEditor)
    FString GetPlayerVariant(const FString& TestID, const FString& PlayerID);

    /**
     * Registrar resultado de teste A/B
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|ABTesting", CallInEditor)
    bool RecordABTestResult(const FString& TestID, const FString& PlayerID, const FString& Variant, const TMap<FString, float>& Results);

    // === Balance Analytics ===

    /**
     * Coletar mÃ©tricas de campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Balance", CallInEditor)
    bool CollectChampionMetrics(const FString& ChampionID, const TMap<FString, float>& Metrics);

    /**
     * Coletar mÃ©tricas de habilidade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Balance", CallInEditor)
    bool CollectAbilityMetrics(const FString& ChampionID, const FString& AbilitySlot, const TMap<FString, float>& Metrics);

    /**
     * Coletar mÃ©tricas de Sigilo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Balance", CallInEditor)
    bool CollectSigiloMetrics(const FString& SigiloType, const TMap<FString, float>& Metrics);

    /**
     * Coletar mÃ©tricas de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Balance", CallInEditor)
    bool CollectRealmMetrics(int32 RealmIndex, const TMap<FString, float>& Metrics);

    // === User Behavior ===

    /**
     * Rastrear comportamento do jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Behavior", CallInEditor)
    bool TrackPlayerBehavior(const FString& BehaviorType, const TMap<FString, FString>& BehaviorData);

    /**
     * Registrar sessÃ£o de jogo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Behavior", CallInEditor)
    bool RecordGameSession(int32 DurationMinutes, const FString& GameMode, const TMap<FString, FString>& SessionData);

    /**
     * Rastrear engajamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Behavior", CallInEditor)
    bool TrackEngagement(const FString& EngagementType, float Value, const FString& Context = TEXT(""));

    // === Performance Monitoring ===

    /**
     * Monitorar FPS
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Performance", CallInEditor)
    bool MonitorFrameRate(float AverageFPS, float MinFPS, float MaxFPS);

    /**
     * Monitorar uso de memÃ³ria
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Performance", CallInEditor)
    bool MonitorMemoryUsage(float UsedMemoryMB, float AvailableMemoryMB);

    /**
     * Monitorar latÃªncia de rede
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Performance", CallInEditor)
    bool MonitorNetworkLatency(float PingMS, float PacketLossPercentage);

    /**
     * Monitorar tempo de carregamento
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Performance", CallInEditor)
    bool MonitorLoadingTimes(const FString& LoadingStage, float LoadingTimeSeconds);

    // === Data Export ===

    /**
     * Exportar dados analÃ­ticos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Export", CallInEditor)
    bool ExportAnalyticsData(const FString& ExportFormat = TEXT("JSON"));

    /**
     * Sincronizar com Firebase Analytics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Export", CallInEditor)
    bool SyncWithFirebaseAnalytics();

    /**
     * Enviar dados para servidor de analytics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Analytics|Export", CallInEditor)
    bool SendDataToAnalyticsServer();

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de analytics */
    bool InitializeAnalyticsSystem();
    
    /** Configurar providers de analytics */
    bool SetupAnalyticsProviders();
    
    /** Processar fila de eventos */
    void ProcessEventQueue(float DeltaTime);
    
    /** Coletar mÃ©tricas automÃ¡ticas */
    void CollectAutomaticMetrics(float DeltaTime);
    
    /** Validar evento analÃ­tico */
    bool ValidateAnalyticsEvent(const FAuracronAnalyticsEvent& Event) const;
    
    /** Serializar evento para JSON */
    FString SerializeEventToJSON(const FAuracronAnalyticsEvent& Event) const;
    
    /** Enviar evento para provider */
    bool SendEventToProvider(const FAuracronAnalyticsEvent& Event);

public:
    // === Configuration Properties ===

    /** Provider de analytics ativo (C++ only - TSharedPtr cannot be exposed to Blueprints) */
    TSharedPtr<IAnalyticsProvider> AnalyticsProvider;

    /** Eventos na fila para envio */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<FAuracronAnalyticsEvent> EventQueue;

    /** MÃ©tricas de balanceamento coletadas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    TArray<FAuracronBalanceMetrics> BalanceMetrics;

    /** Testes A/B ativos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronABTestConfiguration> ActiveABTests;

    /** ID da sessÃ£o atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    FString CurrentSessionID;

    /** Estatísticas da sessão atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TMap<FString, FString> SessionStatistics;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Analytics habilitado */
    bool bAnalyticsEnabled = true;
    
    /** Timer para processamento de eventos */
    FTimerHandle EventProcessingTimer;
    
    /** Timer para coleta automÃ¡tica */
    FTimerHandle AutoCollectionTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection AnalyticsMutex;
    
    /** Cache de eventos para otimizaÃ§Ã£o */
    TMap<FString, FAuracronAnalyticsEvent> EventCache;

    /** Flag para armazenar analytics localmente */
    bool bStoreLocalAnalytics = false;

    /** Dados de analytics locais */
    TArray<FAuracronAnalyticsEvent> LocalAnalyticsData;

public:
    // === Delegates ===
    
    /** Delegate chamado quando evento Ã© registrado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAnalyticsEventRecorded, FAuracronAnalyticsEvent, Event);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Analytics|Events")
    FOnAnalyticsEventRecorded OnAnalyticsEventRecorded;
    
    /** Delegate chamado quando dados sÃ£o sincronizados */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAnalyticsDataSynced, bool, bSuccess);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Analytics|Events")
    FOnAnalyticsDataSynced OnAnalyticsDataSynced;
};

