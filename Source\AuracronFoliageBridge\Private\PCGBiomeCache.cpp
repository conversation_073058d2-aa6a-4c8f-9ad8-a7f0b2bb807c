// Copyright Epic Games, Inc. All Rights Reserved.

#include "PCGBiomeCache.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "Engine/World.h"

DEFINE_LOG_CATEGORY(LogPCGBiomeCache);

UPCGBiomeCache::UPCGBiomeCache()
{
    bCacheInitialized = false;
}

void UPCGBiomeCache::InitializeCache()
{
    BiomeCache.Empty();
    bCacheInitialized = true;
    UE_LOG(LogPCGBiomeCache, Log, TEXT("PCG Biome Cache initialized"));
}

void UPCGBiomeCache::ClearCache()
{
    BiomeCache.Empty();
    bCacheInitialized = false;
    UE_LOG(LogPCGBiomeCache, Log, TEXT("PCG Biome Cache cleared"));
}

bool UPCGBiomeCache::AddBiomeEntry(const FString& BiomeId, UPCGGraph* PCGGraph)
{
    if (!IsValidBiomeId(BiomeId) || !PCGGraph)
    {
        UE_LOG(LogPCGBiomeCache, Warning, TEXT("Invalid BiomeId or PCGGraph for AddBiomeEntry"));
        return false;
    }

    FPCGBiomeCacheEntry NewEntry;
    NewEntry.BiomeId = BiomeId;
    NewEntry.PCGGraph = PCGGraph;
    NewEntry.CacheState = EPCGBiomeCacheState::Ready;
    NewEntry.LastAccessTime = FDateTime::Now();
    NewEntry.bIsValid = true;

    BiomeCache.Add(BiomeId, NewEntry);
    UE_LOG(LogPCGBiomeCache, Log, TEXT("Added biome entry: %s"), *BiomeId);
    return true;
}

bool UPCGBiomeCache::RemoveBiomeEntry(const FString& BiomeId)
{
    if (BiomeCache.Contains(BiomeId))
    {
        BiomeCache.Remove(BiomeId);
        UE_LOG(LogPCGBiomeCache, Log, TEXT("Removed biome entry: %s"), *BiomeId);
        return true;
    }
    
    UE_LOG(LogPCGBiomeCache, Warning, TEXT("Biome entry not found for removal: %s"), *BiomeId);
    return false;
}

FPCGBiomeCacheEntry UPCGBiomeCache::GetBiomeEntry(const FString& BiomeId)
{
    if (FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        Entry->LastAccessTime = FDateTime::Now();
        return *Entry;
    }
    
    // Return empty entry if not found
    FPCGBiomeCacheEntry EmptyEntry;
    EmptyEntry.bIsValid = false;
    return EmptyEntry;
}

TArray<FString> UPCGBiomeCache::GetAllBiomeIds() const
{
    TArray<FString> BiomeIds;
    BiomeCache.GetKeys(BiomeIds);
    return BiomeIds;
}

bool UPCGBiomeCache::RegisterPCGComponent(const FString& BiomeId, UPCGComponent* PCGComponent)
{
    if (!IsValidBiomeId(BiomeId) || !PCGComponent)
    {
        return false;
    }

    if (FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        Entry->PCGComponents.AddUnique(PCGComponent);
        return true;
    }
    
    return false;
}

bool UPCGBiomeCache::UnregisterPCGComponent(const FString& BiomeId, UPCGComponent* PCGComponent)
{
    if (FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        Entry->PCGComponents.Remove(PCGComponent);
        return true;
    }
    
    return false;
}

TArray<UPCGComponent*> UPCGBiomeCache::GetPCGComponents(const FString& BiomeId) const
{
    TArray<UPCGComponent*> Result;
    if (const FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        for (const TWeakObjectPtr<UPCGComponent>& WeakComponent : Entry->PCGComponents)
        {
            if (UPCGComponent* Component = WeakComponent.Get())
            {
                Result.Add(Component);
            }
        }
    }
    
    return Result;
}

EPCGBiomeCacheState UPCGBiomeCache::GetBiomeCacheState(const FString& BiomeId) const
{
    if (const FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        return Entry->CacheState;
    }
    
    return EPCGBiomeCacheState::Error;
}

void UPCGBiomeCache::SetBiomeCacheState(const FString& BiomeId, EPCGBiomeCacheState NewState)
{
    if (FPCGBiomeCacheEntry* Entry = BiomeCache.Find(BiomeId))
    {
        Entry->CacheState = NewState;
    }
}

bool UPCGBiomeCache::ValidateCache()
{
    CleanupInvalidEntries();
    
    for (auto& Pair : BiomeCache)
    {
        FPCGBiomeCacheEntry& Entry = Pair.Value;
        UPCGGraph* PCGGraph = Entry.PCGGraph.Get();
        if (!PCGGraph || !IsValid(PCGGraph))
        {
            Entry.bIsValid = false;
            Entry.CacheState = EPCGBiomeCacheState::Error;
        }
    }
    
    return true;
}

void UPCGBiomeCache::RefreshCache()
{
    ValidateCache();
    UE_LOG(LogPCGBiomeCache, Log, TEXT("PCG Biome Cache refreshed"));
}

void UPCGBiomeCache::CleanupInvalidEntries()
{
    TArray<FString> InvalidKeys;
    
    for (const auto& Pair : BiomeCache)
    {
        const FPCGBiomeCacheEntry& Entry = Pair.Value;
        UPCGGraph* PCGGraph = Entry.PCGGraph.Get();
        if (!Entry.bIsValid || !PCGGraph || !IsValid(PCGGraph))
        {
            InvalidKeys.Add(Pair.Key);
        }
    }
    
    for (const FString& Key : InvalidKeys)
    {
        BiomeCache.Remove(Key);
        UE_LOG(LogPCGBiomeCache, Log, TEXT("Removed invalid biome entry: %s"), *Key);
    }
}

bool UPCGBiomeCache::IsValidBiomeId(const FString& BiomeId) const
{
    return !BiomeId.IsEmpty() && BiomeId.Len() > 0;
}