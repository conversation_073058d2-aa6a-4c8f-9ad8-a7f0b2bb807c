// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Tutorial Save Game
// Sistema de salvamento de progresso do tutorial usando UE5.6 Save Game API

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/SaveGame.h"
#include "AuracronTutorialBridge.h"
#include "AuracronTutorialSaveGame.generated.h"

/**
 * Estrutura para dados de progresso de um tutorial específico
 */
USTRUCT(BlueprintType)
struct AURACRONTUTORIALBRIDGE_API FAuracronTutorialProgressData
{
    GENERATED_BODY()

    /** ID do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    FString TutorialID;

    /** Estado atual do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    EAuracronTutorialState TutorialState = EAuracronTutorialState::NotStarted;

    /** Índice do passo atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    int32 CurrentStepIndex = 0;

    /** Progresso percentual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    float ProgressPercentage = 0.0f;

    /** Timestamp da última atualização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    FDateTime LastUpdated;

    /** Passos completados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    TArray<int32> CompletedSteps;

    /** Tempo total gasto no tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    float TotalTimeSpent = 0.0f;

    /** Número de tentativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    int32 AttemptCount = 0;

    FAuracronTutorialProgressData()
    {
        LastUpdated = FDateTime::Now();
    }
};

/**
 * Classe de Save Game para armazenar progresso dos tutoriais
 */
UCLASS(BlueprintType)
class AURACRONTUTORIALBRIDGE_API UAuracronTutorialSaveGame : public USaveGame
{
    GENERATED_BODY()

public:
    UAuracronTutorialSaveGame();

    /** Nome do slot de salvamento */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Save Game")
    FString SaveSlotName;

    /** Índice do usuário (usando int32 para compatibilidade com Blueprint) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Save Game")
    int32 UserIndex;

    /** Versão do save game */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Save Game")
    int32 SaveGameVersion;

    /** Progresso de todos os tutoriais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    TMap<FString, FAuracronTutorialProgressData> TutorialProgressMap;

    /** Lista de tutoriais completados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Progress")
    TArray<FString> CompletedTutorials;

    /** Configurações do AI Mentor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Mentor")
    bool bAIMentorEnabled = true;

    /** Volume do AI Mentor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Mentor")
    float AIMentorVolume = 1.0f;

    /** Velocidade de fala do AI Mentor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Mentor")
    float AIMentorSpeechRate = 1.0f;

    /** Timestamp da criação do save */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Game")
    FDateTime CreationTime;

    /** Timestamp da última modificação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Game")
    FDateTime LastModified;

    /**
     * Atualiza o progresso de um tutorial específico
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    void UpdateTutorialProgress(const FString& TutorialID, const FAuracronTutorialProgressData& ProgressData);

    /**
     * Obtém o progresso de um tutorial específico
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    FAuracronTutorialProgressData GetTutorialProgress(const FString& TutorialID) const;

    /**
     * Verifica se um tutorial foi completado
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    bool IsTutorialCompleted(const FString& TutorialID) const;

    /**
     * Marca um tutorial como completado
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    void MarkTutorialCompleted(const FString& TutorialID);

    /**
     * Reseta o progresso de um tutorial específico
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    void ResetTutorialProgress(const FString& TutorialID);

    /**
     * Reseta todo o progresso dos tutoriais
     */
    UFUNCTION(BlueprintCallable, Category = "Tutorial Progress")
    void ResetAllProgress();

public:
    // Delegate para notificar quando todo o progresso for resetado
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAllProgressReset, int32, TutorialsInProgress, int32, CompletedTutorials);
    UPROPERTY(BlueprintAssignable, Category = "Tutorial Events")
    FOnAllProgressReset OnAllProgressReset;
};