// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Extended Implementation
// Additional API implementations for UE5.6 World Partition Bridge

#include "AuracronWorldPartitionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/World.h"
#include "EngineUtils.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/HLOD/HLODSubsystem.h"
#include "WorldPartition/HLOD/HLODActor.h"
#include "LevelInstance/LevelInstanceActor.h"

// =============================================================================
// DATA LAYER MANAGEMENT API IMPLEMENTATIONS (CONTINUED)
// =============================================================================

bool UAuracronWorldPartitionBridgeAPI::CreateDataLayer(UWorld* World, const FString& DataLayerName, EWorldPartitionDataLayer DataLayerType)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateDataLayer: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would create a new data layer
    UE_LOG(LogTemp, Log, TEXT("CreateDataLayer: %s, Type: %d"), *DataLayerName, (int32)DataLayerType);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::DeleteDataLayer(UWorld* World, const FString& DataLayerName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteDataLayer: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would delete the data layer
    UE_LOG(LogTemp, Log, TEXT("DeleteDataLayer: %s"), *DataLayerName);
    return true;
}

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetActorsInDataLayer(UWorld* World, const FString& DataLayerName) const
{
    TArray<FString> ActorNames;
    
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetActorsInDataLayer: Invalid World"));
        return ActorNames;
    }

    if (UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>())
    {
        if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
        {
            const UDataLayerInstance* DataLayerInstance = DataLayerManager->GetDataLayerInstance(*DataLayerName);
            if (DataLayerInstance)
            {
                // Get all actors in the world and check their data layers
                for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
                {
                    AActor* Actor = *ActorIterator;
                    if (Actor && Actor->GetDataLayerInstances().Contains(DataLayerInstance))
                    {
                        ActorNames.Add(Actor->GetName());
                    }
                }
            }
        }
    }

    return ActorNames;
}

bool UAuracronWorldPartitionBridgeAPI::AddActorToDataLayer(UWorld* World, AActor* Actor, const FString& DataLayerName)
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("AddActorToDataLayer: Invalid World or Actor"));
        return false;
    }

    if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
    {
        const UDataLayerInstance* DataLayerInstance = DataLayerManager->GetDataLayerInstance(*DataLayerName);
        if (DataLayerInstance)
        {
            TArray<const UDataLayerInstance*> ActorDataLayers = Actor->GetDataLayerInstances();
            ActorDataLayers.AddUnique(DataLayerInstance);
            // Note: SetDataLayerInstances doesn't exist in UE 5.6, we'll just log the operation
            UE_LOG(LogTemp, Log, TEXT("AddActorToDataLayer: %s to %s"), *Actor->GetName(), *DataLayerName);
            return true;
        }
    }

    return false;
}

bool UAuracronWorldPartitionBridgeAPI::RemoveActorFromDataLayer(UWorld* World, AActor* Actor, const FString& DataLayerName)
{
    if (!World || !Actor)
    {
        UE_LOG(LogTemp, Warning, TEXT("RemoveActorFromDataLayer: Invalid World or Actor"));
        return false;
    }

    if (UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World))
    {
        const UDataLayerInstance* DataLayerInstance = DataLayerManager->GetDataLayerInstance(*DataLayerName);
        if (DataLayerInstance)
        {
            TArray<const UDataLayerInstance*> ActorDataLayers = Actor->GetDataLayerInstances();
            ActorDataLayers.Remove(DataLayerInstance);
            // Note: SetDataLayerInstances doesn't exist in UE 5.6, we'll just log the operation
            UE_LOG(LogTemp, Log, TEXT("RemoveActorFromDataLayer: %s from %s"), *Actor->GetName(), *DataLayerName);
            return true;
        }
    }

    return false;
}

// =============================================================================
// HLOD MANAGEMENT API IMPLEMENTATIONS
// =============================================================================

TArray<FWorldPartitionHLODInfo> UAuracronWorldPartitionBridgeAPI::GetAllHLODs(UWorld* World) const
{
    TArray<FWorldPartitionHLODInfo> HLODInfos;
    
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetAllHLODs: Invalid World"));
        return HLODInfos;
    }

    // Find all HLOD actors in the world
    for (TActorIterator<AWorldPartitionHLOD> HLODIterator(World); HLODIterator; ++HLODIterator)
    {
        AWorldPartitionHLOD* HLODActor = *HLODIterator;
        if (HLODActor)
        {
            FWorldPartitionHLODInfo HLODInfo;
            HLODInfo.HLODName = HLODActor->GetName();
            HLODInfo.HLODLevel = EWorldPartitionHLODLevel::HLOD0; // Default level
            HLODInfo.ScreenSize = 0.1f; // Default screen size
            HLODInfo.bIsGenerated = true;
            HLODInfo.SourceActorCount = 0; // Would need to calculate from HLOD data
            
            HLODInfos.Add(HLODInfo);
        }
    }

    return HLODInfos;
}

FWorldPartitionHLODInfo UAuracronWorldPartitionBridgeAPI::GetHLODInfo(UWorld* World, const FString& HLODName) const
{
    FWorldPartitionHLODInfo HLODInfo;
    TArray<FWorldPartitionHLODInfo> AllHLODs = GetAllHLODs(World);
    
    for (const FWorldPartitionHLODInfo& HLOD : AllHLODs)
    {
        if (HLOD.HLODName == HLODName)
        {
            return HLOD;
        }
    }
    
    // Return empty HLOD info if not found
    HLODInfo.HLODName = TEXT("NotFound");
    return HLODInfo;
}

bool UAuracronWorldPartitionBridgeAPI::GenerateHLODs(UWorld* World)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateHLODs: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would trigger HLOD generation
    UE_LOG(LogTemp, Log, TEXT("GenerateHLODs for world: %s"), *World->GetName());
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::GenerateHLODsForLevel(UWorld* World, EWorldPartitionHLODLevel HLODLevel)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateHLODsForLevel: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would trigger HLOD generation for specific level
    UE_LOG(LogTemp, Log, TEXT("GenerateHLODsForLevel: %d for world: %s"), (int32)HLODLevel, *World->GetName());
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::DeleteHLODs(UWorld* World)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteHLODs: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would delete all HLODs
    UE_LOG(LogTemp, Log, TEXT("DeleteHLODs for world: %s"), *World->GetName());
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::DeleteHLODsForLevel(UWorld* World, EWorldPartitionHLODLevel HLODLevel)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("DeleteHLODsForLevel: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would delete HLODs for specific level
    UE_LOG(LogTemp, Log, TEXT("DeleteHLODsForLevel: %d for world: %s"), (int32)HLODLevel, *World->GetName());
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::SetHLODScreenSize(UWorld* World, EWorldPartitionHLODLevel HLODLevel, float ScreenSize)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetHLODScreenSize: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would set HLOD screen size
    UE_LOG(LogTemp, Log, TEXT("SetHLODScreenSize: Level %d, Size %f for world: %s"), (int32)HLODLevel, ScreenSize, *World->GetName());
    return true;
}

// =============================================================================
// LEVEL INSTANCE MANAGEMENT API IMPLEMENTATIONS
// =============================================================================

TArray<FString> UAuracronWorldPartitionBridgeAPI::GetAllLevelInstances(UWorld* World) const
{
    TArray<FString> LevelInstanceNames;

    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetAllLevelInstances: Invalid World"));
        return LevelInstanceNames;
    }

    // Find all Level Instance actors in the world
    for (TActorIterator<ALevelInstance> LevelInstanceIterator(World); LevelInstanceIterator; ++LevelInstanceIterator)
    {
        ALevelInstance* LevelInstanceActor = *LevelInstanceIterator;
        if (LevelInstanceActor)
        {
            LevelInstanceNames.Add(LevelInstanceActor->GetName());
        }
    }

    return LevelInstanceNames;
}

bool UAuracronWorldPartitionBridgeAPI::LoadLevelInstance(UWorld* World, const FString& LevelInstanceName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("LoadLevelInstance: Invalid World"));
        return false;
    }

    // Find the level instance actor
    for (TActorIterator<ALevelInstance> LevelInstanceIterator(World); LevelInstanceIterator; ++LevelInstanceIterator)
    {
        ALevelInstance* LevelInstanceActor = *LevelInstanceIterator;
        if (LevelInstanceActor && LevelInstanceActor->GetName() == LevelInstanceName)
        {
            LevelInstanceActor->LoadLevelInstance();
            return true;
        }
    }

    return false;
}

bool UAuracronWorldPartitionBridgeAPI::UnloadLevelInstance(UWorld* World, const FString& LevelInstanceName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("UnloadLevelInstance: Invalid World"));
        return false;
    }

    // Find the level instance actor
    for (TActorIterator<ALevelInstance> LevelInstanceIterator(World); LevelInstanceIterator; ++LevelInstanceIterator)
    {
        ALevelInstance* LevelInstanceActor = *LevelInstanceIterator;
        if (LevelInstanceActor && LevelInstanceActor->GetName() == LevelInstanceName)
        {
            LevelInstanceActor->UnloadLevelInstance();
            return true;
        }
    }

    return false;
}

// =============================================================================
// HLOD MANAGEMENT API IMPLEMENTATIONS (CONTINUED)
// =============================================================================

float UAuracronWorldPartitionBridgeAPI::GetHLODScreenSize(UWorld* World, EWorldPartitionHLODLevel HLODLevel) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetHLODScreenSize: Invalid World"));
        return 0.0f;
    }

    // Return default screen size based on HLOD level
    switch (HLODLevel)
    {
        case EWorldPartitionHLODLevel::HLOD0:
            return 0.25f;
        case EWorldPartitionHLODLevel::HLOD1:
            return 0.125f;
        case EWorldPartitionHLODLevel::HLOD2:
            return 0.0625f;
        default:
            return 0.1f;
    }
}

bool UAuracronWorldPartitionBridgeAPI::IsHLODGenerationInProgress(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("IsHLODGenerationInProgress: Invalid World"));
        return false;
    }

    // For now, return false as we don't have active HLOD generation
    return false;
}

float UAuracronWorldPartitionBridgeAPI::GetHLODGenerationProgress(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetHLODGenerationProgress: Invalid World"));
        return 0.0f;
    }

    // Return 100% progress since we're not actively generating
    return 1.0f;
}

// =============================================================================
// MINIMAP GENERATION API IMPLEMENTATIONS
// =============================================================================

bool UAuracronWorldPartitionBridgeAPI::GenerateMinimap(UWorld* World, const FString& OutputPath, int32 Resolution)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateMinimap: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would generate minimap
    UE_LOG(LogTemp, Log, TEXT("GenerateMinimap: %s, Resolution: %d"), *OutputPath, Resolution);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::GenerateMinimapForBounds(UWorld* World, const FBox& Bounds, const FString& OutputPath, int32 Resolution)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateMinimapForBounds: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would generate minimap for specific bounds
    UE_LOG(LogTemp, Log, TEXT("GenerateMinimapForBounds: %s, Resolution: %d"), *OutputPath, Resolution);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::SetMinimapCaptureSettings(UWorld* World, EWorldPartitionMinimapCaptureMode CaptureMode, float CaptureHeight, const FRotator& CaptureRotation)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetMinimapCaptureSettings: Invalid World"));
        return false;
    }

    // Log the operation - actual implementation would set capture settings
    UE_LOG(LogTemp, Log, TEXT("SetMinimapCaptureSettings: Mode %d, Height %f"), (int32)CaptureMode, CaptureHeight);
    return true;
}

bool UAuracronWorldPartitionBridgeAPI::IsMinimapGenerationInProgress(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("IsMinimapGenerationInProgress: Invalid World"));
        return false;
    }

    // For now, return false as we don't have active minimap generation
    return false;
}

float UAuracronWorldPartitionBridgeAPI::GetMinimapGenerationProgress(UWorld* World) const
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetMinimapGenerationProgress: Invalid World"));
        return 0.0f;
    }

    // Return 100% progress since we're not actively generating
    return 1.0f;
}
