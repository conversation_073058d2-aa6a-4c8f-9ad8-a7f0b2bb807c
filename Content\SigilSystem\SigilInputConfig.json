{"sigil_input_system": {"version": "1.0.0", "description": "Input configuration for Auracron Sigil System (Fusion 2.0)", "input_method": "Enhanced Input System (UE 5.6)", "platform_support": ["PC", "<PERSON><PERSON><PERSON>", "Mobile"]}, "input_actions": {"aegis_sigil": {"name": "ActivateAegisSigil", "description": "Activate equipped Aegis Sigil", "input_type": "Digital", "default_key": "Q", "gamepad_button": "LeftShoulder", "mobile_button": "SigilButton1", "hold_threshold": 0.0, "tap_release_threshold": 0.2, "gameplay_tag": "Sigil.Input.Aegis"}, "ruin_sigil": {"name": "ActivateRuinSigil", "description": "Activate equipped Ruin Sigil", "input_type": "Digital", "default_key": "W", "gamepad_button": "RightShoulder", "mobile_button": "SigilButton2", "hold_threshold": 0.0, "tap_release_threshold": 0.2, "gameplay_tag": "Sigil.Input.Ruin"}, "vesper_sigil": {"name": "ActivateVesperSigil", "description": "Activate equipped Vesper Sigil", "input_type": "Digital", "default_key": "E", "gamepad_button": "LeftTrigger", "mobile_button": "SigilButton3", "hold_threshold": 0.0, "tap_release_threshold": 0.2, "gameplay_tag": "Sigil.Input.Vesper"}, "fusion_20": {"name": "ActivateFusion20", "description": "Activate Fusion 2.0 (all three Sigils)", "input_type": "Digital", "default_key": "R", "gamepad_button": "RightTrigger", "mobile_button": "FusionButton", "hold_threshold": 1.0, "tap_release_threshold": 0.0, "gameplay_tag": "Sigil.Input.Fusion20", "requires_hold": true, "hold_duration": 1.0, "visual_feedback": true}, "sigil_wheel": {"name": "OpenSigilWheel", "description": "Open Sigil selection wheel", "input_type": "Digital", "default_key": "Tab", "gamepad_button": "DPadUp", "mobile_button": "WheelButton", "hold_threshold": 0.3, "tap_release_threshold": 0.0, "gameplay_tag": "Sigil.Input.Wheel"}, "quick_swap_1": {"name": "QuickSwapSlot1", "description": "Quick swap to Sigil preset 1", "input_type": "Digital", "default_key": "1", "gamepad_button": "DPadLeft", "mobile_button": "QuickSlot1", "gameplay_tag": "Sigil.Input.QuickSwap.1"}, "quick_swap_2": {"name": "QuickSwapSlot2", "description": "Quick swap to <PERSON>gil preset 2", "input_type": "Digital", "default_key": "2", "gamepad_button": "DPadRight", "mobile_button": "QuickSlot2", "gameplay_tag": "Sigil.Input.QuickSwap.2"}, "quick_swap_3": {"name": "QuickSwapSlot3", "description": "Quick swap to Sigil preset 3", "input_type": "Digital", "default_key": "3", "gamepad_button": "DPadDown", "mobile_button": "QuickSlot3", "gameplay_tag": "Sigil.Input.QuickSwap.3"}}, "input_contexts": {"default_context": {"name": "SigilSystemDefault", "priority": 1, "description": "Default input context for Sigil System", "mappings": [{"action": "ActivateAegisSigil", "key": "Q", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateRuinSigil", "key": "W", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateVesperSigil", "key": "E", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateFusion20", "key": "R", "modifiers": [], "triggers": ["Hold", "Released"], "hold_threshold": 1.0}, {"action": "OpenSigilWheel", "key": "Tab", "modifiers": [], "triggers": ["Hold", "Released"], "hold_threshold": 0.3}]}, "gamepad_context": {"name": "SigilSystemGamepad", "priority": 1, "description": "Gamepad input context for Sigil System", "mappings": [{"action": "ActivateAegisSigil", "key": "Gamepad_LeftShoulder", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateRuinSigil", "key": "Gamepad_RightShoulder", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateVesperSigil", "key": "Gamepad_LeftTrigger", "modifiers": [], "triggers": ["Pressed"]}, {"action": "ActivateFusion20", "key": "Gamepad_RightTrigger", "modifiers": [], "triggers": ["Hold", "Released"], "hold_threshold": 1.0}, {"action": "OpenSigilWheel", "key": "Gamepad_DPad_Up", "modifiers": [], "triggers": ["Hold", "Released"], "hold_threshold": 0.3}]}, "mobile_context": {"name": "SigilSystemMobile", "priority": 1, "description": "Mobile input context for Sigil System", "touch_regions": {"aegis_button": {"position": {"x": 0.1, "y": 0.8}, "size": {"width": 0.08, "height": 0.08}, "action": "ActivateAegisSigil"}, "ruin_button": {"position": {"x": 0.2, "y": 0.8}, "size": {"width": 0.08, "height": 0.08}, "action": "ActivateRuinSigil"}, "vesper_button": {"position": {"x": 0.3, "y": 0.8}, "size": {"width": 0.08, "height": 0.08}, "action": "ActivateVesperSigil"}, "fusion_button": {"position": {"x": 0.9, "y": 0.8}, "size": {"width": 0.1, "height": 0.1}, "action": "ActivateFusion20", "requires_hold": true, "hold_duration": 1.0}, "wheel_button": {"position": {"x": 0.5, "y": 0.1}, "size": {"width": 0.1, "height": 0.1}, "action": "OpenSigilWheel"}}}}, "input_feedback": {"visual_feedback": {"button_highlight": true, "cooldown_overlay": true, "charging_animation": true, "fusion_glow": true, "archetype_indicator": true}, "haptic_feedback": {"activation_vibration": {"intensity": 0.7, "duration": 0.2, "pattern": "Single"}, "fusion_vibration": {"intensity": 1.0, "duration": 1.0, "pattern": "Pulse"}, "cooldown_vibration": {"intensity": 0.3, "duration": 0.1, "pattern": "Tick"}}, "audio_feedback": {"button_press": "/Game/Audio/UI/ButtonPress", "button_hold": "/Game/Audio/UI/ButtonHold", "fusion_charge": "/Game/Audio/UI/FusionCharge", "cooldown_ready": "/Game/Audio/UI/CooldownReady", "invalid_action": "/Game/Audio/UI/InvalidAction"}}, "accessibility": {"colorblind_support": true, "high_contrast_mode": true, "large_text_mode": true, "simplified_ui": true, "voice_commands": false, "gesture_controls": false}, "customization": {"allow_key_rebinding": true, "allow_button_remapping": true, "save_user_preferences": true, "cloud_sync": true, "preset_configurations": ["<PERSON><PERSON><PERSON>", "MOBA Style", "FPS Style", "MMO Style", "Custom"]}}