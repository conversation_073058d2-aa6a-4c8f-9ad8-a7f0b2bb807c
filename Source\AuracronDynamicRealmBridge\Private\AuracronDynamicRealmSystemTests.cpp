#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronDynamicRealmBridge.h"

/**
 * Auracron Dynamic Realm System - Comprehensive Test Suite
 *
 * Tests all advanced features of the Dynamic Realm System using UE 5.6 testing framework
 */

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FAuracronDynamicRealmSystemTest, "Auracron.DynamicRealm.SystemTest", EAutomationTestFlags::EditorContext | EAutomationTestFlags::ProductFilter)

bool FAuracronDynamicRealmSystemTest::RunTest(const FString& Parameters)
{
    // Test Dynamic Realm System functionality
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    if (!TestWorld)
    {
        AddError(TEXT("Failed to create test world"));
        return false;
    }

    // Get Dynamic Realm Subsystem
    UAuracronDynamicRealmSubsystem* RealmSubsystem = TestWorld->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    if (!RealmSubsystem)
    {
        AddError(TEXT("Failed to get Dynamic Realm Subsystem"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Test 1: Layer Initialization
    bool bLayerInitSuccess = true;
    TArray<EAuracronRealmLayer> LayersToTest = {
        EAuracronRealmLayer::Terrestrial,
        EAuracronRealmLayer::Celestial,
        EAuracronRealmLayer::Abyssal
    };

    for (EAuracronRealmLayer Layer : LayersToTest)
    {
        if (!RealmSubsystem->IsLayerActive(Layer))
        {
            AddError(FString::Printf(TEXT("Layer %s not properly initialized"), *UEnum::GetValueAsString(Layer)));
            bLayerInitSuccess = false;
        }
    }
    TestTrue(TEXT("Layer initialization should succeed"), bLayerInitSuccess);

    // Test 2: Layer Evolution System
    FAuracronRealmLayerEvolutionData TerrestrialEvolution = RealmSubsystem->GetLayerEvolutionData(EAuracronRealmLayer::Terrestrial);
    if (TerrestrialEvolution.EvolutionStage == EAuracronLayerEvolutionStage::Dormant)
    {
        AddError(TEXT("Evolution data not properly initialized"));
        TestWorld->DestroyWorld(false);
        return false;
    }

    // Test forced evolution
    RealmSubsystem->ForceLayerEvolution(EAuracronRealmLayer::Terrestrial, EAuracronLayerEvolutionStage::Active);
    FAuracronRealmLayerEvolutionData UpdatedEvolution = RealmSubsystem->GetLayerEvolutionData(EAuracronRealmLayer::Terrestrial);
    if (UpdatedEvolution.EvolutionStage == TerrestrialEvolution.EvolutionStage)
    {
        AddError(TEXT("Forced evolution failed"));
        TestWorld->DestroyWorld(false);
        return false;
    }
    TestTrue(TEXT("Layer evolution should work correctly"), true);

    // Test 3: Performance Monitoring
    RealmSubsystem->TestMonitorAdvancedPerformance();
    RealmSubsystem->TestOptimizeEvolutionPerformance();
    RealmSubsystem->TestUpdateLayerEvolution();
    TestTrue(TEXT("Performance monitoring should work"), true);

    // Cleanup
    TestWorld->DestroyWorld(false);
    
    return true;
}
