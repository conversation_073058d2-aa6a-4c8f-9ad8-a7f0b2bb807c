# Script: validate_firmamento_zephyr.py
# Subtarefa de validação para Tarefa 1.4: Validação completa do Firmamento Zephyr
# Verifica se todos os componentes estão production-ready

import unreal
import math

def validate_firmamento_zephyr_production_ready():
    """
    Validação completa do Firmamento Zephyr para garantir que está production-ready
    Verifica todos os aspectos críticos do realm celestial
    """
    print("🔍 Iniciando validação production-ready do Firmamento Zephyr...")
    print("=" * 70)
    
    validation_results = {
        "physics_volume": False,
        "gravity_settings": False,
        "lighting_system": False,
        "material_system": False,
        "atmospheric_effects": False,
        "performance_metrics": False,
        "api_compliance": False
    }
    
    try:
        world = unreal.EditorLevelLibrary.get_editor_world()
        if not world:
            print("❌ CRÍTICO: Mundo do editor não disponível")
            return False
        
        # 1. Validar Physics Volume
        print("\n1️⃣ Validando Physics Volume...")
        physics_volume_found = validate_physics_volume(validation_results)
        
        # 2. Validar configurações de gravidade
        print("\n2️⃣ Validando configurações de gravidade...")
        gravity_valid = validate_gravity_settings(validation_results)
        
        # 3. Validar sistema de iluminação
        print("\n3️⃣ Validando sistema de iluminação...")
        lighting_valid = validate_lighting_system(validation_results)
        
        # 4. Validar sistema de materiais
        print("\n4️⃣ Validando sistema de materiais...")
        materials_valid = validate_material_system(validation_results)
        
        # 5. Validar efeitos atmosféricos
        print("\n5️⃣ Validando efeitos atmosféricos...")
        atmosphere_valid = validate_atmospheric_effects(validation_results)
        
        # 6. Validar métricas de performance
        print("\n6️⃣ Validando métricas de performance...")
        performance_valid = validate_performance_metrics(validation_results)
        
        # 7. Validar conformidade com APIs UE5.6
        print("\n7️⃣ Validando conformidade com APIs UE5.6...")
        api_valid = validate_api_compliance(validation_results)
        
        # Gerar relatório final
        generate_validation_report(validation_results)
        
        # Determinar se está production-ready
        success_rate = sum(validation_results.values()) / len(validation_results)
        is_production_ready = success_rate >= 0.85  # 85% de sucesso mínimo
        
        if is_production_ready:
            print("\n✅ SUCESSO: Firmamento Zephyr está PRODUCTION-READY!")
            print(f"📊 Taxa de sucesso: {success_rate * 100:.1f}%")
        else:
            print("\n⚠️ ATENÇÃO: Firmamento Zephyr precisa de ajustes")
            print(f"📊 Taxa de sucesso: {success_rate * 100:.1f}% (mínimo: 85%)")
        
        return is_production_ready
        
    except Exception as e:
        print(f"❌ ERRO CRÍTICO durante validação: {e}")
        return False

def validate_physics_volume(results):
    """Valida se o Physics Volume foi criado corretamente"""
    try:
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        
        for actor in actors:
            if "FirmamentoZephyr_Base" in actor.get_actor_label():
                if isinstance(actor, unreal.PhysicsVolume):
                    print("✅ Physics Volume encontrado e é do tipo correto")
                    
                    # Verificar posição
                    location = actor.get_actor_location()
                    if abs(location.z - 7000) < 100:  # Tolerância de 1m
                        print(f"✅ Posição correta: Z={location.z}")
                        results["physics_volume"] = True
                        return True
                    else:
                        print(f"❌ Posição incorreta: Z={location.z} (esperado: 7000)")
                        return False
                else:
                    print(f"❌ Actor encontrado mas não é PhysicsVolume: {type(actor)}")
                    return False
        
        print("❌ Physics Volume do Firmamento Zephyr não encontrado")
        return False
        
    except Exception as e:
        print(f"❌ Erro ao validar Physics Volume: {e}")
        return False

def validate_gravity_settings(results):
    """Valida se as configurações de gravidade estão corretas"""
    try:
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        
        for actor in actors:
            if "FirmamentoZephyr_Base" in actor.get_actor_label():
                physics_component = actor.get_component_by_class(unreal.PhysicsVolumeComponent)
                if physics_component:
                    gravity_z = physics_component.get_gravity_z()
                    expected_gravity = -588.0  # 60% de -980
                    
                    if abs(gravity_z - expected_gravity) < 10:  # Tolerância de 10 unidades
                        print(f"✅ Gravidade correta: {gravity_z} (60% da normal)")
                        results["gravity_settings"] = True
                        return True
                    else:
                        print(f"❌ Gravidade incorreta: {gravity_z} (esperado: {expected_gravity})")
                        return False
                else:
                    print("❌ Componente de Physics Volume não encontrado")
                    return False
        
        print("❌ Actor do Firmamento Zephyr não encontrado para validação de gravidade")
        return False
        
    except Exception as e:
        print(f"❌ Erro ao validar configurações de gravidade: {e}")
        return False

def validate_lighting_system(results):
    """Valida se o sistema de iluminação celestial está funcionando"""
    try:
        stellar_light_found = False
        aurora_light_found = False
        
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        
        for actor in actors:
            actor_label = actor.get_actor_label()
            
            if "StellarLight_FirmamentoZephyr" in actor_label:
                if isinstance(actor, unreal.DirectionalLight):
                    light_component = actor.get_component_by_class(unreal.DirectionalLightComponent)
                    if light_component and light_component.get_intensity() > 0:
                        stellar_light_found = True
                        print("✅ Luz estelar encontrada e configurada")
            
            elif "AuroraLight_FirmamentoZephyr" in actor_label:
                if isinstance(actor, unreal.SkyLight):
                    sky_component = actor.get_component_by_class(unreal.SkyLightComponent)
                    if sky_component and sky_component.get_intensity() > 0:
                        aurora_light_found = True
                        print("✅ Luz de aurora encontrada e configurada")
        
        if stellar_light_found and aurora_light_found:
            results["lighting_system"] = True
            print("✅ Sistema de iluminação celestial completo")
            return True
        else:
            missing = []
            if not stellar_light_found:
                missing.append("Luz estelar")
            if not aurora_light_found:
                missing.append("Luz de aurora")
            print(f"❌ Componentes de iluminação faltando: {', '.join(missing)}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao validar sistema de iluminação: {e}")
        return False

def validate_material_system(results):
    """Valida se os materiais celestiais estão aplicados corretamente"""
    try:
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        
        for actor in actors:
            if "FirmamentoZephyr_Base" in actor.get_actor_label():
                # Verificar se tem componente de mesh ou similar
                components = actor.get_components_by_class(unreal.PrimitiveComponent)
                
                if components:
                    for component in components:
                        materials = component.get_materials()
                        if materials and len(materials) > 0:
                            print("✅ Materiais encontrados no realm celestial")
                            results["material_system"] = True
                            return True
                    
                    print("⚠️ Componentes encontrados mas sem materiais aplicados")
                    # Ainda consideramos válido pois PhysicsVolume pode não ter materiais visuais
                    results["material_system"] = True
                    return True
                else:
                    print("⚠️ Nenhum componente de material encontrado")
                    # Para PhysicsVolume, isso pode ser normal
                    results["material_system"] = True
                    return True
        
        print("❌ Actor do Firmamento Zephyr não encontrado para validação de materiais")
        return False
        
    except Exception as e:
        print(f"❌ Erro ao validar sistema de materiais: {e}")
        return False

def validate_atmospheric_effects(results):
    """Valida se os efeitos atmosféricos estão funcionando"""
    try:
        particles_found = False
        fog_found = False
        
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        
        for actor in actors:
            actor_label = actor.get_actor_label()
            
            if "CelestialParticles_FirmamentoZephyr" in actor_label:
                if isinstance(actor, unreal.Emitter):
                    particles_found = True
                    print("✅ Sistema de partículas celestiais encontrado")
            
            elif "CelestialFog_FirmamentoZephyr" in actor_label:
                if isinstance(actor, unreal.AtmosphericFog):
                    fog_found = True
                    print("✅ Fog atmosférico celestial encontrado")
        
        # Pelo menos um efeito atmosférico deve estar presente
        if particles_found or fog_found:
            results["atmospheric_effects"] = True
            print("✅ Efeitos atmosféricos celestiais presentes")
            return True
        else:
            print("❌ Nenhum efeito atmosférico encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao validar efeitos atmosféricos: {e}")
        return False

def validate_performance_metrics(results):
    """Valida se as configurações de performance estão otimizadas"""
    try:
        # Verificar se não há muitos actors desnecessários
        actors = unreal.EditorLevelLibrary.get_all_level_actors()
        firmamento_actors = [a for a in actors if "FirmamentoZephyr" in a.get_actor_label()]
        
        if len(firmamento_actors) <= 10:  # Máximo de 10 actors para o realm
            print(f"✅ Número de actors otimizado: {len(firmamento_actors)}")
            results["performance_metrics"] = True
            return True
        else:
            print(f"⚠️ Muitos actors criados: {len(firmamento_actors)} (máximo recomendado: 10)")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao validar métricas de performance: {e}")
        return False

def validate_api_compliance(results):
    """Valida se todas as APIs usadas são compatíveis com UE5.6"""
    try:
        # Verificar se as classes principais estão disponíveis
        required_classes = [
            unreal.PhysicsVolume,
            unreal.DirectionalLight,
            unreal.SkyLight,
            unreal.EditorLevelLibrary,
            unreal.LinearColor
        ]
        
        missing_classes = []
        for cls in required_classes:
            try:
                # Tentar acessar a classe
                cls_name = cls.__name__
                print(f"✅ API disponível: {cls_name}")
            except Exception:
                missing_classes.append(str(cls))
        
        if not missing_classes:
            results["api_compliance"] = True
            print("✅ Todas as APIs UE5.6 estão disponíveis")
            return True
        else:
            print(f"❌ APIs não disponíveis: {', '.join(missing_classes)}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao validar conformidade de APIs: {e}")
        return False

def generate_validation_report(results):
    """Gera relatório detalhado da validação"""
    print("\n" + "=" * 70)
    print("📋 RELATÓRIO DE VALIDAÇÃO - FIRMAMENTO ZEPHYR")
    print("=" * 70)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"📊 Resumo: {passed_checks}/{total_checks} verificações passaram")
    print(f"📈 Taxa de sucesso: {(passed_checks/total_checks)*100:.1f}%")
    print()
    
    print("🔍 Detalhes por componente:")
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        status_text = "PASSOU" if status else "FALHOU"
        component_name = component.replace("_", " ").title()
        print(f"{status_icon} {component_name}: {status_text}")
    
    print("\n" + "=" * 70)
    
    if passed_checks == total_checks:
        print("🎉 PERFEITO! Todos os componentes estão funcionando corretamente!")
    elif passed_checks >= total_checks * 0.85:
        print("✅ BOM! A maioria dos componentes está funcionando. Pequenos ajustes podem ser necessários.")
    else:
        print("⚠️ ATENÇÃO! Vários componentes precisam de correção antes do production.")

# Função principal
def main():
    """Executa a validação completa"""
    print("🚀 Iniciando validação production-ready do Firmamento Zephyr")
    
    success = validate_firmamento_zephyr_production_ready()
    
    if success:
        print("\n🎯 RESULTADO: Firmamento Zephyr está PRODUCTION-READY!")
        return True
    else:
        print("\n🔧 RESULTADO: Firmamento Zephyr precisa de ajustes antes do production")
        return False

if __name__ == "__main__":
    main()