#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "AuracronCombatInterface.generated.h"

/**
 * Production Ready: UE 5.6 Combat Interface for Auracron Sigil System
 * This interface provides combat-related functionality for the Auracron system
 * Following official UE 5.6 interface implementation patterns
 */

/*
Empty class for reflection system visibility.
Uses the UINTERFACE macro.
Inherits from UInterface.
*/
UINTERFACE(MinimalAPI, Blueprintable, BlueprintType)
class UAuracronCombatInterface : public UInterface
{
    GENERATED_BODY()
};

/* Actual Interface declaration. */
class AURACRONSIGILOSBRIDGE_API IAuracronCombatInterface
{
    GENERATED_BODY()

public:
    /**
     * Production Ready: Register a combat participant with the system
     * @param Participant The actor to register as a combat participant
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    void RegisterCombatParticipant(AActor* Participant);
    virtual void RegisterCombatParticipant_Implementation(AActor* Participant) {}

    /**
     * Production Ready: Unregister a combat participant from the system
     * @param Participant The actor to unregister from combat
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    void UnregisterCombatParticipant(AActor* Participant);
    virtual void UnregisterCombatParticipant_Implementation(AActor* Participant) {}

    /**
     * Production Ready: Notify when combat starts
     * @param Instigator The actor that started combat
     * @param Target The target of combat
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    void OnCombatStarted(AActor* Instigator, AActor* Target);
    virtual void OnCombatStarted_Implementation(AActor* Instigator, AActor* Target) {}

    /**
     * Production Ready: Notify when combat ends
     * @param Participants All actors that were involved in combat
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    void OnCombatEnded(const TArray<AActor*>& Participants);
    virtual void OnCombatEnded_Implementation(const TArray<AActor*>& Participants) {}

    /**
     * Production Ready: Handle damage dealt by sigil effects
     * @param DamageDealer The actor dealing damage
     * @param DamageReceiver The actor receiving damage
     * @param DamageAmount The amount of damage dealt
     * @param SigilType The type of sigil that caused the damage
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    void OnSigilDamageDealt(AActor* DamageDealer, AActor* DamageReceiver, float DamageAmount, const FString& SigilType);
    virtual void OnSigilDamageDealt_Implementation(AActor* DamageDealer, AActor* DamageReceiver, float DamageAmount, const FString& SigilType) {}

    /**
     * Production Ready: Check if an actor is currently in combat
     * @param Actor The actor to check
     * @return True if the actor is in combat, false otherwise
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Combat")
    bool IsInCombat(AActor* Actor) const;
    virtual bool IsInCombat_Implementation(AActor* Actor) const { return false; }
};
