#include "CommunityHealingManager.h"
#include "AuracronHarmonyEngineBridge.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "Online/CoreOnline.h"

UCommunityHealingManager::UCommunityHealingManager()
{
    // Default configuration
    MaxConcurrentSessions = 10;
    MaxSessionDuration = 1800.0f; // 30 minutes
    MinHealerSkillLevel = 0.6f;
    MaxHealersPerSession = 3;
    bEnableAutomaticMatching = true;
    bEnableSessionRecording = true;
}

FString UCommunityHealingManager::InitiateHealingSession(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    if (!ValidateHealingRequest(VictimPlayerID, SessionType))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Invalid healing request for player: %s"), *VictimPlayerID);
        return TEXT("");
    }
    
    if (ActiveSessions.Num() >= MaxConcurrentSessions)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Maximum concurrent healing sessions reached"));
        return TEXT("");
    }
    
    // Create new healing session
    FHealingSession NewSession;
    NewSession.SessionID = GenerateSessionID();
    NewSession.SessionType = SessionType;
    NewSession.VictimPlayerID = VictimPlayerID;
    NewSession.Status = EHealingStatus::Pending;
    NewSession.StartTime = FDateTime::Now();
    NewSession.HealingGoal = GenerateHealingGoal(SessionType, VictimPlayerID);
    
    // Add session tags
    NewSession.SessionTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
    
    // Store session
    ActiveSessions.Add(NewSession.SessionID, NewSession);
    
    // Start session timer
    StartSessionTimer(NewSession.SessionID);
    
    // Auto-match healers if enabled
    if (bEnableAutomaticMatching)
    {
        TArray<FString> LocalAvailableHealers = FindAvailableHealers(SessionType);
        if (LocalAvailableHealers.Num() > 0)
        {
            FString BestHealer = FindBestMatchedHealer(VictimPlayerID, SessionType);
            if (!BestHealer.IsEmpty())
            {
                AddHealerToSession(NewSession.SessionID, BestHealer);
            }
        }
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initiated healing session %s for player %s"), *NewSession.SessionID, *VictimPlayerID);
    
    return NewSession.SessionID;
}

bool UCommunityHealingManager::AddHealerToSession(const FString& SessionID, const FString& HealerPlayerID)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Healing session not found: %s"), *SessionID);
        return false;
    }
    
    if (!ValidateHealerEligibility(HealerPlayerID, Session->SessionType))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Healer %s not eligible for session %s"), *HealerPlayerID, *SessionID);
        return false;
    }
    
    if (Session->HealerPlayerIDs.Num() >= MaxHealersPerSession)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Maximum healers reached for session %s"), *SessionID);
        return false;
    }
    
    // Add healer to session
    Session->HealerPlayerIDs.AddUnique(HealerPlayerID);
    
    // Update healer availability
    if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerPlayerID))
    {
        HealerProfile->bIsAvailable = false;
        HealerProfile->LastActiveTime = FDateTime::Now();
    }
    
    // Update session status
    if (Session->Status == EHealingStatus::Pending)
    {
        Session->Status = EHealingStatus::Active;
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Added healer %s to session %s"), *HealerPlayerID, *SessionID);
    
    return true;
}

bool UCommunityHealingManager::CompleteHealingSession(const FString& SessionID, float SuccessRating)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        return false;
    }
    
    // Update session completion data
    Session->Status = EHealingStatus::Completed;
    Session->EndTime = FDateTime::Now();
    Session->HealingProgress = 1.0f;
    
    // Update healer statistics
    for (const FString& HealerID : Session->HealerPlayerIDs)
    {
        UpdateHealerStatistics(HealerID, SuccessRating);
        
        // Make healer available again
        if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID))
        {
            HealerProfile->bIsAvailable = true;
        }
    }
    
    // Record session outcome
    RecordSessionOutcome(*Session, SuccessRating > 0.6f);
    
    // Move to completed sessions
    CompletedSessions.Add(SessionID, *Session);
    ActiveSessions.Remove(SessionID);
    
    // Clear session timer
    if (UWorld* World = GetWorld())
    {
        if (FTimerHandle* Timer = SessionTimers.Find(SessionID))
        {
            World->GetTimerManager().ClearTimer(*Timer);
            SessionTimers.Remove(SessionID);
        }
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Completed healing session %s with rating %.2f"), *SessionID, SuccessRating);
    
    return true;
}

void UCommunityHealingManager::RegisterHealer(const FString& PlayerID, const TArray<EHealingSessionType>& Specializations)
{
    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot register healer with empty PlayerID"));
        return;
    }
    
    // Create or update healer profile
    FHealerProfile HealerProfile;
    HealerProfile.PlayerID = PlayerID;
    HealerProfile.Specializations = Specializations;
    HealerProfile.HealingSkillLevel = CalculateInitialHealerSkill(PlayerID);
    HealerProfile.bIsAvailable = true;
    HealerProfile.LastActiveTime = FDateTime::Now();
    
    // Add healer tags based on specializations
    for (EHealingSessionType Specialization : Specializations)
    {
        switch (Specialization)
        {
            case EHealingSessionType::Mentorship:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Behavior_Mentoring);
                break;
            case EHealingSessionType::PeerSupport:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
                break;
            case EHealingSessionType::CrisisIntervention:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Intervention_Emergency);
                break;
        }
    }
    
    RegisteredHealers.Add(PlayerID, HealerProfile);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Registered healer %s with %d specializations"), *PlayerID, Specializations.Num());
}

TArray<FString> UCommunityHealingManager::FindAvailableHealers(EHealingSessionType SessionType)
{
    TArray<FString> LocalAvailableHealers;
    
    for (const auto& HealerPair : RegisteredHealers)
    {
        const FHealerProfile& Profile = HealerPair.Value;
        
        if (Profile.bIsAvailable && 
            Profile.HealingSkillLevel >= MinHealerSkillLevel &&
            IsHealerSpecializedFor(Profile.PlayerID, SessionType))
        {
            LocalAvailableHealers.Add(Profile.PlayerID);
        }
    }

    // Sort by skill level (highest first)
    LocalAvailableHealers.Sort([this](const FString& A, const FString& B) {
        const FHealerProfile* ProfileA = RegisteredHealers.Find(A);
        const FHealerProfile* ProfileB = RegisteredHealers.Find(B);
        
        if (ProfileA && ProfileB)
        {
            return ProfileA->HealingSkillLevel > ProfileB->HealingSkillLevel;
        }
        return false;
    });
    
    return LocalAvailableHealers;
}

FString UCommunityHealingManager::FindBestMatchedHealer(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    TArray<FString> LocalAvailableHealers = FindAvailableHealers(SessionType);

    if (LocalAvailableHealers.Num() == 0)
    {
        return TEXT("");
    }

    // Rank healers by compatibility
    TArray<FString> RankedHealers = RankHealersByCompatibility(LocalAvailableHealers, VictimPlayerID);
    
    return RankedHealers.Num() > 0 ? RankedHealers[0] : TEXT("");
}

bool UCommunityHealingManager::IsPlayerInHealingSession(const FString& PlayerID)
{
    // Check if player is victim in any active session
    for (const auto& SessionPair : ActiveSessions)
    {
        const FHealingSession& Session = SessionPair.Value;
        if (Session.VictimPlayerID == PlayerID)
        {
            return true;
        }
        
        // Check if player is healer in any active session
        if (Session.HealerPlayerIDs.Contains(PlayerID))
        {
            return true;
        }
    }
    
    return false;
}

float UCommunityHealingManager::GetCommunityHealingEffectiveness()
{
    if (CompletedSessions.Num() == 0)
    {
        return 0.0f;
    }
    
    float TotalEffectiveness = 0.0f;
    int32 ValidSessions = 0;
    
    for (const auto& SessionPair : CompletedSessions)
    {
        const FHealingSession& Session = SessionPair.Value;
        if (Session.Status == EHealingStatus::Completed)
        {
            TotalEffectiveness += Session.HealingProgress;
            ValidSessions++;
        }
    }
    
    return ValidSessions > 0 ? (TotalEffectiveness / ValidSessions) : 0.0f;
}

TArray<FString> UCommunityHealingManager::GetTopHealers(int32 Count)
{
    TArray<FString> TopHealers;
    
    // Create array of healer IDs with their ratings
    TArray<TPair<FString, float>> HealerRatings;
    
    for (const auto& HealerPair : RegisteredHealers)
    {
        const FHealerProfile& Profile = HealerPair.Value;
        float Rating = (Profile.HealingSkillLevel * 0.6f) + (Profile.AverageSessionRating * 0.4f);
        HealerRatings.Add(TPair<FString, float>(Profile.PlayerID, Rating));
    }
    
    // Sort by rating (highest first)
    HealerRatings.Sort([](const TPair<FString, float>& A, const TPair<FString, float>& B) {
        return A.Value > B.Value;
    });
    
    // Extract top healers
    int32 NumToReturn = FMath::Min(Count, HealerRatings.Num());
    for (int32 i = 0; i < NumToReturn; i++)
    {
        TopHealers.Add(HealerRatings[i].Key);
    }
    
    return TopHealers;
}

// Private helper function implementations

bool UCommunityHealingManager::ValidateHealingRequest(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    if (VictimPlayerID.IsEmpty())
    {
        return false;
    }
    
    // Check if player is already in a healing session
    if (IsPlayerInHealingSession(VictimPlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is already in a healing session"), *VictimPlayerID);
        return false;
    }
    
    // Validate session type
    if (SessionType == EHealingSessionType::CrisisIntervention)
    {
        // Crisis intervention requires special validation
        // Check if player's emotional state warrants crisis intervention
        return true; // Simplified validation
    }
    
    return true;
}

bool UCommunityHealingManager::ValidateHealerEligibility(const FString& HealerPlayerID, EHealingSessionType SessionType)
{
    const FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return false;
    }
    
    // Check availability
    if (!Profile->bIsAvailable)
    {
        return false;
    }
    
    // Check skill level
    if (Profile->HealingSkillLevel < MinHealerSkillLevel)
    {
        return false;
    }
    
    // Check specialization
    if (!IsHealerSpecializedFor(HealerPlayerID, SessionType))
    {
        return false;
    }
    
    return true;
}

void UCommunityHealingManager::StartSessionTimer(const FString& SessionID)
{
    if (UWorld* World = GetWorld())
    {
        FTimerHandle SessionTimer;
        FTimerDelegate TimerDelegate;
        TimerDelegate.BindUFunction(this, FName("OnSessionTimeout"), SessionID);
        
        World->GetTimerManager().SetTimer(SessionTimer, TimerDelegate, MaxSessionDuration, false);
        SessionTimers.Add(SessionID, SessionTimer);
    }
}

void UCommunityHealingManager::OnSessionTimeout(const FString& SessionID)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        return;
    }
    
    UE_LOG(LogHarmonyEngine, Warning, TEXT("Healing session %s timed out"), *SessionID);
    
    // Mark session as failed due to timeout
    Session->Status = EHealingStatus::Failed;
    Session->EndTime = FDateTime::Now();
    
    // Make healers available again
    for (const FString& HealerID : Session->HealerPlayerIDs)
    {
        if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID))
        {
            HealerProfile->bIsAvailable = true;
        }
    }
    
    // Record failed session
    RecordSessionOutcome(*Session, false);
    
    // Move to completed sessions
    CompletedSessions.Add(SessionID, *Session);
    ActiveSessions.Remove(SessionID);
    SessionTimers.Remove(SessionID);
}

float UCommunityHealingManager::CalculateHealerCompatibility(const FString& HealerPlayerID, const FString& VictimPlayerID)
{
    const FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerPlayerID);
    if (!HealerProfile)
    {
        return 0.0f;
    }
    
    float Compatibility = 0.0f;
    
    // Base compatibility from healer skill level
    Compatibility += HealerProfile->HealingSkillLevel * 0.4f;
    
    // Bonus for successful session history
    Compatibility += (HealerProfile->AverageSessionRating / 5.0f) * 0.3f;
    
    // Bonus for experience
    float ExperienceBonus = FMath::Min(HealerProfile->SuccessfulHealingSessions / 10.0f, 1.0f) * 0.2f;
    Compatibility += ExperienceBonus;
    
    // Recent activity bonus
    FDateTime CurrentTime = FDateTime::Now();
    float TimeSinceActive = (CurrentTime - HealerProfile->LastActiveTime).GetTotalHours();
    float ActivityBonus = FMath::Max(0.0f, (24.0f - TimeSinceActive) / 24.0f) * 0.1f;
    Compatibility += ActivityBonus;
    
    return FMath::Clamp(Compatibility, 0.0f, 1.0f);
}

TArray<FString> UCommunityHealingManager::RankHealersByCompatibility(const TArray<FString>& LocalAvailableHealers, const FString& VictimPlayerID)
{
    TArray<TPair<FString, float>> HealerCompatibility;
    
    for (const FString& HealerID : LocalAvailableHealers)
    {
        float Compatibility = CalculateHealerCompatibility(HealerID, VictimPlayerID);
        HealerCompatibility.Add(TPair<FString, float>(HealerID, Compatibility));
    }
    
    // Sort by compatibility (highest first)
    HealerCompatibility.Sort([](const TPair<FString, float>& A, const TPair<FString, float>& B) {
        return A.Value > B.Value;
    });
    
    TArray<FString> RankedHealers;
    for (const auto& Pair : HealerCompatibility)
    {
        RankedHealers.Add(Pair.Key);
    }
    
    return RankedHealers;
}

bool UCommunityHealingManager::IsHealerSpecializedFor(const FString& HealerPlayerID, EHealingSessionType SessionType)
{
    const FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return false;
    }
    
    return Profile->Specializations.Contains(SessionType);
}

void UCommunityHealingManager::UpdateHealerStatistics(const FString& HealerPlayerID, float SessionRating)
{
    FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return;
    }
    
    // Update successful sessions count
    if (SessionRating > 0.6f)
    {
        Profile->SuccessfulHealingSessions++;
    }
    
    // Update average rating
    float TotalRating = Profile->AverageSessionRating * (Profile->SuccessfulHealingSessions - 1);
    TotalRating += SessionRating;
    Profile->AverageSessionRating = TotalRating / Profile->SuccessfulHealingSessions;
    
    // Update skill level based on performance
    float SkillImprovement = (SessionRating - 0.5f) * 0.01f; // Small incremental improvement
    Profile->HealingSkillLevel = FMath::Clamp(Profile->HealingSkillLevel + SkillImprovement, 0.0f, 1.0f);
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated healer %s statistics - Rating: %.2f, Skill: %.3f"), 
        *HealerPlayerID, Profile->AverageSessionRating, Profile->HealingSkillLevel);
}

void UCommunityHealingManager::RecordSessionOutcome(const FHealingSession& Session, bool bSuccessful)
{
    // Record session outcome for analytics
    FString OutcomeData = FString::Printf(
        TEXT("Session %s: Type=%d, Duration=%.2f, Healers=%d, Success=%s"),
        *Session.SessionID,
        static_cast<int32>(Session.SessionType),
        (Session.EndTime - Session.StartTime).GetTotalMinutes(),
        Session.HealerPlayerIDs.Num(),
        bSuccessful ? TEXT("Yes") : TEXT("No")
    );
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Session outcome: %s"), *OutcomeData);
    
    // Update player healing history
    FPlayerHealingHistory& VictimHistory = PlayerHealingHistory.FindOrAdd(Session.VictimPlayerID);
    VictimHistory.SessionIDs.Add(Session.SessionID);

    for (const FString& HealerID : Session.HealerPlayerIDs)
    {
        FPlayerHealingHistory& HealerHistory = PlayerHealingHistory.FindOrAdd(HealerID);
        HealerHistory.SessionIDs.Add(Session.SessionID);
    }
}

FString UCommunityHealingManager::GenerateSessionID()
{
    return FGuid::NewGuid().ToString();
}

FString UCommunityHealingManager::GenerateHealingGoal(EHealingSessionType SessionType, const FString& VictimPlayerID)
{
    switch (SessionType)
    {
        case EHealingSessionType::PeerSupport:
            return TEXT("Provide peer support and encouragement");
        case EHealingSessionType::Mentorship:
            return TEXT("Guide player improvement and skill development");
        case EHealingSessionType::GroupTherapy:
            return TEXT("Facilitate group discussion and mutual support");
        case EHealingSessionType::CrisisIntervention:
            return TEXT("Provide immediate emotional support and crisis management");
        case EHealingSessionType::CelebrationCircle:
            return TEXT("Celebrate achievements and build positive momentum");
        default:
            return TEXT("Provide general emotional support");
    }
}

float UCommunityHealingManager::CalculateInitialHealerSkill(const FString& PlayerID)
{
    // Calculate initial healer skill based on player's positive behavior history
    // This would integrate with the player's overall positivity score
    return 0.5f; // Default starting skill level
}

void UCommunityHealingManager::CancelHealingSession(const FString& SessionID, const FString& Reason)
{
    // Cancel healing session using UE 5.6 robust implementation
    if (SessionID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot cancel healing session - empty SessionID"));
        return;
    }

    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healing session not found: %s"), *SessionID);
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Canceling healing session: %s, Reason: %s"), *SessionID, *Reason);

    // Update session status
    Session->Status = EHealingStatus::Cancelled;
    Session->EndTime = FDateTime::Now();
    Session->CancellationReason = Reason;

    // Notify all participants
    for (const FString& HealerID : Session->AssignedHealers)
    {
        NotifyHealerOfCancellation(HealerID, SessionID, Reason);
    }

    // Notify victim
    NotifyVictimOfCancellation(Session->VictimPlayerID, SessionID, Reason);

    // Clear session timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(Session->SessionTimer);
    }

    // Move to completed sessions for analytics
    CompletedSessions.Add(SessionID, *Session);
    ActiveSessions.Remove(SessionID);

    // Update statistics
    TotalSessionsCancelled++;

    // Broadcast cancellation event
    OnHealingSessionCancelled.Broadcast(SessionID, Reason);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing session cancelled successfully: %s"), *SessionID);
}

void UCommunityHealingManager::UnregisterHealer(const FString& HealerID)
{
    // Unregister healer using UE 5.6 robust implementation
    if (HealerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot unregister healer - empty HealerID"));
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Unregistering healer: %s"), *HealerID);

    // Remove from registered healers
    if (!RegisteredHealers.Contains(HealerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healer not registered: %s"), *HealerID);
        return;
    }

    FHealerProfile HealerProfile = RegisteredHealers[HealerID];
    RegisteredHealers.Remove(HealerID);

    // Remove from availability list
    AvailableHealers.Remove(HealerID);

    // Cancel any active sessions where this healer is assigned
    TArray<FString> SessionsToUpdate;
    for (auto& SessionPair : ActiveSessions)
    {
        FHealingSession& Session = SessionPair.Value;
        if (Session.AssignedHealers.Contains(HealerID))
        {
            Session.AssignedHealers.Remove(HealerID);
            SessionsToUpdate.Add(SessionPair.Key);

            // Notify victim of healer change
            NotifyVictimOfHealerChange(Session.VictimPlayerID, SessionPair.Key, HealerID, TEXT("Healer unregistered"));
        }
    }

    // Try to reassign healers for affected sessions
    for (const FString& SessionID : SessionsToUpdate)
    {
        if (FHealingSession* Session = ActiveSessions.Find(SessionID))
        {
            if (Session->AssignedHealers.Num() == 0)
            {
                // Try to find replacement healers
                TryAssignHealersToSession(SessionID);
            }
        }
    }

    // Update statistics
    TotalHealersUnregistered++;

    // Broadcast unregistration event
    OnHealerUnregistered.Broadcast(HealerID, HealerProfile.SkillLevel);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healer unregistered successfully: %s"), *HealerID);
}

TArray<FHealingSession> UCommunityHealingManager::GetActiveHealingSessions()
{
    // Get active healing sessions using UE 5.6 robust implementation
    TArray<FHealingSession> ActiveSessionsList;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieving active healing sessions, Count: %d"), ActiveSessions.Num());

    // Convert map to array
    for (const auto& SessionPair : ActiveSessions)
    {
        ActiveSessionsList.Add(SessionPair.Value);
    }

    // Sort by start time (most recent first)
    ActiveSessionsList.Sort([](const FHealingSession& A, const FHealingSession& B)
    {
        return A.StartTime > B.StartTime;
    });

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieved %d active healing sessions"), ActiveSessionsList.Num());

    return ActiveSessionsList;
}

FHealingSession UCommunityHealingManager::GetHealingSession(const FString& SessionID)
{
    // Get healing session using UE 5.6 robust implementation
    if (SessionID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot get healing session - empty SessionID"));
        return FHealingSession();
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieving healing session: %s"), *SessionID);

    // Check active sessions first
    if (FHealingSession* Session = ActiveSessions.Find(SessionID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Found active healing session: %s"), *SessionID);
        return *Session;
    }

    // Check completed sessions
    if (FHealingSession* Session = CompletedSessions.Find(SessionID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Found completed healing session: %s"), *SessionID);
        return *Session;
    }

    UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healing session not found: %s"), *SessionID);
    return FHealingSession();
}

void UCommunityHealingManager::UpdateHealingProgress(const FString& SessionID, float ProgressValue)
{
    // Update healing progress using UE 5.6 robust implementation
    if (SessionID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot update healing progress - empty SessionID"));
        return;
    }

    if (ProgressValue < 0.0f || ProgressValue > 1.0f)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid progress value: %f (must be 0.0-1.0)"), ProgressValue);
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Updating healing progress for session: %s, Progress: %.2f"), *SessionID, ProgressValue);

    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Active healing session not found: %s"), *SessionID);
        return;
    }

    // Update progress
    float PreviousProgress = Session->Progress;
    Session->Progress = ProgressValue;
    Session->LastUpdateTime = FDateTime::Now();

    // Check for significant progress milestones
    TArray<float> Milestones = {0.25f, 0.5f, 0.75f, 1.0f};
    for (float Milestone : Milestones)
    {
        if (PreviousProgress < Milestone && ProgressValue >= Milestone)
        {
            // Milestone reached
            NotifyMilestoneReached(SessionID, Milestone);

            // Award progress rewards to healers
            for (const FString& HealerID : Session->AssignedHealers)
            {
                AwardProgressReward(HealerID, Milestone);
            }
        }
    }

    // Check if session is complete
    if (ProgressValue >= 1.0f && Session->Status != EHealingStatus::Completed)
    {
        CompleteHealingSession(SessionID);
    }

    // Update session effectiveness based on progress rate
    float ProgressRate = ProgressValue / FMath::Max(1.0f, (float)(FDateTime::Now() - Session->StartTime).GetTotalMinutes());
    Session->EffectivenessScore = FMath::Clamp(ProgressRate * 100.0f, 0.0f, 100.0f);

    // Broadcast progress update
    OnHealingProgressUpdated.Broadcast(SessionID, ProgressValue);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing progress updated successfully: %s, Progress: %.2f, Effectiveness: %.2f"),
           *SessionID, ProgressValue, Session->EffectivenessScore);
}

int32 UCommunityHealingManager::GetTotalHealingSessionsCompleted()
{
    // Get total healing sessions completed using UE 5.6 robust implementation
    int32 TotalCompleted = CompletedSessions.Num();

    // Also count completed sessions from statistics
    int32 StatisticsTotal = TotalSessionsCompleted;

    // Use the higher value to account for any discrepancies
    int32 FinalTotal = FMath::Max(TotalCompleted, StatisticsTotal);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Total healing sessions completed: %d (Map: %d, Stats: %d)"),
           FinalTotal, TotalCompleted, StatisticsTotal);

    return FinalTotal;
}

float UCommunityHealingManager::GetHealerRating(const FString& HealerID)
{
    // Get healer rating using UE 5.6 robust implementation
    if (HealerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot get healer rating - empty HealerID"));
        return 0.0f;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Retrieving healer rating for: %s"), *HealerID);

    // Check if healer is registered
    if (!RegisteredHealers.Contains(HealerID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healer not registered: %s"), *HealerID);
        return 0.0f;
    }

    const FHealerProfile& HealerProfile = RegisteredHealers[HealerID];

    // Calculate comprehensive rating based on multiple factors
    float BaseRating = HealerProfile.SkillLevel;
    float ExperienceBonus = FMath::Min(HealerProfile.SessionsCompleted * 0.01f, 0.3f); // Max 30% bonus
    float SuccessRateBonus = (HealerProfile.SuccessRate - 0.5f) * 0.4f; // -20% to +20% based on success rate
    float RecentActivityBonus = 0.0f;

    // Recent activity bonus (last 7 days)
    FDateTime WeekAgo = FDateTime::Now() - FTimespan::FromDays(7);
    if (HealerProfile.LastActiveTime > WeekAgo)
    {
        RecentActivityBonus = 0.1f;
    }

    // Feedback score bonus
    float FeedbackBonus = (HealerProfile.AverageRating - 3.0f) * 0.1f; // -20% to +20% based on 1-5 rating

    // Calculate final rating
    float FinalRating = BaseRating + ExperienceBonus + SuccessRateBonus + RecentActivityBonus + FeedbackBonus;
    FinalRating = FMath::Clamp(FinalRating, 0.0f, 1.0f);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healer rating calculated: %s, Rating: %.3f (Base: %.3f, Experience: %.3f, Success: %.3f, Activity: %.3f, Feedback: %.3f)"),
           *HealerID, FinalRating, BaseRating, ExperienceBonus, SuccessRateBonus, RecentActivityBonus, FeedbackBonus);

    return FinalRating;
}

void UCommunityHealingManager::NotifyHealerOfCancellation(const FString& HealerID, const FString& SessionID, const FString& Reason)
{
    // Notify healer of cancellation using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying healer of cancellation: %s, Session: %s, Reason: %s"), *HealerID, *SessionID, *Reason);

    // Find player controller
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString() == HealerID)
                {
                    // Send notification through UI system
                    FString NotificationText = FString::Printf(TEXT("Healing session %s has been cancelled: %s"), *SessionID, *Reason);

                    // In a real implementation, this would show a UI notification
                    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notification sent to healer: %s"), *NotificationText);
                    break;
                }
            }
        }
    }
}

void UCommunityHealingManager::NotifyVictimOfCancellation(const FString& VictimID, const FString& SessionID, const FString& Reason)
{
    // Notify victim of cancellation using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying victim of cancellation: %s, Session: %s, Reason: %s"), *VictimID, *SessionID, *Reason);

    // Find player controller
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString() == VictimID)
                {
                    // Send notification through UI system
                    FString NotificationText = FString::Printf(TEXT("Your healing session %s has been cancelled: %s"), *SessionID, *Reason);

                    // In a real implementation, this would show a UI notification
                    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notification sent to victim: %s"), *NotificationText);
                    break;
                }
            }
        }
    }
}

void UCommunityHealingManager::NotifyVictimOfHealerChange(const FString& VictimID, const FString& SessionID, const FString& HealerID, const FString& Reason)
{
    // Notify victim of healer change using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying victim of healer change: %s, Session: %s, Healer: %s, Reason: %s"), *VictimID, *SessionID, *HealerID, *Reason);

    // Find player controller
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString() == VictimID)
                {
                    // Send notification through UI system
                    FString NotificationText = FString::Printf(TEXT("Healer %s has left your healing session %s: %s"), *HealerID, *SessionID, *Reason);

                    // In a real implementation, this would show a UI notification
                    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notification sent to victim: %s"), *NotificationText);
                    break;
                }
            }
        }
    }
}

void UCommunityHealingManager::TryAssignHealersToSession(const FString& SessionID)
{
    // Try to assign healers to session using UE 5.6 robust implementation
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot assign healers - session not found: %s"), *SessionID);
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Trying to assign healers to session: %s"), *SessionID);

    // Find available healers
    TArray<FString> SuitableHealers;
    for (const FString& HealerID : AvailableHealers)
    {
        if (RegisteredHealers.Contains(HealerID))
        {
            const FHealerProfile& Profile = RegisteredHealers[HealerID];

            // Check if healer meets minimum requirements
            if (Profile.SkillLevel >= MinHealerSkillLevel &&
                Profile.SpecializedTypes.Contains(Session->SessionType))
            {
                SuitableHealers.Add(HealerID);
            }
        }
    }

    // Sort healers by rating
    SuitableHealers.Sort([this](const FString& A, const FString& B)
    {
        return GetHealerRating(A) > GetHealerRating(B);
    });

    // Assign up to MaxHealersPerSession
    int32 HealersToAssign = FMath::Min(SuitableHealers.Num(), MaxHealersPerSession - Session->AssignedHealers.Num());

    for (int32 i = 0; i < HealersToAssign; i++)
    {
        const FString& HealerID = SuitableHealers[i];
        Session->AssignedHealers.AddUnique(HealerID);
        AvailableHealers.Remove(HealerID);

        // Notify healer of assignment
        NotifyHealerOfAssignment(HealerID, SessionID);

        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Assigned healer to session: %s -> %s"), *HealerID, *SessionID);
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Assigned %d healers to session: %s"), HealersToAssign, *SessionID);
}

void UCommunityHealingManager::NotifyMilestoneReached(const FString& SessionID, float Milestone)
{
    // Notify milestone reached using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Milestone reached for session: %s, Milestone: %.2f"), *SessionID, Milestone);

    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        return;
    }

    // Notify all participants
    for (const FString& HealerID : Session->AssignedHealers)
    {
        NotifyHealerOfMilestone(HealerID, SessionID, Milestone);
    }

    // Notify victim
    NotifyVictimOfMilestone(Session->VictimPlayerID, SessionID, Milestone);

    // Broadcast milestone event
    OnHealingMilestoneReached.Broadcast(SessionID, Milestone);
}

void UCommunityHealingManager::AwardProgressReward(const FString& HealerID, float Milestone)
{
    // Award progress reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Awarding progress reward to healer: %s, Milestone: %.2f"), *HealerID, Milestone);

    if (!RegisteredHealers.Contains(HealerID))
    {
        return;
    }

    FHealerProfile& Profile = RegisteredHealers[HealerID];

    // Calculate reward based on milestone
    float RewardAmount = Milestone * 100.0f; // Base reward

    // Apply skill multiplier
    RewardAmount *= (1.0f + Profile.SkillLevel);

    // Award experience points
    Profile.ExperiencePoints += RewardAmount;

    // Update skill level based on experience
    float NewSkillLevel = FMath::Min(1.0f, Profile.ExperiencePoints / 10000.0f);
    if (NewSkillLevel > Profile.SkillLevel)
    {
        Profile.SkillLevel = NewSkillLevel;
        NotifyHealerOfSkillIncrease(HealerID, NewSkillLevel);
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Progress reward awarded: %s, Amount: %.2f, New Skill: %.3f"), *HealerID, RewardAmount, Profile.SkillLevel);
}

void UCommunityHealingManager::CompleteHealingSession(const FString& SessionID)
{
    // Complete healing session using UE 5.6 robust implementation
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot complete session - not found: %s"), *SessionID);
        return;
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Completing healing session: %s"), *SessionID);

    // Update session status
    Session->Status = EHealingStatus::Completed;
    Session->EndTime = FDateTime::Now();
    Session->Progress = 1.0f;

    // Calculate session duration
    FTimespan Duration = Session->EndTime - Session->StartTime;
    Session->Duration = Duration.GetTotalMinutes();

    // Update healer statistics
    for (const FString& HealerID : Session->AssignedHealers)
    {
        if (RegisteredHealers.Contains(HealerID))
        {
            FHealerProfile& Profile = RegisteredHealers[HealerID];
            Profile.SessionsCompleted++;
            Profile.TotalHealingTime += Session->Duration;

            // Update success rate
            float TotalSessions = Profile.SessionsCompleted + Profile.SessionsFailed;
            if (TotalSessions > 0)
            {
                Profile.SuccessRate = (float)Profile.SessionsCompleted / TotalSessions;
            }

            // Make healer available again
            if (!AvailableHealers.Contains(HealerID))
            {
                AvailableHealers.Add(HealerID);
            }

            // Award completion rewards
            AwardCompletionReward(HealerID, *Session);
        }
    }

    // Clear session timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(Session->SessionTimer);
    }

    // Move to completed sessions
    CompletedSessions.Add(SessionID, *Session);
    ActiveSessions.Remove(SessionID);

    // Update statistics
    TotalSessionsCompleted++;

    // Notify participants
    for (const FString& HealerID : Session->AssignedHealers)
    {
        NotifyHealerOfCompletion(HealerID, SessionID);
    }
    NotifyVictimOfCompletion(Session->VictimPlayerID, SessionID);

    // Broadcast completion event
    OnHealingSessionCompleted.Broadcast(SessionID, Session->EffectivenessScore);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing session completed successfully: %s, Duration: %.2f min, Effectiveness: %.2f"),
           *SessionID, Session->Duration, Session->EffectivenessScore);
}

void UCommunityHealingManager::NotifyHealerOfAssignment(const FString& HealerID, const FString& SessionID)
{
    // Notify healer of assignment using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying healer of assignment: %s -> %s"), *HealerID, *SessionID);

    // In a real implementation, this would send a UI notification to the healer
    // For now, we'll log the notification
    FString NotificationText = FString::Printf(TEXT("You have been assigned to healing session: %s"), *SessionID);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Assignment notification: %s"), *NotificationText);
}

void UCommunityHealingManager::NotifyHealerOfMilestone(const FString& HealerID, const FString& SessionID, float Milestone)
{
    // Notify healer of milestone using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying healer of milestone: %s, Session: %s, Milestone: %.2f"), *HealerID, *SessionID, Milestone);

    FString NotificationText = FString::Printf(TEXT("Healing session %s reached %.0f%% progress!"), *SessionID, Milestone * 100.0f);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Milestone notification: %s"), *NotificationText);
}

void UCommunityHealingManager::NotifyVictimOfMilestone(const FString& VictimID, const FString& SessionID, float Milestone)
{
    // Notify victim of milestone using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying victim of milestone: %s, Session: %s, Milestone: %.2f"), *VictimID, *SessionID, Milestone);

    FString NotificationText = FString::Printf(TEXT("Your healing session %s is %.0f%% complete!"), *SessionID, Milestone * 100.0f);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Milestone notification: %s"), *NotificationText);
}

void UCommunityHealingManager::NotifyHealerOfSkillIncrease(const FString& HealerID, float NewSkillLevel)
{
    // Notify healer of skill increase using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying healer of skill increase: %s, New Level: %.3f"), *HealerID, NewSkillLevel);

    FString NotificationText = FString::Printf(TEXT("Your healing skill has increased to %.1f%%!"), NewSkillLevel * 100.0f);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Skill increase notification: %s"), *NotificationText);
}

void UCommunityHealingManager::AwardCompletionReward(const FString& HealerID, const FHealingSession& Session)
{
    // Award completion reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Awarding completion reward to healer: %s, Session: %s"), *HealerID, *Session.SessionID);

    if (!RegisteredHealers.Contains(HealerID))
    {
        return;
    }

    FHealerProfile& Profile = RegisteredHealers[HealerID];

    // Calculate base reward
    float BaseReward = 500.0f;

    // Apply effectiveness multiplier
    float EffectivenessMultiplier = Session.EffectivenessScore / 100.0f;

    // Apply session type multiplier
    float TypeMultiplier = 1.0f;
    switch (Session.SessionType)
    {
        case EHealingSessionType::CrisisIntervention:
            TypeMultiplier = 2.0f;
            break;
        case EHealingSessionType::GroupTherapy:
            TypeMultiplier = 1.8f;
            break;
        case EHealingSessionType::Mentorship:
            TypeMultiplier = 1.2f;
            break;
        case EHealingSessionType::CelebrationCircle:
            TypeMultiplier = 1.0f;
            break;
    }

    // Calculate final reward
    float FinalReward = BaseReward * EffectivenessMultiplier * TypeMultiplier * (1.0f + Profile.SkillLevel);

    // Award experience points
    Profile.ExperiencePoints += FinalReward;

    // Update total rewards earned
    Profile.TotalRewardsEarned += FinalReward;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Completion reward awarded: %s, Amount: %.2f, Total: %.2f"),
           *HealerID, FinalReward, Profile.TotalRewardsEarned);
}

void UCommunityHealingManager::NotifyHealerOfCompletion(const FString& HealerID, const FString& SessionID)
{
    // Notify healer of completion using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying healer of completion: %s, Session: %s"), *HealerID, *SessionID);

    FString NotificationText = FString::Printf(TEXT("Healing session %s has been completed successfully!"), *SessionID);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Completion notification: %s"), *NotificationText);
}

void UCommunityHealingManager::NotifyVictimOfCompletion(const FString& VictimID, const FString& SessionID)
{
    // Notify victim of completion using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying victim of completion: %s, Session: %s"), *VictimID, *SessionID);

    FString NotificationText = FString::Printf(TEXT("Your healing session %s has been completed! Thank you for participating."), *SessionID);
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Completion notification: %s"), *NotificationText);
}

TArray<FString> UCommunityHealingManager::GetAvailableHealers(EHealingSessionType SessionType)
{
    // Get available healers for session type using UE 5.6 robust implementation
    TArray<FString> AvailableHealersList;

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Getting available healers for session type: %d"), (int32)SessionType);

    // Check all registered healers
    for (const auto& HealerPair : RegisteredHealers)
    {
        const FString& HealerID = HealerPair.Key;
        const FHealerProfile& Profile = HealerPair.Value;

        // Check if healer is available
        bool bIsAvailable = true;

        // Check if healer is currently in a session
        for (const auto& SessionPair : ActiveSessions)
        {
            const FHealingSession& Session = SessionPair.Value;
            if (Session.HealerPlayerIDs.Contains(HealerID))
            {
                bIsAvailable = false;
                break;
            }
        }

        if (!bIsAvailable)
        {
            continue;
        }

        // Check if healer supports this session type
        if (!Profile.Specializations.Contains(SessionType))
        {
            continue;
        }

        // Check healer skill level (using both properties available)
        float EffectiveSkillLevel = FMath::Max(Profile.HealingSkillLevel, Profile.SkillLevel);
        if (EffectiveSkillLevel < MinHealerSkillLevel)
        {
            continue;
        }

        // Check if healer is available
        if (Profile.bIsAvailable)
        {
            AvailableHealersList.Add(HealerID);
            float CurrentSkillLevel = FMath::Max(Profile.HealingSkillLevel, Profile.SkillLevel);
            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Available healer found: %s (Skill: %.2f)"),
                   *HealerID, CurrentSkillLevel);
        }
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Found %d available healers for session type %d"),
           AvailableHealersList.Num(), (int32)SessionType);

    return AvailableHealersList;
}

float UCommunityHealingManager::GetHealerEffectiveness(const FString& HealerID)
{
    // Get healer effectiveness using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Getting effectiveness for healer: %s"), *HealerID);

    if (HealerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid healer ID"));
        return 0.0f;
    }

    // Check if healer is registered
    const FHealerProfile* Profile = RegisteredHealers.Find(HealerID);
    if (!Profile)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healer not found: %s"), *HealerID);
        return 0.0f;
    }

    // Calculate effectiveness based on multiple factors using EXACT properties
    float BaseEffectiveness = FMath::Max(Profile->HealingSkillLevel, Profile->SkillLevel);
    float ExperienceBonus = FMath::Min(Profile->SuccessfulHealingSessions / 100.0f, 0.3f); // Max 30% bonus
    float RatingBonus = (Profile->AverageSessionRating - 3.0f) / 2.0f * 0.2f; // Rating bonus/penalty
    float RecentActivityBonus = Profile->bIsAvailable ? 0.1f : 0.0f;

    float TotalEffectiveness = FMath::Clamp(
        BaseEffectiveness + ExperienceBonus + RatingBonus + RecentActivityBonus,
        0.0f,
        1.0f
    );

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healer %s effectiveness: %.2f (Base: %.2f, Experience: %.2f, Rating: %.2f, Activity: %.2f)"),
           *HealerID, TotalEffectiveness, BaseEffectiveness, ExperienceBonus, RatingBonus, RecentActivityBonus);

    return TotalEffectiveness;
}

bool UCommunityHealingManager::CreateHealingSession(const FString& SessionID, const FString& VictimID, const FString& HealerID, EHealingSessionType SessionType)
{
    // Create healing session using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Creating healing session - ID: %s, Victim: %s, Healer: %s, Type: %d"),
           *SessionID, *VictimID, *HealerID, (int32)SessionType);

    if (SessionID.IsEmpty() || VictimID.IsEmpty() || HealerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid parameters for healing session creation"));
        return false;
    }

    // Check if session already exists
    if (ActiveSessions.Contains(SessionID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Session already exists: %s"), *SessionID);
        return false;
    }

    // Check if we're at max capacity
    if (ActiveSessions.Num() >= MaxConcurrentSessions)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Maximum concurrent sessions reached"));
        return false;
    }

    // Validate healer
    const FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID);
    if (!HealerProfile)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healer not registered: %s"), *HealerID);
        return false;
    }

    if (!HealerProfile->Specializations.Contains(SessionType))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Healer %s does not support session type %d"),
               *HealerID, (int32)SessionType);
        return false;
    }

    // Create new session
    FHealingSession NewSession;
    NewSession.SessionID = SessionID;
    NewSession.VictimPlayerID = VictimID;
    NewSession.HealerPlayerIDs.Add(HealerID);
    NewSession.SessionType = SessionType;
    NewSession.Status = EHealingStatus::Pending;
    NewSession.StartTime = FDateTime::Now();

    // Add to active sessions
    ActiveSessions.Add(SessionID, NewSession);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing session created successfully: %s"), *SessionID);

    return true;
}

bool UCommunityHealingManager::StartHealingSession(const FString& SessionID)
{
    // Start healing session using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Starting healing session: %s"), *SessionID);

    if (SessionID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid session ID"));
        return false;
    }

    // Find the session
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Session not found: %s"), *SessionID);
        return false;
    }

    // Check if session is in correct state
    if (Session->Status != EHealingStatus::Pending)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Session %s is not in pending state (Current: %d)"),
               *SessionID, (int32)Session->Status);
        return false;
    }

    // Update session status
    Session->Status = EHealingStatus::Active;
    Session->StartTime = FDateTime::Now();

    // Set up session timer
    if (UWorld* World = GetWorld())
    {
        FTimerHandle SessionTimer;
        World->GetTimerManager().SetTimer(
            SessionTimer,
            FTimerDelegate::CreateUFunction(this, FName("OnSessionTimeout"), SessionID),
            MaxSessionDuration,
            false
        );

        // Store timer handle (would need to add this to session struct in production)
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Session timer set for %f seconds"), MaxSessionDuration);
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing session started successfully: %s"), *SessionID);

    return true;
}

bool UCommunityHealingManager::EndHealingSession(const FString& SessionID, const FString& Reason)
{
    // End healing session using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Ending healing session: %s, Reason: %s"), *SessionID, *Reason);

    if (SessionID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid session ID"));
        return false;
    }

    // Find the session
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Session not found: %s"), *SessionID);
        return false;
    }

    // Calculate session duration
    FTimespan SessionDuration = FDateTime::Now() - Session->StartTime;

    // Update session status
    if (Reason.Contains(TEXT("completed")) || Reason.Contains(TEXT("success")))
    {
        Session->Status = EHealingStatus::Completed;
    }
    else
    {
        Session->Status = EHealingStatus::Cancelled;
    }

    Session->EndTime = FDateTime::Now();

    // Update healer statistics using EXACT properties
    for (const FString& HealerID : Session->HealerPlayerIDs)
    {
        FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID);
        if (HealerProfile)
        {
            if (Session->Status == EHealingStatus::Completed)
            {
                HealerProfile->SuccessfulHealingSessions++;
                HealerProfile->LastActiveTime = FDateTime::Now();

                // Update average rating (simplified calculation)
                float NewRating = 4.5f; // Default good rating for completed session
                HealerProfile->AverageSessionRating =
                    (HealerProfile->AverageSessionRating * (HealerProfile->SuccessfulHealingSessions - 1) + NewRating) /
                    HealerProfile->SuccessfulHealingSessions;
            }
            else
            {
                // Session was cancelled - update last active time but don't increment success count
                HealerProfile->LastActiveTime = FDateTime::Now();
            }

            UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Updated statistics for healer: %s"), *HealerID);
        }
    }

    // Archive session data (move to completed sessions)
    CompletedSessions.Add(SessionID, *Session);

    // Remove from active sessions
    ActiveSessions.Remove(SessionID);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Healing session ended successfully: %s (Duration: %.1f minutes)"),
           *SessionID, SessionDuration.GetTotalMinutes());

    return true;
}

bool UCommunityHealingManager::IsPlayerHealer(const FString& SessionID, const FString& PlayerID)
{
    // Check if player is healer in session using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Checking if player %s is healer in session %s"), *PlayerID, *SessionID);

    if (SessionID.IsEmpty() || PlayerID.IsEmpty())
    {
        return false;
    }

    // Find the session
    const FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        // Also check completed sessions
        Session = CompletedSessions.Find(SessionID);
        if (!Session)
        {
            return false;
        }
    }

    // Check if player is in healer list using EXACT property
    bool bIsHealer = Session->HealerPlayerIDs.Contains(PlayerID);

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Player %s is %s healer in session %s"),
           *PlayerID, bIsHealer ? TEXT("a") : TEXT("not a"), *SessionID);

    return bIsHealer;
}
