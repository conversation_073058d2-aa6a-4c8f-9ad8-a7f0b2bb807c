#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "PCGComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronPrismalFlow.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronPrismalIsland;

/**
 * Prismal Flow segment data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPrismalFlowSegment
{
    GENERATED_BODY()

    /** Segment start location */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    FVector StartLocation;

    /** Segment end location */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    FVector EndLocation;

    /** Segment width */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    float Width;

    /** Flow speed in this segment */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    float FlowSpeed;

    /** Flow direction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    FVector FlowDirection;

    /** Controlling team (0 = neutral, 1 = team A, 2 = team B) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    int32 ControllingTeam;

    /** Segment color based on control */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    FLinearColor SegmentColor;

    /** Islands in this segment */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Segment")
    TArray<TObjectPtr<AAuracronPrismalIsland>> Islands;

    FPrismalFlowSegment()
    {
        StartLocation = FVector::ZeroVector;
        EndLocation = FVector::ZeroVector;
        Width = 1000.0f;
        FlowSpeed = 500.0f;
        FlowDirection = FVector::ForwardVector;
        ControllingTeam = 0;
        SegmentColor = FLinearColor::White;
    }
};

/**
 * Prismal Flow configuration
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPrismalFlowConfig
{
    GENERATED_BODY()

    /** Total flow length */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float TotalLength;

    /** Number of serpentine curves */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    int32 CurveCount;

    /** Curve amplitude */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float CurveAmplitude;

    /** Flow pattern change interval */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float PatternChangeInterval;

    /** Base flow speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float BaseFlowSpeed;

    /** Flow speed variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float FlowSpeedVariation;

    /** Energy intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    float EnergyIntensity;

    FPrismalFlowConfig()
    {
        TotalLength = 10000.0f;
        CurveCount = 8;
        CurveAmplitude = 2000.0f;
        PatternChangeInterval = 600.0f; // 10 minutes
        BaseFlowSpeed = 500.0f;
        FlowSpeedVariation = 200.0f;
        EnergyIntensity = 1.0f;
    }
};

/**
 * Auracron Prismal Flow
 * 
 * The central energy river that connects all three layers:
 * - Serpentine pattern that changes every 10 minutes
 * - Contains 23 strategic islands (5 Nexus, 8 Santuário, 6 Arsenal, 4 Caos)
 * - Variable flow speed affects movement and abilities
 * - Color changes based on controlling team
 * - Connects all three vertical layers
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronPrismalFlow : public AActor
{
    GENERATED_BODY()

public:
    AAuracronPrismalFlow();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Flow management
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void InitializeFlow();

    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void RegenerateFlowPattern();

    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void UpdateFlowDynamics(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void SetFlowSpeed(float NewSpeed);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Prismal Flow")
    float GetFlowSpeed() const { return CurrentFlowSpeed; }

    // Team control
    UFUNCTION(BlueprintCallable, Category = "Team Control")
    void SetSegmentControl(int32 SegmentIndex, int32 TeamID);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Team Control")
    int32 GetSegmentControl(int32 SegmentIndex) const;

    UFUNCTION(BlueprintCallable, Category = "Team Control")
    void UpdateFlowColors();

    // Island management
    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void SpawnPrismalIslands();

    UFUNCTION(BlueprintCallable, Category = "Island Management")
    void UpdateIslandStates(float DeltaTime);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Management")
    TArray<AAuracronPrismalIsland*> GetIslandsByType(EPrismalIslandType IslandType) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Island Management")
    AAuracronPrismalIsland* GetNearestIsland(const FVector& Location, EPrismalIslandType IslandType = EPrismalIslandType::Nexus) const;

    // Flow interaction
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Flow Interaction")
    bool IsLocationInFlow(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Flow Interaction")
    FVector GetFlowVelocityAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Flow Interaction")
    float GetFlowIntensityAtLocation(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Flow Interaction")
    void ApplyFlowEffectToActor(AActor* Actor);

    // Visual effects
    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void UpdateFlowVisuals(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void SetFlowIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void PlayFlowTransitionEffect();

    // Debug functions
    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugShowFlowPath();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugShowIslandLocations();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugRegenerateFlow();

protected:
    // Core components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USplineComponent> FlowSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<USplineMeshComponent>> FlowMeshComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UNiagaraComponent>> FlowEffectComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> FlowAudioComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UPCGComponent>> FlowPCGComponents;

    // Flow components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> FlowMeshComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UNiagaraComponent> FlowVFX;

    // Flow configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    FPrismalFlowConfig FlowConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    TObjectPtr<UStaticMesh> FlowMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    TObjectPtr<UMaterialInterface> FlowMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    TObjectPtr<UNiagaraSystem> FlowEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow Configuration")
    TObjectPtr<USoundBase> FlowAudio;

    // Flow state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow State")
    TArray<FPrismalFlowSegment> FlowSegments;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow State")
    float CurrentFlowSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow State")
    float LastPatternChange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flow State")
    int32 CurrentPatternSeed;

    // Island data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Islands")
    TArray<TObjectPtr<AAuracronPrismalIsland>> SpawnedIslands;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Islands")
    TMap<EPrismalIslandType, int32> IslandCounts;

    // Dynamic materials
    UPROPERTY()
    TArray<TObjectPtr<UMaterialInstanceDynamic>> DynamicMaterials;

private:
    // Flow generation
    void GenerateFlowSpline();
    void CreateFlowMeshes();
    void SetupFlowEffects();
    void ConfigureFlowAudio();
    
    // Serpentine pattern generation
    TArray<FVector> GenerateSerpentinePoints();
    void ApplyCurveVariation();
    void EnsureLayerConnectivity();
    
    // Island placement
    void PlaceIslandsByType(EPrismalIslandType IslandType, int32 Count);
    FVector FindOptimalIslandLocation(EPrismalIslandType IslandType) const;
    bool ValidateIslandPlacement(const FVector& Location, float Radius) const;
    
    // Visual updates
    void UpdateFlowMaterials();
    void UpdateFlowEffects();
    void UpdateFlowAudio();
    
    // Performance optimization
    void OptimizeFlowRendering();
    void UpdateFlowLOD(const FVector& ViewerLocation);
    void CullDistantSegments(const FVector& ViewerLocation, float CullDistance);
    
    // Utility functions
    int32 GetSegmentIndexAtLocation(const FVector& Location) const;
    float GetDistanceAlongSpline(const FVector& Location) const;
    FVector GetClosestPointOnFlow(const FVector& Location) const;
    bool IsLocationNearFlow(const FVector& Location, float Tolerance = 500.0f) const;

    // Missing utility methods
    TArray<AAuracronDynamicRail*> GetActiveRails() const;
    void OptimizeIslandRendering(const FVector& ViewerLocation);
    void HandlePlayerFlowEntry(APawn* Player);
    void HandlePlayerFlowExit(APawn* Player);
    void ProcessFlowSegmentCapture(int32 SegmentIndex, int32 CapturingTeam);
    void PlaySegmentCaptureEffects(int32 SegmentIndex, int32 CapturingTeam);
    void AwardSegmentCaptureRewards(int32 TeamID, int32 SegmentIndex);
    void AwardPlayerCaptureReward(APawn* Player, int32 SegmentIndex);
    int32 GetPlayerTeamID(APawn* Player) const;

    // Missing generation methods
    void InitializeFlowSegments();
    void AssignIslandsToSegments();
    void RepositionIslandsForNewPattern();
    void AddLayerConnectionPoint(float TargetZ);
    void GenerateFlowMeshSegments();
    float GetLayerHeight(int32 LayerIndex) const;
    void UpdateIslandFlowInteraction(AAuracronPrismalIsland* Island, float DeltaTime);
    void UpdateIslandTeamControl(AAuracronPrismalIsland* Island);
    FLinearColor CalculateDominantTeamColor() const;
    float CalculateTeamControlStrength() const;
    float CalculateFlowActivityLevel() const;
    float GetIslandRadiusForType(EPrismalIslandType IslandType) const;
    
    // State management
    bool bIsInitialized;
    bool bFlowActive;
    float LastUpdateTime;
    
    // Performance tracking
    float LastPerformanceUpdate;
    int32 ActiveSegmentCount;
    int32 VisibleIslandCount;
};
