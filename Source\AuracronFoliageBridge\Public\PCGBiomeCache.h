// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGBiomeCache.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPCGBiomeCache, Log, All);

/**
 * Enum for PCG biome cache states
 */
UENUM(BlueprintType)
enum class EPCGBiomeCacheState : uint8
{
    Uninitialized   UMETA(DisplayName = "Uninitialized"),
    Loading         UMETA(DisplayName = "Loading"),
    Ready           UMETA(DisplayName = "Ready"),
    Error           UMETA(DisplayName = "Error")
};

/**
 * Struct for PCG biome cache entry
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FPCGBiomeCacheEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    FString BiomeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    TWeakObjectPtr<UPCGGraph> PCGGraph;

    UPROPERTY(EditAnywhere, Category = "PCG Biome")
    TArray<TWeakObjectPtr<UPCGComponent>> PCGComponents;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    EPCGBiomeCacheState CacheState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    FDateTime LastUpdateTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    FDateTime LastAccessTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Biome")
    bool bIsValid;

    FPCGBiomeCacheEntry()
    {
        BiomeId = TEXT("");
        PCGGraph = nullptr;
        CacheState = EPCGBiomeCacheState::Uninitialized;
        LastUpdateTime = FDateTime::Now();
        LastAccessTime = FDateTime::Now();
        bIsValid = true;
    }
};

/**
 * PCG Biome Cache Manager
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UPCGBiomeCache : public UObject
{
    GENERATED_BODY()

public:
    UPCGBiomeCache();

    // Cache Management
    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    void InitializeCache();

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    void ClearCache();

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    bool AddBiomeEntry(const FString& BiomeId, UPCGGraph* PCGGraph);

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    bool RemoveBiomeEntry(const FString& BiomeId);

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    FPCGBiomeCacheEntry GetBiomeEntry(const FString& BiomeId);

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    TArray<FString> GetAllBiomeIds() const;

    // PCG Component Management
    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    bool RegisterPCGComponent(const FString& BiomeId, UPCGComponent* PCGComponent);

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    bool UnregisterPCGComponent(const FString& BiomeId, UPCGComponent* PCGComponent);

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    TArray<UPCGComponent*> GetPCGComponents(const FString& BiomeId) const;

    // Cache State Management
    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    EPCGBiomeCacheState GetBiomeCacheState(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    void SetBiomeCacheState(const FString& BiomeId, EPCGBiomeCacheState NewState);

    // Cache Validation
    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    bool ValidateCache();

    UFUNCTION(BlueprintCallable, Category = "PCG Biome Cache")
    void RefreshCache();

protected:
    UPROPERTY()
    TMap<FString, FPCGBiomeCacheEntry> BiomeCache;

    UPROPERTY()
    bool bCacheInitialized;

private:
    void CleanupInvalidEntries();
    bool IsValidBiomeId(const FString& BiomeId) const;
};