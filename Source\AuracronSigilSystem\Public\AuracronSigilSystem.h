// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sigil System Module Header

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

/**
 * Auracron Sigil System Module
 * 
 * Advanced sigil fusion system with 150 archetype combinations
 * Integrates with UE 5.6 Gameplay Ability System and Enhanced Input
 */
class FAuracronSigilSystemModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Check if module is available */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronSigilSystem");
    }
    
    /** Get module instance */
    static inline FAuracronSigilSystemModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FAuracronSigilSystemModule>("AuracronSigilSystem");
    }
};
