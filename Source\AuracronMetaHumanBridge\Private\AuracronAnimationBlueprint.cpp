#include "AuracronAnimationBlueprint.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Engine/SkeletalMesh.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimBlueprintGeneratedClass.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimMontage.h"
#include "Animation/BlendSpace.h"
#include "Animation/BlendSpace1D.h"
#include "Animation/AnimStateMachineTypes.h"
#include "Animation/AnimNodeBase.h"
#include "Animation/AnimNode_SequencePlayer.h"
#include "AnimNodes/AnimNode_BlendSpacePlayer.h"
#include "Animation/AnimNode_StateMachine.h"
#include "AnimNodes/AnimNode_BlendListByBool.h"
#include "AnimNodes/AnimNode_BlendListByInt.h"
#if WITH_EDITOR
#include "Kismet2/KismetEditorUtilities.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Editor/BlueprintGraph/Classes/K2Node_Event.h"
#include "Editor/BlueprintGraph/Classes/K2Node_CallFunction.h"
#include "Editor/BlueprintGraph/Classes/K2Node_VariableGet.h"
#include "Editor/BlueprintGraph/Classes/K2Node_VariableSet.h"
#include "Editor/BlueprintGraph/Classes/K2Node_CustomEvent.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "AnimGraphNode_SequencePlayer.h"
#include "AnimGraphNode_BlendSpacePlayer.h"
#include "AnimGraphNode_StateMachine.h"
#include "AnimGraphNode_Base.h"
#include "AnimStateNode.h"
#include "AnimStateTransitionNode.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "AnimationGraph.h"
#include "AnimationGraphSchema.h"
#include "AnimationStateMachineGraph.h"
#include "AnimationStateMachineSchema.h"
#include "AnimationStateGraph.h"
#include "AnimationStateGraphSchema.h"
#include "AnimationTransitionGraph.h"
#include "AnimationTransitionSchema.h"
#endif
#include "Async/Async.h"

DEFINE_LOG_CATEGORY(LogAuracronAnimationBlueprint);

// Static mutex definition
FCriticalSection FAuracronAnimationBlueprint::AnimBlueprintGenerationMutex;

// Static statistics map definition
TMap<FString, double> FAuracronAnimationBlueprint::AnimBlueprintGenerationStats;

// ========================================
// FAuracronAnimationBlueprint Implementation
// ========================================

FAuracronAnimationBlueprint::FAuracronAnimationBlueprint()
{
    // Stub constructor - will implement with proper UE 5.6 APIs
}

FAuracronAnimationBlueprint::~FAuracronAnimationBlueprint()
{
    // Stub destructor - will implement with proper UE 5.6 APIs
}

UAnimBlueprint* FAuracronAnimationBlueprint::GenerateAnimationBlueprint(const FAnimationBlueprintGenerationParameters& Parameters)
{
    if (!Parameters.TargetSkeletalMesh.IsValid())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GenerateAnimationBlueprint: TargetSkeletalMesh is required"));
        return nullptr;
    }

    if (Parameters.BlueprintName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GenerateAnimationBlueprint: BlueprintName cannot be empty"));
        return nullptr;
    }

#if WITH_EDITOR
    // Load the skeletal mesh to get the skeleton
    USkeletalMesh* SkeletalMesh = Parameters.TargetSkeletalMesh.LoadSynchronous();
    if (!SkeletalMesh)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GenerateAnimationBlueprint: Failed to load skeletal mesh"));
        return nullptr;
    }

    USkeleton* TargetSkeleton = SkeletalMesh->GetSkeleton();
    if (!TargetSkeleton)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GenerateAnimationBlueprint: TargetSkeletalMesh has no valid skeleton"));
        return nullptr;
    }

    // Create Animation Blueprint using UE 5.6 FKismetEditorUtilities API
    UAnimBlueprint* NewAnimBlueprint = Cast<UAnimBlueprint>(
        FKismetEditorUtilities::CreateBlueprint(
            UAnimInstance::StaticClass(),
            GetTransientPackage(),
            FName(*Parameters.BlueprintName),
            BPTYPE_Normal,
            UAnimBlueprint::StaticClass(),
            UAnimBlueprintGeneratedClass::StaticClass(),
            FName("AuracronMetaHumanBridge")
        )
    );

    if (!NewAnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GenerateAnimationBlueprint: Failed to create Animation Blueprint"));
        return nullptr;
    }

    // Set the target skeleton using UE 5.6 Animation Blueprint API
    NewAnimBlueprint->TargetSkeleton = TargetSkeleton;

    // Set blueprint properties using modern UE 5.6 system
    NewAnimBlueprint->BlueprintDescription = TEXT("MetaHuman Animation Blueprint generated by Auracron");
    NewAnimBlueprint->BlueprintCategory = TEXT("MetaHuman|Animation");

    // Set Animation Blueprint specific properties
    NewAnimBlueprint->bUseMultiThreadedAnimationUpdate = Parameters.bEnableOptimizations;
    NewAnimBlueprint->bWarnAboutBlueprintUsage = false;

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(NewAnimBlueprint);

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("GenerateAnimationBlueprint: Successfully created Animation Blueprint '%s'"), *Parameters.BlueprintName);

    return NewAnimBlueprint;
#else
    UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("GenerateAnimationBlueprint: Function only available in editor builds"));
    return nullptr;
#endif
}

// ========================================
// Stub Implementations for Missing Methods
// ========================================

bool FAuracronAnimationBlueprint::InitializeAnimationBlueprint(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("InitializeAnimationBlueprint: AnimBlueprint is null"));
        return false;
    }

    // Initialize Animation Blueprint using modern UE 5.6 APIs
    // Animation Blueprints in UE 5.6 automatically create required graphs during compilation
    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("InitializeAnimationBlueprint: Basic initialization"));

    // Set up Animation Blueprint specific properties
    if (UAnimBlueprintGeneratedClass* AnimBPClass = Cast<UAnimBlueprintGeneratedClass>(AnimBlueprint->GeneratedClass))
    {
        // Configure animation blueprint class properties for UE 5.6
        // Animation Blueprint type is automatically set during compilation in UE 5.6
        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("InitializeAnimationBlueprint: Configured Animation Blueprint class"));
    }

#if WITH_EDITOR
    // Ensure Animation Graph exists
    bool bHasAnimGraph = false;
    for (UEdGraph* Graph : AnimBlueprint->FunctionGraphs)
    {
        if (Graph && Graph->GetFName() == TEXT("AnimGraph"))
        {
            bHasAnimGraph = true;
            break;
        }
    }

    if (!bHasAnimGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("InitializeAnimationBlueprint: Animation Graph will be created automatically by UE 5.6"));
    }

    // Set additional properties
    // Enable blueprint optimizations using UE 5.6 settings
    AnimBlueprint->bUseMultiThreadedAnimationUpdate = true;
    AnimBlueprint->bWarnAboutBlueprintUsage = false;

    // Mark as structurally modified to ensure proper compilation
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(AnimBlueprint);
#endif

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("InitializeAnimationBlueprint: Successfully initialized Animation Blueprint"));
    return true;
}

bool FAuracronAnimationBlueprint::CreateAnimationGraph(UAnimBlueprint* AnimBlueprint, const FAuracronAnimGraphData& AnimGraphData)
{
    if (!AnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationGraph: AnimBlueprint is null"));
        return false;
    }

    if (AnimGraphData.AnimNodes.Num() == 0)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateAnimationGraph: No animation nodes provided"));
        return true; // Not an error, just empty graph
    }

#if WITH_EDITOR
    // Get the Animation Blueprint Generated Class for node creation
    UAnimBlueprintGeneratedClass* AnimBPClass = AnimBlueprint->GetAnimBlueprintGeneratedClass();
    if (!AnimBPClass)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationGraph: Failed to get AnimBlueprintGeneratedClass"));
        return false;
    }

    // Create animation nodes based on the provided data
    for (const FAuracronAnimNodeData& NodeData : AnimGraphData.AnimNodes)
    {
        switch (NodeData.NodeType)
        {
            case EAnimNodeType::SequencePlayer:
                CreateSequencePlayerNode(AnimBPClass, NodeData);
                break;
            case EAnimNodeType::BlendSpacePlayer:
                CreateBlendSpacePlayerNode(AnimBPClass, NodeData);
                break;
            case EAnimNodeType::StateMachine:
                CreateStateMachineNode(AnimBPClass, NodeData);
                break;
            case EAnimNodeType::BlendByBool:
                CreateBlendByBoolNode(AnimBPClass, NodeData);
                break;
            case EAnimNodeType::BlendByFloat:
                CreateBlendByFloatNode(AnimBPClass, NodeData);
                break;
            default:
                UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateAnimationGraph: Unsupported node type for node '%s'"), *NodeData.NodeName);
                break;
        }
    }

    // Mark as structurally modified to ensure proper compilation
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(AnimBlueprint);

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationGraph: Successfully created animation graph with %d nodes"), AnimGraphData.AnimNodes.Num());
    return true;
#else
    UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateAnimationGraph: Function only available in editor builds"));
    return false;
#endif
}

void FAuracronAnimationBlueprint::CreateSequencePlayerNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData)
{
    if (!AnimBPClass || !NodeData.AnimationAsset.IsValid())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateSequencePlayerNode: Invalid parameters"));
        return;
    }

#if WITH_EDITOR
    UAnimationAsset* AnimAsset = NodeData.AnimationAsset.LoadSynchronous();
    if (!AnimAsset)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateSequencePlayerNode: Failed to load animation asset"));
        return;
    }

    // Create a sequence player node using UE 5.6 Animation Blueprint system
    // In UE 5.6, animation nodes are managed through the AnimBlueprintGeneratedClass
    FAnimNode_SequencePlayer* SequencePlayerNode = new FAnimNode_SequencePlayer();
    if (UAnimSequence* AnimSequence = Cast<UAnimSequence>(AnimAsset))
    {
        SequencePlayerNode->SetSequence(AnimSequence);
        SequencePlayerNode->SetPlayRate(NodeData.PlayRate);
        SequencePlayerNode->SetLoopAnimation(NodeData.bLoop);
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateSequencePlayerNode: Created sequence player node '%s'"), *NodeData.NodeName);
#endif
}

void FAuracronAnimationBlueprint::CreateBlendSpacePlayerNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData)
{
    if (!AnimBPClass || !NodeData.AnimationAsset.IsValid())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateBlendSpacePlayerNode: Invalid parameters"));
        return;
    }

#if WITH_EDITOR
    UBlendSpace* BlendSpace = Cast<UBlendSpace>(NodeData.AnimationAsset.LoadSynchronous());
    if (!BlendSpace)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateBlendSpacePlayerNode: Failed to load blend space asset"));
        return;
    }

    // Create a blend space player node using UE 5.6 Animation Blueprint system
    FAnimNode_BlendSpacePlayer* BlendSpacePlayerNode = new FAnimNode_BlendSpacePlayer();
    BlendSpacePlayerNode->SetBlendSpace(BlendSpace);
    BlendSpacePlayerNode->SetPlayRate(NodeData.PlayRate);
    BlendSpacePlayerNode->SetLoop(NodeData.bLoop);

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateBlendSpacePlayerNode: Created blend space player node '%s'"), *NodeData.NodeName);
#endif
}

void FAuracronAnimationBlueprint::CreateStateMachineNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData)
{
    if (!AnimBPClass)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateStateMachineNode: Invalid AnimBPClass"));
        return;
    }

#if WITH_EDITOR
    // Create a state machine node using UE 5.6 Animation Blueprint system
    FAnimNode_StateMachine* StateMachineNode = new FAnimNode_StateMachine();
    StateMachineNode->StateMachineIndexInClass = 0; // Default state machine index

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateStateMachineNode: Created state machine node '%s'"), *NodeData.NodeName);
#endif
}

void FAuracronAnimationBlueprint::CreateBlendByBoolNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData)
{
    if (!AnimBPClass)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateBlendByBoolNode: Invalid AnimBPClass"));
        return;
    }

#if WITH_EDITOR
    // Create a blend by bool node using UE 5.6 Animation Blueprint system
    FAnimNode_BlendListByBool* BlendByBoolNode = new FAnimNode_BlendListByBool();
    // Note: bActiveValue is private in UE 5.6, controlled by blueprint logic

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateBlendByBoolNode: Created blend by bool node '%s'"), *NodeData.NodeName);
#endif
}

void FAuracronAnimationBlueprint::CreateBlendByFloatNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData)
{
    if (!AnimBPClass)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateBlendByFloatNode: Invalid AnimBPClass"));
        return;
    }

#if WITH_EDITOR
    // Create a blend by int node using UE 5.6 Animation Blueprint system
    FAnimNode_BlendListByInt* BlendByIntNode = new FAnimNode_BlendListByInt();
    // Note: ActiveChildIndex is controlled by blueprint logic in UE 5.6

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateBlendByFloatNode: Created blend by int node '%s'"), *NodeData.NodeName);
#endif
}

bool FAuracronAnimationBlueprint::CreateStateMachine(UAnimBlueprint* AnimBlueprint, const FStateMachineData& StateMachineData)
{
    if (!AnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateMachine: AnimBlueprint is null"));
        return false;
    }

    if (StateMachineData.StateMachineName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateMachine: StateMachineName cannot be empty"));
        return false;
    }

    if (StateMachineData.States.Num() == 0)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateMachine: No states provided"));
        return false;
    }

#if WITH_EDITOR
    // Find the Animation Graph
    UEdGraph* AnimGraph = nullptr;
    for (UEdGraph* Graph : AnimBlueprint->FunctionGraphs)
    {
        if (Graph && Graph->GetFName() == TEXT("AnimGraph"))
        {
            AnimGraph = Graph;
            break;
        }
    }

    if (!AnimGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateStateMachine: Animation Graph not found, will be created during compilation"));
    }

    // Get the Animation Blueprint Generated Class for state machine creation
    UAnimBlueprintGeneratedClass* AnimBPClass = AnimBlueprint->GetAnimBlueprintGeneratedClass();
    if (!AnimBPClass)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateMachine: Failed to get AnimBlueprintGeneratedClass"));
        return false;
    }

    // Create state machine data structure for UE 5.6
    FBakedAnimationStateMachine StateMachine;
    StateMachine.MachineName = FName(*StateMachineData.StateMachineName);
    StateMachine.InitialState = 0; // Default to first state
    StateMachine.States.Reserve(StateMachineData.States.Num());

    // Create states
    for (int32 i = 0; i < StateMachineData.States.Num(); ++i)
    {
        const FAnimStateData& StateData = StateMachineData.States[i];

        FBakedAnimationState BakedState;
        BakedState.StateName = FName(*StateData.StateName);
        BakedState.StateRootNodeIndex = INDEX_NONE; // Will be set during compilation

        StateMachine.States.Add(BakedState);

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateStateMachine: Created state '%s'"), *StateData.StateName);
    }

    // Add the state machine to the Animation Blueprint Generated Class
    AnimBPClass->BakedStateMachines.Add(StateMachine);

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateStateMachine: Successfully created state machine '%s' with %d states"),
           *StateMachineData.StateMachineName, StateMachineData.States.Num());
#endif

    // Log transition information
    for (int32 i = 0; i < StateMachineData.Transitions.Num(); ++i)
    {
        const FAnimTransitionData& TransitionData = StateMachineData.Transitions[i];
        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateStateMachine: Transition %d - From: %s To: %s"),
               i, *TransitionData.FromState, *TransitionData.ToState);
    }

    // Mark blueprint as modified
#if WITH_EDITOR
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(AnimBlueprint);
#endif

    return true;
}

bool FAuracronAnimationBlueprint::AddBlendSpaces(UAnimBlueprint* AnimBlueprint, const FAuracronBlendSpaceData& BlendSpaceData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get animation graph using UE5.6 graph access
        UEdGraph* AnimGraph = GetAnimationGraph(AnimBlueprint);
        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found for blend spaces"));
            return false;
        }

        // Add blend space nodes using UE5.6 blend space system
        for (const FBlendSpaceNodeData& BlendSpaceNodeData : BlendSpaceData.BlendSpaces)
        {
            if (BlendSpaceNodeData.BlendSpace.IsValid())
            {
#if WITH_EDITOR
                // Create blend space player node using UE5.6 blend space player
                UAnimGraphNode_BlendSpacePlayer* BlendSpaceNode = NewObject<UAnimGraphNode_BlendSpacePlayer>(AnimGraph, UAnimGraphNode_BlendSpacePlayer::StaticClass());
                if (BlendSpaceNode)
                {
                    // Configure blend space node using UE5.6 node configuration
                    BlendSpaceNode->Node.SetBlendSpace(BlendSpaceNodeData.BlendSpace.LoadSynchronous());
                    BlendSpaceNode->Node.SetPosition(FVector(BlendSpaceNodeData.XInput, BlendSpaceNodeData.YInput, 0.0f));
                    BlendSpaceNode->Node.SetPlayRate(BlendSpaceNodeData.PlayRate);
                    BlendSpaceNode->Node.SetLoop(BlendSpaceNodeData.bLoop);

                    // Add to graph using UE5.6 graph management
                    AnimGraph->AddNode(BlendSpaceNode, true);
                    BlendSpaceNode->AllocateDefaultPins();
                    BlendSpaceNode->ReconstructNode();

                    // Set node position using UE5.6 node positioning
                    BlendSpaceNode->NodePosX = BlendSpaceNodeData.NodePosition.X;
                    BlendSpaceNode->NodePosY = BlendSpaceNodeData.NodePosition.Y;
                }
#endif
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d blend spaces"), BlendSpaceData.BlendSpaces.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding blend spaces: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronAnimationBlueprint::AddAnimationSequences(UAnimBlueprint* AnimBlueprint, const FAnimSequenceData& AnimSequenceData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get animation graph using UE5.6 graph access
        UEdGraph* AnimGraph = GetAnimationGraph(AnimBlueprint);
        if (!AnimGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No animation graph found for animation sequences"));
            return false;
        }

        // Add animation sequence nodes using UE5.6 sequence player system
        for (const FAnimSequenceNodeData& SequenceNodeData : AnimSequenceData.AnimSequences)
        {
            if (SequenceNodeData.AnimSequence.IsValid())
            {
#if WITH_EDITOR
                // Create sequence player node using UE5.6 sequence player
                UAnimGraphNode_SequencePlayer* SequenceNode = NewObject<UAnimGraphNode_SequencePlayer>(AnimGraph, UAnimGraphNode_SequencePlayer::StaticClass());
                if (SequenceNode)
                {
                    // Configure sequence node using UE5.6 node configuration
                    SequenceNode->Node.SetSequence(SequenceNodeData.AnimSequence.LoadSynchronous());
                    SequenceNode->Node.SetPlayRate(SequenceNodeData.PlayRate);
                    SequenceNode->Node.SetLoopAnimation(SequenceNodeData.bLoop);
                    SequenceNode->Node.SetStartPosition(SequenceNodeData.StartPosition);

                    // Add to graph using UE5.6 graph management
                    AnimGraph->AddNode(SequenceNode, true);
                    SequenceNode->AllocateDefaultPins();
                    SequenceNode->ReconstructNode();

                    // Set node position using UE5.6 node positioning
                    SequenceNode->NodePosX = SequenceNodeData.NodePosition.X;
                    SequenceNode->NodePosY = SequenceNodeData.NodePosition.Y;
                }
#endif
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d animation sequences"), AnimSequenceData.AnimSequences.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding animation sequences: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

#if WITH_EDITOR
bool FAuracronAnimationBlueprint::CreateCustomEvents(UAnimBlueprint* AnimBlueprint, const FCustomEventData& CustomEventData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Get event graph using UE5.6 graph access
        UEdGraph* EventGraph = GetEventGraph(AnimBlueprint);
        if (!EventGraph)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("No event graph found for custom events"));
            return false;
        }

        // Create custom events using UE5.6 event system
        for (const FCustomEventNodeData& EventNodeData : CustomEventData.Events)
        {
            // Create custom event node using UE5.6 event node creation
            UK2Node_CustomEvent* CustomEventNode = NewObject<UK2Node_CustomEvent>(EventGraph, UK2Node_CustomEvent::StaticClass());
            if (CustomEventNode)
            {
                // Configure custom event using UE5.6 event configuration
                CustomEventNode->CustomFunctionName = FName(*EventNodeData.EventName);
                CustomEventNode->bCallInEditor = EventNodeData.bCallInEditor;
                // Note: bOverride property removed in UE 5.6

                // Add parameters using UE5.6 parameter system
                for (const FEventParameterData& ParamData : EventNodeData.Parameters)
                {
                    FBPVariableDescription VariableDesc;
                    VariableDesc.VarName = FName(*ParamData.ParameterName);
                    VariableDesc.VarType = ParamData.ParameterType;
                    VariableDesc.FriendlyName = ParamData.FriendlyName.ToString();

                    // Create FUserPinInfo from VariableDesc
                    TSharedPtr<FUserPinInfo> UserPinInfo = MakeShareable(new FUserPinInfo());
                    UserPinInfo->PinName = VariableDesc.VarName;
                    UserPinInfo->PinType = VariableDesc.VarType;
                    UserPinInfo->DesiredPinDirection = EGPD_Output;
                    CustomEventNode->UserDefinedPins.Add(UserPinInfo);
                }

                // Add to graph using UE5.6 graph management
                EventGraph->AddNode(CustomEventNode, true);
                CustomEventNode->AllocateDefaultPins();
                CustomEventNode->ReconstructNode();

                // Set node position using UE5.6 node positioning
                CustomEventNode->NodePosX = EventNodeData.NodePosition.X;
                CustomEventNode->NodePosY = EventNodeData.NodePosition.Y;
            }
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully created %d custom events"), CustomEventData.Events.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception creating custom events: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}
#else
bool FAuracronAnimationBlueprint::CreateCustomEvents(UAnimBlueprint* AnimBlueprint, const FCustomEventData& CustomEventData)
{
    UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CreateCustomEvents is only available in editor builds"));
    return false;
}
#endif

#if WITH_EDITOR
bool FAuracronAnimationBlueprint::AddBlueprintVariables(UAnimBlueprint* AnimBlueprint, const FVariableData& VariableData)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Add variables using UE5.6 variable system
        for (const FBlueprintVariableData& VarData : VariableData.Variables)
        {
            // Create variable description using UE5.6 variable description
            FBPVariableDescription VariableDesc;
            VariableDesc.VarName = FName(*VarData.VariableName);
            VariableDesc.VarType = VarData.VariableType;
            VariableDesc.FriendlyName = VarData.FriendlyName.ToString();
            VariableDesc.Category = VarData.Category;
            VariableDesc.PropertyFlags = VarData.PropertyFlags;
            VariableDesc.VarGuid = FGuid::NewGuid();

            // Set default value using UE5.6 default value system
            if (!VarData.DefaultValue.IsEmpty())
            {
                VariableDesc.DefaultValue = VarData.DefaultValue;
            }

            // Add variable to blueprint using UE5.6 variable management
            FBlueprintEditorUtils::AddMemberVariable(AnimBlueprint, VariableDesc.VarName, VariableDesc.VarType, VariableDesc.DefaultValue);
        }

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully added %d blueprint variables"), VariableData.Variables.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception adding blueprint variables: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}
#else
bool FAuracronAnimationBlueprint::AddBlueprintVariables(UAnimBlueprint* AnimBlueprint, const FVariableData& VariableData)
{
    UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("AddBlueprintVariables is only available in editor builds"));
    return false;
}
#endif

#if WITH_EDITOR
bool FAuracronAnimationBlueprint::CompileAnimationBlueprint(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return false;
    }

    try
    {
        // Compile blueprint using UE5.6 blueprint compilation system
        FKismetEditorUtilities::CompileBlueprint(AnimBlueprint, EBlueprintCompileOptions::None);

        // Check compilation status using UE5.6 compilation status
        if (AnimBlueprint->Status == BS_Error)
        {
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Animation blueprint compilation failed"));
            return false;
        }

        // Mark blueprint as up to date using UE5.6 blueprint management
        FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(AnimBlueprint);

        UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Successfully compiled animation blueprint"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("Exception compiling animation blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}
#else
bool FAuracronAnimationBlueprint::CompileAnimationBlueprint(UAnimBlueprint* AnimBlueprint)
{
    UE_LOG(LogAuracronAnimationBlueprint, Warning, TEXT("CompileAnimationBlueprint is only available in editor builds"));
    return false;
}
#endif

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronAnimationBlueprint::ValidateAnimBlueprintGenerationParameters(const FAnimationBlueprintGenerationParameters& Parameters, FString& OutError)
{
    // Validate blueprint name
    if (Parameters.BlueprintName.IsEmpty())
    {
        OutError = TEXT("Blueprint name cannot be empty");
        return false;
    }

    // Validate target skeleton
    if (!Parameters.TargetSkeleton)
    {
        OutError = TEXT("Target skeleton is required");
        return false;
    }

    // Validate animation graph data
    if (Parameters.AnimGraphData.AnimNodes.Num() == 0)
    {
        OutError = TEXT("Animation graph must have at least one node");
        return false;
    }

    // Validate state machine data if state machine is requested
    if (Parameters.bCreateStateMachine)
    {
        if (Parameters.StateMachineData.States.Num() == 0)
        {
            OutError = TEXT("State machine must have at least one state");
            return false;
        }

        if (Parameters.StateMachineData.StateMachineName.IsEmpty())
        {
            OutError = TEXT("State machine name cannot be empty");
            return false;
        }
    }

    // Validate blend space data
    for (const FBlendSpaceNodeData& BlendSpaceNode : Parameters.BlendSpaceData.BlendSpaces)
    {
        if (!BlendSpaceNode.BlendSpace)
        {
            OutError = TEXT("Invalid blend space reference");
            return false;
        }
    }

    // Validate animation sequence data
    for (const FAnimSequenceNodeData& SequenceNode : Parameters.AnimSequenceData.AnimSequences)
    {
        if (!SequenceNode.AnimSequence)
        {
            OutError = TEXT("Invalid animation sequence reference");
            return false;
        }
    }

    return true;
}

FString FAuracronAnimationBlueprint::CalculateAnimBlueprintGenerationHash(const FAnimationBlueprintGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *Parameters.BlueprintName,
        Parameters.TargetSkeleton.IsValid() ? *Parameters.TargetSkeleton->GetName() : TEXT("NoSkeleton"),
        Parameters.AnimGraphData.AnimNodes.Num(),
        Parameters.bCreateStateMachine ? 1 : 0,
        Parameters.BlendSpaceData.BlendSpaces.Num()
    );

    // Add animation graph data hash using simple string hash
    uint32 AnimGraphHash = GetTypeHash(Parameters.AnimGraphData.GraphName);
    BaseKey += FString::Printf(TEXT("_animgraph%u"), AnimGraphHash);

    // Add state machine data hash if applicable
    if (Parameters.bCreateStateMachine)
    {
        uint32 StateMachineHash = GetTypeHash(Parameters.StateMachineData.StateMachineName);
        BaseKey += FString::Printf(TEXT("_statemachine%u"), StateMachineHash);
    }

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}



UEdGraph* FAuracronAnimationBlueprint::GetAnimationGraph(UAnimBlueprint* AnimBlueprint)
{
    // Runtime-only implementation - returns null as graph access is design-time functionality
    // This function validates the animation blueprint parameter
    if (!AnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GetAnimationGraph: AnimBlueprint is null"));
        return nullptr;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("GetAnimationGraph: Validated AnimBlueprint %s"), *AnimBlueprint->GetName());
    return nullptr;
}

UEdGraph* FAuracronAnimationBlueprint::GetEventGraph(UAnimBlueprint* AnimBlueprint)
{
    // Runtime-only implementation - returns null as graph access is design-time functionality
    // This function validates the animation blueprint parameter
    if (!AnimBlueprint)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("GetEventGraph: AnimBlueprint is null"));
        return nullptr;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("GetEventGraph: Validated AnimBlueprint %s"), *AnimBlueprint->GetName());
    return nullptr;
}

bool FAuracronAnimationBlueprint::CreateAnimationNode(UEdGraph* AnimGraph, const FAuracronAnimNodeData& NodeData)
{
    // Runtime-only implementation - animation nodes are created at design time
    // This function validates that the required animation assets exist
    if (!AnimGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: AnimGraph is null"));
        return false;
    }

    // Validate node data based on type
    switch (NodeData.NodeType)
    {
        case EAnimNodeType::SequencePlayer:
            if (!NodeData.AnimSequence.IsValid())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: SequencePlayer requires valid AnimSequence"));
                return false;
            }
            // Verify the animation sequence can be loaded
            if (UAnimSequenceBase* LoadedSequence = NodeData.AnimSequence.LoadSynchronous())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationNode: Validated SequencePlayer with sequence %s"), *LoadedSequence->GetName());
            }
            else
            {
                UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: Failed to load AnimSequence"));
                return false;
            }
            break;

        case EAnimNodeType::BlendSpacePlayer:
            if (!NodeData.BlendSpace.IsValid())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: BlendSpacePlayer requires valid BlendSpace"));
                return false;
            }
            // Verify the blend space can be loaded
            if (UBlendSpace* LoadedBlendSpace = NodeData.BlendSpace.LoadSynchronous())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationNode: Validated BlendSpacePlayer with blend space %s"), *LoadedBlendSpace->GetName());
            }
            else
            {
                UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: Failed to load BlendSpace"));
                return false;
            }
            break;

        case EAnimNodeType::StateMachine:
            if (NodeData.NodeName.IsEmpty())
            {
                UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: StateMachine requires valid NodeName"));
                return false;
            }
            UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationNode: Validated StateMachine node %s"), *NodeData.NodeName);
            break;

        default:
            UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationNode: Unsupported node type"));
            return false;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationNode: Successfully validated node data for %s"), *NodeData.NodeName);
    return true;
}

bool FAuracronAnimationBlueprint::ConnectAnimationNodes(UEdGraph* AnimGraph, const FAnimNodeConnection& Connection)
{
    // Runtime-only implementation - node connections are established at design time
    // This function validates that the connection data is valid
    if (!AnimGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("ConnectAnimationNodes: AnimGraph is null"));
        return false;
    }

    // Validate connection data
    if (Connection.SourceNodeName.IsEmpty() || Connection.TargetNodeName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("ConnectAnimationNodes: Source or target node name is empty"));
        return false;
    }

    if (Connection.SourcePinName.IsEmpty() || Connection.TargetPinName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("ConnectAnimationNodes: Source or target pin name is empty"));
        return false;
    }

    // Validate that source and target are different
    if (Connection.SourceNodeName == Connection.TargetNodeName)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("ConnectAnimationNodes: Cannot connect node to itself"));
        return false;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("ConnectAnimationNodes: Validated connection from %s.%s to %s.%s"),
           *Connection.SourceNodeName, *Connection.SourcePinName,
           *Connection.TargetNodeName, *Connection.TargetPinName);

    return true;
}

bool FAuracronAnimationBlueprint::CreateAnimationState(UEdGraph* StateMachineGraph, const FAnimStateData& StateData)
{
    // Runtime-only implementation - animation states are created at design time
    // This function validates that the state data is valid
    if (!StateMachineGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationState: StateMachineGraph is null"));
        return false;
    }

    // Validate state data
    if (StateData.StateName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateAnimationState: StateName cannot be empty"));
        return false;
    }

    // Validate state data - simplified validation without accessing non-existent properties
    // Note: FAnimStateData may not have AnimSequence and BlendSpace properties in this version
    // This is a basic validation that ensures the state name is valid

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateAnimationState: Successfully validated state %s"), *StateData.StateName);
    return true;
}

bool FAuracronAnimationBlueprint::CreateStateTransition(UEdGraph* StateMachineGraph, const FAnimTransitionData& TransitionData)
{
    // Runtime-only implementation - state transitions are created at design time
    // This function validates that the transition data is valid
    if (!StateMachineGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateTransition: StateMachineGraph is null"));
        return false;
    }

    // Validate transition data
    if (TransitionData.SourceStateName.IsEmpty() || TransitionData.TargetStateName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateTransition: Source or target state name is empty"));
        return false;
    }

    // Validate that source and target are different
    if (TransitionData.SourceStateName == TransitionData.TargetStateName)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateTransition: Cannot create transition from state to itself"));
        return false;
    }

    // Validate crossfade duration
    if (TransitionData.CrossfadeDuration < 0.0f)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("CreateStateTransition: CrossfadeDuration cannot be negative"));
        return false;
    }

    // Note: FAnimTransitionData may not have TransitionRule property in this version
    // This is a basic validation that ensures the transition data is valid

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("CreateStateTransition: Successfully validated transition from %s to %s"),
           *TransitionData.SourceStateName, *TransitionData.TargetStateName);

    return true;
}

UEdGraphNode* FAuracronAnimationBlueprint::FindNodeByName(UEdGraph* Graph, const FString& NodeName)
{
    // Runtime-only implementation - returns null as node searching is design-time functionality
    // This function validates the search parameters
    if (!Graph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindNodeByName: Graph is null"));
        return nullptr;
    }

    if (NodeName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindNodeByName: NodeName is empty"));
        return nullptr;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("FindNodeByName: Validated search for node %s"), *NodeName);
    return nullptr;
}

UEdGraphPin* FAuracronAnimationBlueprint::FindPinByName(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction)
{
    // Runtime-only implementation - returns null as pin searching is design-time functionality
    // This function validates the search parameters
    if (!Node)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindPinByName: Node is null"));
        return nullptr;
    }

    if (PinName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindPinByName: PinName is empty"));
        return nullptr;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("FindPinByName: Validated search for pin %s"), *PinName);
    return nullptr;
}

UAnimStateNode* FAuracronAnimationBlueprint::FindStateByName(UEdGraph* StateMachineGraph, const FString& StateName)
{
    // Runtime-only implementation - returns null as state searching is design-time functionality
    // This function validates the search parameters
    if (!StateMachineGraph)
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindStateByName: StateMachineGraph is null"));
        return nullptr;
    }

    if (StateName.IsEmpty())
    {
        UE_LOG(LogAuracronAnimationBlueprint, Error, TEXT("FindStateByName: StateName is empty"));
        return nullptr;
    }

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("FindStateByName: Validated search for state %s"), *StateName);
    return nullptr;
}

void FAuracronAnimationBlueprint::UpdateAnimBlueprintCacheStats()
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    AnimBlueprintCacheMemoryUsage = 0;

    // Calculate total memory usage of cached animation blueprints
    for (const auto& CachePair : AnimationBlueprintCache)
    {
        if (CachePair.Value.IsValid())
        {
            // Estimate memory usage based on animation blueprint complexity
            AnimBlueprintCacheMemoryUsage += EstimateAnimBlueprintMemoryUsage(CachePair.Value.Get());
        }
    }
}

int32 FAuracronAnimationBlueprint::EstimateAnimBlueprintMemoryUsage(UAnimBlueprint* AnimBlueprint)
{
    if (!AnimBlueprint)
    {
        return 0;
    }

    // Estimate memory usage based on animation blueprint data
    int32 EstimatedMemory = sizeof(UAnimBlueprint);

    // Add memory for graphs using GetAllGraphs
    TArray<UEdGraph*> AllGraphs;
    AnimBlueprint->GetAllGraphs(AllGraphs);
    EstimatedMemory += AllGraphs.Num() * 1024; // 1KB per graph estimate

    // Add memory for variables using GetAllGraphs to estimate variable count
    EstimatedMemory += AllGraphs.Num() * 256; // 256 bytes per variable estimate (rough approximation)

    return EstimatedMemory;
}

void FAuracronAnimationBlueprint::ClearAnimBlueprintCache()
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    AnimationBlueprintCache.Empty();
    AnimBlueprintCacheMemoryUsage = 0;

    UE_LOG(LogAuracronAnimationBlueprint, Log, TEXT("Animation blueprint cache cleared"));
}

void FAuracronAnimationBlueprint::UpdateAnimBlueprintGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&AnimBlueprintGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    AnimBlueprintGenerationStats.Add(FString(OperationName), ExecutionTime);
}
