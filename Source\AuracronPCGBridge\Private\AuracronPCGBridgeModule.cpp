// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Module Implementation
// Bridge 2.2: PCG Framework - Module Startup and Shutdown

#include "AuracronPCGBridgeModule.h"
#include "AuracronPCGBase.h"
#include "AuracronPCGAdvanced.h"
#include "Modules/ModuleManager.h"
#include "PCGModule.h"
#include "PCGSubsystem.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY(LogAuracronPCGBridge);

#define LOCTEXT_NAMESPACE "FAuracronPCGBridgeModule"

void FAuracronPCGBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("AuracronPCGBridge module starting up"));

    // Initialize PCG Bridge systems
    InitializePCGSystems();

    // Register custom PCG nodes and elements
    RegisterPCGNodes();

    // Set up integration with other Auracron bridges
    InitializeBridgeIntegrations();

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("AuracronPCGBridge module startup complete"));
}

void FAuracronPCGBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("AuracronPCGBridge module shutting down"));

    // Clean up bridge integrations
    ShutdownBridgeIntegrations();

    // Unregister custom PCG nodes and elements
    UnregisterPCGNodes();

    // Clean up PCG Bridge systems
    ShutdownPCGSystems();

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("AuracronPCGBridge module shutdown complete"));
}

void FAuracronPCGBridgeModule::InitializePCGSystems()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Initializing Auracron PCG systems..."));

    // Initialize core PCG systems
    // This includes setting up the main PCG bridge API

    // Validate PCG module is loaded
    FModuleManager& ModuleManager = FModuleManager::Get();
    if (!ModuleManager.IsModuleLoaded("PCG"))
    {
        UE_LOG(LogAuracronPCGBridge, VeryVerbose, TEXT("PCG module not loaded - some features may not be available"));
    }

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG systems initialized"));
}

void FAuracronPCGBridgeModule::RegisterPCGNodes()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Registering Auracron PCG nodes..."));

    // PCG nodes are automatically registered through UE5.6 reflection system
    // when classes are defined with UCLASS macros

    // Log registered node types
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Registered PCG node types:"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronBiomePCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronTerrainPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronStructurePCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronVegetationPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronResourcePCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronEnemySpawnPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronDungeonPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronWeatherPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronMasterPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronBiomeTransitionPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronQuestPCGSettings"));
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("- UAuracronPerformancePCGSettings"));

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG nodes registered successfully"));
}

void FAuracronPCGBridgeModule::InitializeBridgeIntegrations()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Initializing bridge integrations..."));

    // Initialize integrations with other Auracron bridges
    IntegratedBridges.Empty();

    // Mark bridges as available for integration
    AvailableBridges.Add(TEXT("FoliageBridge"));
    AvailableBridges.Add(TEXT("DynamicRealmBridge"));
    AvailableBridges.Add(TEXT("WorldPartitionBridge"));
    AvailableBridges.Add(TEXT("CombatBridge"));
    AvailableBridges.Add(TEXT("VFXBridge"));
    AvailableBridges.Add(TEXT("AudioBridge"));
    AvailableBridges.Add(TEXT("LivingWorldBridge"));
    AvailableBridges.Add(TEXT("RealmsBridge"));
    AvailableBridges.Add(TEXT("ProgressionBridge"));
    AvailableBridges.Add(TEXT("LoreBridge"));

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Bridge integrations initialized - %d bridges available"),
           AvailableBridges.Num());
}

void FAuracronPCGBridgeModule::ShutdownPCGSystems()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Shutting down PCG systems..."));

    // Clean up any runtime PCG systems
    // This includes stopping any background generation tasks

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG systems shutdown complete"));
}

void FAuracronPCGBridgeModule::UnregisterPCGNodes()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Unregistering PCG nodes..."));

    // PCG nodes are automatically unregistered when the module shuts down
    // No explicit cleanup needed for UE5.6 reflection-based registration

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("PCG nodes unregistered"));
}

void FAuracronPCGBridgeModule::ShutdownBridgeIntegrations()
{
    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Shutting down bridge integrations..."));

    // Clean up bridge integrations
    IntegratedBridges.Empty();
    AvailableBridges.Empty();

    UE_LOG(LogAuracronPCGBridge, Log, TEXT("Bridge integrations shutdown complete"));
}

bool FAuracronPCGBridgeModule::IsBridgeAvailable(const FString& BridgeName) const
{
    return AvailableBridges.Contains(BridgeName);
}

bool FAuracronPCGBridgeModule::IsBridgeIntegrated(const FString& BridgeName) const
{
    return IntegratedBridges.Contains(BridgeName);
}

void FAuracronPCGBridgeModule::RegisterBridgeIntegration(const FString& BridgeName)
{
    if (AvailableBridges.Contains(BridgeName))
    {
        IntegratedBridges.AddUnique(BridgeName);
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Bridge integration registered: %s"), *BridgeName);
    }
    else
    {
        UE_LOG(LogAuracronPCGBridge, Warning, TEXT("Attempted to register unknown bridge: %s"), *BridgeName);
    }
}

void FAuracronPCGBridgeModule::UnregisterBridgeIntegration(const FString& BridgeName)
{
    if (IntegratedBridges.Remove(BridgeName) > 0)
    {
        UE_LOG(LogAuracronPCGBridge, Log, TEXT("Bridge integration unregistered: %s"), *BridgeName);
    }
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronPCGBridgeModule, AuracronPCGBridge)
