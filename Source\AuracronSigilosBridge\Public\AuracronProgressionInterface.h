#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "AuracronProgressionInterface.generated.h"

/**
 * Production Ready: UE 5.6 Progression Interface for Auracron Sigil System
 * This interface provides progression-related functionality for the Auracron system
 * Following official UE 5.6 interface implementation patterns
 */

/*
Empty class for reflection system visibility.
Uses the UINTERFACE macro.
Inherits from UInterface.
*/
UINTERFACE(MinimalAPI, Blueprintable, BlueprintType)
class UAuracronProgressionInterface : public UInterface
{
    GENERATED_BODY()
};

/* Actual Interface declaration. */
class AURACRONSIGILOSBRIDGE_API IAuracronProgressionInterface
{
    GENERATED_BODY()

public:
    /**
     * Production Ready: Register a progression participant with the system
     * @param Participant The actor to register for progression tracking
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    void RegisterProgressionParticipant(AActor* Participant);
    virtual void RegisterProgressionParticipant_Implementation(AActor* Participant) {}

    /**
     * Production Ready: Unregister a progression participant from the system
     * @param Participant The actor to unregister from progression tracking
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    void UnregisterProgressionParticipant(AActor* Participant);
    virtual void UnregisterProgressionParticipant_Implementation(AActor* Participant) {}

    /**
     * Production Ready: Award experience points to a participant
     * @param Participant The actor receiving experience
     * @param ExperienceAmount The amount of experience to award
     * @param Source The source of the experience (e.g., "SigilActivation", "Combat", etc.)
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    void AwardExperience(AActor* Participant, int32 ExperienceAmount, const FString& Source);
    virtual void AwardExperience_Implementation(AActor* Participant, int32 ExperienceAmount, const FString& Source) {}

    /**
     * Production Ready: Handle sigil level up event
     * @param Participant The actor whose sigil leveled up
     * @param SigilType The type of sigil that leveled up
     * @param NewLevel The new level of the sigil
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    void OnSigilLevelUp(AActor* Participant, const FString& SigilType, int32 NewLevel);
    virtual void OnSigilLevelUp_Implementation(AActor* Participant, const FString& SigilType, int32 NewLevel) {}

    /**
     * Production Ready: Handle archetype unlock event
     * @param Participant The actor who unlocked the archetype
     * @param ArchetypeName The name of the unlocked archetype
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    void OnArchetypeUnlocked(AActor* Participant, const FString& ArchetypeName);
    virtual void OnArchetypeUnlocked_Implementation(AActor* Participant, const FString& ArchetypeName) {}

    /**
     * Production Ready: Get the current level of a participant
     * @param Participant The actor to check
     * @return The current level of the participant
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    int32 GetParticipantLevel(AActor* Participant) const;
    virtual int32 GetParticipantLevel_Implementation(AActor* Participant) const { return 1; }

    /**
     * Production Ready: Get the current experience of a participant
     * @param Participant The actor to check
     * @return The current experience points of the participant
     */
    UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Auracron Progression")
    int32 GetParticipantExperience(AActor* Participant) const;
    virtual int32 GetParticipantExperience_Implementation(AActor* Participant) const { return 0; }
};
