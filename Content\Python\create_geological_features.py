#!/usr/bin/env python3
"""
AURACRON - Geological Features Creation Script
Creates 8 crystal plateaus and 4 living canyons for Planície Radiante
Uses AuracronPCGBridge C++ bridge and UE5.6 Python API
Production-ready implementation with complete error handling
"""

import unreal
import sys
import os
import math
import random

class GeologicalFeaturesCreator:
    """Production-ready geological features creator for Planície Radiante"""
    
    def __init__(self):
        """Initialize the geological features creator with UE5.6 subsystems"""
        try:
            # Initialize UE5.6 subsystems
            self.editor_level_lib = unreal.EditorLevelLibrary
            self.editor_asset_lib = unreal.EditorAssetLibrary
            self.editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
            self.landscape_subsystem = unreal.get_editor_subsystem(unreal.LandscapeSubsystem)
            
            # Initialize AuracronPCGBridge
            self.pcg_bridge = unreal.get_editor_subsystem(unreal.AuracronPCGBridge)
            
            # Geological features configuration from requirements
            self.features_config = {
                'crystal_plateaus': {
                    'count': 8,
                    'size_range': {'min': 500, 'max': 1200},  # Units as per requirements
                    'height_range': {'min': 50, 'max': 200},
                    'crystal_density': 0.7,
                    'material_type': 'crystalline'
                },
                'living_canyons': {
                    'count': 4,
                    'length_range': {'min': 800, 'max': 1500},
                    'depth_range': {'min': 200, 'max': 500},
                    'width_range': {'min': 100, 'max': 300},
                    'has_water_flow': True,
                    'water_speed': 150.0  # Units per second
                }
            }
            
            # Strategic positions for crystal plateaus (8 total)
            self.plateau_positions = [
                unreal.Vector(2000, 2000, 0),    # Northeast quadrant
                unreal.Vector(-2000, -2000, 0),  # Southwest quadrant
                unreal.Vector(2000, -2000, 0),   # Southeast quadrant
                unreal.Vector(-2000, 2000, 0),   # Northwest quadrant
                unreal.Vector(3500, 0, 0),       # East center
                unreal.Vector(-3500, 0, 0),      # West center
                unreal.Vector(0, 3500, 0),       # North center
                unreal.Vector(0, -3500, 0)       # South center
            ]
            
            # Canyon path configurations (4 total)
            self.canyon_paths = [
                {
                    'name': 'Canyon_Norte_Sul',
                    'start': unreal.Vector(-4000, -2000, 0),
                    'end': unreal.Vector(-2000, 0, 0),
                    'control_points': [unreal.Vector(-3000, -1000, -50)]
                },
                {
                    'name': 'Canyon_Leste_Oeste',
                    'start': unreal.Vector(4000, 2000, 0),
                    'end': unreal.Vector(2000, 0, 0),
                    'control_points': [unreal.Vector(3000, 1000, -50)]
                },
                {
                    'name': 'Canyon_Nordeste',
                    'start': unreal.Vector(-2000, 4000, 0),
                    'end': unreal.Vector(0, 2000, 0),
                    'control_points': [unreal.Vector(-1000, 3000, -50)]
                },
                {
                    'name': 'Canyon_Sudeste',
                    'start': unreal.Vector(2000, -4000, 0),
                    'end': unreal.Vector(0, -2000, 0),
                    'control_points': [unreal.Vector(1000, -3000, -50)]
                }
            ]
            
            unreal.log("✅ GeologicalFeaturesCreator initialized successfully")
            
        except Exception as e:
            unreal.log_error(f"❌ Failed to initialize GeologicalFeaturesCreator: {str(e)}")
            raise
    
    def verify_ue56_compatibility(self):
        """Verify UE5.6 compatibility and required APIs"""
        try:
            # Check engine version
            engine_version = unreal.SystemLibrary.get_engine_version()
            unreal.log(f"🔍 Engine Version: {engine_version}")
            
            # Verify required APIs are available
            required_apis = [
                'WaterBodyRiver',
                'ProceduralMeshComponent',
                'StaticMeshActor',
                'MaterialInstanceDynamic',
                'SplineComponent',
                'LandscapeSplineActor'
            ]
            
            for api in required_apis:
                if hasattr(unreal, api):
                    unreal.log(f"✅ {api} API available")
                else:
                    unreal.log_error(f"❌ {api} API not available")
                    return False
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ UE5.6 compatibility check failed: {str(e)}")
            return False    
            
        def load_planicie_radiante_level(self):

            """Load the Planície Radiante level for geological feature creation"""
        
        try:
            level_path = "/Game/Levels/Realms/PlanicieRadiante"
            
            # Check if level exists
            if not self.editor_asset_lib.does_asset_exist(level_path):
                unreal.log_error(f"❌ Planície Radiante level not found: {level_path}")
                unreal.log_error("❌ Please run Task 1.1 first to create the base terrain")
                return False
            
            # Load the level
            if not self.editor_level_lib.load_level(level_path):
                unreal.log_error(f"❌ Failed to load level: {level_path}")
                return False
            
            unreal.log(f"✅ Planície Radiante level loaded: {level_path}")
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Level loading failed: {str(e)}")
            return False
    
    def create_crystal_plateau(self, position, plateau_index):
        """Create a single crystal plateau with procedural generation"""
        try:
            unreal.log(f"💎 Creating Crystal Plateau {plateau_index + 1} at {position}")
            
            # Generate random size within specified range
            config = self.features_config['crystal_plateaus']
            size = random.uniform(config['size_range']['min'], config['size_range']['max'])
            height = random.uniform(config['height_range']['min'], config['height_range']['max'])
            
            # Create static mesh actor for the plateau base
            plateau_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.StaticMeshActor,
                position,
                unreal.Rotator(0, 0, 0)
            )
            
            if plateau_actor:
                # Set plateau name
                plateau_actor.set_actor_label(f"Crystal_Plateau_{plateau_index + 1}")
                
                # Scale the plateau to the generated size
                scale_factor = size / 100.0  # Convert to UE scale
                plateau_actor.set_actor_scale3d(unreal.Vector(scale_factor, scale_factor, height / 100.0))
                
                # Create material instance for crystalline appearance
                material_instance = unreal.MaterialInstanceDynamic.create(
                    None,  # Parent material will be set by PCG bridge
                    plateau_actor
                )
                
                if material_instance:
                    # Set crystalline material properties
                    material_instance.set_vector_parameter_value(
                        "BaseColor", 
                        unreal.LinearColor(0.8, 0.6, 0.2, 1.0)  # Golden crystal color
                    )
                    material_instance.set_scalar_parameter_value("Metallic", 0.8)
                    material_instance.set_scalar_parameter_value("Roughness", 0.2)
                    material_instance.set_scalar_parameter_value("Emissive", 0.3)
                    
                    # Apply crystal density parameter
                    material_instance.set_scalar_parameter_value(
                        "CrystalDensity", 
                        config['crystal_density']
                    )
                
                # Add procedural crystal formations using PCG
                if self.pcg_bridge:
                    crystal_config = {
                        'formation_type': 'crystalline_cluster',
                        'density': config['crystal_density'],
                        'size_variation': 0.3,
                        'height_variation': 0.4,
                        'crystal_count': int(size / 50)  # Scale crystal count with plateau size
                    }
                    
                    try:
                        self.pcg_bridge.generate_crystal_formation(plateau_actor, crystal_config)
                        unreal.log(f"✅ Crystal formation generated for Plateau {plateau_index + 1}")
                    except Exception as e:
                        unreal.log_warning(f"⚠️ PCG crystal formation failed: {str(e)}")
                
                unreal.log(f"✅ Crystal Plateau {plateau_index + 1} created successfully")
                return plateau_actor
            else:
                unreal.log_error(f"❌ Failed to create plateau actor at {position}")
                return None
                
        except Exception as e:
            unreal.log_error(f"❌ Crystal plateau creation failed: {str(e)}")
            return None
    
    def create_all_crystal_plateaus(self):
        """Create all 8 crystal plateaus as per requirements"""
        try:
            unreal.log("💎 Creating 8 Crystal Plateaus...")
            
            created_plateaus = []
            
            for i, position in enumerate(self.plateau_positions):
                plateau = self.create_crystal_plateau(position, i)
                if plateau:
                    created_plateaus.append(plateau)
                else:
                    unreal.log_error(f"❌ Failed to create Crystal Plateau {i + 1}")
            
            unreal.log(f"✅ Created {len(created_plateaus)}/8 Crystal Plateaus")
            
            if len(created_plateaus) == 8:
                unreal.log("🎉 All Crystal Plateaus created successfully!")
                return True
            else:
                unreal.log_warning(f"⚠️ Only {len(created_plateaus)} plateaus created out of 8")
                return len(created_plateaus) > 0
                
        except Exception as e:
            unreal.log_error(f"❌ Crystal plateaus creation failed: {str(e)}")
            return False 
    def create_canyon_spline(self, canyon_config):
        """Create a spline path for a living canyon"""
        try:
            unreal.log(f"🏔️ Creating canyon spline: {canyon_config['name']}")
            
            # Create landscape spline actor for canyon path
            spline_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.LandscapeSplineActor,
                canyon_config['start'],
                unreal.Rotator(0, 0, 0)
            )
            
            if spline_actor:
                spline_actor.set_actor_label(canyon_config['name'])
                
                # Get spline component
                spline_component = spline_actor.get_component_by_class(unreal.SplineComponent)
                
                if spline_component:
                    # Clear existing spline points
                    spline_component.clear_spline_points()
                    
                    # Add start point
                    spline_component.add_spline_point(canyon_config['start'], unreal.SplineCoordinateSpace.WORLD)
                    
                    # Add control points for natural canyon curves
                    for control_point in canyon_config['control_points']:
                        spline_component.add_spline_point(control_point, unreal.SplineCoordinateSpace.WORLD)
                    
                    # Add end point
                    spline_component.add_spline_point(canyon_config['end'], unreal.SplineCoordinateSpace.WORLD)
                    
                    # Configure spline properties for canyon
                    spline_component.set_closed_loop(False)
                    spline_component.update_spline()
                    
                    # Set canyon width based on configuration
                    config = self.features_config['living_canyons']
                    canyon_width = random.uniform(config['width_range']['min'], config['width_range']['max'])
                    
                    # Apply width to all spline points
                    num_points = spline_component.get_number_of_spline_points()
                    for i in range(num_points):
                        spline_component.set_spline_point_type(i, unreal.SplinePointType.CURVE_CUSTOM_TANGENT)
                    
                    unreal.log(f"✅ Canyon spline created: {canyon_config['name']}")
                    return spline_actor
                else:
                    unreal.log_error(f"❌ Failed to get spline component for {canyon_config['name']}")
                    return None
            else:
                unreal.log_error(f"❌ Failed to create spline actor for {canyon_config['name']}")
                return None
                
        except Exception as e:
            unreal.log_error(f"❌ Canyon spline creation failed: {str(e)}")
            return None
    
    def create_water_flow(self, spline_actor, canyon_config):
        """Create water flow along the canyon using UWaterBodyRiver"""
        try:
            unreal.log(f"🌊 Creating water flow for: {canyon_config['name']}")
            
            # Create water body river actor
            water_position = canyon_config['start']
            water_actor = self.editor_actor_subsystem.spawn_actor_from_class(
                unreal.WaterBodyRiver,
                water_position,
                unreal.Rotator(0, 0, 0)
            )
            
            if water_actor:
                water_actor.set_actor_label(f"Water_Flow_{canyon_config['name']}")
                
                # Get water spline component
                water_spline = water_actor.get_component_by_class(unreal.SplineComponent)
                
                if water_spline and spline_actor:
                    # Copy spline points from canyon spline to water spline
                    canyon_spline = spline_actor.get_component_by_class(unreal.SplineComponent)
                    
                    if canyon_spline:
                        # Clear existing water spline points
                        water_spline.clear_spline_points()
                        
                        # Copy all points from canyon spline
                        num_points = canyon_spline.get_number_of_spline_points()
                        for i in range(num_points):
                            point_location = canyon_spline.get_location_at_spline_point(i, unreal.SplineCoordinateSpace.WORLD)
                            # Lower water level slightly below canyon floor
                            point_location.z -= 20.0
                            water_spline.add_spline_point(point_location, unreal.SplineCoordinateSpace.WORLD)
                        
                        water_spline.update_spline()
                
                # Configure water properties
                config = self.features_config['living_canyons']
                
                # Set water material properties
                water_material = unreal.MaterialInstanceDynamic.create(None, water_actor)
                if water_material:
                    water_material.set_vector_parameter_value(
                        "WaterColor",
                        unreal.LinearColor(0.2, 0.4, 0.8, 0.8)  # Blue water with transparency
                    )
                    water_material.set_scalar_parameter_value("FlowSpeed", config['water_speed'])
                    water_material.set_scalar_parameter_value("Turbulence", 0.3)
                
                # Configure water physics
                water_body_component = water_actor.get_component_by_class(unreal.WaterBodyComponent)
                if water_body_component:
                    water_body_component.set_water_velocity(unreal.Vector(config['water_speed'], 0, 0))
                
                unreal.log(f"✅ Water flow created for: {canyon_config['name']}")
                return water_actor
            else:
                unreal.log_error(f"❌ Failed to create water body for {canyon_config['name']}")
                return None
                
        except Exception as e:
            unreal.log_error(f"❌ Water flow creation failed: {str(e)}")
            return None
    
    def create_living_canyon(self, canyon_config, canyon_index):
        """Create a complete living canyon with spline path and water flow"""
        try:
            unreal.log(f"🏞️ Creating Living Canyon {canyon_index + 1}: {canyon_config['name']}")
            
            # Create canyon spline path
            spline_actor = self.create_canyon_spline(canyon_config)
            if not spline_actor:
                unreal.log_error(f"❌ Failed to create spline for {canyon_config['name']}")
                return None
            
            # Create water flow along the canyon
            water_actor = None
            if canyon_config.get('has_water_flow', False):
                water_actor = self.create_water_flow(spline_actor, canyon_config)
                if not water_actor:
                    unreal.log_warning(f"⚠️ Water flow creation failed for {canyon_config['name']}")
            
            # Use PCG bridge to generate canyon geometry
            if self.pcg_bridge:
                try:
                    canyon_generation_config = {
                        'canyon_type': 'living_canyon',
                        'depth_range': self.features_config['living_canyons']['depth_range'],
                        'width_range': self.features_config['living_canyons']['width_range'],
                        'erosion_pattern': 'natural',
                        'vegetation_density': 0.4,
                        'rock_formation_density': 0.6
                    }
                    
                    self.pcg_bridge.generate_canyon_geometry(spline_actor, canyon_generation_config)
                    unreal.log(f"✅ Canyon geometry generated for {canyon_config['name']}")
                except Exception as e:
                    unreal.log_warning(f"⚠️ PCG canyon generation failed: {str(e)}")
            
            unreal.log(f"✅ Living Canyon {canyon_index + 1} created successfully: {canyon_config['name']}")
            return {
                'spline_actor': spline_actor,
                'water_actor': water_actor,
                'name': canyon_config['name']
            }
            
        except Exception as e:
            unreal.log_error(f"❌ Living canyon creation failed: {str(e)}")
            return None    
            
        def create_all_living_canyons(self):
            """Create all 4 living canyons as per requirements"""
        try:
            unreal.log("🏞️ Creating 4 Living Canyons with water flow...")
            
            created_canyons = []
            
            for i, canyon_config in enumerate(self.canyon_paths):
                canyon = self.create_living_canyon(canyon_config, i)
                if canyon:
                    created_canyons.append(canyon)
                else:
                    unreal.log_error(f"❌ Failed to create Living Canyon {i + 1}: {canyon_config['name']}")
            
            unreal.log(f"✅ Created {len(created_canyons)}/4 Living Canyons")
            
            if len(created_canyons) == 4:
                unreal.log("🎉 All Living Canyons created successfully!")
                return True
            else:
                unreal.log_warning(f"⚠️ Only {len(created_canyons)} canyons created out of 4")
                return len(created_canyons) > 0
                
        except Exception as e:
            unreal.log_error(f"❌ Living canyons creation failed: {str(e)}")
            return False
    
    def apply_geological_materials(self):
        """Apply unique materials to all geological features"""
        try:
            unreal.log("🎨 Applying geological materials...")
            
            # Get all actors in the level
            all_actors = self.editor_level_lib.get_all_level_actors()
            
            # Apply materials to crystal plateaus
            plateau_actors = [actor for actor in all_actors if 'Crystal_Plateau' in actor.get_actor_label()]
            
            for plateau in plateau_actors:
                try:
                    # Create unique material instance for each plateau
                    material_instance = unreal.MaterialInstanceDynamic.create(None, plateau)
                    if material_instance:
                        # Vary crystal colors slightly for each plateau
                        hue_variation = random.uniform(-0.1, 0.1)
                        base_color = unreal.LinearColor(
                            0.8 + hue_variation, 
                            0.6 + hue_variation * 0.5, 
                            0.2, 
                            1.0
                        )
                        
                        material_instance.set_vector_parameter_value("BaseColor", base_color)
                        material_instance.set_scalar_parameter_value("Metallic", 0.8)
                        material_instance.set_scalar_parameter_value("Roughness", 0.2)
                        material_instance.set_scalar_parameter_value("Emissive", 0.3)
                        
                        # Apply material to static mesh component
                        static_mesh_component = plateau.get_component_by_class(unreal.StaticMeshComponent)
                        if static_mesh_component:
                            static_mesh_component.set_material(0, material_instance)
                            
                except Exception as e:
                    unreal.log_warning(f"⚠️ Failed to apply material to {plateau.get_actor_label()}: {str(e)}")
            
            # Apply materials to canyon water flows
            water_actors = [actor for actor in all_actors if 'Water_Flow' in actor.get_actor_label()]
            
            for water in water_actors:
                try:
                    # Create water material instance
                    water_material = unreal.MaterialInstanceDynamic.create(None, water)
                    if water_material:
                        water_material.set_vector_parameter_value(
                            "WaterColor",
                            unreal.LinearColor(0.2, 0.4, 0.8, 0.8)
                        )
                        water_material.set_scalar_parameter_value("FlowSpeed", 150.0)
                        water_material.set_scalar_parameter_value("Transparency", 0.8)
                        water_material.set_scalar_parameter_value("Refraction", 1.33)  # Water refraction index
                        
                        # Apply to water body component
                        water_component = water.get_component_by_class(unreal.WaterBodyComponent)
                        if water_component:
                            water_component.set_water_material(water_material)
                            
                except Exception as e:
                    unreal.log_warning(f"⚠️ Failed to apply water material to {water.get_actor_label()}: {str(e)}")
            
            unreal.log("✅ Geological materials applied successfully")
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Material application failed: {str(e)}")
            return False
    
    def save_level(self):
        """Save the level with geological features"""
        try:
            unreal.log("💾 Saving level with geological features...")
            
            if self.editor_level_lib.save_current_level():
                unreal.log("✅ Level saved successfully")
                return True
            else:
                unreal.log_error("❌ Failed to save level")
                return False
                
        except Exception as e:
            unreal.log_error(f"❌ Level saving failed: {str(e)}")
            return False
    
    def create_geological_features_complete(self):
        """Main function to create all geological features"""
        try:
            unreal.log("🌟 Starting Geological Features creation...")
            
            # Step 1: Verify UE5.6 compatibility
            if not self.verify_ue56_compatibility():
                unreal.log_error("❌ UE5.6 compatibility check failed")
                return False
            
            # Step 2: Load Planície Radiante level
            if not self.load_planicie_radiante_level():
                unreal.log_error("❌ Failed to load Planície Radiante level")
                return False
            
            # Step 3: Create 8 crystal plateaus
            if not self.create_all_crystal_plateaus():
                unreal.log_error("❌ Crystal plateaus creation failed")
                return False
            
            # Step 4: Create 4 living canyons with water flow
            if not self.create_all_living_canyons():
                unreal.log_error("❌ Living canyons creation failed")
                return False
            
            # Step 5: Apply geological materials
            if not self.apply_geological_materials():
                unreal.log_error("❌ Material application failed")
                return False
            
            # Step 6: Save level
            if not self.save_level():
                unreal.log_error("❌ Level saving failed")
                return False
            
            unreal.log("🎉 Geological Features created successfully!")
            unreal.log("📊 Features Statistics:")
            unreal.log("   - ✅ 8 Crystal Plateaus (500-1200 units)")
            unreal.log("   - ✅ 4 Living Canyons with water flow")
            unreal.log("   - ✅ Unique materials applied")
            unreal.log("   - ✅ PCG procedural generation")
            
            return True
            
        except Exception as e:
            unreal.log_error(f"❌ Geological Features creation failed: {str(e)}")
            return False

def main():
    """Main execution function"""
    try:
        # Create geological features creator
        creator = GeologicalFeaturesCreator()
        
        # Execute complete creation process
        success = creator.create_geological_features_complete()
        
        if success:
            unreal.log("✅ AURACRON Geological Features creation completed successfully!")
            return 0
        else:
            unreal.log_error("❌ AURACRON Geological Features creation failed!")
            return 1
            
    except Exception as e:
        unreal.log_error(f"❌ Critical error in main execution: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)