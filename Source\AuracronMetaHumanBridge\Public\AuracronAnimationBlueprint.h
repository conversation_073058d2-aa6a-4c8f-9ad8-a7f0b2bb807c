#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimBlueprintGeneratedClass.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimSequence.h"
#include "Animation/PoseAsset.h"
#include "Animation/AnimMontage.h"
#include "Animation/BlendSpace.h"
#include "Animation/BlendSpace1D.h"
// BlendSpace2D is now part of BlendSpace.h in UE 5.6
#include "Animation/Skeleton.h"
#include "Engine/SkeletalMesh.h"
#include "Sound/SoundWave.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"
#include "EdGraph/EdGraph.h"

#include "AuracronAnimationBlueprint.generated.h"

#ifdef WITH_EDITOR
// Animation Graph Editor includes for UE 5.6
// AnimGraphNode_Base.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraphNode_Base.h"
// AnimGraphNode_Root.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_Root.h"
// AnimGraphNode_StateMachine.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_StateMachine.h"
// AnimGraphNode_BlendListByBool.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_BlendListByBool.h"
// AnimGraphNode_BlendSpacePlayer.h is not available in UE 5.6 - using alternative approach
// #include "AnimGraph/Public/AnimGraphNode_BlendSpacePlayer.h"
// AnimGraph includes removed - not needed for runtime functionality
// Blueprint editor includes removed - not needed for runtime functionality
// KismetEditor includes removed - not needed for runtime functionality
#endif

/**
 * Estrutura wrapper para TArray<FString> em TMap (Animation Blueprint)
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationStringArrayWrapper
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "String Array")
    TArray<FString> Strings;

    FAnimationStringArrayWrapper()
    {
    }

    FAnimationStringArrayWrapper(const TArray<FString>& InStrings)
        : Strings(InStrings)
    {
    }
};

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronAnimationBlueprint, Log, All);

// Enums for animation blueprint generation
UENUM(BlueprintType)
enum class EAnimationBlueprintType : uint8
{
    Basic           UMETA(DisplayName = "Basic"),
    Facial          UMETA(DisplayName = "Facial"),
    FullBody        UMETA(DisplayName = "Full Body"),
    LipSync         UMETA(DisplayName = "Lip Sync"),
    Emotion         UMETA(DisplayName = "Emotion"),
    Advanced        UMETA(DisplayName = "Advanced")
};

UENUM(BlueprintType)
enum class EFacialAnimationType : uint8
{
    BlendShapes     UMETA(DisplayName = "Blend Shapes"),
    BoneTransforms  UMETA(DisplayName = "Bone Transforms"),
    Hybrid          UMETA(DisplayName = "Hybrid")
};

UENUM(BlueprintType)
enum class ELipSyncMethod : uint8
{
    Phoneme         UMETA(DisplayName = "Phoneme Based"),
    Viseme          UMETA(DisplayName = "Viseme Based"),
    AudioAnalysis   UMETA(DisplayName = "Audio Analysis"),
    ML              UMETA(DisplayName = "Machine Learning")
};

UENUM(BlueprintType)
enum class EEmotionBlendMode : uint8
{
    Replace         UMETA(DisplayName = "Replace"),
    Additive        UMETA(DisplayName = "Additive"),
    Multiply        UMETA(DisplayName = "Multiply"),
    Overlay         UMETA(DisplayName = "Overlay")
};

UENUM(BlueprintType)
enum class EAnimNodeType : uint8
{
    SequencePlayer      UMETA(DisplayName = "Sequence Player"),
    BlendSpacePlayer    UMETA(DisplayName = "Blend Space Player"),
    StateMachine        UMETA(DisplayName = "State Machine"),
    BlendByBool         UMETA(DisplayName = "Blend By Bool"),
    BlendByFloat        UMETA(DisplayName = "Blend By Float"),
    BlendByInt          UMETA(DisplayName = "Blend By Int"),
    PoseBlender         UMETA(DisplayName = "Pose Blender"),
    Custom              UMETA(DisplayName = "Custom")
};

// Structures for animation blueprint generation
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FFacialAnimationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    EFacialAnimationType AnimationType = EFacialAnimationType::BlendShapes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    TArray<FString> BlendShapeNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    TArray<FString> ControlBoneNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyeTracking = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyeBlink = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableEyebrowControl = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    bool bEnableJawControl = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    float BlendShapeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Facial Animation")
    float AnimationSpeed = 1.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FLipSyncData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    ELipSyncMethod LipSyncMethod = ELipSyncMethod::Phoneme;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TMap<FString, FString> PhonemeToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TMap<FString, FString> VisemeToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    TSoftObjectPtr<USoundWave> AudioAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float LipSyncIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float SmoothingFactor = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    bool bEnableAudioAnalysis = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    int32 FFTSize = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lip Sync")
    float FrequencyRange = 8000.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEmotionMappingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    TMap<FString, FAnimationStringArrayWrapper> EmotionToBlendShapeMapping;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    TMap<FString, float> EmotionIntensities;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    EEmotionBlendMode BlendMode = EEmotionBlendMode::Replace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    float TransitionSpeed = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    bool bEnableEmotionBlending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    bool bEnableSubtleExpressions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Emotion Mapping")
    float SubtleExpressionIntensity = 0.3f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationBlueprintLODData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    int32 NumLODs = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<float> LODDistances = {500.0f, 1000.0f, 2000.0f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<float> BlendShapeReductions = {1.0f, 0.7f, 0.4f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    TArray<int32> UpdateFrequencies = {60, 30, 15};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    bool bEnableScreenSizeBasedLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    float ScreenSizeThreshold = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    bool bDisableFacialAnimationAtDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint LOD")
    float FacialAnimationCullDistance = 1500.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAuracronAnimNodeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    EAnimNodeType NodeType = EAnimNodeType::SequencePlayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    FString NodeName = TEXT("AnimNode");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    TSoftObjectPtr<UAnimationAsset> AnimationAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    float PlayRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    bool bLooping = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    FVector2D NodePosition = FVector2D::ZeroVector;

    // Additional properties for different node types
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    TSoftObjectPtr<UAnimSequenceBase> AnimSequence;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    TSoftObjectPtr<UBlendSpace> BlendSpace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    float XInput = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    float YInput = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node")
    bool bLoop = true;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAuracronAnimGraphData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Graph")
    TArray<FAuracronAnimNodeData> AnimNodes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Graph")
    FString GraphName = TEXT("AnimGraph");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Graph")
    bool bOptimizeForPerformance = true;
};

// Additional structures needed for animation blueprint generation
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimStateData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation State")
    FString StateName = TEXT("DefaultState");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation State")
    TSoftObjectPtr<UAnimSequenceBase> StateAnimation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation State")
    FVector2D StatePosition = FVector2D::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation State")
    bool bIsDefaultState = false;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimTransitionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    FString FromState = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    FString ToState = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    FString SourceStateName = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    FString TargetStateName = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    float CrossfadeDuration = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    uint8 BlendMode = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Transition")
    uint8 LogicType = 0;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FStateMachineData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Machine")
    FString StateMachineName = TEXT("DefaultStateMachine");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Machine")
    TArray<FAnimStateData> States;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Machine")
    TArray<FAnimTransitionData> Transitions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Machine")
    int32 DefaultStateIndex = 0;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FBlendSpaceNodeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    TSoftObjectPtr<UBlendSpace> BlendSpace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    float XInput = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    float YInput = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    float PlayRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    bool bLoop = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Node")
    FVector2D NodePosition = FVector2D::ZeroVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAuracronBlendSpaceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend Space Data")
    TArray<FBlendSpaceNodeData> BlendSpaces;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimSequenceNodeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Node")
    TSoftObjectPtr<UAnimSequence> AnimSequence;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Node")
    float PlayRate = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Node")
    bool bLoop = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Node")
    float StartPosition = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Node")
    FVector2D NodePosition = FVector2D::ZeroVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimSequenceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Sequence Data")
    TArray<FAnimSequenceNodeData> AnimSequences;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FEventParameterData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Parameter")
    FString ParameterName = TEXT("Parameter");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Parameter")
    FEdGraphPinType ParameterType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Parameter")
    FText FriendlyName;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FCustomEventNodeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Node")
    FString EventName = TEXT("CustomEvent");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Node")
    TArray<FEventParameterData> Parameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Node")
    bool bCallInEditor = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Node")
    bool bOverride = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Node")
    FVector2D NodePosition = FVector2D::ZeroVector;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FCustomEventData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Event Data")
    TArray<FCustomEventNodeData> Events;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FBlueprintVariableData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Variable")
    FString VariableName = TEXT("Variable");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Variable")
    FEdGraphPinType VariableType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Variable")
    FText FriendlyName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Variable")
    FText Category;

    UPROPERTY(EditAnywhere, Category = "Blueprint Variable")
    uint64 PropertyFlags = CPF_Edit | CPF_BlueprintVisible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Variable")
    FString DefaultValue = TEXT("");
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FVariableData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variable Data")
    TArray<FBlueprintVariableData> Variables;
};



USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimNodeConnection
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node Connection")
    FString SourceNodeName = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node Connection")
    FString TargetNodeName = TEXT("");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node Connection")
    FString SourcePinName = TEXT("Pose");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Node Connection")
    FString TargetPinName = TEXT("Pose");
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAnimationBlueprintGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    EAnimationBlueprintType BlueprintType = EAnimationBlueprintType::Facial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    TSoftObjectPtr<USkeletalMesh> TargetSkeletalMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    TSoftObjectPtr<USkeleton> TargetSkeleton;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FFacialAnimationData FacialData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FLipSyncData LipSyncData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FEmotionMappingData EmotionData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FAnimationBlueprintLODData LODData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FAuracronAnimGraphData AnimGraphData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FStateMachineData StateMachineData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FAuracronBlendSpaceData BlendSpaceData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FAnimSequenceData AnimSequenceData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bCreateStateMachine = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bGenerateLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bEnableRealTimeUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bEnableOptimizations = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    bool bOptimizeForPerformance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation Blueprint Generation")
    FString BlueprintName = TEXT("MetaHuman_AnimBP");
};

/**
 * Animation Blueprint generation system for MetaHuman Bridge
 * Provides advanced Animation Blueprint creation with UE5.6 Animation APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronAnimationBlueprint
{
public:
    FAuracronAnimationBlueprint();
    ~FAuracronAnimationBlueprint();

    // Core Animation Blueprint generation
    UAnimBlueprint* GenerateAnimationBlueprint(const FAnimationBlueprintGenerationParameters& Parameters);
    bool SetupFacialAnimationSystem(UAnimBlueprint* AnimBlueprint, const FFacialAnimationData& FacialData);
    bool SetupLipSyncSystem(UAnimBlueprint* AnimBlueprint, const FLipSyncData& LipSyncData);
    bool SetupEmotionMappingSystem(UAnimBlueprint* AnimBlueprint, const FEmotionMappingData& EmotionData);

    // Animation Blueprint LOD generation
    bool GenerateAnimationBlueprintLODs(UAnimBlueprint* AnimBlueprint, const FAnimationBlueprintLODData& LODData);



    // Core Animation Blueprint functions
    bool InitializeAnimationBlueprint(UAnimBlueprint* AnimBlueprint);
    bool CreateAnimationGraph(UAnimBlueprint* AnimBlueprint, const FAuracronAnimGraphData& AnimGraphData);
    bool CreateStateMachine(UAnimBlueprint* AnimBlueprint, const FStateMachineData& StateMachineData);
    bool AddBlendSpaces(UAnimBlueprint* AnimBlueprint, const FAuracronBlendSpaceData& BlendSpaceData);
    bool AddAnimationSequences(UAnimBlueprint* AnimBlueprint, const FAnimSequenceData& AnimSequenceData);
    bool CreateCustomEvents(UAnimBlueprint* AnimBlueprint, const FCustomEventData& CustomEventData);
    bool AddBlueprintVariables(UAnimBlueprint* AnimBlueprint, const FVariableData& VariableData);
    bool CompileAnimationBlueprint(UAnimBlueprint* AnimBlueprint);

    // System management
    void InitializeAnimationBlueprintSystem();
    void CleanupAnimationBlueprintSystem();

    // Validation
    bool ValidateAnimationBlueprintGenerationParameters(const FAnimationBlueprintGenerationParameters& Parameters, FString& ErrorMessage) const;
    bool ValidateAnimBlueprintGenerationParameters(const FAnimationBlueprintGenerationParameters& Parameters, FString& OutError);

    // Helper methods for creating specific node types
    void CreateSequencePlayerNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData);
    void CreateBlendSpacePlayerNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData);
    void CreateStateMachineNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData);
    void CreateBlendByBoolNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData);
    void CreateBlendByFloatNode(UAnimBlueprintGeneratedClass* AnimBPClass, const FAuracronAnimNodeData& NodeData);

    // Thread safety
    static FCriticalSection AnimBlueprintGenerationMutex;

    // Statistics tracking
    void UpdateAnimBlueprintGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);
    static TMap<FString, double> AnimBlueprintGenerationStats;

    // Templates and presets
    void InitializeDefaultAnimationBlueprintTemplates();
    void InitializeDefaultPhonemeMappings();
    void InitializeDefaultEmotionMappings();
    void InitializeAnimationBlueprintLODConfigurations();

    // Cache management
    void CacheAnimationBlueprint(const FString& CacheKey, UAnimBlueprint* AnimBlueprint);
    void ClearAnimationBlueprintCache();

    // Cache management
    void ClearAnimBlueprintCache();

    // Statistics
    void UpdateAnimationBlueprintGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    // Helper methods
    FString CalculateAnimBlueprintGenerationHash(const FAnimationBlueprintGenerationParameters& Parameters);
    UEdGraph* GetAnimationGraph(UAnimBlueprint* AnimBlueprint);
    UEdGraph* GetEventGraph(UAnimBlueprint* AnimBlueprint);
    bool CreateAnimationNode(UEdGraph* AnimGraph, const FAuracronAnimNodeData& NodeData);
    bool ConnectAnimationNodes(UEdGraph* AnimGraph, const FAnimNodeConnection& Connection);
    bool CreateAnimationState(UEdGraph* StateMachineGraph, const FAnimStateData& StateData);
    bool CreateStateTransition(UEdGraph* StateMachineGraph, const FAnimTransitionData& TransitionData);
    UEdGraphNode* FindNodeByName(UEdGraph* Graph, const FString& NodeName);
    UEdGraphPin* FindPinByName(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction);
    UAnimStateNode* FindStateByName(UEdGraph* StateMachineGraph, const FString& StateName);
    void UpdateAnimBlueprintCacheStats();
    int32 EstimateAnimBlueprintMemoryUsage(UAnimBlueprint* AnimBlueprint);

    // Thread safety
    mutable FCriticalSection AnimationBlueprintGenerationMutex;

private:
    // Animation Blueprint cache
    mutable TMap<FString, TWeakObjectPtr<UAnimBlueprint>> AnimationBlueprintCache;
    mutable int32 AnimBlueprintCacheMemoryUsage = 0;

    // Default templates and mappings
    TMap<FString, FAnimationBlueprintGenerationParameters> DefaultTemplates;
    TMap<FString, FString> DefaultPhonemeMappings;
    TMap<FString, TArray<FString>> DefaultEmotionMappings;
    TArray<FAnimationBlueprintLODData> DefaultLODConfigurations;

    // Prevent copying
    FAuracronAnimationBlueprint(const FAuracronAnimationBlueprint&) = delete;
    FAuracronAnimationBlueprint& operator=(const FAuracronAnimationBlueprint&) = delete;
};
