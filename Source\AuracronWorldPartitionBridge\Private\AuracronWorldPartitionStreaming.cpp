#include "AuracronWorldPartitionStreaming.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Async/Async.h"
#include "UObject/UObjectGlobals.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronStreaming, Log, All);

// Static instance
UAuracronWorldPartitionStreamingManager* UAuracronWorldPartitionStreamingManager::Instance = nullptr;

UAuracronWorldPartitionStreamingManager* UAuracronWorldPartitionStreamingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionStreamingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionStreamingManager::Initialize(const FAuracronStreamingConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        UE_LOG(LogAuracronStreaming, Warning, TEXT("Streaming Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    bIsInitialized = true;
    
    // Initialize streaming sources
    StreamingSources.Empty();
    StreamingRequests.Empty();
    ActiveRequests.Empty();
    
    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming Manager initialized with distance: %.2f"), InConfiguration.StreamingDistance);
}

void UAuracronWorldPartitionStreamingManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Cancel all pending requests
    StreamingRequests.Empty();
    ActiveRequests.Empty();
    StreamingSources.Empty();
    
    bIsInitialized = false;
    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming Manager shutdown"));
}

bool UAuracronWorldPartitionStreamingManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionStreamingManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Process streaming requests
    ProcessStreamingRequests();
    
    // Update memory management
    UpdateMemoryManagement();
    
    // Update distance-based streaming
    UpdateDistanceBasedStreaming();
}

FString UAuracronWorldPartitionStreamingManager::RequestCellLoading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized || CellId.IsEmpty())
    {
        return FString();
    }

    // Create streaming request
    FAuracronStreamingRequest Request;
    Request.RequestId = FGuid::NewGuid().ToString();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Load;
    Request.Priority = Priority;
    Request.RequestTime = FPlatformTime::Seconds();
    Request.State = EAuracronStreamingRequestState::Pending;

    StreamingRequests.Add(Request.RequestId, Request);
    
    UE_LOG(LogAuracronStreaming, Log, TEXT("Requested cell loading: %s (Priority: %d)"), *CellId, (int32)Priority);
    return Request.RequestId;
}

FString UAuracronWorldPartitionStreamingManager::RequestCellUnloading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized || CellId.IsEmpty())
    {
        return FString();
    }

    // Create streaming request
    FAuracronStreamingRequest Request;
    Request.RequestId = FGuid::NewGuid().ToString();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Unload;
    Request.Priority = Priority;
    Request.RequestTime = FPlatformTime::Seconds();
    Request.State = EAuracronStreamingRequestState::Pending;

    StreamingRequests.Add(Request.RequestId, Request);
    
    UE_LOG(LogAuracronStreaming, Log, TEXT("Requested cell unloading: %s (Priority: %d)"), *CellId, (int32)Priority);
    return Request.RequestId;
}

FString UAuracronWorldPartitionStreamingManager::RequestCellPreloading(const FString& CellId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized || CellId.IsEmpty())
    {
        return FString();
    }

    // Create streaming request
    FAuracronStreamingRequest Request;
    Request.RequestId = FGuid::NewGuid().ToString();
    Request.CellId = CellId;
    Request.RequestType = EAuracronStreamingRequestType::Preload;
    Request.Priority = Priority;
    Request.RequestTime = FPlatformTime::Seconds();
    Request.State = EAuracronStreamingRequestState::Pending;

    StreamingRequests.Add(Request.RequestId, Request);
    
    UE_LOG(LogAuracronStreaming, Log, TEXT("Requested cell preloading: %s (Priority: %d)"), *CellId, (int32)Priority);
    return Request.RequestId;
}

bool UAuracronWorldPartitionStreamingManager::CancelStreamingRequest(const FString& RequestId)
{
    if (!bIsInitialized || RequestId.IsEmpty())
    {
        return false;
    }

    // Find and cancel request
    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request)
    {
        Request->State = EAuracronStreamingRequestState::Cancelled;
        ActiveRequests.Remove(RequestId);
        UE_LOG(LogAuracronStreaming, Log, TEXT("Cancelled streaming request: %s"), *RequestId);
        return true;
    }

    return false;
}

void UAuracronWorldPartitionStreamingManager::ProcessStreamingRequests()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Simplified processing - just mark requests as completed after some time
    TArray<FString> CompletedRequests;
    for (auto& RequestPair : StreamingRequests)
    {
        FAuracronStreamingRequest& Request = RequestPair.Value;
        
        if (Request.State == EAuracronStreamingRequestState::Pending)
        {
            Request.State = EAuracronStreamingRequestState::Processing;
            ActiveRequests.Add(RequestPair.Key);
        }
        else if (Request.State == EAuracronStreamingRequestState::Processing)
        {
            float ProcessingTime = FPlatformTime::Seconds() - Request.RequestTime;
            if (ProcessingTime > 1.0f) // Complete after 1 second
            {
                Request.State = EAuracronStreamingRequestState::Completed;
                CompletedRequests.Add(RequestPair.Key);
            }
        }
    }

    // Remove completed requests from active set
    for (const FString& RequestId : CompletedRequests)
    {
        ActiveRequests.Remove(RequestId);
    }
}

FString UAuracronWorldPartitionStreamingManager::AddStreamingSource(const FAuracronStreamingSource& Source)
{
    if (!bIsInitialized)
    {
        return FString();
    }

    FString SourceId = FGuid::NewGuid().ToString();
    FAuracronStreamingSource NewSource = Source;
    NewSource.SourceId = SourceId;
    
    StreamingSources.Add(SourceId, NewSource);
    
    UE_LOG(LogAuracronStreaming, Log, TEXT("Added streaming source: %s at location %s"), *SourceId, *Source.Location.ToString());
    return SourceId;
}

bool UAuracronWorldPartitionStreamingManager::RequestCellLoad(const FString& CellId, float Priority)
{
    EAuracronStreamingPriority StreamingPriority = EAuracronStreamingPriority::Normal;
    if (Priority > 0.8f)
    {
        StreamingPriority = EAuracronStreamingPriority::Critical;
    }
    else if (Priority > 0.6f)
    {
        StreamingPriority = EAuracronStreamingPriority::High;
    }
    else if (Priority < 0.3f)
    {
        StreamingPriority = EAuracronStreamingPriority::Low;
    }

    FString RequestId = RequestCellLoading(CellId, StreamingPriority);
    return !RequestId.IsEmpty();
}

bool UAuracronWorldPartitionStreamingManager::RequestCellUnload(const FString& CellId)
{
    FString RequestId = RequestCellUnloading(CellId, EAuracronStreamingPriority::Normal);
    return !RequestId.IsEmpty();
}

bool UAuracronWorldPartitionStreamingManager::CancelCellLoad(const FString& CellId)
{
    // Find and cancel any requests for this cell
    bool bCancelled = false;
    
    for (auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.CellId == CellId && RequestPair.Value.RequestType == EAuracronStreamingRequestType::Load)
        {
            RequestPair.Value.State = EAuracronStreamingRequestState::Cancelled;
            ActiveRequests.Remove(RequestPair.Key);
            bCancelled = true;
        }
    }
    
    return bCancelled;
}

EAuracronCellStreamingState UAuracronWorldPartitionStreamingManager::GetCellStreamingState(const FString& CellId) const
{
    if (!bIsInitialized || CellId.IsEmpty())
    {
        return EAuracronCellStreamingState::Unloaded;
    }

    // Check if cell has active requests
    for (const auto& RequestPair : StreamingRequests)
    {
        const FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.CellId == CellId)
        {
            switch (Request.RequestType)
            {
                case EAuracronStreamingRequestType::Load:
                    return EAuracronCellStreamingState::Loading;
                case EAuracronStreamingRequestType::Unload:
                    return EAuracronCellStreamingState::Unloading;
                case EAuracronStreamingRequestType::Preload:
                    return EAuracronCellStreamingState::Loading;
            }
        }
    }

    // Default to loaded (simplified)
    return EAuracronCellStreamingState::Loaded;
}

bool UAuracronWorldPartitionStreamingManager::RemoveStreamingSource(const FString& SourceId)
{
    if (!bIsInitialized || SourceId.IsEmpty())
    {
        return false;
    }

    int32 RemovedCount = StreamingSources.Remove(SourceId);
    if (RemovedCount > 0)
    {
        UE_LOG(LogAuracronStreaming, Log, TEXT("Removed streaming source: %s"), *SourceId);
        return true;
    }

    return false;
}

void UAuracronWorldPartitionStreamingManager::UpdateStreamingSource(const FString& SourceId, const FVector& NewLocation, const FVector& NewVelocity)
{
    if (!bIsInitialized || SourceId.IsEmpty())
    {
        return;
    }

    FAuracronStreamingSource* Source = StreamingSources.Find(SourceId);
    if (Source)
    {
        Source->Location = NewLocation;
        Source->Velocity = NewVelocity;
        UE_LOG(LogAuracronStreaming, VeryVerbose, TEXT("Updated streaming source %s to location %s"), *SourceId, *NewLocation.ToString());
    }
}

TArray<FAuracronStreamingSource> UAuracronWorldPartitionStreamingManager::GetActiveStreamingSources() const
{
    TArray<FAuracronStreamingSource> Sources;
    for (const auto& SourcePair : StreamingSources)
    {
        Sources.Add(SourcePair.Value);
    }
    return Sources;
}

void UAuracronWorldPartitionStreamingManager::UpdateStreamingSourcesFromWorld(UWorld* World)
{
    if (!bIsInitialized || !World)
    {
        return;
    }

    // This is a simplified implementation
    // In a real implementation, you would iterate through players, cameras, etc.
    UE_LOG(LogAuracronStreaming, VeryVerbose, TEXT("Updated streaming sources from world"));
}

void UAuracronWorldPartitionStreamingManager::UpdateDistanceBasedStreaming()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Process each streaming source
    for (const auto& SourcePair : StreamingSources)
    {
        const FAuracronStreamingSource& Source = SourcePair.Value;

        // Get cells in streaming range
        TArray<FString> CellsInRange = GetCellsInStreamingRange(Source.Location, Configuration.StreamingDistance);

        // Request loading for cells in range
        for (const FString& CellId : CellsInRange)
        {
            EAuracronCellStreamingState CurrentState = GetCellStreamingState(CellId);
            if (CurrentState == EAuracronCellStreamingState::Unloaded)
            {
                float Priority = CalculateCellPriority(CellId, Source.Location);
                RequestCellLoad(CellId, Priority);
            }
        }
    }
}

TArray<FString> UAuracronWorldPartitionStreamingManager::GetCellsInStreamingRange(const FVector& Location, float Range) const
{
    TArray<FString> CellsInRange;

    if (!bIsInitialized)
    {
        return CellsInRange;
    }

    // This is a simplified implementation
    // In a real implementation, you would query the world partition grid
    // For now, generate some dummy cell IDs based on location
    int32 GridX = FMath::FloorToInt(Location.X / 25600.0f); // 256m cells
    int32 GridY = FMath::FloorToInt(Location.Y / 25600.0f);
    int32 GridZ = FMath::FloorToInt(Location.Z / 25600.0f);

    int32 CellRange = FMath::CeilToInt(Range / 25600.0f);

    for (int32 X = GridX - CellRange; X <= GridX + CellRange; ++X)
    {
        for (int32 Y = GridY - CellRange; Y <= GridY + CellRange; ++Y)
        {
            for (int32 Z = GridZ - CellRange; Z <= GridZ + CellRange; ++Z)
            {
                FString CellId = FString::Printf(TEXT("Cell_%d_%d_%d"), X, Y, Z);
                CellsInRange.Add(CellId);
            }
        }
    }

    return CellsInRange;
}

float UAuracronWorldPartitionStreamingManager::CalculateCellPriority(const FString& CellId, const FVector& ViewerLocation) const
{
    if (!bIsInitialized || CellId.IsEmpty())
    {
        return 0.0f;
    }

    // Extract grid coordinates from cell ID (simplified)
    TArray<FString> Parts;
    CellId.ParseIntoArray(Parts, TEXT("_"));

    if (Parts.Num() >= 4)
    {
        int32 GridX = FCString::Atoi(*Parts[1]);
        int32 GridY = FCString::Atoi(*Parts[2]);
        int32 GridZ = FCString::Atoi(*Parts[3]);

        FVector CellCenter(GridX * 25600.0f, GridY * 25600.0f, GridZ * 25600.0f);
        float Distance = FVector::Dist(ViewerLocation, CellCenter);

        // Higher priority for closer cells
        float MaxDistance = Configuration.StreamingDistance;
        float Priority = FMath::Clamp(1.0f - (Distance / MaxDistance), 0.0f, 1.0f);

        return Priority;
    }

    return 0.5f; // Default priority
}

void UAuracronWorldPartitionStreamingManager::UpdateMemoryManagement()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Check memory pressure
    if (IsMemoryPressureHigh())
    {
        // Force cleanup of completed requests
        ClearCompletedRequests();

        // Consider unloading distant cells
        UE_LOG(LogAuracronStreaming, Warning, TEXT("High memory pressure detected, performing cleanup"));
    }
}

bool UAuracronWorldPartitionStreamingManager::IsMemoryPressureHigh() const
{
    // Simplified memory pressure detection
    float CurrentMemory = GetCurrentMemoryUsage();
    float MemoryThreshold = Configuration.MaxMemoryUsageMB * 0.9f; // 90% threshold

    return CurrentMemory > MemoryThreshold;
}

void UAuracronWorldPartitionStreamingManager::ForceMemoryCleanup()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear completed requests
    ClearCompletedRequests();

    // Force garbage collection
    GEngine->ForceGarbageCollection(true);

    UE_LOG(LogAuracronStreaming, Log, TEXT("Forced memory cleanup completed"));
}

float UAuracronWorldPartitionStreamingManager::GetCurrentMemoryUsage() const
{
    // Simplified memory usage calculation
    // In a real implementation, you would query actual memory usage
    return StreamingSources.Num() * 10.0f + ActiveRequests.Num() * 5.0f + StreamingRequests.Num() * 2.0f;
}

float UAuracronWorldPartitionStreamingManager::GetMemoryPressure() const
{
    float CurrentMemory = GetCurrentMemoryUsage();
    float MaxMemory = Configuration.MaxMemoryUsageMB;

    return MaxMemory > 0.0f ? (CurrentMemory / MaxMemory) : 0.0f;
}

FAuracronStreamingRequest UAuracronWorldPartitionStreamingManager::GetStreamingRequest(const FString& RequestId) const
{
    FAuracronStreamingRequest EmptyRequest;

    if (!bIsInitialized || RequestId.IsEmpty())
    {
        return EmptyRequest;
    }

    const FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request)
    {
        return *Request;
    }

    return EmptyRequest;
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetPendingRequests() const
{
    TArray<FAuracronStreamingRequest> PendingRequests;

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Pending)
        {
            PendingRequests.Add(RequestPair.Value);
        }
    }

    return PendingRequests;
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetActiveRequests() const
{
    TArray<FAuracronStreamingRequest> ActiveRequestsList;

    for (const FString& RequestId : ActiveRequests)
    {
        const FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
        if (Request)
        {
            ActiveRequestsList.Add(*Request);
        }
    }

    return ActiveRequestsList;
}

void UAuracronWorldPartitionStreamingManager::ClearCompletedRequests()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Remove completed requests
    TArray<FString> CompletedRequestIds;
    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Completed ||
            RequestPair.Value.State == EAuracronStreamingRequestState::Cancelled)
        {
            CompletedRequestIds.Add(RequestPair.Key);
        }
    }

    for (const FString& RequestId : CompletedRequestIds)
    {
        StreamingRequests.Remove(RequestId);
        ActiveRequests.Remove(RequestId);
    }

    if (CompletedRequestIds.Num() > 0)
    {
        UE_LOG(LogAuracronStreaming, VeryVerbose, TEXT("Cleared %d completed requests"), CompletedRequestIds.Num());
    }
}

void UAuracronWorldPartitionStreamingManager::UpdateRequestPriorities()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update priorities based on current streaming sources
    for (auto& RequestPair : StreamingRequests)
    {
        FAuracronStreamingRequest& Request = RequestPair.Value;
        if (Request.State != EAuracronStreamingRequestState::Pending)
            continue;

        float HighestPriority = 0.0f;

        for (const auto& SourcePair : StreamingSources)
        {
            const FAuracronStreamingSource& Source = SourcePair.Value;
            float Priority = CalculateCellPriority(Request.CellId, Source.Location);
            HighestPriority = FMath::Max(HighestPriority, Priority);
        }

        // Convert float priority to enum
        if (HighestPriority > 0.8f)
        {
            Request.Priority = EAuracronStreamingPriority::Critical;
        }
        else if (HighestPriority > 0.6f)
        {
            Request.Priority = EAuracronStreamingPriority::High;
        }
        else if (HighestPriority > 0.3f)
        {
            Request.Priority = EAuracronStreamingPriority::Normal;
        }
        else
        {
            Request.Priority = EAuracronStreamingPriority::Low;
        }
    }
}

void UAuracronWorldPartitionStreamingManager::SetRequestPriority(const FString& RequestId, EAuracronStreamingPriority Priority)
{
    if (!bIsInitialized || RequestId.IsEmpty())
    {
        return;
    }

    FAuracronStreamingRequest* Request = StreamingRequests.Find(RequestId);
    if (Request && Request->State == EAuracronStreamingRequestState::Pending)
    {
        Request->Priority = Priority;
        UE_LOG(LogAuracronStreaming, VeryVerbose, TEXT("Updated priority for request %s to %d"), *RequestId, (int32)Priority);
    }
}

TArray<FAuracronStreamingRequest> UAuracronWorldPartitionStreamingManager::GetRequestsByPriority(EAuracronStreamingPriority Priority) const
{
    TArray<FAuracronStreamingRequest> FilteredRequests;

    if (!bIsInitialized)
    {
        return FilteredRequests;
    }

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.Priority == Priority)
        {
            FilteredRequests.Add(RequestPair.Value);
        }
    }

    return FilteredRequests;
}

void UAuracronWorldPartitionStreamingManager::SetConfiguration(const FAuracronStreamingConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    UE_LOG(LogAuracronStreaming, Log, TEXT("Updated streaming configuration"));
}

FAuracronStreamingConfiguration UAuracronWorldPartitionStreamingManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionStreamingManager::SetStreamingDistance(float Distance)
{
    Configuration.StreamingDistance = Distance;
    UE_LOG(LogAuracronStreaming, Log, TEXT("Updated streaming distance to %.2f"), Distance);
}

float UAuracronWorldPartitionStreamingManager::GetStreamingDistance() const
{
    return Configuration.StreamingDistance;
}

FAuracronStreamingBridgeStatistics UAuracronWorldPartitionStreamingManager::GetStreamingStatistics() const
{
    FAuracronStreamingBridgeStatistics Stats;

    if (!bIsInitialized)
    {
        return Stats;
    }

    // Fill statistics
    Stats.TotalStreamingRequests = StreamingRequests.Num();
    Stats.PendingRequests = GetPendingRequestCount();
    Stats.CompletedRequests = 0; // Count completed requests
    Stats.FailedRequests = 0; // Count failed requests

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Completed)
        {
            Stats.CompletedRequests++;
        }
        else if (RequestPair.Value.State == EAuracronStreamingRequestState::Cancelled)
        {
            Stats.FailedRequests++;
        }
    }

    Stats.CurrentMemoryUsageMB = GetCurrentMemoryUsage();
    Stats.MemoryPressure = GetMemoryPressure();
    Stats.StreamingEfficiency = GetStreamingEfficiency();
    Stats.CellsLoaded = 0; // Simplified - would count actual loaded cells
    Stats.CellsUnloaded = 0; // Simplified - would count actual unloaded cells
    Stats.LastUpdateTime = FDateTime::Now();

    return Stats;
}

void UAuracronWorldPartitionStreamingManager::ResetStatistics()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear completed and cancelled requests
    ClearCompletedRequests();

    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming statistics reset"));
}

int32 UAuracronWorldPartitionStreamingManager::GetPendingRequestCount() const
{
    if (!bIsInitialized)
    {
        return 0;
    }

    int32 Count = 0;
    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Pending)
        {
            Count++;
        }
    }

    return Count;
}

int32 UAuracronWorldPartitionStreamingManager::GetActiveRequestCount() const
{
    return ActiveRequests.Num();
}

float UAuracronWorldPartitionStreamingManager::GetStreamingEfficiency() const
{
    if (!bIsInitialized || StreamingRequests.Num() == 0)
    {
        return 1.0f; // Perfect efficiency when no requests
    }

    int32 CompletedRequests = 0;
    int32 TotalRequests = StreamingRequests.Num();

    for (const auto& RequestPair : StreamingRequests)
    {
        if (RequestPair.Value.State == EAuracronStreamingRequestState::Completed)
        {
            CompletedRequests++;
        }
    }

    return TotalRequests > 0 ? (float)CompletedRequests / (float)TotalRequests : 1.0f;
}

void UAuracronWorldPartitionStreamingManager::EnableStreamingDebug(bool bEnable)
{
    bStreamingDebugEnabled = bEnable;
    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming debug %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionStreamingManager::IsStreamingDebugEnabled() const
{
    return bStreamingDebugEnabled;
}

void UAuracronWorldPartitionStreamingManager::DrawDebugStreamingInfo(UWorld* World) const
{
    if (!bIsInitialized || !bStreamingDebugEnabled || !World)
    {
        return;
    }

    // This would draw debug information in the world
    // For now, just log the information
    UE_LOG(LogAuracronStreaming, VeryVerbose, TEXT("Drawing debug streaming info for world: %s"), *World->GetName());
}

void UAuracronWorldPartitionStreamingManager::LogStreamingState() const
{
    if (!bIsInitialized)
    {
        UE_LOG(LogAuracronStreaming, Warning, TEXT("Streaming Manager not initialized"));
        return;
    }

    UE_LOG(LogAuracronStreaming, Log, TEXT("=== Streaming Manager State ==="));
    UE_LOG(LogAuracronStreaming, Log, TEXT("Total Requests: %d"), StreamingRequests.Num());
    UE_LOG(LogAuracronStreaming, Log, TEXT("Active Requests: %d"), ActiveRequests.Num());
    UE_LOG(LogAuracronStreaming, Log, TEXT("Pending Requests: %d"), GetPendingRequestCount());
    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming Sources: %d"), StreamingSources.Num());
    UE_LOG(LogAuracronStreaming, Log, TEXT("Memory Usage: %.2f MB"), GetCurrentMemoryUsage());
    UE_LOG(LogAuracronStreaming, Log, TEXT("Memory Pressure: %.2f%%"), GetMemoryPressure() * 100.0f);
    UE_LOG(LogAuracronStreaming, Log, TEXT("Streaming Efficiency: %.2f%%"), GetStreamingEfficiency() * 100.0f);
    UE_LOG(LogAuracronStreaming, Log, TEXT("==============================="));
}
