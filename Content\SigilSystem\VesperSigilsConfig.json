{"vesper_sigils": {"curativo": {"id": 1, "name": "V<PERSON>per <PERSON>", "description": "Regeneração contínua de HP para o usuário e aliados próximos", "type": "Support", "element": "Life", "rarity": "Common", "stats": {"healing_per_second": 30.0, "healing_radius": 400.0, "overheal_percentage": 0.2, "duration": 10.0, "cooldown": 20.0, "energy_cost": 50.0, "scaling_per_level": 0.08}, "effects": {"primary_effect": "HP Regeneration", "secondary_effect": "Area Healing", "passive_bonus": "+15% <PERSON><PERSON> Received", "active_bonus": "Heal allies within 400 units"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperCurativo_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperCurativo_Active", "healing_vfx": "/Game/VFX/Sigils/Vesper/VesperCurativo_Healing", "primary_color": "#00FF7F", "secondary_color": "#98FB98", "particle_count": 400, "effect_scale": 1.0}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperCurativo_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperCurativo_Active", "healing_sound": "/Game/Audio/Sigils/Vesper/VesperCurativo_Healing", "volume": 0.7, "pitch": 1.2}, "gameplay_tags": ["Sigil.Type.Vesper.Curativo", "Sigil.<PERSON>.Heal", "Sigil.Effect.AreaEffect", "Sigil.Element.Life"]}, "energetico": {"id": 2, "name": "<PERSON><PERSON><PERSON>", "description": "Regeneração contínua de MP e redução de custos de habilidades", "type": "Support", "element": "Energy", "rarity": "Common", "stats": {"mana_per_second": 20.0, "cost_reduction": 0.25, "energy_efficiency": 1.3, "duration": 12.0, "cooldown": 25.0, "energy_cost": 40.0, "scaling_per_level": 0.06}, "effects": {"primary_effect": "MP Regeneration", "secondary_effect": "Ability Cost Reduction", "passive_bonus": "+10% <PERSON>", "active_bonus": "25% Ability Cost Reduction"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperEnergetico_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperEnergetico_Active", "energy_vfx": "/Game/VFX/Sigils/Vesper/VesperEnergetico_Energy", "primary_color": "#1E90FF", "secondary_color": "#87CEEB", "particle_count": 350, "effect_scale": 0.9}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperEnergetico_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperEnergetico_Active", "energy_sound": "/Game/Audio/Sigils/Vesper/VesperEnergetico_Energy", "volume": 0.6, "pitch": 1.3}, "gameplay_tags": ["Sigil.Type.Vesper.Energetico", "Sigil.Effect.ManaRegen", "Sigil.Effect.CostReduction", "Sigil.Element.Energy"]}, "velocidade": {"id": 3, "name": "Vesper Velocidade", "description": "Aumento significativo de velocidade de movimento e ataque", "type": "Support", "element": "Wind", "rarity": "Uncommon", "stats": {"movement_speed_bonus": 0.4, "attack_speed_bonus": 0.3, "dash_charges": 2, "duration": 8.0, "cooldown": 22.0, "energy_cost": 60.0, "scaling_per_level": 0.05}, "effects": {"primary_effect": "Movement Speed Boost", "secondary_effect": "Attack Speed Boost", "passive_bonus": "+8% Movement Speed", "active_bonus": "40% Movement Speed + 2 Dash Charges"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperVelocidade_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperVelocidade_Active", "speed_vfx": "/Game/VFX/Sigils/Vesper/VesperVelocidade_Speed", "primary_color": "#FFFF00", "secondary_color": "#FFD700", "particle_count": 450, "effect_scale": 1.1}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperVelocidade_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperVelocidade_Active", "speed_sound": "/Game/Audio/Sigils/Vesper/VesperVelocidade_Speed", "volume": 0.8, "pitch": 1.4}, "gameplay_tags": ["Sigil.Type.Vesper.Velocidade", "Sigil.Effect.SpeedBoost", "Sigil.Effect.Dash", "Sigil.Element.Wind"]}, "visao": {"id": 4, "name": "<PERSON><PERSON><PERSON>", "description": "Aumento de alcance de visão e detecção de inimigos invisíveis", "type": "Support", "element": "Light", "rarity": "Uncommon", "stats": {"vision_range_bonus": 0.5, "true_sight_radius": 600.0, "detection_bonus": 0.8, "duration": 15.0, "cooldown": 30.0, "energy_cost": 45.0, "scaling_per_level": 0.04}, "effects": {"primary_effect": "Extended Vision", "secondary_effect": "True Sight", "passive_bonus": "+20% Vision Range", "active_bonus": "Detect invisible enemies within 600 units"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperVisao_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperVisao_Active", "sight_vfx": "/Game/VFX/Sigils/Vesper/VesperVisao_TrueSight", "primary_color": "#FFFFFF", "secondary_color": "#F0F8FF", "particle_count": 300, "effect_scale": 1.5}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperVisao_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperVisao_Active", "sight_sound": "/Game/Audio/Sigils/Vesper/VesperVisao_TrueSight", "volume": 0.5, "pitch": 1.5}, "gameplay_tags": ["Sigil.Type.Vesper.Visao", "Sigil.Effect.VisionBoost", "Sigil.Effect.TrueSight", "Sigil.Element.Light"]}, "teleporte": {"id": 5, "name": "<PERSON><PERSON><PERSON>", "description": "Teletransporte de curto alcance com múltiplas cargas", "type": "Support", "element": "Space", "rarity": "Rare", "stats": {"teleport_range": 800.0, "charges": 3, "charge_cooldown": 8.0, "duration": 20.0, "cooldown": 35.0, "energy_cost": 70.0, "scaling_per_level": 0.07}, "effects": {"primary_effect": "Short Range Teleport", "secondary_effect": "Multiple Charges", "passive_bonus": "+5% Dodge Chance", "active_bonus": "3 Teleport charges with 800 unit range"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperTeleporte_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperTeleporte_Active", "teleport_vfx": "/Game/VFX/Sigils/Vesper/VesperTeleporte_Teleport", "primary_color": "#FF00FF", "secondary_color": "#DA70D6", "particle_count": 800, "effect_scale": 1.8}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperTeleporte_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperTeleporte_Active", "teleport_sound": "/Game/Audio/Sigils/Vesper/VesperTeleporte_Teleport", "volume": 0.9, "pitch": 1.6}, "gameplay_tags": ["Sigil.Type.Vesper.Teleporte", "Sigil.Effect.Teleport", "Sigil.Effect.Mobility", "Sigil.Element.Space"]}, "temporal": {"id": 6, "name": "<PERSON><PERSON><PERSON>", "description": "Manipulação do tempo - acelera aliados e desacelera inimigos", "type": "Support", "element": "Time", "rarity": "Epic", "stats": {"time_acceleration": 1.5, "time_deceleration": 0.6, "effect_radius": 500.0, "duration": 12.0, "cooldown": 40.0, "energy_cost": 100.0, "scaling_per_level": 0.1}, "effects": {"primary_effect": "Time Manipulation", "secondary_effect": "Area Time Effects", "passive_bonus": "+10% Cooldown Recovery", "active_bonus": "50% faster allies, 40% slower enemies"}, "visual_effects": {"activation_vfx": "/Game/VFX/Sigils/Vesper/VesperTemporal_Activation", "active_vfx": "/Game/VFX/Sigils/Vesper/VesperTemporal_Active", "time_vfx": "/Game/VFX/Sigils/Vesper/VesperTemporal_TimeField", "primary_color": "#9370DB", "secondary_color": "#DDA0DD", "particle_count": 900, "effect_scale": 2.0}, "audio_effects": {"activation_sound": "/Game/Audio/Sigils/Vesper/VesperTemporal_Activation", "active_sound": "/Game/Audio/Sigils/Vesper/VesperTemporal_Active", "time_sound": "/Game/Audio/Sigils/Vesper/VesperTemporal_TimeField", "volume": 0.8, "pitch": 0.8}, "gameplay_tags": ["Sigil.Type.Vesper.Temporal", "Sigil.Effect.TimeManipulation", "Sigil.Effect.AreaEffect", "Sigil.Element.Time"]}}, "support_mechanics": {"healing_scaling": {"base_multiplier": 1.0, "level_scaling": 0.05, "archetype_bonus": 0.15, "fusion_bonus": 0.25}, "utility_effectiveness": {"base_effectiveness": 1.0, "synergy_bonus": 0.2, "resonance_bonus": 0.15, "archetype_specialization": 0.3}, "area_effects": {"base_radius": 400.0, "radius_scaling": 0.02, "max_targets": 5, "friendly_fire": false}}, "elemental_synergies": {"life_energy": {"combination": ["Vesper.Curativo", "Vesper.Energetico"], "bonus": "+20% Regeneration Effectiveness", "description": "Life and Energy synergize for better regeneration"}, "time_space": {"combination": ["Vesper.<PERSON>", "Vesper.Teleporte"], "bonus": "+25% Mobility Effects", "description": "Time and Space manipulation enhance mobility"}, "light_life": {"combination": ["Vesper.Visao", "Vesper.Curativo"], "bonus": "+15% Detection and Healing", "description": "Light reveals and life heals"}}}