#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Containers/Queue.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "Async/TaskGraphInterfaces.h"
#include "Async/Async.h"
#include "Engine/Texture2D.h"
#include "Engine/StaticMesh.h"
#include "RenderTargetPool.h"

#include "AuracronPerformanceOptimization.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPerformanceOptimization, Log, All);

// Enums for performance optimization
UENUM(BlueprintType)
enum class EPerformanceOptimizationLevel : uint8
{
    None            UMETA(DisplayName = "None"),
    Basic           UMETA(DisplayName = "Basic"),
    Moderate        UMETA(DisplayName = "Moderate"),
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Custom          UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EMemoryPoolType : uint8
{
    DNA             UMETA(DisplayName = "DNA"),
    Texture         UMETA(DisplayName = "Texture"),
    Mesh            UMETA(DisplayName = "Mesh"),
    Animation       UMETA(DisplayName = "Animation"),
    Audio           UMETA(DisplayName = "Audio"),
    General         UMETA(DisplayName = "General")
};

UENUM(BlueprintType)
enum class EAsyncProcessingPriority : uint8
{
    Low             UMETA(DisplayName = "Low"),
    Normal          UMETA(DisplayName = "Normal"),
    High            UMETA(DisplayName = "High"),
    Critical        UMETA(DisplayName = "Critical")
};

UENUM(BlueprintType)
enum class EBatchOperationType : uint8
{
    DNAProcessing   UMETA(DisplayName = "DNA Processing"),
    TextureGeneration UMETA(DisplayName = "Texture Generation"),
    MeshDeformation UMETA(DisplayName = "Mesh Deformation"),
    AnimationBaking UMETA(DisplayName = "Animation Baking")
};

// Structures for performance optimization
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMemoryPoolConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    EMemoryPoolType PoolType = EMemoryPoolType::General;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 InitialSizeMB = 64;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 MaxSizeMB = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    int32 GrowthSizeMB = 32;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    bool bAutoGrow = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    bool bAutoShrink = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Pool")
    float ShrinkThreshold = 0.3f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FAsyncProcessingConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    int32 MaxConcurrentTasks = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    int32 TaskQueueSize = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    bool bEnableTaskPrioritization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    bool bEnableLoadBalancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    float TaskTimeoutSeconds = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    bool bEnableTaskRetry = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Async Processing")
    int32 MaxRetryAttempts = 3;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FGPUAccelerationConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    bool bEnableGPUAcceleration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    bool bEnableComputeShaders = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    int32 MaxGPUMemoryMB = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    int32 ComputeShaderThreadGroups = 64;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    bool bEnableGPUProfiling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU Acceleration")
    bool bPreferDedicatedGPU = true;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FBatchProcessingConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    int32 BatchSize = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    int32 MaxBatchQueueSize = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    bool bEnableBatchOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    bool bEnableProgressReporting = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    float BatchTimeoutSeconds = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch Processing")
    bool bEnableBatchCaching = true;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FPerformanceOptimizationConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    EPerformanceOptimizationLevel OptimizationLevel = EPerformanceOptimizationLevel::Moderate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    TArray<FMemoryPoolConfiguration> MemoryPools;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    FAsyncProcessingConfiguration AsyncProcessing;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    FGPUAccelerationConfiguration GPUAcceleration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    FBatchProcessingConfiguration BatchProcessing;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    bool bEnableAutomaticOptimizations = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    float MonitoringIntervalSeconds = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    bool bEnableMemoryOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    bool bEnableCPUOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    bool bEnableGPUOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    float MaxMemoryUsagePercent = 80.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    float MaxCPUUsagePercent = 85.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Optimization")
    float OptimizationCheckInterval = 5.0f;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float CPUUsagePercent = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float MemoryUsagePercent = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float GPUUsagePercent = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int64 MemoryUsageMB = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float CacheHitRatio = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int32 ActiveThreadCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int32 AsyncOperationsCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    int32 BatchOperationsCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    float AverageFrameTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance Metrics")
    FDateTime LastUpdateTime;
};

/**
 * Performance optimization system for MetaHuman Bridge
 * Provides advanced performance management with UE5.6 optimization APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronPerformanceOptimization
{
public:
    FAuracronPerformanceOptimization();
    ~FAuracronPerformanceOptimization();

    // Core performance optimization
    bool InitializePerformanceOptimization(const FPerformanceOptimizationConfiguration& Configuration);
    void ShutdownPerformanceOptimization();

    // Memory pool management
    bool ConfigureMemoryPools(const TArray<FMemoryPoolConfiguration>& PoolConfigurations);
    bool GetMemoryPoolStatus(EMemoryPoolType PoolType, float& UsagePercentage, int32& AllocatedSizeMB, int32& AvailableSizeMB);

    // Async processing
    bool ConfigureAsyncProcessing(const FAsyncProcessingConfiguration& Configuration);
    bool ExecuteAsyncDNAProcessing(const TArray<FString>& DNAFilePaths, EAsyncProcessingPriority Priority);
    bool ExecuteAsyncTextureGeneration(const TArray<struct FTextureGenerationParameters>& TextureParams, EAsyncProcessingPriority Priority);

    // GPU acceleration
    bool ConfigureGPUAcceleration(const FGPUAccelerationConfiguration& Configuration);
    bool ExecuteGPUMeshDeformation(UStaticMesh* SourceMesh, const TArray<FVector>& DeformationVectors, UStaticMesh*& OutDeformedMesh);
    bool ExecuteGPUTextureProcessing(UTexture2D* SourceTexture, const FString& ProcessingShader, UTexture2D*& OutProcessedTexture);

    // Batch processing
    bool ConfigureBatchProcessing(const FBatchProcessingConfiguration& Configuration);
    bool ExecuteBatchDNAOperations(const TArray<FString>& DNAFilePaths, EBatchOperationType OperationType);
    bool ExecuteBatchTextureOperations(const TArray<struct FTextureGenerationParameters>& TextureParams, EBatchOperationType OperationType);

    // Performance monitoring
    FPerformanceMetrics GetCurrentPerformanceMetrics();
    void SetPerformanceMonitoringEnabled(bool bEnabled);
    bool ProfileOperationPerformance(const FString& OperationName, float& ExecutionTimeMS, float& MemoryUsageMB);

    // Optimization utilities
    bool OptimizeMemoryUsage(bool bForceGarbageCollection = false);
    void ClearPerformanceCaches(bool bClearMemoryPools = false);
    bool ApplyAutomaticOptimizations();
    bool ApplyPerformanceOptimizations(const TArray<FString>& OptimizationTypes);

    // Thread safety
    mutable FCriticalSection PerformanceOptimizationMutex;

private:
    // Configuration
    FPerformanceOptimizationConfiguration CurrentConfiguration;
    FThreadSafeBool bPerformanceMonitoringEnabled;

    // Memory pools
    TMap<EMemoryPoolType, TSharedPtr<class FMemoryPool>> MemoryPools;

    // Task managers
    TSharedPtr<class FAsyncTaskManager> AsyncTaskManager;
    TSharedPtr<class FGPUAccelerationManager> GPUAccelerationManager;
    TSharedPtr<class FBatchProcessingManager> BatchProcessingManager;

    // Performance metrics
    mutable FPerformanceMetrics CurrentMetrics;
    mutable TMap<FString, TArray<float>> PerformanceHistory;

    // Internal methods
    bool InitializeMemoryPools();
    bool InitializeAsyncTaskManager();
    bool InitializeGPUAccelerationManager();
    bool InitializeBatchProcessingManager();
    
    void UpdatePerformanceMetrics();
    float GetSystemCPUUsage();
    float GetSystemMemoryUsage();
    float GetSystemGPUUsage();
    float CalculateCacheHitRatio();
    int32 GetActiveThreadCount();
    int32 GetAsyncOperationsCount();
    int32 GetBatchOperationsCount();

    void UpdatePerformanceStatistics(const FString& OperationName, double ExecutionTime, bool bSuccess, float MemoryUsage);
    bool ValidatePerformanceConfiguration(const FPerformanceOptimizationConfiguration& Configuration);

    // Cache management
    void CleanupExpiredCacheEntries();
    void OptimizeCacheUsage();
    void BalanceThreadWorkloads();
    bool OptimizeGPUMemoryUsage();
    void MonitorMemoryPoolUsage();

    // Prevent copying
    FAuracronPerformanceOptimization(const FAuracronPerformanceOptimization&) = delete;
    FAuracronPerformanceOptimization& operator=(const FAuracronPerformanceOptimization&) = delete;
};
