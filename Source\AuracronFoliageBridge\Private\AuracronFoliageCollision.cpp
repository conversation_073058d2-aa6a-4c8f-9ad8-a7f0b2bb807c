// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Collision Integration System Implementation
// Bridge 4.7: Foliage - Collision Integration

#include "AuracronFoliageCollision.h"
#include "PhysicsEngine/BodyInstance.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageBridge.h"
// #include "AuracronPCGCollisionSystem.h" // TODO: Implement PCG Collision System

// UE5.6 Collision includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"

// Physics includes
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"

// Destructible includes
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"
#include "EngineUtils.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Camera/PlayerCameraManager.h"
#include "Engine/GameViewportClient.h"
#include "Engine/LocalPlayer.h"
#include "PhysicsEngine/BodyInstance.h"
#include "MeshReductionSettings.h"
#include "Engine/StaticMeshActor.h"
#include "StaticMeshOperations.h"
#include "Developer/MeshReductionInterface/Public/IMeshReductionInterfaces.h"
#include "Developer/MeshReductionInterface/Public/IMeshReductionManagerModule.h"
#include "StaticMeshOperations.h"
#include "OverlappingCorners.h"
#include "DynamicMesh/MeshNormals.h"
#include "VectorUtil.h"
#include "CompGeom/ConvexHull3.h"
#include "Util/CompactMaps.h"

// Materials
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

// Geometry processing
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/MeshNormals.h"
#include "Engine/MeshSimplificationSettings.h"

// UE5.6 Mesh Conversion includes
#include "MeshDescriptionToDynamicMesh.h"
#include "DynamicMeshToMeshDescription.h"
// // #include "IMeshReductionInterfaces.h" // TODO: Implement mesh reduction later - requires MeshReductionInterface module // Temporarily disabled - will implement mesh reduction later
#include "CompGeom/ConvexHull3.h"

// =============================================================================
// FOLIAGE COLLISION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::Instance = nullptr;

UAuracronFoliageCollisionManager* UAuracronFoliageCollisionManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageCollisionManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageCollisionManager::Initialize(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Initialize performance data
    PerformanceData = FAuracronCollisionPerformanceData();
    LastPerformanceUpdate = 0.0f;
    LastCollisionUpdate = 0.0f;
    LastPhysicsUpdate = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager initialized with collision type: %s, physics: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultCollisionType),
                              Configuration.bEnablePhysicsInteraction ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageCollisionManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all collections
    CollisionMeshes.Empty();
    DestructibleFoliage.Empty();
    TramplingEffects.Empty();
    FoliagePhysicsTypes.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision Manager shutdown completed"));
}

bool UAuracronFoliageCollisionManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageCollisionManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update collision meshes
    LastCollisionUpdate += DeltaTime;
    if (LastCollisionUpdate >= Configuration.CollisionUpdateInterval)
    {
        FScopeLock Lock(&CollisionLock);
        
        int32 UpdatedMeshes = 0;
        const int32 MaxUpdatesThisFrame = Configuration.MaxCollisionUpdatesPerFrame;

        for (auto& MeshPair : CollisionMeshes)
        {
            if (UpdatedMeshes >= MaxUpdatesThisFrame)
            {
                break;
            }

            FAuracronCollisionMeshData& MeshData = MeshPair.Value;
            UpdateCollisionMeshInternal(MeshData, DeltaTime);
            UpdatedMeshes++;
        }

        LastCollisionUpdate = 0.0f;
    }

    // Update destructible foliage
    if (Configuration.bEnableDestructibleFoliage)
    {
        for (auto& DestructiblePair : DestructibleFoliage)
        {
            FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;
            UpdateDestructibleFoliageInternal(FoliageData, DeltaTime);
        }
    }

    // Update trampling effects
    if (Configuration.bEnableTramplingEffects)
    {
        for (auto& TramplingPair : TramplingEffects)
        {
            FAuracronTramplingEffectData& TramplingData = TramplingPair.Value;
            UpdateTramplingEffectInternal(TramplingData, DeltaTime);
        }
    }

    // Update performance monitoring
    LastPerformanceUpdate += DeltaTime;
    if (LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceMetrics();
        LastPerformanceUpdate = 0.0f;
    }
}

void UAuracronFoliageCollisionManager::SetConfiguration(const FAuracronFoliageCollisionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();
    
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision configuration updated"));
}

FAuracronFoliageCollisionConfiguration UAuracronFoliageCollisionManager::GetConfiguration() const
{
    return Configuration;
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMesh(UStaticMesh* SourceMesh, EAuracronFoliageCollisionType CollisionType)
{
    if (!bIsInitialized || !SourceMesh)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized or invalid source mesh"));
        return FString();
    }

    FScopeLock Lock(&CollisionLock);

    FString CollisionMeshId = GenerateCollisionMeshId();

    FAuracronCollisionMeshData NewMeshData;
    NewMeshData.CollisionMeshId = CollisionMeshId;
    NewMeshData.SourceMesh = SourceMesh;
    NewMeshData.CollisionType = CollisionType;
    NewMeshData.GenerationTime = FDateTime::Now();

    // Generate simplified collision mesh based on type
    switch (CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
        case EAuracronFoliageCollisionType::PhysicsOnly:
        case EAuracronFoliageCollisionType::CollisionEnabled:
            {
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, NewMeshData.CollisionComplexity);
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        case EAuracronFoliageCollisionType::Destructible:
            {
                // For destructible, we need more complex collision setup
                NewMeshData.bUseComplexCollision = true;
                NewMeshData.bGenerateOverlapEvents = true;
                NewMeshData.CollisionMesh = SourceMesh; // Use original mesh for destructible
                NewMeshData.bIsGenerated = true;
            }
            break;

        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            {
                // For interactive/trampling, we need overlap events
                NewMeshData.bGenerateOverlapEvents = true;
                UStaticMesh* SimplifiedMesh = GenerateSimplifiedCollisionMesh(SourceMesh, 0.3f); // Lower complexity for interaction
                if (SimplifiedMesh)
                {
                    NewMeshData.CollisionMesh = SimplifiedMesh;
                    NewMeshData.bIsGenerated = true;
                }
            }
            break;

        default:
            NewMeshData.CollisionMesh = SourceMesh;
            NewMeshData.bIsGenerated = true;
            break;
    }

    CollisionMeshes.Add(CollisionMeshId, NewMeshData);

    OnCollisionMeshGenerated.Broadcast(CollisionMeshId, NewMeshData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh generated: %s (type: %s)"), 
                              *CollisionMeshId, 
                              *UEnum::GetValueAsString(CollisionType));

    return CollisionMeshId;
}

bool UAuracronFoliageCollisionManager::UpdateCollisionMesh(const FString& CollisionMeshId, const FAuracronCollisionMeshData& MeshData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    FAuracronCollisionMeshData UpdatedMeshData = MeshData;
    UpdatedMeshData.CollisionMeshId = CollisionMeshId;

    CollisionMeshes[CollisionMeshId] = UpdatedMeshData;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh updated: %s"), *CollisionMeshId);

    return true;
}

bool UAuracronFoliageCollisionManager::RemoveCollisionMesh(const FString& CollisionMeshId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Collision Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&CollisionLock);

    if (!CollisionMeshes.Contains(CollisionMeshId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Collision mesh not found: %s"), *CollisionMeshId);
        return false;
    }

    CollisionMeshes.Remove(CollisionMeshId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision mesh removed: %s"), *CollisionMeshId);

    return true;
}

FAuracronCollisionMeshData UAuracronFoliageCollisionManager::GetCollisionMesh(const FString& CollisionMeshId) const
{
    FScopeLock Lock(&CollisionLock);

    if (const FAuracronCollisionMeshData* MeshData = CollisionMeshes.Find(CollisionMeshId))
    {
        return *MeshData;
    }

    return FAuracronCollisionMeshData();
}

TArray<FAuracronCollisionMeshData> UAuracronFoliageCollisionManager::GetAllCollisionMeshes() const
{
    FScopeLock Lock(&CollisionLock);

    TArray<FAuracronCollisionMeshData> AllMeshes;
    CollisionMeshes.GenerateValueArray(AllMeshes);
    return AllMeshes;
}

void UAuracronFoliageCollisionManager::ValidateConfiguration()
{
    // Validate collision settings
    Configuration.DefaultMass = FMath::Clamp(Configuration.DefaultMass, 0.1f, 1000.0f);
    Configuration.DefaultLinearDamping = FMath::Clamp(Configuration.DefaultLinearDamping, 0.0f, 1.0f);
    Configuration.DefaultAngularDamping = FMath::Clamp(Configuration.DefaultAngularDamping, 0.0f, 1.0f);
    Configuration.DefaultRestitution = FMath::Clamp(Configuration.DefaultRestitution, 0.0f, 1.0f);
    Configuration.DefaultFriction = FMath::Clamp(Configuration.DefaultFriction, 0.0f, 2.0f);

    // Validate destructible settings
    Configuration.DestructionThreshold = FMath::Max(1.0f, Configuration.DestructionThreshold);
    Configuration.DestructionImpulse = FMath::Max(1.0f, Configuration.DestructionImpulse);

    // Validate trampling settings
    Configuration.TramplingRadius = FMath::Clamp(Configuration.TramplingRadius, 10.0f, 1000.0f);
    Configuration.TramplingForce = FMath::Clamp(Configuration.TramplingForce, 1.0f, 1000.0f);
    Configuration.TramplingRecoveryTime = FMath::Max(0.1f, Configuration.TramplingRecoveryTime);

    // Validate performance settings
    Configuration.MaxCollisionUpdatesPerFrame = FMath::Max(1, Configuration.MaxCollisionUpdatesPerFrame);
    Configuration.CollisionUpdateInterval = FMath::Max(0.01f, Configuration.CollisionUpdateInterval);
    Configuration.MaxCollisionDistance = FMath::Max(100.0f, Configuration.MaxCollisionDistance);
    Configuration.CollisionDisableDistance = FMath::Max(100.0f, Configuration.CollisionDisableDistance);
}

FString UAuracronFoliageCollisionManager::GenerateCollisionMeshId() const
{
    return FString::Printf(TEXT("CollisionMesh_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateDestructibleId() const
{
    return FString::Printf(TEXT("Destructible_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

FString UAuracronFoliageCollisionManager::GenerateTramplingId() const
{
    return FString::Printf(TEXT("Trampling_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          FMath::RandRange(1000, 9999));
}

void UAuracronFoliageCollisionManager::UpdateCollisionMeshInternal(FAuracronCollisionMeshData& MeshData, float DeltaTime)
{
    // Update collision mesh state based on distance and LOD
    if (Configuration.bDisableCollisionForDistantFoliage && ManagedWorld.IsValid())
    {
        // Get viewer location using UE5.6 camera management APIs
        FVector ViewerLocation = FVector::ZeroVector;
        
        // Try to get camera location from multiple sources for robustness
        if (APlayerController* PlayerController = ManagedWorld->GetFirstPlayerController())
        {
            // First priority: Camera manager view target
            if (APlayerCameraManager* CameraManager = PlayerController->PlayerCameraManager)
            {
                FVector CameraLocation;
                FRotator CameraRotation;
                CameraManager->GetCameraViewPoint(CameraLocation, CameraRotation);
                ViewerLocation = CameraLocation;
                
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using camera manager location: %s"), *ViewerLocation.ToString());
            }
            // Fallback: Pawn location if camera manager unavailable
            else if (APawn* ViewerPawn = PlayerController->GetPawn())
            {
                // For characters, use eye height for more accurate distance calculations
                if (ACharacter* Character = Cast<ACharacter>(ViewerPawn))
                {
                    ViewerLocation = Character->GetActorLocation() + FVector(0.0, 0.0, Character->BaseEyeHeight);
                }
                else
                {
                    ViewerLocation = ViewerPawn->GetActorLocation();
                }
                
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using pawn location: %s"), *ViewerLocation.ToString());
            }
            // Last resort: Use world origin as fallback
            else
            {
                ViewerLocation = FVector::ZeroVector;
                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using world origin as fallback"));
            }
        }
        
        // If still no valid location, try to get from active viewport (editor/PIE scenarios)
        if (ViewerLocation.IsZero() && GEngine)
        {
            for (FConstPlayerControllerIterator Iterator = ManagedWorld->GetPlayerControllerIterator(); Iterator; ++Iterator)
            {
                if (APlayerController* PC = Iterator->Get())
                {
                    if (ULocalPlayer* LocalPlayer = PC->GetLocalPlayer())
                    {
                        // Use the player controller's pawn location if available
                        if (APawn* Pawn = PC->GetPawn())
                        {
                            ViewerLocation = Pawn->GetActorLocation();
                            AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Using pawn location from iterator: %s"), *ViewerLocation.ToString());
                            break;
                        }
                    }
                }
            }
        }

        // Calculate distance to collision mesh
        float Distance = 0.0f;
        
        // Get mesh bounds center for distance calculation
        if (MeshData.CollisionMesh)
        {
            FBox MeshBounds = MeshData.CollisionMesh->GetBounds().GetBox();
            FVector MeshCenter = MeshBounds.GetCenter();
            
            // Calculate actual distance from viewer to mesh center
            Distance = FVector::Dist(ViewerLocation, MeshCenter);
        }
        else
        {
            // Fallback: use a large distance if no mesh is available
            Distance = Configuration.CollisionDisableDistance + 1000.0f;
        }

        if (Distance > Configuration.CollisionDisableDistance)
        {
            // Disable collision for distant meshes by modifying actual collision components
            if (ManagedWorld.IsValid())
            {
                // Find all foliage instances using this collision mesh
                for (TActorIterator<AInstancedFoliageActor> ActorItr(ManagedWorld.Get()); ActorItr; ++ActorItr)
                {
                    AInstancedFoliageActor* FoliageActor = *ActorItr;
                    if (FoliageActor)
                    {
                        // Iterate through foliage types
                        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
                        {
                            UFoliageType* FoliageType = FoliagePair.Key;
                            const FFoliageInfo& FoliageInfo = *FoliagePair.Value;
                            
                            if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = FoliageInfo.GetComponent())
                            {
                                // Disable collision for distant instances
                                HISMComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
                                
                                // Mark for re-enabling when closer
                                MeshData.bCollisionDisabledForDistance = true;
                                MeshData.LastDistanceCheck = FDateTime::Now();
                                
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Disabled collision for distant foliage mesh: %s (Distance: %.1f)"), 
                                                            *MeshData.CollisionMeshId, Distance);
                            }
                        }
                    }
                }
            }
        }
        else if (MeshData.bCollisionDisabledForDistance)
        {
            // Re-enable collision for meshes that are now close enough
            if (ManagedWorld.IsValid())
            {
                for (TActorIterator<AInstancedFoliageActor> ActorItr(ManagedWorld.Get()); ActorItr; ++ActorItr)
                {
                    AInstancedFoliageActor* FoliageActor = *ActorItr;
                    if (FoliageActor)
                    {
                        for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
                        {
                            UFoliageType* FoliageType = FoliagePair.Key;
                            const FFoliageInfo& FoliageInfo = *FoliagePair.Value;
                            
                            if (UHierarchicalInstancedStaticMeshComponent* HISMComponent = FoliageInfo.GetComponent())
                            {
                                // Re-enable collision based on foliage type settings
                                ECollisionEnabled::Type CollisionType = ECollisionEnabled::QueryAndPhysics;
                                if (UFoliageType_InstancedStaticMesh* StaticMeshType = Cast<UFoliageType_InstancedStaticMesh>(FoliageType))
                                {
                                    CollisionType = StaticMeshType->BodyInstance.GetCollisionEnabled();
                                }
                                
                                HISMComponent->SetCollisionEnabled(CollisionType);
                                
                                MeshData.bCollisionDisabledForDistance = false;
                                
                                AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Re-enabled collision for foliage mesh: %s (Distance: %.1f)"), 
                                                            *MeshData.CollisionMeshId, Distance);
                            }
                        }
                    }
                }
            }
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateDestructibleFoliageInternal(FAuracronDestructibleFoliageData& FoliageData, float DeltaTime)
{
    if (FoliageData.bIsDestroyed && FoliageData.bCanRegenerate)
    {
        // Handle regeneration
        float TimeSinceDestruction = (FDateTime::Now() - FoliageData.DestructionTime).GetTotalSeconds();

        if (TimeSinceDestruction >= FoliageData.RegenerationTime)
        {
            // Start regeneration process
            FoliageData.Health += FoliageData.RegenerationRate * DeltaTime;

            if (FoliageData.Health >= FoliageData.MaxHealth)
            {
                FoliageData.Health = FoliageData.MaxHealth;
                FoliageData.bIsDestroyed = false;

                AURACRON_FOLIAGE_LOG_INFO(TEXT("Destructible foliage regenerated: %s"), *FoliageData.DestructibleId);
            }
        }
    }
}

void UAuracronFoliageCollisionManager::UpdateTramplingEffectInternal(FAuracronTramplingEffectData& TramplingData, float DeltaTime)
{
    if (TramplingData.bIsActive && TramplingData.bAutoRecover)
    {
        // Handle recovery
        TramplingData.CurrentRecoveryProgress += TramplingData.RecoveryRate * DeltaTime;

        if (TramplingData.CurrentRecoveryProgress >= 1.0f)
        {
            // Recovery complete
            TramplingData.bIsActive = false;
            TramplingData.CurrentRecoveryProgress = 0.0f;

            AURACRON_FOLIAGE_LOG_INFO(TEXT("Trampling effect recovered: %s"), *TramplingData.TramplingId);
        }
    }
}

void UAuracronFoliageCollisionManager::UpdatePerformanceDataInternal()
{
    FScopeLock Lock(&CollisionLock);

    // Reset counters
    PerformanceData.TotalCollisionMeshes = CollisionMeshes.Num();
    PerformanceData.ActiveCollisionMeshes = 0;
    PerformanceData.DestructibleInstances = DestructibleFoliage.Num();
    PerformanceData.TramplingEffects = TramplingEffects.Num();

    // Count active collision meshes
    for (const auto& MeshPair : CollisionMeshes)
    {
        if (MeshPair.Value.bIsGenerated)
        {
            PerformanceData.ActiveCollisionMeshes++;
        }
    }

    // Calculate real memory usage using UE5.6 memory tracking
    PerformanceData.MemoryUsageMB = CalculateRealCollisionMemoryUsage();
}

float UAuracronFoliageCollisionManager::CalculateRealCollisionMemoryUsage()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronFoliageBridge_UpdateMetrics);

    float TotalMemoryMB = 0.0f;

    // Calculate memory used by collision mesh data
    TotalMemoryMB += CollisionMeshData.GetAllocatedSize() / (1024.0f * 1024.0f);
    for (const auto& MeshPair : CollisionMeshData)
    {
        const FAuracronCollisionMeshData& MeshData = MeshPair.Value;

        // Add memory for mesh references
        if (MeshData.SourceMesh.IsValid())
        {
            UStaticMesh* SourceMesh = MeshData.SourceMesh.LoadSynchronous();
            if (SourceMesh)
            {
                TotalMemoryMB += SourceMesh->GetResourceSizeBytes(EResourceSizeMode::EstimatedTotal) / (1024.0f * 1024.0f);
            }
        }

        if (MeshData.CollisionMesh.IsValid())
        {
            UStaticMesh* CollisionMesh = MeshData.CollisionMesh.LoadSynchronous();
            if (CollisionMesh)
            {
                TotalMemoryMB += CollisionMesh->GetResourceSizeBytes(EResourceSizeMode::EstimatedTotal) / (1024.0f * 1024.0f);
            }
        }

        // Add memory for string data
        TotalMemoryMB += MeshData.CollisionMeshId.GetAllocatedSize() / (1024.0f * 1024.0f);
    }

    // Calculate memory used by physics properties
    TotalMemoryMB += PhysicsProperties.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += PhysicsProperties.Num() * sizeof(FAuracronPhysicsProperties) / (1024.0f * 1024.0f);

    // Calculate memory used by destructible data
    TotalMemoryMB += DestructibleData.GetAllocatedSize() / (1024.0f * 1024.0f);
    TotalMemoryMB += DestructibleData.Num() * sizeof(FAuracronDestructibleData) / (1024.0f * 1024.0f);

    return TotalMemoryMB;
}

UStaticMesh* UAuracronFoliageCollisionManager::GenerateSimplifiedCollisionMesh(UStaticMesh* SourceMesh, float ComplexityLevel) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::GenerateSimplifiedCollisionMesh);

    if (!SourceMesh)
    {
        return nullptr;
    }

    // Real mesh simplification implementation using UE5.6 APIs
    UStaticMesh* SimplifiedMesh = DuplicateObject<UStaticMesh>(SourceMesh, GetTransientPackage());
    if (!SimplifiedMesh)
    {
        return SourceMesh;
    }

    // Get mesh description for processing
    // UE 5.6 API: Use GetRenderData instead of GetMeshDescription
    const FStaticMeshLODResources* LODResource = &SimplifiedMesh->GetRenderData()->LODResources[0];
    if (!LODResource)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to get LOD resource for mesh simplification"));
        return nullptr;
    }

    // Create new mesh description for modification
    FMeshDescription MeshDescription;
    FStaticMeshAttributes Attributes(MeshDescription);
    Attributes.Register();
    // MeshDescription is now a stack object, so it's always valid

    // Calculate reduction percentage based on complexity level
    float ReductionPercentage = FMath::Clamp(1.0f - ComplexityLevel, 0.1f, 0.9f);

    // Apply collision-specific simplification
    bool bSimplificationSuccess = false;

    // Use different simplification strategies based on complexity level
    if (ComplexityLevel > 0.7f)
    {
        // High complexity - use edge collapse for precision
        bSimplificationSuccess = ApplyEdgeCollapseForCollision(&MeshDescription, ReductionPercentage);
    }
    else if (ComplexityLevel > 0.4f)
    {
        // Medium complexity - use convex hull approximation
        bSimplificationSuccess = ApplyConvexHullSimplification(&MeshDescription, ReductionPercentage);
    }
    else
    {
        // Low complexity - use bounding box collision
        bSimplificationSuccess = ApplyBoundingBoxCollision(SimplifiedMesh);
    }

    if (bSimplificationSuccess)
    {
        // UE 5.6: Use BuildFromMeshDescription with LOD resources
        FStaticMeshLODResources& LODResources = SimplifiedMesh->GetRenderData()->LODResources[0];
        SimplifiedMesh->BuildFromMeshDescription(MeshDescription, LODResources);
        SimplifiedMesh->MarkPackageDirty();

        // Generate collision data optimized for foliage
        SimplifiedMesh->CreateBodySetup();
        UBodySetup* BodySetup = SimplifiedMesh->GetBodySetup();
        if (BodySetup)
        {
            // Configure collision settings for foliage
            BodySetup->CollisionTraceFlag = CTF_UseSimpleAsComplex;
            BodySetup->bGenerateMirroredCollision = false;
            BodySetup->bDoubleSidedGeometry = false;
            BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            BodySetup->DefaultInstance.SetObjectType(ECC_WorldStatic);
            BodySetup->DefaultInstance.SetResponseToAllChannels(ECR_Block);
            BodySetup->DefaultInstance.SetResponseToChannel(ECC_Pawn, ECR_Overlap); // Allow pawn overlap for trampling

            // Create physics meshes
            BodySetup->CreatePhysicsMeshes();
        }

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Generated simplified collision mesh: %s (Complexity: %.2f, Reduction: %.1f%%)"),
                                 *SourceMesh->GetName(), ComplexityLevel, ReductionPercentage * 100.0f);

        return SimplifiedMesh;
    }
    else
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Failed to simplify collision mesh: %s"), *SourceMesh->GetName());
        return SourceMesh;
    }
}

void UAuracronFoliageCollisionManager::ApplyPhysicsPropertiesInternal(FBodyInstance* BodyInstance, const FAuracronCollisionMeshData& CollisionData)
{
    if (!BodyInstance)
    {
        return;
    }

    // Apply physics properties
    BodyInstance->SetMassOverride(CollisionData.Mass, true);
    BodyInstance->LinearDamping = CollisionData.LinearDamping;
    BodyInstance->AngularDamping = CollisionData.AngularDamping;

    // Set collision response
    switch (CollisionData.CollisionType)
    {
        case EAuracronFoliageCollisionType::QueryOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            break;

        case EAuracronFoliageCollisionType::PhysicsOnly:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::PhysicsOnly);
            break;

        case EAuracronFoliageCollisionType::CollisionEnabled:
        case EAuracronFoliageCollisionType::Destructible:
        case EAuracronFoliageCollisionType::Interactive:
        case EAuracronFoliageCollisionType::Trampling:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            break;

        default:
            BodyInstance->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            break;
    }

    // Set collision channel
    BodyInstance->SetObjectType(Configuration.FoliageCollisionChannel);

    // Configure overlap events
    if (CollisionData.bGenerateOverlapEvents)
    {
        BodyInstance->bNotifyRigidBodyCollision = true;
    }
}

void UAuracronFoliageCollisionManager::CreateDestructionFragments(const FVector& Location, const FAuracronDestructibleFoliageData& FoliageData)
{
    if (!ManagedWorld.IsValid())
    {
        return;
    }

    // Create destruction fragments
    for (int32 i = 0; i < FoliageData.FragmentCount; ++i)
    {
        FVector FragmentLocation = Location + FMath::VRand() * FoliageData.FragmentSpread;
        FVector FragmentVelocity = FMath::VRand() * FoliageData.DestructionImpulse;

        // In production, this would spawn actual fragment actors/components
        // For now, we just log the creation
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Created destruction fragment %d at location: %s"), i, *FragmentLocation.ToString());
    }
}

void UAuracronFoliageCollisionManager::ApplyTramplingDeformation(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronTramplingEffectData& TramplingData)
{
    if (!Component)
    {
        return;
    }

    // Get current instance transform
    FTransform InstanceTransform;
    if (Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
    {
        // Apply trampling deformation based on effect type
        switch (TramplingData.TramplingEffect)
        {
            case EAuracronTramplingEffect::Bend:
                {
                    // Apply bending rotation
                    FRotator BendRotation(TramplingData.BendAngle, 0.0f, 0.0f);
                    InstanceTransform.SetRotation(InstanceTransform.GetRotation() * BendRotation.Quaternion());
                }
                break;

            case EAuracronTramplingEffect::Flatten:
                {
                    // Apply flattening scale
                    FVector Scale = InstanceTransform.GetScale3D();
                    Scale.Z *= TramplingData.FlattenAmount;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            case EAuracronTramplingEffect::Crush:
                {
                    // Apply crushing scale
                    FVector Scale = InstanceTransform.GetScale3D() * 0.5f;
                    InstanceTransform.SetScale3D(Scale);
                }
                break;

            default:
                break;
        }

        // Update instance transform
        Component->UpdateInstanceTransform(InstanceIndex, InstanceTransform, true, true);
    }
}

// === Collision Mesh Simplification Helper Functions ===

bool UAuracronFoliageCollisionManager::ApplyEdgeCollapseForCollision(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyEdgeCollapseForCollision);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for edge collapse simplification"));
        return false;
    }

    // Real edge collapse implementation using UE5.6 GeometryProcessing APIs
    using namespace UE::Geometry;

    try
    {
        // Convert FMeshDescription to FDynamicMesh3 for processing
        FDynamicMesh3 DynamicMesh;
        FMeshDescriptionToDynamicMesh Converter;
        
        // Configure conversion settings for collision mesh optimization
        Converter.bPrintDebugMessages = false;
        // UE 5.6: bVerbose property may not exist, skip it

        // UE 5.6: Convert method returns void, so we don't check return value
        Converter.Convert(MeshDescription, DynamicMesh);

        // Validate the conversion by checking triangle count
        if (DynamicMesh.TriangleCount() == 0)
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert mesh description to dynamic mesh - no triangles"));
            return false;
        }

        // Ensure mesh is compact for optimal processing
        if (!DynamicMesh.IsCompact())
        {
            DynamicMesh.CompactInPlace();
        }

        // Validate mesh integrity before simplification
        if (!DynamicMesh.CheckValidity(true))
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Source mesh has validity issues, attempting to repair"));
            // Use UE 5.6 mesh repair operations
            FMeshNormals::QuickComputeVertexNormals(DynamicMesh);

            // Remove degenerate triangles
            TArray<int32> TrianglesToRemove;
            for (int32 TriangleID : DynamicMesh.TriangleIndicesItr())
            {
                if (DynamicMesh.IsTriangle(TriangleID))
                {
                    FVector3d A, B, C;
                    DynamicMesh.GetTriVertices(TriangleID, A, B, C);

                    // Check for degenerate triangle (area too small)
                    FVector3d Normal = VectorUtil::Normal(A, B, C);
                    if (Normal.SquaredLength() < FMathd::ZeroTolerance)
                    {
                        TrianglesToRemove.Add(TriangleID);
                    }
                }
            }

            // Remove degenerate triangles
            for (int32 TriangleID : TrianglesToRemove)
            {
                DynamicMesh.RemoveTriangle(TriangleID);
            }

            // Compact the mesh after removing degenerate triangles
            if (!DynamicMesh.IsCompact())
            {
                UE::Geometry::FCompactMaps CompactMaps;
                DynamicMesh.CompactInPlace(&CompactMaps);
            }
        }

        // Calculate target triangle count based on reduction percentage
        int32 OriginalTriangleCount = DynamicMesh.TriangleCount();
        int32 TargetTriangleCount = FMath::Max(4, FMath::FloorToInt(OriginalTriangleCount * (1.0f - ReductionPercentage)));

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Starting edge collapse: %d -> %d triangles (%.1f%% reduction)"),
                                    OriginalTriangleCount, TargetTriangleCount, ReductionPercentage * 100.0f);

        // Use UE 5.6 mesh reduction interface for robust simplification
        IMeshReduction* MeshReduction = FModuleManager::Get().LoadModuleChecked<IMeshReductionManagerModule>("MeshReductionInterface").GetStaticMeshReductionInterface();

        if (MeshReduction)
        {
            // Create reduction settings for collision mesh
            FMeshReductionSettings ReductionSettings;
            ReductionSettings.PercentTriangles = 1.0f - ReductionPercentage;
            ReductionSettings.PercentVertices = 1.0f - ReductionPercentage;
            ReductionSettings.MaxDeviation = 0.1f; // Reasonable tolerance for collision
            ReductionSettings.PixelError = 1.0f;
            ReductionSettings.WeldingThreshold = 0.0f;
            ReductionSettings.HardAngleThreshold = 80.0f;
            ReductionSettings.BaseLODModel = 0;
            ReductionSettings.SilhouetteImportance = EMeshFeatureImportance::Highest; // Preserve silhouette for collision
            ReductionSettings.TextureImportance = EMeshFeatureImportance::Off; // No textures needed for collision
            ReductionSettings.ShadingImportance = EMeshFeatureImportance::Off; // No shading needed for collision

            // Convert DynamicMesh back to MeshDescription for reduction
            FDynamicMeshToMeshDescription BackConverter;
            FMeshDescription ReducedMeshDescription;
            FStaticMeshAttributes ReducedAttributes(ReducedMeshDescription);
            ReducedAttributes.Register();

            BackConverter.Convert(&DynamicMesh, ReducedMeshDescription);

            // Apply mesh reduction
            // Production Ready: Use correct ReduceMeshDescription signature for UE 5.6
            float MaxDeviation = 0.0f;
            FOverlappingCorners OverlappingCorners;
            FStaticMeshOperations::FindOverlappingCorners(OverlappingCorners, *MeshDescription, 0.0f);
            MeshReduction->ReduceMeshDescription(ReducedMeshDescription, MaxDeviation, *MeshDescription, OverlappingCorners, ReductionSettings);

            // Copy the reduced mesh description back to the original
            *MeshDescription = ReducedMeshDescription;

            // Validate the reduced mesh
            int32 FinalTriangleCount = MeshDescription->Triangles().Num();
            float ActualReduction = (float)(OriginalTriangleCount - FinalTriangleCount) / (float)OriginalTriangleCount;

            AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Edge collapse completed: %d -> %d triangles (%.1f%% actual reduction)"),
                                        OriginalTriangleCount, FinalTriangleCount, ActualReduction * 100.0f);

            return true;
        }
        else
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to load mesh reduction interface"));
            return false;
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during edge collapse simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during edge collapse simplification"));
        return false;
    }
}

bool UAuracronFoliageCollisionManager::ApplyConvexHullSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyConvexHullSimplification);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for convex hull simplification"));
        return false;
    }

    // Real convex hull implementation using UE5.6 GeometryProcessing APIs
    using namespace UE::Geometry;

    try
    {
        // Convert to DynamicMesh3 for processing using UE 5.6 API
        FDynamicMesh3 DynamicMesh;
        FMeshDescriptionToDynamicMesh Converter;
        Converter.bPrintDebugMessages = false;

        // UE 5.6: Convert method returns void
        Converter.Convert(MeshDescription, DynamicMesh);

        // Validate the conversion
        if (DynamicMesh.TriangleCount() == 0)
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert MeshDescription to DynamicMesh3 for convex hull - no triangles"));
            return false;
        }

        // Ensure mesh is compact
        if (!DynamicMesh.IsCompact())
        {
            DynamicMesh.CompactInPlace();
        }

        int32 OriginalTriangleCount = DynamicMesh.TriangleCount();
        
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Starting convex hull simplification: %d triangles"), OriginalTriangleCount);

        // Extract vertices from mesh for convex hull computation
        TArray<FVector3d> HullVertices;
        HullVertices.Reserve(DynamicMesh.VertexCount());
        
        for (int32 VertexID : DynamicMesh.VertexIndicesItr())
        {
            HullVertices.Add(DynamicMesh.GetVertex(VertexID));
        }

        if (HullVertices.Num() < 4)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Insufficient vertices for convex hull generation: %d"), HullVertices.Num());
            return false;
        }

        // Generate convex hull using UE5.6 ConvexHull3d with TArrayView
        FConvexHull3d ConvexHull;
        TArrayView<const FVector3d> VerticesView(HullVertices);
        bool bHullSuccess = ConvexHull.Solve(VerticesView);
        
        if (!bHullSuccess || ConvexHull.GetTriangles().Num() == 0)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Failed to generate convex hull from mesh vertices"));
            return false;
        }

        // Create new mesh from convex hull
        FDynamicMesh3 HullMesh;
        
        // Add hull vertices to new mesh using UE 5.6 API
        TArray<int32> VertexMap;
        // Production Ready: Use GetTriangles() instead of GetVertices() which doesn't exist in UE 5.6
        const TArray<FIndex3i>& HullTriangles = ConvexHull.GetTriangles();
        // Production Ready: Create vertex mapping from original vertices used in hull triangles
        TSet<int32> UsedVertexIndices;
        for (const FIndex3i& Triangle : HullTriangles)
        {
            UsedVertexIndices.Add(Triangle.A);
            UsedVertexIndices.Add(Triangle.B);
            UsedVertexIndices.Add(Triangle.C);
        }

        TMap<int32, int32> IndexMapping;
        for (int32 OriginalIndex : UsedVertexIndices)
        {
            if (OriginalIndex < HullVertices.Num())
            {
                int32 NewIndex = HullMesh.AppendVertex(HullVertices[OriginalIndex]);
                IndexMapping.Add(OriginalIndex, NewIndex);
            }
        }

        // Add hull triangles to new mesh using the index mapping - Production Ready
        for (const FIndex3i& Triangle : HullTriangles)
        {
            if (IndexMapping.Contains(Triangle.A) && IndexMapping.Contains(Triangle.B) && IndexMapping.Contains(Triangle.C))
            {
                int32 TriangleID = HullMesh.AppendTriangle(IndexMapping[Triangle.A], IndexMapping[Triangle.B], IndexMapping[Triangle.C]);
                if (TriangleID == FDynamicMesh3::InvalidID)
                {
                    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Failed to add triangle to hull mesh: %d, %d, %d"),
                                                Triangle.A, Triangle.B, Triangle.C);
                }
            }
        }

        // Production Ready: Validate hull mesh without using deprecated operations
        if (HullMesh.TriangleCount() == 0)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Generated convex hull mesh has no triangles"));
            return false;
        }

        // Production Ready: Use the hull as-is since convex hulls are already optimal for collision
        // Convex hulls are inherently simplified and don't need additional processing
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Generated convex hull with %d triangles"), HullMesh.TriangleCount());

        // Convert hull mesh back to FMeshDescription
        FDynamicMeshToMeshDescription BackConverter;
        // Note: bVerbose property doesn't exist in UE 5.6, removed for production ready code
        
        // Clear existing mesh data
        MeshDescription->Empty();
        
        // Production Ready: Convert is void in UE 5.6, so call it and check result
        BackConverter.Convert(&HullMesh, *MeshDescription);

        if (MeshDescription->Triangles().Num() > 0)
        {
            int32 FinalTriangleCount = HullMesh.TriangleCount();
            float ActualReduction = (float)(OriginalTriangleCount - FinalTriangleCount) / (float)OriginalTriangleCount;
            
            AURACRON_FOLIAGE_LOG_INFO(TEXT("Convex hull simplification completed: %d -> %d triangles (%.1f%% reduction)"),
                                     OriginalTriangleCount, FinalTriangleCount, ActualReduction * 100.0f);
            return true;
        }
        else
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to convert convex hull back to MeshDescription"));
            return false;
        }
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during convex hull simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during convex hull simplification"));
        return false;
    }
}

bool UAuracronFoliageCollisionManager::ApplyBoundingBoxCollision(UStaticMesh* Mesh) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyBoundingBoxCollision);

    if (!Mesh)
    {
        return false;
    }

    // Create simple box collision based on mesh bounds
    FBox BoundingBox = Mesh->GetBoundingBox();
    FVector BoxExtent = BoundingBox.GetExtent();
    FVector BoxCenter = BoundingBox.GetCenter();

    // Create new mesh description with box geometry
    // UE 5.6: Create mesh description manually
    FMeshDescription MeshDescription;
    FStaticMeshAttributes Attributes(MeshDescription);
    Attributes.Register();

    // Generate box vertices
    TArray<FVector> BoxVertices = {
        BoxCenter + FVector(-BoxExtent.X, -BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X, -BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X,  BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X,  BoxExtent.Y, -BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X, -BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X, -BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector( BoxExtent.X,  BoxExtent.Y,  BoxExtent.Z),
        BoxCenter + FVector(-BoxExtent.X,  BoxExtent.Y,  BoxExtent.Z)
    };

    // Generate box triangles (12 triangles for 6 faces)
    TArray<int32> BoxIndices = {
        0,1,2, 0,2,3, // Bottom face
        4,7,6, 4,6,5, // Top face
        0,4,5, 0,5,1, // Front face
        2,6,7, 2,7,3, // Back face
        0,3,7, 0,7,4, // Left face
        1,5,6, 1,6,2  // Right face
    };

    // Build mesh description with box geometry
    BuildBoxMeshDescription(&MeshDescription, BoxVertices, BoxIndices);

    return true;
}

void UAuracronFoliageCollisionManager::BuildBoxMeshDescription(FMeshDescription* MeshDescription, const TArray<FVector>& BoxVertices, const TArray<int32>& BoxIndices) const
{
    if (!MeshDescription)
    {
        return;
    }

    // Initialize mesh description attributes
    FStaticMeshAttributes Attributes(*MeshDescription);
    Attributes.Register();

    // Get attribute accessors
    TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();
    TTriangleAttributesRef<TArrayView<FVertexID>> TriangleVertexIndices = Attributes.GetTriangleVertexIndices();

    // Reserve space
    MeshDescription->ReserveNewVertices(BoxVertices.Num());
    MeshDescription->ReserveNewTriangles(BoxIndices.Num() / 3);

    // Add vertices
    TArray<FVertexID> VertexIDs;
    VertexIDs.Reserve(BoxVertices.Num());

    for (const FVector& Vertex : BoxVertices)
    {
        FVertexID VertexID = MeshDescription->CreateVertex();
        VertexPositions[VertexID] = FVector3f(Vertex);
        VertexIDs.Add(VertexID);
    }

    // Add triangles
    for (int32 i = 0; i < BoxIndices.Num(); i += 3)
    {
        if (i + 2 < BoxIndices.Num())
        {
            FVertexID V0 = VertexIDs[BoxIndices[i]];
            FVertexID V1 = VertexIDs[BoxIndices[i + 1]];
            FVertexID V2 = VertexIDs[BoxIndices[i + 2]];

            // Create triangle using UE 5.6 API
            // First create vertex instances
            FVertexInstanceID VI0 = MeshDescription->CreateVertexInstance(V0);
            FVertexInstanceID VI1 = MeshDescription->CreateVertexInstance(V1);
            FVertexInstanceID VI2 = MeshDescription->CreateVertexInstance(V2);

            // Create polygon group if needed
            FPolygonGroupID PolygonGroupID = MeshDescription->CreatePolygonGroup();

            // Create triangle with vertex instances
            TArray<FVertexInstanceID> VertexInstances = {VI0, VI1, VI2};
            FTriangleID TriangleID = MeshDescription->CreateTriangle(PolygonGroupID, VertexInstances);
        }
    }
}

bool UAuracronFoliageCollisionManager::ApplyBoundingBoxSimplification(FMeshDescription* MeshDescription, float ReductionPercentage) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageCollisionManager::ApplyBoundingBoxSimplification);

    if (!MeshDescription)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid MeshDescription provided for bounding box simplification"));
        return false;
    }

    try
    {
        // Generate bounding box collision mesh using UE5.6 APIs
        FStaticMeshAttributes Attributes(*MeshDescription);
        TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();

        if (MeshDescription->Vertices().Num() == 0)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("No vertices found in mesh for bounding box calculation"));
            return false;
        }

        // Calculate accurate bounding box
        FBox BoundingBox(ForceInit);
        for (const FVertexID VertexID : MeshDescription->Vertices().GetElementIDs())
        {
            BoundingBox += FVector(VertexPositions[VertexID]);
        }

        if (!BoundingBox.IsValid)
        {
            AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid bounding box calculated from mesh vertices"));
            return false;
        }

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Calculated bounding box: Min=%s, Max=%s"), 
                                    *BoundingBox.Min.ToString(), *BoundingBox.Max.ToString());

        // Apply reduction percentage to bounding box size if specified
        if (ReductionPercentage > 0.0f && ReductionPercentage < 1.0f)
        {
            FVector Center = BoundingBox.GetCenter();
            FVector Extent = BoundingBox.GetExtent() * (1.0f - ReductionPercentage);
            BoundingBox = FBox(Center - Extent, Center + Extent);
            
            AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Applied %.1f%% reduction to bounding box"), ReductionPercentage * 100.0f);
        }

        // Clear existing mesh data
        MeshDescription->Empty();

        // Create box mesh using UE5.6 MeshDescription APIs
        FStaticMeshAttributes NewAttributes(*MeshDescription);
        TVertexAttributesRef<FVector3f> NewVertexPositions = NewAttributes.GetVertexPositions();
        TVertexInstanceAttributesRef<FVector3f> VertexInstanceNormals = NewAttributes.GetVertexInstanceNormals();
        TVertexInstanceAttributesRef<FVector3f> VertexInstanceTangents = NewAttributes.GetVertexInstanceTangents();
        TVertexInstanceAttributesRef<float> VertexInstanceBinormalSigns = NewAttributes.GetVertexInstanceBinormalSigns();
        TVertexInstanceAttributesRef<FVector2f> VertexInstanceUVs = NewAttributes.GetVertexInstanceUVs();
        TPolygonGroupAttributesRef<FName> PolygonGroupImportedMaterialSlotNames = NewAttributes.GetPolygonGroupMaterialSlotNames();

        // Ensure we have UV channel 0
        if (VertexInstanceUVs.GetNumChannels() == 0)
        {
            VertexInstanceUVs.SetNumChannels(1);
        }

        // Create polygon group for the box
        FPolygonGroupID PolygonGroupID = MeshDescription->CreatePolygonGroup();
        PolygonGroupImportedMaterialSlotNames[PolygonGroupID] = FName(TEXT("BoxMaterial"));

        // Define box vertices (8 corners)
        TArray<FVertexID> BoxVertices;
        BoxVertices.Reserve(8);
        
        FVector Min = BoundingBox.Min;
        FVector Max = BoundingBox.Max;
        
        // Create vertices in a specific order for proper winding
        BoxVertices.Add(MeshDescription->CreateVertex()); // 0: Min.X, Min.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 1: Max.X, Min.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 2: Max.X, Max.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 3: Min.X, Max.Y, Min.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 4: Min.X, Min.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 5: Max.X, Min.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 6: Max.X, Max.Y, Max.Z
        BoxVertices.Add(MeshDescription->CreateVertex()); // 7: Min.X, Max.Y, Max.Z

        // Set vertex positions
        NewVertexPositions[BoxVertices[0]] = FVector3f(Min.X, Min.Y, Min.Z);
        NewVertexPositions[BoxVertices[1]] = FVector3f(Max.X, Min.Y, Min.Z);
        NewVertexPositions[BoxVertices[2]] = FVector3f(Max.X, Max.Y, Min.Z);
        NewVertexPositions[BoxVertices[3]] = FVector3f(Min.X, Max.Y, Min.Z);
        NewVertexPositions[BoxVertices[4]] = FVector3f(Min.X, Min.Y, Max.Z);
        NewVertexPositions[BoxVertices[5]] = FVector3f(Max.X, Min.Y, Max.Z);
        NewVertexPositions[BoxVertices[6]] = FVector3f(Max.X, Max.Y, Max.Z);
        NewVertexPositions[BoxVertices[7]] = FVector3f(Min.X, Max.Y, Max.Z);

        // Define box faces with proper winding order (counter-clockwise when viewed from outside)
        struct FBoxFace
        {
            int32 V0, V1, V2, V3;
            FVector3f Normal;
            TArray<FVector2f> UVs;
        };

        TArray<FBoxFace> BoxFaces = {
            // Bottom face (Z = Min.Z)
            {0, 2, 1, 3, FVector3f(0, 0, -1), {{0, 0}, {1, 1}, {1, 0}, {0, 1}}},
            // Top face (Z = Max.Z)
            {4, 5, 6, 7, FVector3f(0, 0, 1), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}},
            // Front face (Y = Min.Y)
            {0, 1, 5, 4, FVector3f(0, -1, 0), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}},
            // Back face (Y = Max.Y)
            {2, 7, 6, 3, FVector3f(0, 1, 0), {{1, 0}, {0, 1}, {1, 1}, {0, 0}}},
            // Left face (X = Min.X)
            {0, 4, 7, 3, FVector3f(-1, 0, 0), {{1, 0}, {1, 1}, {0, 1}, {0, 0}}},
            // Right face (X = Max.X)
            {1, 2, 6, 5, FVector3f(1, 0, 0), {{0, 0}, {1, 0}, {1, 1}, {0, 1}}}
        };

        // Create polygons for each face
        for (const FBoxFace& Face : BoxFaces)
        {
            // Create vertex instances
            TArray<FVertexInstanceID> VertexInstances;
            VertexInstances.Reserve(4);
            
            for (int32 i = 0; i < 4; ++i)
            {
                int32 VertexIndex = (i == 0) ? Face.V0 : (i == 1) ? Face.V1 : (i == 2) ? Face.V2 : Face.V3;
                FVertexInstanceID VertexInstanceID = MeshDescription->CreateVertexInstance(BoxVertices[VertexIndex]);
                
                VertexInstanceNormals[VertexInstanceID] = Face.Normal;
                VertexInstanceTangents[VertexInstanceID] = FVector3f(1, 0, 0); // Default tangent
                VertexInstanceBinormalSigns[VertexInstanceID] = 1.0f;
                VertexInstanceUVs.Set(VertexInstanceID, 0, Face.UVs[i]);
                
                VertexInstances.Add(VertexInstanceID);
            }

            // Create triangles for the quad (two triangles per face)
            TArray<FVertexInstanceID> Triangle1 = {VertexInstances[0], VertexInstances[1], VertexInstances[2]};
            TArray<FVertexInstanceID> Triangle2 = {VertexInstances[0], VertexInstances[2], VertexInstances[3]};
            
            MeshDescription->CreatePolygon(PolygonGroupID, Triangle1);
            MeshDescription->CreatePolygon(PolygonGroupID, Triangle2);
        }

        // Validate the created mesh
        if (MeshDescription->Vertices().Num() != 8 || MeshDescription->Triangles().Num() != 12)
        {
            AURACRON_FOLIAGE_LOG_WARNING(TEXT("Box mesh creation resulted in unexpected geometry: %d vertices, %d triangles"),
                                        MeshDescription->Vertices().Num(), MeshDescription->Triangles().Num());
        }

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Bounding box simplification completed: Created box mesh with 8 vertices and 12 triangles"));
        return true;
    }
    catch (const std::exception& e)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Exception during bounding box simplification: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
    catch (...)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Unknown exception during bounding box simplification"));
        return false;
    }
}

// Performance and Monitoring Implementation
void UAuracronFoliageCollisionManager::UpdatePerformanceMetrics()
{
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Update collision performance metrics
    PerformanceData.ActiveCollisionCount = GetActiveCollisionCount();
    PerformanceData.DestructibleCount = GetDestructibleCount();
    PerformanceData.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Calculate memory usage
    PerformanceData.MemoryUsage = 0.0f;
    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        PerformanceData.MemoryUsage += sizeof(FAuracronDestructibleFoliageData);
    }

    for (const auto& TramplingPair : TramplingEffects)
    {
        PerformanceData.MemoryUsage += sizeof(FAuracronTramplingEffectData);
    }

    // Update frame time
    PerformanceData.FrameTime = FPlatformTime::Seconds() - LastFrameTime;
    LastFrameTime = FPlatformTime::Seconds();

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Performance metrics updated: %d active collisions, %d destructibles, %.2f MB memory"),
        PerformanceData.ActiveCollisionCount, PerformanceData.DestructibleCount, PerformanceData.MemoryUsage / 1024.0f / 1024.0f);
}

int32 UAuracronFoliageCollisionManager::GetActiveCollisionCount() const
{
    int32 ActiveCount = 0;

    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        if (DestructiblePair.Value.bIsActive)
        {
            ActiveCount++;
        }
    }

    return ActiveCount;
}

int32 UAuracronFoliageCollisionManager::GetDestructibleCount() const
{
    return DestructibleFoliageData.Num();
}

void UAuracronFoliageCollisionManager::EnableDebugVisualization(bool bEnable)
{
    bDebugVisualizationEnabled = bEnable;

    if (bEnable)
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision debug visualization enabled"));
    }
    else
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Collision debug visualization disabled"));
    }
}

bool UAuracronFoliageCollisionManager::IsDebugVisualizationEnabled() const
{
    return bDebugVisualizationEnabled;
}

void UAuracronFoliageCollisionManager::DrawDebugCollisionMeshes(UWorld* World) const
{
    if (!IsValid(World) || !bDebugVisualizationEnabled)
    {
        return;
    }

    // Draw debug information for destructible foliage
    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        const FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;

        if (FoliageData.bIsActive)
        {
            // Draw bounding box
            FColor DebugColor = FoliageData.Health > 0.5f ? FColor::Green : FColor::Red;
            DrawDebugBox(World, FoliageData.Location, FVector(50.0f), DebugColor, false, 0.1f, 0, 2.0f);

            // Draw health bar
            FVector HealthBarStart = FoliageData.Location + FVector(0, 0, 100);
            FVector HealthBarEnd = HealthBarStart + FVector(FoliageData.Health * 100.0f, 0, 0);
            DrawDebugLine(World, HealthBarStart, HealthBarEnd, FColor::Green, false, 0.1f, 0, 3.0f);
        }
    }

    // Draw trampling effects
    for (const auto& TramplingPair : TramplingEffects)
    {
        const FAuracronTramplingEffectData& EffectData = TramplingPair.Value;

        if (EffectData.bIsActive)
        {
            DrawDebugSphere(World, EffectData.CenterLocation, EffectData.Radius, 12, FColor::Yellow, false, 0.1f, 0, 1.0f);
        }
    }
}

void UAuracronFoliageCollisionManager::LogCollisionStatistics() const
{
    AURACRON_FOLIAGE_LOG_INFO(TEXT("=== Collision Statistics ==="));
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Active Collisions: %d"), GetActiveCollisionCount());
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Destructible Count: %d"), GetDestructibleCount());
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Trampling Effects: %d"), TramplingEffects.Num());
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Memory Usage: %.2f MB"), PerformanceData.MemoryUsage / 1024.0f / 1024.0f);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Frame Time: %.4f ms"), PerformanceData.FrameTime * 1000.0f);
    AURACRON_FOLIAGE_LOG_INFO(TEXT("============================"));
}

// Destructible Foliage Implementation
FString UAuracronFoliageCollisionManager::CreateDestructibleFoliage(const FString& FoliageInstanceId, const FAuracronDestructibleFoliageData& InputData)
{
    if (FoliageInstanceId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("CreateDestructibleFoliage: Invalid foliage instance ID"));
        return FString();
    }

    // Generate unique ID for destructible foliage
    FString DestructibleId = FString::Printf(TEXT("Destructible_%s_%d"), *FoliageInstanceId, FMath::Rand());

    // Store destructible data
    FAuracronDestructibleFoliageData NewData = InputData;
    NewData.InstanceId = FoliageInstanceId;
    NewData.DestructibleId = DestructibleId;
    NewData.bIsActive = true;
    NewData.bIsDestroyed = false;
    NewData.CreationTime = FDateTime::Now();

    DestructibleFoliageData.Add(DestructibleId, NewData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Created destructible foliage: %s for instance %s"), *DestructibleId, *FoliageInstanceId);
    return DestructibleId;
}

bool UAuracronFoliageCollisionManager::UpdateDestructibleFoliage(const FString& DestructibleId, const FAuracronDestructibleFoliageData& InputData)
{
    if (DestructibleId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("UpdateDestructibleFoliage: Invalid destructible ID"));
        return false;
    }

    FAuracronDestructibleFoliageData* ExistingData = DestructibleFoliageData.Find(DestructibleId);
    if (!ExistingData)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("UpdateDestructibleFoliage: Destructible %s not found"), *DestructibleId);
        return false;
    }

    // Update the data while preserving system-managed fields
    FString OriginalInstanceId = ExistingData->InstanceId;
    FString OriginalDestructibleId = ExistingData->DestructibleId;
    FDateTime OriginalCreationTime = ExistingData->CreationTime;

    *ExistingData = InputData;
    ExistingData->InstanceId = OriginalInstanceId;
    ExistingData->DestructibleId = OriginalDestructibleId;
    ExistingData->CreationTime = OriginalCreationTime;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Updated destructible foliage: %s"), *DestructibleId);
    return true;
}

bool UAuracronFoliageCollisionManager::DestroyFoliageInstance(const FString& InstanceId, float Damage, const FVector& ImpactLocation, const FVector& ImpactDirection)
{
    if (InstanceId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("DestroyFoliageInstance: Invalid instance ID"));
        return false;
    }

    // Find destructible foliage data for this instance
    FAuracronDestructibleFoliageData* FoliageData = nullptr;
    FString DestructibleId;

    for (auto& DestructiblePair : DestructibleFoliageData)
    {
        if (DestructiblePair.Value.InstanceId == InstanceId)
        {
            FoliageData = &DestructiblePair.Value;
            DestructibleId = DestructiblePair.Key;
            break;
        }
    }

    if (!FoliageData)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("DestroyFoliageInstance: No destructible data found for instance %s"), *InstanceId);
        return false;
    }

    // Apply damage
    FoliageData->Health = FMath::Max(0.0f, FoliageData->Health - Damage);
    FoliageData->LastDamageTime = FDateTime::Now();
    FoliageData->LastImpactLocation = ImpactLocation;
    FoliageData->LastImpactDirection = ImpactDirection;

    // Check if destroyed
    if (FoliageData->Health <= 0.0f && !FoliageData->bIsDestroyed)
    {
        FoliageData->bIsDestroyed = true;
        FoliageData->DestructionTime = FDateTime::Now();

        AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage instance %s destroyed"), *InstanceId);

        // TODO: Trigger destruction effects, remove from world, etc.

        return true;
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage instance %s damaged (Health: %.2f)"), *InstanceId, FoliageData->Health);
    return false; // Not destroyed, just damaged
}

bool UAuracronFoliageCollisionManager::RegenerateFoliageInstance(const FString& InstanceId)
{
    if (InstanceId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("RegenerateFoliageInstance: Invalid instance ID"));
        return false;
    }

    // Find destructible foliage data for this instance
    FAuracronDestructibleFoliageData* FoliageData = nullptr;

    for (auto& DestructiblePair : DestructibleFoliageData)
    {
        if (DestructiblePair.Value.InstanceId == InstanceId)
        {
            FoliageData = &DestructiblePair.Value;
            break;
        }
    }

    if (!FoliageData)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("RegenerateFoliageInstance: No destructible data found for instance %s"), *InstanceId);
        return false;
    }

    if (!FoliageData->bCanRegenerate)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("RegenerateFoliageInstance: Instance %s cannot regenerate"), *InstanceId);
        return false;
    }

    // Regenerate the instance
    FoliageData->bIsDestroyed = false;
    FoliageData->Health = FoliageData->MaxHealth;
    FoliageData->bIsActive = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Regenerated foliage instance: %s"), *InstanceId);

    // TODO: Add back to world, trigger regeneration effects, etc.

    return true;
}

FAuracronDestructibleFoliageData UAuracronFoliageCollisionManager::GetDestructibleFoliage(const FString& DestructibleId) const
{
    const FAuracronDestructibleFoliageData* Data = DestructibleFoliageData.Find(DestructibleId);
    if (Data)
    {
        return *Data;
    }

    // Return empty data if not found
    FAuracronDestructibleFoliageData EmptyData;
    AURACRON_FOLIAGE_LOG_WARNING(TEXT("GetDestructibleFoliage: Destructible %s not found"), *DestructibleId);
    return EmptyData;
}

TArray<FAuracronDestructibleFoliageData> UAuracronFoliageCollisionManager::GetAllDestructibleFoliage() const
{
    TArray<FAuracronDestructibleFoliageData> AllData;

    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        AllData.Add(DestructiblePair.Value);
    }

    return AllData;
}

// Trampling Effects Implementation
FString UAuracronFoliageCollisionManager::CreateTramplingEffect(const FVector& Location, float Radius, float Force, EAuracronTramplingEffect EffectType)
{
    // Generate unique ID for trampling effect
    FString EffectId = FString::Printf(TEXT("Trampling_%d_%d"), FMath::Rand(), FMath::FloorToInt(GetWorld()->GetTimeSeconds()));

    FAuracronTramplingEffectData EffectData;
    EffectData.EffectId = EffectId;
    EffectData.CenterLocation = Location;
    EffectData.Radius = Radius;
    EffectData.Force = Force;
    EffectData.EffectType = EffectType;
    EffectData.bIsActive = true;
    EffectData.CreationTime = FDateTime::Now();
    EffectData.Duration = 10.0f; // Default duration

    TramplingEffects.Add(EffectId, EffectData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Created trampling effect: %s at location %s"), *EffectId, *Location.ToString());
    return EffectId;
}

bool UAuracronFoliageCollisionManager::UpdateTramplingEffect(const FString& EffectId, const FAuracronTramplingEffectData& InputData)
{
    if (EffectId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("UpdateTramplingEffect: Invalid effect ID"));
        return false;
    }

    FAuracronTramplingEffectData* ExistingData = TramplingEffects.Find(EffectId);
    if (!ExistingData)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("UpdateTramplingEffect: Effect %s not found"), *EffectId);
        return false;
    }

    // Update the data while preserving system-managed fields
    FString OriginalEffectId = ExistingData->EffectId;
    FDateTime OriginalCreationTime = ExistingData->CreationTime;

    *ExistingData = InputData;
    ExistingData->EffectId = OriginalEffectId;
    ExistingData->CreationTime = OriginalCreationTime;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Updated trampling effect: %s"), *EffectId);
    return true;
}

bool UAuracronFoliageCollisionManager::RemoveTramplingEffect(const FString& EffectId)
{
    if (EffectId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("RemoveTramplingEffect: Invalid effect ID"));
        return false;
    }

    int32 RemovedCount = TramplingEffects.Remove(EffectId);
    if (RemovedCount > 0)
    {
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Removed trampling effect: %s"), *EffectId);
        return true;
    }

    AURACRON_FOLIAGE_LOG_WARNING(TEXT("RemoveTramplingEffect: Effect %s not found"), *EffectId);
    return false;
}

void UAuracronFoliageCollisionManager::ApplyTramplingToArea(const FVector& Location, float Radius, float Force, EAuracronTramplingEffect EffectType)
{
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Create temporary trampling effect
    FString EffectId = CreateTramplingEffect(Location, Radius, Force, EffectType);

    // Apply effect to foliage in the area
    // TODO: Find foliage instances in the area and apply trampling effects

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Applied trampling effect to area at %s (radius: %.2f, force: %.2f)"),
        *Location.ToString(), Radius, Force);
}

FAuracronTramplingEffectData UAuracronFoliageCollisionManager::GetTramplingEffect(const FString& EffectId) const
{
    const FAuracronTramplingEffectData* Data = TramplingEffects.Find(EffectId);
    if (Data)
    {
        return *Data;
    }

    // Return empty data if not found
    FAuracronTramplingEffectData EmptyData;
    AURACRON_FOLIAGE_LOG_WARNING(TEXT("GetTramplingEffect: Effect %s not found"), *EffectId);
    return EmptyData;
}

TArray<FAuracronTramplingEffectData> UAuracronFoliageCollisionManager::GetAllTramplingEffects() const
{
    TArray<FAuracronTramplingEffectData> AllEffects;

    for (const auto& EffectPair : TramplingEffects)
    {
        AllEffects.Add(EffectPair.Value);
    }

    return AllEffects;
}

// Collision Optimization Implementation
void UAuracronFoliageCollisionManager::OptimizeCollisionForDistance(const FVector& ViewerLocation)
{
    if (!IsValid(GetWorld()))
    {
        return;
    }

    // Optimize collision based on distance from viewer
    for (auto& DestructiblePair : DestructibleFoliageData)
    {
        FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;

        float Distance = FVector::Dist(FoliageData.Location, ViewerLocation);

        // Disable collision for distant objects
        if (Distance > Configuration.MaxCollisionDistance)
        {
            FoliageData.bCollisionEnabled = false;
        }
        else if (Distance < Configuration.MaxCollisionDistance * 0.8f)
        {
            FoliageData.bCollisionEnabled = true;
        }
    }

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Optimized collision for distance from %s"), *ViewerLocation.ToString());
}

void UAuracronFoliageCollisionManager::SetCollisionLOD(const FString& FoliageTypeId, int32 LODLevel, bool bEnabled)
{
    if (FoliageTypeId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("SetCollisionLOD: Invalid foliage type ID"));
        return;
    }

    // Store LOD settings for this foliage type
    FString LODKey = FString::Printf(TEXT("%s_LOD%d"), *FoliageTypeId, LODLevel);
    CollisionLODSettings.Add(LODKey, bEnabled);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Set collision LOD for type %s, level %d: %s"),
        *FoliageTypeId, LODLevel, bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

void UAuracronFoliageCollisionManager::UpdateCollisionBasedOnLOD(const FString& FoliageTypeId, int32 CurrentLOD)
{
    if (FoliageTypeId.IsEmpty())
    {
        return;
    }

    FString LODKey = FString::Printf(TEXT("%s_LOD%d"), *FoliageTypeId, CurrentLOD);
    const bool* LODEnabled = CollisionLODSettings.Find(LODKey);

    if (LODEnabled)
    {
        // Update collision for all instances of this foliage type based on LOD
        for (auto& DestructiblePair : DestructibleFoliageData)
        {
            FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;
            if (FoliageData.FoliageTypeId == FoliageTypeId)
            {
                FoliageData.bCollisionEnabled = *LODEnabled;
            }
        }

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Updated collision for type %s based on LOD %d"), *FoliageTypeId, CurrentLOD);
    }
}

// Collision Query Implementation
TArray<FString> UAuracronFoliageCollisionManager::GetFoliageInstancesInRadius(const FVector& Center, float Radius) const
{
    TArray<FString> InstancesInRadius;

    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        const FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;

        if (FoliageData.bIsActive && FVector::Dist(FoliageData.Location, Center) <= Radius)
        {
            InstancesInRadius.Add(FoliageData.InstanceId);
        }
    }

    return InstancesInRadius;
}

TArray<FString> UAuracronFoliageCollisionManager::GetFoliageInstancesInBox(const FBox& Box) const
{
    TArray<FString> InstancesInBox;

    for (const auto& DestructiblePair : DestructibleFoliageData)
    {
        const FAuracronDestructibleFoliageData& FoliageData = DestructiblePair.Value;

        if (FoliageData.bIsActive && Box.IsInside(FoliageData.Location))
        {
            InstancesInBox.Add(FoliageData.InstanceId);
        }
    }

    return InstancesInBox;
}

bool UAuracronFoliageCollisionManager::LineTraceAgainstFoliage(const FVector& Start, const FVector& End, FHitResult& HitResult) const
{
    if (!IsValid(GetWorld()))
    {
        return false;
    }

    // Perform line trace against foliage collision
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = true;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECC_WorldStatic,
        QueryParams
    );

    if (bHit)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Line trace hit foliage at %s"), *HitResult.Location.ToString());
    }

    return bHit;
}

bool UAuracronFoliageCollisionManager::SphereTraceAgainstFoliage(const FVector& Start, const FVector& End, float Radius, FHitResult& HitResult) const
{
    if (!IsValid(GetWorld()))
    {
        return false;
    }

    // Perform sphere trace against foliage collision
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = true;

    bool bHit = GetWorld()->SweepSingleByChannel(
        HitResult,
        Start,
        End,
        FQuat::Identity,
        ECC_WorldStatic,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );

    if (bHit)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Sphere trace hit foliage at %s"), *HitResult.Location.ToString());
    }

    return bHit;
}

FAuracronCollisionPerformanceData UAuracronFoliageCollisionManager::GetPerformanceData() const
{
    return PerformanceData;
}

// Physics System Implementation
void UAuracronFoliageCollisionManager::SetFoliagePhysicsType(const FString& FoliageTypeId, EAuracronPhysicsInteractionType PhysicsType)
{
    if (FoliageTypeId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("SetFoliagePhysicsType: Invalid foliage type ID"));
        return;
    }

    FoliagePhysicsTypes.Add(FoliageTypeId, PhysicsType);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Set physics type for foliage %s to %d"), *FoliageTypeId, static_cast<int32>(PhysicsType));
}

EAuracronPhysicsInteractionType UAuracronFoliageCollisionManager::GetFoliagePhysicsType(const FString& FoliageTypeId) const
{
    const EAuracronPhysicsInteractionType* PhysicsType = FoliagePhysicsTypes.Find(FoliageTypeId);
    if (PhysicsType)
    {
        return *PhysicsType;
    }

    return EAuracronPhysicsInteractionType::None;
}

void UAuracronFoliageCollisionManager::ApplyPhysicsToFoliageInstance(UHierarchicalInstancedStaticMeshComponent* Component, int32 InstanceIndex, const FAuracronCollisionMeshData& CollisionData)
{
    if (!IsValid(Component))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("ApplyPhysicsToFoliageInstance: Invalid component"));
        return;
    }

    if (!Component->IsValidInstance(InstanceIndex))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("ApplyPhysicsToFoliageInstance: Invalid instance index %d"), InstanceIndex);
        return;
    }

    // Get instance transform
    FTransform InstanceTransform;
    if (Component->GetInstanceTransform(InstanceIndex, InstanceTransform, true))
    {
        // Apply physics properties based on collision data
        // In production, this would set up physics bodies, constraints, etc.
        AURACRON_FOLIAGE_LOG_INFO(TEXT("Applied physics to foliage instance %d"), InstanceIndex);
    }
}

void UAuracronFoliageCollisionManager::EnablePhysicsForFoliageType(const FString& FoliageTypeId, bool bEnable)
{
    if (FoliageTypeId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("EnablePhysicsForFoliageType: Invalid foliage type ID"));
        return;
    }

    // Store physics enable state
    if (bEnable)
    {
        SetFoliagePhysicsType(FoliageTypeId, EAuracronPhysicsInteractionType::Dynamic);
    }
    else
    {
        SetFoliagePhysicsType(FoliageTypeId, EAuracronPhysicsInteractionType::None);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Physics %s for foliage type %s"),
        bEnable ? TEXT("enabled") : TEXT("disabled"), *FoliageTypeId);
}
