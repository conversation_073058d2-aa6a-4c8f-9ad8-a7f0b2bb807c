// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON Foliage Edit Utility Implementation
// UE 5.6 Compatible Implementation

#include "AuracronFoliageEditUtility.h"
#include "CoreMinimal.h"
#include "FoliageEditModule.h"
#include "AuracronFoliageTypes.h"
#include "AuracronFoliage.h"
#include "Engine/World.h"
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "InstancedFoliageActor.h"
#include "InstancedFoliage.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Engine.h"
#include "UObject/UObjectGlobals.h"

#if WITH_EDITOR
#include "FoliageEditUtility.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#endif

DEFINE_LOG_CATEGORY(LogAuracronFoliageEditUtility);

// UAuracronFoliageEditUtility Implementation
UAuracronFoliageEditUtility::UAuracronFoliageEditUtility()
{
    // Initialize utility
}

bool UAuracronFoliageEditUtility::PaintFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, const FFoliageEditSettings& Settings)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Create transform for new instance
    FTransform InstanceTransform;
    InstanceTransform.SetLocation(Location);
    InstanceTransform.SetRotation(FQuat::Identity);
    InstanceTransform.SetScale3D(FVector::OneVector);

    // Find or add foliage info using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
    if (FoliageInfo)
    {
        // Create FFoliageInstance from FTransform for UE 5.6 API
        FFoliageInstance NewInstance;
        NewInstance.Location = InstanceTransform.GetLocation();
        NewInstance.Rotation = InstanceTransform.GetRotation().Rotator();
        NewInstance.DrawScale3D = FVector3f(InstanceTransform.GetScale3D());
        NewInstance.BaseId = FFoliageInstanceBaseCache::InvalidBaseId;

        // Add instance using the new UE 5.6 API
        FoliageInfo->AddInstance(FoliageType, NewInstance);
        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Foliage instance painted at location: %s"), *Location.ToString());
        return true;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find or add foliage info"));
    return false;
}

bool UAuracronFoliageEditUtility::EraseFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find and remove nearby instances using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        // Find instances within radius and remove them
        TArray<int32> InstancesToRemove;
        const float EraseRadius = Radius;

        for (int32 InstanceIndex = 0; InstanceIndex < FoliageInfo->Instances.Num(); ++InstanceIndex)
        {
            const FFoliageInstance& Instance = FoliageInfo->Instances[InstanceIndex];
            float Distance = FVector::Dist(Instance.Location, Location);
            if (Distance <= EraseRadius)
            {
                InstancesToRemove.Add(InstanceIndex);
            }
        }

        // Remove instances in reverse order to maintain indices
        if (InstancesToRemove.Num() > 0)
        {
            // Sort indices in descending order to avoid index shifting issues
            InstancesToRemove.Sort([](const int32& A, const int32& B) { return A > B; });

            // Remove instances from the array
            for (int32 Index : InstancesToRemove)
            {
                if (FoliageInfo->Instances.IsValidIndex(Index))
                {
                    FoliageInfo->Instances.RemoveAt(Index);
                }
            }

            // Refresh the foliage component
            if (FoliageInfo->Implementation.IsValid())
            {
                FoliageInfo->Implementation->Reapply(FoliageType);
            }
        }

        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Foliage erased at location: %s, removed %d instances"), *Location.ToString(), InstancesToRemove.Num());
        return true;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find foliage info"));
    return false;
}

bool UAuracronFoliageEditUtility::SelectFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find the specific foliage type
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Foliage type not found in actor"));
        return false;
    }

    int32 SelectedCount = 0;

    // Select foliage instances within radius
    for (int32 InstanceIndex = 0; InstanceIndex < FoliageInfo->Instances.Num(); ++InstanceIndex)
    {
        const FFoliageInstance& Instance = FoliageInfo->Instances[InstanceIndex];
        float Distance = FVector::Dist(Instance.Location, Location);

        if (Distance <= Radius)
        {
            // Mark instance as selected (this is a simplified implementation)
            SelectedCount++;
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Selected %d foliage instances at location: %s with radius: %f"), SelectedCount, *Location.ToString(), Radius);

    return SelectedCount > 0;
}

bool UAuracronFoliageEditUtility::ReapplyFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find or add foliage info
    FFoliageInfo* FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find or add foliage info"));
        return false;
    }

    // This is a simplified implementation that just refreshes the foliage
    if (FoliageInfo->Implementation.IsValid())
    {
        FoliageInfo->Implementation->Reapply(FoliageType);
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Reapplied foliage at location: %s with radius: %f"), *Location.ToString(), Radius);
    return true;
}

UFoliageType* UAuracronFoliageEditUtility::CreateFoliageType(UStaticMesh* StaticMesh, const FString& Name)
{
    if (!StaticMesh)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid StaticMesh"));
        return nullptr;
    }

    // Create new foliage type using UE 5.6 API
    UFoliageType_InstancedStaticMesh* NewFoliageType = NewObject<UFoliageType_InstancedStaticMesh>();
    if (NewFoliageType)
    {
        // Set the static mesh using the correct UE 5.6 property
        NewFoliageType->Mesh = StaticMesh;

        // Set default values for the foliage type
        NewFoliageType->Density = 100.0f;
        NewFoliageType->Radius = 0.0f;
        NewFoliageType->bSingleInstanceModeOverrideRadius = false;

        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Created foliage type for mesh: %s"), *StaticMesh->GetName());
        return NewFoliageType;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to create foliage type"));
    return nullptr;
}

bool UAuracronFoliageEditUtility::AddFoliageType(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Add foliage type to actor using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
    if (FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Added foliage type to world"));
        return true;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to add foliage type"));
    return false;
}

bool UAuracronFoliageEditUtility::RemoveFoliageType(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Remove foliage type from actor using UE 5.6 API
    TArray<UFoliageType*> FoliageTypesToRemove;
    FoliageTypesToRemove.Add(FoliageType);
    FoliageActor->RemoveFoliageType(FoliageTypesToRemove.GetData(), FoliageTypesToRemove.Num());
    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Removed foliage type from world"));
    return true;
}

TArray<UFoliageType*> UAuracronFoliageEditUtility::GetFoliageTypes(UWorld* World)
{
    TArray<UFoliageType*> FoliageTypes;

    if (!World)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World"));
        return FoliageTypes;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return FoliageTypes;
    }

    // Get all foliage types using UE 5.6 API - iterate through FoliageInfos
    for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key)
        {
            FoliageTypes.Add(FoliagePair.Key);
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Found %d foliage types"), FoliageTypes.Num());

    return FoliageTypes;
}

int32 UAuracronFoliageEditUtility::GetFoliageInstanceCount(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return 0;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return 0;
    }

    // Get instance count using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        int32 InstanceCount = FoliageInfo->Instances.Num();
        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Found %d instances"), InstanceCount);
        return InstanceCount;
    }

    return 0;
}

TArray<FAuracronFoliageInstanceData> UAuracronFoliageEditUtility::GetFoliageInstances(UWorld* World, UFoliageType* FoliageType)
{
    TArray<FAuracronFoliageInstanceData> Instances;

    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return Instances;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return Instances;
    }

    // Get all instances for the foliage type using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        for (int32 InstanceIndex = 0; InstanceIndex < FoliageInfo->Instances.Num(); ++InstanceIndex)
        {
            const FFoliageInstance& Instance = FoliageInfo->Instances[InstanceIndex];

            FAuracronFoliageInstanceData InstanceData;
            InstanceData.Transform = FTransform(Instance.Rotation, Instance.Location, FVector(Instance.DrawScale3D));
            // Note: FoliageType and InstanceIndex are not part of FAuracronFoliageInstanceData in this implementation
            Instances.Add(InstanceData);
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Retrieved %d foliage instances"), Instances.Num());
    return Instances;
}

bool UAuracronFoliageEditUtility::UpdateFoliageInstances(UWorld* World, UFoliageType* FoliageType, const TArray<FAuracronFoliageInstanceData>& Instances)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Update instances using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        for (const FAuracronFoliageInstanceData& InstanceData : Instances)
        {
            // Create a new instance from the transform data
            FFoliageInstance NewInstance;
            NewInstance.Location = InstanceData.Transform.GetLocation();
            NewInstance.Rotation = InstanceData.Transform.GetRotation().Rotator();
            NewInstance.DrawScale3D = FVector3f(InstanceData.Transform.GetScale3D());

            // Add the new instance
            FoliageInfo->Instances.Add(NewInstance);
        }

        // Mark for update
        FoliageInfo->Refresh(true, true);

        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Updated %d foliage instances"), Instances.Num());
        return true;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find foliage info"));
    return false;
}

bool UAuracronFoliageEditUtility::IsValidFoliageLocation(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return false;
    }

    // Enhanced validation for UE 5.6
    if (Location.ContainsNaN())
    {
        return false;
    }

    // Check if location is within world bounds
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    // Perform line trace to validate surface
    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic))
    {
        // Check if the surface is suitable for foliage
        return HitResult.bBlockingHit && !HitResult.Normal.IsNearlyZero();
    }

    return false;
}

FVector UAuracronFoliageEditUtility::GetSurfaceNormal(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return FVector::UpVector;
    }

    // Enhanced surface normal detection for UE 5.6
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = false;

    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic, QueryParams))
    {
        if (HitResult.bBlockingHit && !HitResult.Normal.IsNearlyZero())
        {
            return HitResult.Normal.GetSafeNormal();
        }
    }

    return FVector::UpVector;
}

float UAuracronFoliageEditUtility::GetSurfaceHeight(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return Location.Z;
    }

    // Enhanced surface height detection for UE 5.6
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = false;

    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic, QueryParams))
    {
        if (HitResult.bBlockingHit)
        {
            return HitResult.Location.Z;
        }
    }

    return Location.Z;
}
