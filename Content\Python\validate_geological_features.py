#!/usr/bin/env python3
"""
AURACRON - Geological Features Production Validation Script
Validates that the created geological features meet all production requirements
"""

import unreal
import sys

def validate_geological_features_production():
    """Validate geological features meet production standards"""
    try:
        unreal.log("🔍 Starting Geological Features production validation...")
        
        validation_results = {
            'level_loaded': False,
            'crystal_plateaus_created': False,
            'living_canyons_created': False,
            'water_flows_created': False,
            'materials_applied': False,
            'spline_paths_created': False,
            'count_validation': False
        }
        
        # Load Planície Radiante level
        level_path = "/Game/Levels/Realms/PlanicieRadiante"
        if unreal.EditorAssetLibrary.does_asset_exist(level_path):
            if unreal.EditorLevelLibrary.load_level(level_path):
                validation_results['level_loaded'] = True
                unreal.log("✅ Level loaded successfully")
                
                # Get all actors in the level
                all_actors = unreal.EditorLevelLibrary.get_all_level_actors()
                
                # Check for crystal plateaus (should be 8)
                plateau_actors = [actor for actor in all_actors if 'Crystal_Plateau' in actor.get_actor_label()]
                if len(plateau_actors) >= 8:
                    validation_results['crystal_plateaus_created'] = True
                    unreal.log(f"✅ Found {len(plateau_actors)} Crystal Plateaus")
                else:
                    unreal.log_error(f"❌ Expected 8 Crystal Plateaus, found {len(plateau_actors)}")
                
                # Check for living canyons (should be 4)
                canyon_actors = [actor for actor in all_actors if 'Canyon_' in actor.get_actor_label()]
                if len(canyon_actors) >= 4:
                    validation_results['living_canyons_created'] = True
                    unreal.log(f"✅ Found {len(canyon_actors)} Canyon splines")
                else:
                    unreal.log_error(f"❌ Expected 4 Canyon splines, found {len(canyon_actors)}")
                
                # Check for water flows
                water_actors = [actor for actor in all_actors if 'Water_Flow' in actor.get_actor_label()]
                if len(water_actors) >= 4:
                    validation_results['water_flows_created'] = True
                    unreal.log(f"✅ Found {len(water_actors)} Water flows")
                else:
                    unreal.log_error(f"❌ Expected 4 Water flows, found {len(water_actors)}")
                
                # Check for spline components
                spline_actors = [actor for actor in all_actors if actor.get_component_by_class(unreal.SplineComponent)]
                if len(spline_actors) >= 4:
                    validation_results['spline_paths_created'] = True
                    unreal.log(f"✅ Found {len(spline_actors)} Spline components")
                else:
                    unreal.log_error(f"❌ Expected spline components, found {len(spline_actors)}")
                
                # Validate counts match requirements
                if len(plateau_actors) == 8 and len(canyon_actors) >= 4 and len(water_actors) >= 4:
                    validation_results['count_validation'] = True
                    unreal.log("✅ Feature counts match requirements")
                else:
                    unreal.log_error("❌ Feature counts do not match requirements")
                
                # Check materials are applied
                materials_applied = 0
                for plateau in plateau_actors:
                    static_mesh_comp = plateau.get_component_by_class(unreal.StaticMeshComponent)
                    if static_mesh_comp and static_mesh_comp.get_material(0):
                        materials_applied += 1
                
                if materials_applied >= len(plateau_actors) * 0.8:  # 80% threshold
                    validation_results['materials_applied'] = True
                    unreal.log("✅ Materials applied to geological features")
                else:
                    unreal.log_error("❌ Materials not properly applied")
            else:
                unreal.log_error("❌ Failed to load level")
        else:
            unreal.log_error("❌ Level does not exist")
        
        # Calculate validation score
        passed_checks = sum(validation_results.values())
        total_checks = len(validation_results)
        success_rate = (passed_checks / total_checks) * 100
        
        unreal.log(f"📊 Validation Results: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")
        
        if success_rate >= 85:
            unreal.log("✅ Geological Features meet production standards!")
            return True
        else:
            unreal.log_error("❌ Geological Features do not meet production standards")
            return False
            
    except Exception as e:
        unreal.log_error(f"❌ Validation failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = validate_geological_features_production()
    sys.exit(0 if success else 1)