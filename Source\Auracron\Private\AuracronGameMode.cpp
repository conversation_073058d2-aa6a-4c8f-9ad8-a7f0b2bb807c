/**
 * AuracronGameMode.cpp
 * 
 * Implementação do GameMode principal do Auracron
 */

#include "AuracronGameMode.h"
#include "AuracronPlayerController.h"
#include "AuracronCharacter.h"
#include "AuracronHUD.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Subsystems/SubsystemBlueprintLibrary.h"

// Includes dos bridges - temporariamente removidos para compilação inicial
// #include "AuracronMasterOrchestrator/Public/AuracronMasterOrchestrator.h"
// #include "AuracronDynamicRealmBridge/Public/AuracronDynamicRealmSubsystem.h"
// #include "AuracronHarmonyEngineBridge/Public/HarmonyEngineSubsystem.h"

AAuracronGameMode::AAuracronGameMode()
{
    // Configurar tick
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS para lógica de jogo

    // Estado inicial
    CurrentGameState = EAuracronGameState::WaitingToStart;
    CurrentMatchPhase = EAuracronMatchPhase::EarlyGame;
    MatchStartTime = 0.0f;
    bGameInitialized = false;
    LastUpdateTime = 0.0f;

    // Configuração padrão
    MatchConfig = FAuracronMatchConfig();

    // Configurar classes padrão
    DefaultPawnClass = AAuracronCharacter::StaticClass();
    PlayerControllerClass = AAuracronPlayerController::StaticClass();
    HUDClass = AAuracronHUD::StaticClass();
    PlayerStateClass = nullptr; // Será definido no Blueprint

    // Inicializar ponteiros dos bridges
    MasterOrchestrator = nullptr;
    DynamicRealmSubsystem = nullptr;
    HarmonyEngineSubsystem = nullptr;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Construtor executado"));
    }
}

void AAuracronGameMode::BeginPlay()
{
    Super::BeginPlay();

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: BeginPlay iniciado"));
    }

    // Validar configuração
    ValidateGameConfiguration();

    // Inicializar bridges
    InitializeAllBridges();

    // Mudar para estado de lobby
    ChangeGameState(EAuracronGameState::Lobby);

    // Iniciar timer de atualização
    StartGameUpdateTimer();

    bGameInitialized = true;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: BeginPlay concluído"));
    }
}

void AAuracronGameMode::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: EndPlay iniciado"));
    }

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GameUpdateTimer);
        GetWorld()->GetTimerManager().ClearTimer(ChampionSelectTimer);
        GetWorld()->GetTimerManager().ClearTimer(MatchDurationTimer);
    }

    // Cleanup da partida
    CleanupMatch();

    Super::EndPlay(EndPlayReason);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: EndPlay concluído"));
    }
}

void AAuracronGameMode::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bGameInitialized)
    {
        return;
    }

    // Atualizar lógica do jogo
    UpdateGameLogic(DeltaTime);

    // Atualizar fase da partida se estiver em jogo
    if (CurrentGameState == EAuracronGameState::InGame)
    {
        UpdateMatchPhase();
    }

    LastUpdateTime = GetWorld()->GetTimeSeconds();
}

void AAuracronGameMode::PostLogin(APlayerController* NewPlayer)
{
    Super::PostLogin(NewPlayer);

    if (!NewPlayer)
    {
        return;
    }

    // Adicionar à lista de jogadores conectados
    ConnectedPlayers.AddUnique(NewPlayer);

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Jogador conectado. Total: %d/%d"), 
               ConnectedPlayers.Num(), MatchConfig.MaxPlayers);
    }

    // Notificar Harmony Engine sobre novo jogador
    if (HarmonyEngineSubsystem && IsValid(HarmonyEngineSubsystem))
    {
        // Usar reflexão para chamar método quando bridge estiver disponível
        // HarmonyEngineSubsystem->OnPlayerJoined(NewPlayer);
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Notificando Harmony Engine sobre novo jogador"));
        }
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: HarmonyEngineSubsystem não disponível"));
    }

    // Se temos jogadores suficientes, iniciar seleção de campeões
    if (ConnectedPlayers.Num() >= MatchConfig.MaxPlayers && CurrentGameState == EAuracronGameState::Lobby)
    {
        StartChampionSelect();
    }
}

void AAuracronGameMode::Logout(AController* Exiting)
{
    if (APlayerController* PC = Cast<APlayerController>(Exiting))
    {
        ConnectedPlayers.Remove(PC);

        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Jogador desconectado. Total: %d"), 
                   ConnectedPlayers.Num());
        }

        // Notificar Harmony Engine sobre jogador saindo
        if (HarmonyEngineSubsystem && IsValid(HarmonyEngineSubsystem))
        {
            // Usar reflexão para chamar método quando bridge estiver disponível
            // HarmonyEngineSubsystem->OnPlayerLeft(PC);
            if (bVerboseLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Notificando Harmony Engine sobre jogador saindo"));
            }
        }
    }

    Super::Logout(Exiting);
}

void AAuracronGameMode::StartChampionSelect()
{
    if (CurrentGameState != EAuracronGameState::Lobby)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: Tentativa de iniciar seleção de campeões em estado inválido"));
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Iniciando seleção de campeões"));
    }

    ChangeGameState(EAuracronGameState::ChampionSelect);

    // Inicializar sistema de sígilos para seleção
    // Note: SigilosBridge é um ActorComponent, será obtido via GetComponentByClass quando necessário
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Iniciando seleção de campeões"));
    }

    // Configurar timer para timeout da seleção
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            ChampionSelectTimer,
            this,
            &AAuracronGameMode::HandleChampionSelectTimeout,
            MatchConfig.ChampionSelectTime,
            false
        );
    }
}

void AAuracronGameMode::StartMatch()
{
    if (CurrentGameState != EAuracronGameState::ChampionSelect)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: Tentativa de iniciar partida em estado inválido"));
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Iniciando partida"));
    }

    // Limpar timer de seleção
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(ChampionSelectTimer);
    }

    ChangeGameState(EAuracronGameState::LoadingGame);

    // Ativar todos os sistemas de jogo
    if (MasterOrchestrator && IsValid(MasterOrchestrator))
    {
        // Usar reflexão para chamar método quando bridge estiver disponível
        // MasterOrchestrator->StartMatch();
        if (bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Iniciando partida via Master Orchestrator"));
        }
    }
    else if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Master Orchestrator não disponível para iniciar partida"));
    }

    // Configurar timer de duração da partida
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            MatchDurationTimer,
            this,
            &AAuracronGameMode::HandleMatchTimeout,
            MatchConfig.MaxMatchDuration,
            false
        );
    }

    // Registrar tempo de início
    MatchStartTime = GetWorld()->GetTimeSeconds();

    // Mudar para estado de jogo
    ChangeGameState(EAuracronGameState::InGame);
}

void AAuracronGameMode::EndMatch(int32 WinningTeam)
{
    if (CurrentGameState != EAuracronGameState::InGame)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: Tentativa de finalizar partida em estado inválido"));
        return;
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Finalizando partida. Equipe vencedora: %d"), WinningTeam);
    }

    // Limpar timer de duração
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(MatchDurationTimer);
    }

    // Coletar estatísticas finais
    CollectMatchStatistics();

    // Mudar para estado pós-jogo
    ChangeGameState(EAuracronGameState::PostGame);

    // Disparar evento de fim de partida
    OnMatchEnded.Broadcast(WinningTeam, MatchStats);

    // Agendar transição para estado final
    if (GetWorld())
    {
        FTimerHandle PostGameTimer;
        GetWorld()->GetTimerManager().SetTimer(
            PostGameTimer,
            [this]()
            {
                ChangeGameState(EAuracronGameState::Ended);
            },
            10.0f, // 10 segundos de pós-jogo
            false
        );
    }
}

void AAuracronGameMode::InitializeAllBridges()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Inicializando todos os bridges"));
    }

    // Obter referências dos bridges
    InitializeBridgeReferences();

    // Inicializar Master Orchestrator - temporariamente comentado
    // if (MasterOrchestrator)
    // {
    //     MasterOrchestrator->Initialize();
    //
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Master Orchestrator inicializado"));
    //     }
    // }
    // else
    // {
    //     UE_LOG(LogTemp, Error, TEXT("AuracronGameMode: Falha ao obter Master Orchestrator"));
    // }

    // Inicializar outros bridges através do orchestrator - temporariamente comentado
    // if (MasterOrchestrator)
    // {
    //     O Master Orchestrator vai inicializar todos os outros bridges
    //     MasterOrchestrator->InitializeAllBridges();
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Inicializando bridges via Master Orchestrator"));
    //     }
    // }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Todos os bridges inicializados"));
    }
}

// === Private Implementation ===

void AAuracronGameMode::ChangeGameState(EAuracronGameState NewState)
{
    if (CurrentGameState == NewState)
    {
        return;
    }

    EAuracronGameState OldState = CurrentGameState;
    CurrentGameState = NewState;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Estado mudou de %d para %d"),
               (int32)OldState, (int32)NewState);
    }

    // Disparar evento
    OnGameStateChanged.Broadcast(OldState, NewState);
}

void AAuracronGameMode::ChangeMatchPhase(EAuracronMatchPhase NewPhase)
{
    if (CurrentMatchPhase == NewPhase)
    {
        return;
    }

    EAuracronMatchPhase OldPhase = CurrentMatchPhase;
    CurrentMatchPhase = NewPhase;

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Fase mudou de %d para %d"),
               (int32)OldPhase, (int32)NewPhase);
    }

    // Disparar evento
    OnMatchPhaseChanged.Broadcast(OldPhase, NewPhase);
}

void AAuracronGameMode::UpdateGameLogic(float DeltaTime)
{
    // Atualizar estatísticas da partida
    if (CurrentGameState == EAuracronGameState::InGame && MatchStartTime > 0.0f)
    {
        MatchStats.MatchDuration = GetWorld()->GetTimeSeconds() - MatchStartTime;
    }

    // Atualizar Master Orchestrator - temporariamente comentado
    // if (MasterOrchestrator)
    // {
    //     MasterOrchestrator->UpdateSystems(DeltaTime);
    //     Log apenas ocasionalmente para evitar spam
    //     static float LastLogTime = 0.0f;
    //     float CurrentTime = GetWorld()->GetTimeSeconds();
    //     if (CurrentTime - LastLogTime > 30.0f && bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Atualizando sistemas via Master Orchestrator"));
    //         LastLogTime = CurrentTime;
    //     }
    // }
}

void AAuracronGameMode::UpdateMatchPhase()
{
    if (MatchStats.MatchDuration <= 0.0f)
    {
        return;
    }

    // Determinar fase baseada no tempo
    EAuracronMatchPhase NewPhase = CurrentMatchPhase;

    if (MatchStats.MatchDuration < 600.0f) // 10 minutos
    {
        NewPhase = EAuracronMatchPhase::EarlyGame;
    }
    else if (MatchStats.MatchDuration < 1200.0f) // 20 minutos
    {
        NewPhase = EAuracronMatchPhase::MidGame;
    }
    else if (MatchStats.MatchDuration < MatchConfig.MaxMatchDuration)
    {
        NewPhase = EAuracronMatchPhase::LateGame;
    }
    else
    {
        NewPhase = EAuracronMatchPhase::Overtime;
    }

    if (NewPhase != CurrentMatchPhase)
    {
        ChangeMatchPhase(NewPhase);
    }
}

void AAuracronGameMode::InitializeBridgeReferences()
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AuracronGameMode: World não disponível para inicializar bridges"));
        return;
    }

    // Tentar obter bridges usando reflexão para evitar problemas de linkagem
    if (UGameInstance* GameInstance = GetWorld()->GetGameInstance())
    {
        // Buscar por subsystems usando nomes de classe
        TArray<UGameInstanceSubsystem*> Subsystems = GameInstance->GetSubsystemArrayCopy<UGameInstanceSubsystem>();
        for (UGameInstanceSubsystem* Subsystem : Subsystems)
        {
            if (Subsystem && Subsystem->GetClass()->GetName().Contains(TEXT("MasterOrchestrator")))
            {
                MasterOrchestrator = Subsystem;
                break;
            }
        }
    }

    // Buscar por WorldSubsystems
    TArray<UWorldSubsystem*> WorldSubsystems = GetWorld()->GetSubsystemArrayCopy<UWorldSubsystem>();
    for (UWorldSubsystem* Subsystem : WorldSubsystems)
    {
        if (Subsystem)
        {
            FString ClassName = Subsystem->GetClass()->GetName();
            if (ClassName.Contains(TEXT("HarmonyEngine")))
            {
                HarmonyEngineSubsystem = Subsystem;
            }
            else if (ClassName.Contains(TEXT("DynamicRealm")))
            {
                DynamicRealmSubsystem = Subsystem;
            }
        }
    }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Bridge references initialized - MasterOrchestrator: %s, HarmonyEngine: %s, DynamicRealm: %s"),
            MasterOrchestrator ? TEXT("Found") : TEXT("Not Available"),
            HarmonyEngineSubsystem ? TEXT("Found") : TEXT("Not Available"),
            DynamicRealmSubsystem ? TEXT("Found") : TEXT("Not Available"));
    }
}

void AAuracronGameMode::ValidateGameConfiguration()
{
    // Validar configuração da partida
    if (MatchConfig.MaxPlayers <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: MaxPlayers inválido, usando padrão (10)"));
        MatchConfig.MaxPlayers = 10;
    }

    if (MatchConfig.MaxMatchDuration <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: MaxMatchDuration inválido, usando padrão (1800s)"));
        MatchConfig.MaxMatchDuration = 1800.0f;
    }

    if (MatchConfig.ChampionSelectTime <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronGameMode: ChampionSelectTime inválido, usando padrão (120s)"));
        MatchConfig.ChampionSelectTime = 120.0f;
    }
}

void AAuracronGameMode::StartGameUpdateTimer()
{
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            GameUpdateTimer,
            [this]()
            {
                // Timer de atualização adicional se necessário
            },
            1.0f, // 1 segundo
            true // repetir
        );
    }
}

void AAuracronGameMode::HandleChampionSelectTimeout()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Timeout da seleção de campeões"));
    }

    // Forçar início da partida
    StartMatch();
}

void AAuracronGameMode::HandleMatchTimeout()
{
    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Timeout da partida"));
    }

    // Finalizar partida por tempo (empate)
    EndMatch(-1);
}

void AAuracronGameMode::CollectMatchStatistics()
{
    // Coletar estatísticas finais da partida
    if (MatchStartTime > 0.0f)
    {
        MatchStats.MatchDuration = GetWorld()->GetTimeSeconds() - MatchStartTime;
    }

    // Coletar estatísticas dos bridges - temporariamente comentado
    // if (MasterOrchestrator)
    // {
    //     MatchStats = MasterOrchestrator->CollectMatchStatistics();
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Coletando estatísticas via Master Orchestrator"));
    //     }
    // }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Estatísticas coletadas - Duração: %.2f, Objetivos: %d"),
               MatchStats.MatchDuration, MatchStats.ObjectivesCompleted);
    }
}

void AAuracronGameMode::CleanupMatch()
{
    // Limpar dados da partida
    ConnectedPlayers.Empty();
    MatchStartTime = 0.0f;
    MatchStats = FAuracronMatchStats();

    // Notificar bridges para cleanup - temporariamente comentado
    // if (MasterOrchestrator)
    // {
    //     MasterOrchestrator->CleanupMatch();
    //     if (bVerboseLogging)
    //     {
    //         UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Limpando partida via Master Orchestrator"));
    //     }
    // }

    if (bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronGameMode: Cleanup da partida concluído"));
    }
}
