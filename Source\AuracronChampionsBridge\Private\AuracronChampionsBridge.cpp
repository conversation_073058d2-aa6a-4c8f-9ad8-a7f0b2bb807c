﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de CampeÃµes Bridge Implementation

#include "AuracronChampionsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/PlayerController.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/TimelineComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "GameplayAbilities/Public/AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilities/Public/GameplayAbilitySpec.h"
#include "GameplayTagsManager.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
UAuracronChampionsBridge::UAuracronChampionsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimizaÃ§Ã£o
    
    // Configurar replicaÃ§Ã£o
    SetIsReplicatedByDefault(true);
    
    // Inicializar configuraÃ§Ãµes padrÃ£o
    LoadDefaultChampionConfigurations();
}

void UAuracronChampionsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de CampeÃµes"));

    // Obter referÃªncias aos componentes
    if (AActor* Owner = GetOwner())
    {
        AbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Owner);
        if (!AbilitySystemComponent)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: AbilitySystemComponent nÃ£o encontrado no Owner"));
        }

        // Obter Enhanced Input Component
        if (APawn* OwnerPawn = Cast<APawn>(Owner))
        {
            EnhancedInputComponent = Cast<UEnhancedInputComponent>(OwnerPawn->GetComponentByClass(UEnhancedInputComponent::StaticClass()));
            if (!EnhancedInputComponent)
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent nÃ£o encontrado"));
            }
        }

        // Obter Character Movement Component
        if (ACharacter* OwnerCharacter = Cast<ACharacter>(Owner))
        {
            MovementComponent = OwnerCharacter->GetCharacterMovement();
            ChampionMesh = OwnerCharacter->GetMesh();
        }
    }

    // Inicializar sistema
    bSystemInitialized = InitializeChampionSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para regeneraÃ§Ã£o e cooldowns
        GetWorld()->GetTimerManager().SetTimer(
            RegenerationTimer,
            [this]()
            {
                ProcessRegeneration(1.0f); // A cada segundo
            },
            1.0f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            CooldownTimer,
            [this]()
            {
                ProcessCooldowns(0.1f); // A cada 100ms
            },
            0.1f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de CampeÃµes inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de CampeÃµes"));
    }
}

void UAuracronChampionsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(RegenerationTimer);
        GetWorld()->GetTimerManager().ClearTimer(CooldownTimer);
    }
    
    // Remover habilidades concedidas
    RemoveAbilities();
    
    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();

    Super::EndPlay(EndPlayReason);
}

void UAuracronChampionsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronChampionsBridge, ChampionConfigurations);
    DOREPLIFETIME(UAuracronChampionsBridge, SelectedChampionID);
    DOREPLIFETIME(UAuracronChampionsBridge, CurrentChampionState);
    DOREPLIFETIME(UAuracronChampionsBridge, CurrentAttributes);
}

void UAuracronChampionsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar regeneraÃ§Ã£o contÃ­nua
    ProcessRegeneration(DeltaTime);
    
    // Processar cooldowns
    ProcessCooldowns(DeltaTime);
}

// === Core Champion Management ===

bool UAuracronChampionsBridge::SelectChampion(const FString& ChampionID)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema nÃ£o inicializado"));
        return false;
    }

    if (ChampionID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ChampionID vazio"));
        return false;
    }

    // Check if champion exists by searching through the array
    bool bChampionFound = false;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == ChampionID)
        {
            bChampionFound = true;
            break;
        }
    }

    if (!bChampionFound)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CampeÃ£o nÃ£o encontrado: %s"), *ChampionID);
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* ConfigPtr = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == ChampionID)
        {
            ConfigPtr = &Entry.Configuration;
            break;
        }
    }

    if (!ConfigPtr)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Champion configuration not found for ID: %s"), *ChampionID);
        return false;
    }

    const FAuracronChampionConfiguration& Config = *ConfigPtr;
    if (!Config.bAvailableForSelection || !Config.bUnlocked)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CampeÃ£o nÃ£o disponÃ­vel para seleÃ§Ã£o: %s"), *ChampionID);
        return false;
    }

    // Remover configuraÃ§Ã£o anterior se houver
    if (!SelectedChampionID.IsEmpty())
    {
        RemoveAbilities();
    }

    SelectedChampionID = ChampionID;
    CurrentAttributes = Config.BaseAttributes;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: CampeÃ£o selecionado: %s"), *ChampionID);

    // Broadcast evento
    OnChampionSelected.Broadcast(ChampionID);

    return true;
}

bool UAuracronChampionsBridge::SpawnChampion(const FVector& SpawnLocation, const FRotator& SpawnRotation)
{
    if (!bSystemInitialized || SelectedChampionID.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema nÃ£o inicializado ou nenhum campeÃ£o selecionado"));
        return false;
    }

    if (bChampionSpawned)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CampeÃ£o jÃ¡ foi spawnado"));
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: ConfiguraÃ§Ã£o do campeÃ£o nÃ£o encontrada"));
        return false;
    }

    // Configurar sistemas
    SetupAbilitySystem();
    SetupMovementSystem();
    SetupAnimationSystem();
    SetupChampionInput();

    // Aplicar configuraÃ§Ã£o visual
    UpdateChampionMesh(Config->VisualConfig);

    // Aplicar atributos base
    ApplyBaseAttributes(Config->BaseAttributes);

    // Conceder habilidades
    GrantAbilities(Config->Abilities);

    // Teleportar para posiÃ§Ã£o de spawn
    if (AActor* Owner = GetOwner())
    {
        Owner->SetActorLocation(SpawnLocation);
        Owner->SetActorRotation(SpawnRotation);
    }

    bChampionSpawned = true;
    CurrentChampionState = EAuracronChampionState::Idle;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: CampeÃ£o spawnado: %s"), *SelectedChampionID);

    // Broadcast evento
    OnChampionSpawned.Broadcast(SelectedChampionID);

    return true;
}

bool UAuracronChampionsBridge::DespawnChampion()
{
    if (!bChampionSpawned)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum campeÃ£o spawnado"));
        return false;
    }

    // Remover habilidades
    RemoveAbilities();

    // Limpar efeitos visuais
    for (UNiagaraComponent* Component : ActiveVisualEffects)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVisualEffects.Empty();

    bChampionSpawned = false;
    CurrentChampionState = EAuracronChampionState::Idle;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: CampeÃ£o despawnado: %s"), *SelectedChampionID);

    return true;
}

FAuracronChampionConfiguration UAuracronChampionsBridge::GetCurrentChampionConfiguration() const
{
    if (SelectedChampionID.IsEmpty())
    {
        return FAuracronChampionConfiguration();
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (Config)
    {
        return *Config;
    }

    return FAuracronChampionConfiguration();
}

bool UAuracronChampionsBridge::IsChampionAlive() const
{
    return CurrentChampionState != EAuracronChampionState::Dead &&
           CurrentChampionState != EAuracronChampionState::Respawning &&
           CurrentAttributes.CurrentHealth > 0.0f;
}

// === Ability Management ===

bool UAuracronChampionsBridge::ActivateAbility(const FString& AbilitySlot)
{
    if (!bSystemInitialized || !AbilitySystemComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema nÃ£o inicializado ou ASC invÃ¡lido"));
        return false;
    }

    if (!IsChampionAlive())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CampeÃ£o nÃ£o estÃ¡ vivo"));
        return false;
    }

    // Find configuration by iterating through array
    const FAuracronChampionConfiguration* Config = nullptr;
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ConfiguraÃ§Ã£o do campeÃ£o nÃ£o encontrada"));
        return false;
    }

    // Determinar qual habilidade ativar
    TSoftClassPtr<UGameplayAbility> AbilityClass;
    if (AbilitySlot == TEXT("Q"))
    {
        AbilityClass = Config->Abilities.QAbility;
    }
    else if (AbilitySlot == TEXT("W"))
    {
        AbilityClass = Config->Abilities.WAbility;
    }
    else if (AbilitySlot == TEXT("E"))
    {
        AbilityClass = Config->Abilities.EAbility;
    }
    else if (AbilitySlot == TEXT("R"))
    {
        AbilityClass = Config->Abilities.RAbility;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Slot de habilidade invÃ¡lido: %s"), *AbilitySlot);
        return false;
    }

    if (!AbilityClass.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade nÃ£o configurada para slot: %s"), *AbilitySlot);
        return false;
    }

    // Verificar cooldown
    if (!IsAbilityAvailable(AbilitySlot))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Habilidade em cooldown: %s"), *AbilitySlot);
        return false;
    }

    // Verificar mana
    float ManaCost = Config->Abilities.AbilityManaCosts.Contains(AbilitySlot) ?
                     Config->Abilities.AbilityManaCosts[AbilitySlot] : 0.0f;

    if (CurrentAttributes.CurrentMana < ManaCost)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mana insuficiente para habilidade: %s"), *AbilitySlot);
        return false;
    }

    // Tentar ativar a habilidade
    TSubclassOf<UGameplayAbility> LoadedAbility = AbilityClass.LoadSynchronous();
    if (LoadedAbility)
    {
        bool bActivated = AbilitySystemComponent->TryActivateAbilityByClass(LoadedAbility);
        if (bActivated)
        {
            // Consumir mana
            CurrentAttributes.CurrentMana = FMath::Max(0.0f, CurrentAttributes.CurrentMana - ManaCost);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidade ativada: %s"), *AbilitySlot);

            // Broadcast evento
            OnAbilityActivated.Broadcast(SelectedChampionID, AbilitySlot);

            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao ativar habilidade: %s"), *AbilitySlot);
    return false;
}

// === Level and Experience ===

bool UAuracronChampionsBridge::GainExperience(int32 ExperienceAmount)
{
    if (!bSystemInitialized || ExperienceAmount <= 0)
    {
        return false;
    }

    // Find configuration by iterating through array
    FAuracronChampionConfiguration* Config = nullptr;
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config)
    {
        return false;
    }

    Config->CurrentExperience += ExperienceAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: ExperiÃªncia ganha: %d (Total: %d)"), ExperienceAmount, Config->CurrentExperience);

    // Verificar se pode subir de nÃ­vel
    while (Config->CurrentExperience >= Config->ExperienceToNextLevel && Config->ChampionLevel < 18)
    {
        LevelUp();
    }

    return true;
}

bool UAuracronChampionsBridge::LevelUp()
{
    // Find configuration by iterating through array
    FAuracronChampionConfiguration* Config = nullptr;
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == SelectedChampionID)
        {
            Config = &Entry.Configuration;
            break;
        }
    }
    if (!Config || Config->ChampionLevel >= 18)
    {
        return false;
    }

    // Subir nÃ­vel
    Config->ChampionLevel++;
    Config->CurrentExperience -= Config->ExperienceToNextLevel;

    // Calcular nova experiÃªncia necessÃ¡ria
    Config->ExperienceToNextLevel = 280 + (Config->ChampionLevel * 100); // FÃ³rmula de progressÃ£o

    // Ganhar ponto de habilidade
    Config->Abilities.AvailableAbilityPoints++;

    // Recalcular atributos baseados no novo nÃ­vel
    CurrentAttributes = CalculateAttributesForLevel(Config->ChampionLevel);

    // Aplicar novos atributos
    ApplyBaseAttributes(CurrentAttributes);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: CampeÃ£o %s subiu para nÃ­vel %d"), *SelectedChampionID, Config->ChampionLevel);

    // Broadcast evento
    OnChampionLevelUp.Broadcast(SelectedChampionID, Config->ChampionLevel);

    return true;
}

int32 UAuracronChampionsBridge::GetExperienceToNextLevel() const
{
    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    const FAuracronChampionConfiguration* Config = FoundEntry ? &FoundEntry->Configuration : nullptr;
    if (!Config)
    {
        return 0;
    }

    return FMath::Max(0, Config->ExperienceToNextLevel - Config->CurrentExperience);
}

FAuracronChampionBaseAttributes UAuracronChampionsBridge::CalculateAttributesForLevel(int32 Level) const
{
    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return FAuracronChampionBaseAttributes();
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    FAuracronChampionBaseAttributes ScaledAttributes = Config->BaseAttributes;

    // Escalar atributos baseados no nÃ­vel (fÃ³rmula de crescimento)
    float LevelMultiplier = 1.0f + ((Level - 1) * 0.1f); // +10% por nÃ­vel

    ScaledAttributes.MaxHealth *= LevelMultiplier;
    ScaledAttributes.CurrentHealth = ScaledAttributes.MaxHealth; // HP cheio ao subir de nÃ­vel
    ScaledAttributes.MaxMana *= (1.0f + ((Level - 1) * 0.05f)); // +5% mana por nÃ­vel
    ScaledAttributes.CurrentMana = ScaledAttributes.MaxMana; // Mana cheia ao subir de nÃ­vel
    ScaledAttributes.AttackDamage *= LevelMultiplier;
    ScaledAttributes.AbilityPower *= LevelMultiplier;
    ScaledAttributes.Armor += (Level - 1) * 3.0f; // +3 armadura por nÃ­vel
    ScaledAttributes.MagicResistance += (Level - 1) * 2.5f; // +2.5 resistÃªncia mÃ¡gica por nÃ­vel

    return ScaledAttributes;
}

// === MetaHuman Integration ===

bool UAuracronChampionsBridge::ApplyMetaHumanConfiguration()
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Sistema nÃ£o inicializado ou ChampionMesh invÃ¡lido"));
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bUseMetaHuman)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: MetaHuman nÃ£o configurado para este campeÃ£o"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    return UpdateChampionMesh(Config->VisualConfig);
}

bool UAuracronChampionsBridge::CustomizeFacialAppearance(const TMap<FString, float>& FacialParameters)
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bAllowFacialCustomization)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: CustomizaÃ§Ã£o facial nÃ£o permitida para este campeÃ£o"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Aplicar parÃ¢metros faciais ao Control Rig
    if (Config->VisualConfig.FacialControlRig.IsValid())
    {
        // Load the Control Rig asset
        UControlRig* FacialControlRig = Config->VisualConfig.FacialControlRig.LoadSynchronous();
        if (FacialControlRig && ChampionMesh->GetAnimInstance())
        {
            // Get the skeletal mesh component's anim instance
            UAnimInstance* AnimInstance = ChampionMesh->GetAnimInstance();
            
            // Apply facial parameters through Control Rig
            for (const auto& Parameter : FacialParameters)
            {
                const FString& ParameterName = Parameter.Key;
                const float ParameterValue = Parameter.Value;
                
                // Find the control in the Control Rig
                FRigControlElement* ControlElement = FacialControlRig->GetHierarchy()->Find<FRigControlElement>(FRigElementKey(*ParameterName, ERigElementType::Control));
                if (ControlElement)
                {
                    // Set the control value
                    FRigControlValue ControlValue;
                    ControlValue.Set<float>(FMath::Clamp(ParameterValue, 0.0f, 1.0f));
                    FacialControlRig->GetHierarchy()->SetControlValue(ControlElement, ControlValue, ERigControlValueType::Current);
                    
                    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: ParÃ¢metro facial aplicado - %s: %f"), *ParameterName, ParameterValue);
                }
                else
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Controle facial nÃ£o encontrado: %s"), *ParameterName);
                }
            }
            
            // Execute the Control Rig to apply changes using UE 5.6 API
#if WITH_EDITOR
            // FacialControlRig->Execute(FRigUnit_BeginExecution::EventName);
#endif
            // Alternative execution for runtime
            if (FacialControlRig)
            {
                FacialControlRig->RequestInit();
                FacialControlRig->Evaluate_AnyThread();
            }
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: %d parÃ¢metros faciais aplicados via Control Rig"), FacialParameters.Num());
            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar Control Rig facial ou AnimInstance invÃ¡lida"));
        }
    }

    return false;
}

bool UAuracronChampionsBridge::ApplyAlternativeSkin(int32 SkinIndex)
{
    if (!bSystemInitialized || !ChampionMesh)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry || !FoundEntry->Configuration.VisualConfig.bSupportsAlternativeSkins)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Skins alternativas nÃ£o suportadas para este campeÃ£o"));
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    if (!Config->VisualConfig.AlternativeSkins.IsValidIndex(SkinIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ãndice de skin invÃ¡lido: %d"), SkinIndex);
        return false;
    }

    // Aplicar skin alternativa
    UTexture2D* SkinTexture = Config->VisualConfig.AlternativeSkins[SkinIndex].LoadSynchronous();
    if (SkinTexture && ChampionMesh)
    {
        // Create a dynamic material instance for the skin
        UMaterialInterface* BaseMaterial = ChampionMesh->GetMaterial(0);
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
            if (DynamicMaterial)
            {
                // Set the alternative skin texture
                DynamicMaterial->SetTextureParameterValue(TEXT("BaseColorTexture"), SkinTexture);
                DynamicMaterial->SetTextureParameterValue(TEXT("DiffuseTexture"), SkinTexture);
                DynamicMaterial->SetTextureParameterValue(TEXT("AlbedoTexture"), SkinTexture);
                
                // Apply the material to all material slots
                for (int32 MaterialIndex = 0; MaterialIndex < ChampionMesh->GetNumMaterials(); ++MaterialIndex)
                {
                    ChampionMesh->SetMaterial(MaterialIndex, DynamicMaterial);
                }
                
                // If there are additional textures for this skin (normal, roughness, etc.)
                if (Config->VisualConfig.AlternativeSkinNormals.IsValidIndex(SkinIndex))
                {
                    UTexture2D* NormalTexture = Config->VisualConfig.AlternativeSkinNormals[SkinIndex].LoadSynchronous();
                    if (NormalTexture)
                    {
                        DynamicMaterial->SetTextureParameterValue(TEXT("NormalTexture"), NormalTexture);
                        DynamicMaterial->SetTextureParameterValue(TEXT("NormalMap"), NormalTexture);
                    }
                }
                
                if (Config->VisualConfig.AlternativeSkinRoughness.IsValidIndex(SkinIndex))
                {
                    UTexture2D* RoughnessTexture = Config->VisualConfig.AlternativeSkinRoughness[SkinIndex].LoadSynchronous();
                    if (RoughnessTexture)
                    {
                        DynamicMaterial->SetTextureParameterValue(TEXT("RoughnessTexture"), RoughnessTexture);
                        DynamicMaterial->SetTextureParameterValue(TEXT("SpecularTexture"), RoughnessTexture);
                    }
                }
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Skin alternativa aplicada com sucesso: %d"), SkinIndex);
                return true;
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar material dinÃ¢mico para skin alternativa"));
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Material base nÃ£o encontrado no ChampionMesh"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar textura da skin alternativa ou ChampionMesh invÃ¡lido"));
    }

    return false;
}

bool UAuracronChampionsBridge::ResetToDefaultAppearance()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Resetar para configuraÃ§Ã£o visual padrÃ£o
    return UpdateChampionMesh(Config->VisualConfig);
}

// === Input Management ===

bool UAuracronChampionsBridge::SetupChampionInput()
{
    if (!EnhancedInputComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: EnhancedInputComponent nÃ£o disponÃ­vel"));
        return false;
    }

    const FAuracronChampionConfigurationEntry* FoundEntry = ChampionConfigurations.FindByPredicate([&](const FAuracronChampionConfigurationEntry& Entry) {
        return Entry.ChampionID == SelectedChampionID;
    });
    
    if (!FoundEntry)
    {
        return false;
    }
    
    const FAuracronChampionConfiguration* Config = &FoundEntry->Configuration;

    // Configurar Input Mapping Context
    if (Config->InputConfig.InputMappingContext.IsValid())
    {
        UInputMappingContext* MappingContext = Config->InputConfig.InputMappingContext.LoadSynchronous();
        if (MappingContext)
        {
            if (APlayerController* PC = Cast<APlayerController>(GetOwner()->GetInstigatorController()))
            {
                if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PC->GetLocalPlayer()))
                {
                    Subsystem->AddMappingContext(MappingContext, 1);
                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Input Mapping Context configurado"));
                }
            }
        }
    }

    // Configurar bindings de input
    // Movimento
    if (Config->InputConfig.MoveAction.IsValid())
    {
        UInputAction* MoveAction = Config->InputConfig.MoveAction.LoadSynchronous();
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &UAuracronChampionsBridge::ProcessMovementInput);
        }
    }

    // Look
    if (Config->InputConfig.LookAction.IsValid())
    {
        UInputAction* LookAction = Config->InputConfig.LookAction.LoadSynchronous();
        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &UAuracronChampionsBridge::ProcessLookInput);
        }
    }

    // Habilidades
    if (Config->InputConfig.QAbilityAction.IsValid())
    {
        UInputAction* QAction = Config->InputConfig.QAbilityAction.LoadSynchronous();
        if (QAction)
        {
            EnhancedInputComponent->BindAction(QAction, ETriggerEvent::Started, this, &UAuracronChampionsBridge::ActivateQAbility);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Input do campeÃ£o configurado"));
    return true;
}

void UAuracronChampionsBridge::ActivateQAbility()
{
    ActivateAbility(TEXT("Q"));
}

void UAuracronChampionsBridge::ProcessLookInput(const FInputActionValue& Value)
{
    // Extrair o valor 2D do input
    const FVector2D LookVector = Value.Get<FVector2D>();
    
    // Obter o Character owner
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        // Aplicar rotaÃ§Ã£o baseada no input
        Character->AddControllerYawInput(LookVector.X);
        Character->AddControllerPitchInput(LookVector.Y);
    }
}

void UAuracronChampionsBridge::ProcessMovementInput(const FInputActionValue& Value)
{
    // Extrair o valor 2D do input
    const FVector2D MovementVector = Value.Get<FVector2D>();
    
    // Obter o Character owner
    if (ACharacter* Character = Cast<ACharacter>(GetOwner()))
    {
        // Aplicar movimento baseado no input
        Character->AddMovementInput(Character->GetActorForwardVector(), MovementVector.Y);
        Character->AddMovementInput(Character->GetActorRightVector(), MovementVector.X);
    }
}

bool UAuracronChampionsBridge::InitializeChampionSystem()
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: World nÃ£o disponÃ­vel para inicializaÃ§Ã£o do sistema"));
        return false;
    }

    // Inicializar configuraÃ§Ãµes padrÃ£o se necessÃ¡rio
    if (ChampionConfigurations.Num() == 0)
    {
        LoadDefaultChampionConfigurations();
    }

    // Configurar estado inicial
    CurrentChampionState = EAuracronChampionState::Idle;
    CurrentLevel = 1;
    CurrentExperience = 0;

    // Inicializar atributos base
    CurrentAttributes.MaxHealth = 100.0f;
    CurrentAttributes.CurrentHealth = 100.0f;
    CurrentAttributes.MaxMana = 100.0f;
    CurrentAttributes.CurrentMana = 100.0f;
    CurrentAttributes.AttackDamage = 10.0f;
    CurrentAttributes.AbilityPower = 10.0f;
    CurrentAttributes.Armor = 5.0f;
    CurrentAttributes.MagicResistance = 5.0f;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de campeÃµes inicializado com sucesso"));
    return true;
}

bool UAuracronChampionsBridge::SetupAbilitySystem()
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: World nÃ£o disponÃ­vel para configuraÃ§Ã£o do sistema de habilidades"));
        return false;
    }

    // Limpar habilidades existentes
    CurrentAbilities.QAbility = nullptr;
    CurrentAbilities.WAbility = nullptr;
    CurrentAbilities.EAbility = nullptr;
    CurrentAbilities.RAbility = nullptr;

    // Configurar cooldowns iniciais
    CurrentAbilities.AbilityCooldowns.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de habilidades configurado"));
    return true;
}

bool UAuracronChampionsBridge::SetupMovementSystem()
{
    if (!ChampionMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mesh do campeÃ£o nÃ£o disponÃ­vel para configuraÃ§Ã£o de movimento"));
        return false;
    }

    // Configurar parÃ¢metros de movimento bÃ¡sicos
    MovementSpeed = 600.0f; // Velocidade padrÃ£o
    JumpHeight = 420.0f;    // Altura de pulo padrÃ£o

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de movimento configurado"));
    return true;
}

bool UAuracronChampionsBridge::SetupAnimationSystem()
{
    if (!ChampionMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mesh do campeÃ£o nÃ£o disponÃ­vel para configuraÃ§Ã£o de animaÃ§Ã£o"));
        return false;
    }

    // Configurar sistema de animaÃ§Ã£o bÃ¡sico
    // Em uma implementaÃ§Ã£o completa, aqui configuraria o Animation Blueprint

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de animaÃ§Ã£o configurado"));
    return true;
}

bool UAuracronChampionsBridge::ApplyBaseAttributes(const FAuracronChampionBaseAttributes& BaseAttributes)
{
    // Aplicar atributos base
    CurrentAttributes = BaseAttributes;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Atributos base aplicados - Health: %.1f, Mana: %.1f"),
           CurrentAttributes.CurrentHealth, CurrentAttributes.CurrentMana);
    return true;
}

bool UAuracronChampionsBridge::GrantAbilities(const FAuracronChampionAbilities& Abilities)
{
    // Conceder habilidades ao campeÃ£o
    CurrentAbilities = Abilities;

    // Configurar cooldowns para as habilidades
    CurrentAbilities.AbilityCooldowns.Add(TEXT("Q"), 0.0f);
    CurrentAbilities.AbilityCooldowns.Add(TEXT("W"), 0.0f);
    CurrentAbilities.AbilityCooldowns.Add(TEXT("E"), 0.0f);
    CurrentAbilities.AbilityCooldowns.Add(TEXT("R"), 0.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidades concedidas ao campeÃ£o"));
    return true;
}

bool UAuracronChampionsBridge::RemoveAbilities()
{
    // Remover todas as habilidades
    CurrentAbilities.QAbility = nullptr;
    CurrentAbilities.WAbility = nullptr;
    CurrentAbilities.EAbility = nullptr;
    CurrentAbilities.RAbility = nullptr;

    // Limpar cooldowns
    CurrentAbilities.AbilityCooldowns.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Habilidades removidas do campeÃ£o"));
    return true;
}

bool UAuracronChampionsBridge::UpdateChampionMesh(const FAuracronChampionVisualConfig& VisualConfig)
{
    if (!ChampionMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Mesh do campeÃ£o nÃ£o disponÃ­vel para atualizaÃ§Ã£o"));
        return false;
    }

    // Atualizar configuraÃ§Ã£o visual do mesh
    // Em uma implementaÃ§Ã£o completa, aqui aplicaria materiais, texturas, etc.

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mesh do campeÃ£o atualizado com configuraÃ§Ã£o visual"));
    return true;
}

void UAuracronChampionsBridge::ProcessRegeneration(float DeltaTime)
{
    if (CurrentChampionState == EAuracronChampionState::Dead)
    {
        return;
    }

    // RegeneraÃ§Ã£o de saÃºde
    if (CurrentAttributes.CurrentHealth < CurrentAttributes.MaxHealth)
    {
        CurrentAttributes.CurrentHealth = FMath::Min(CurrentAttributes.MaxHealth, CurrentAttributes.CurrentHealth + (5.0f * DeltaTime));
    }

    // RegeneraÃ§Ã£o de mana
    if (CurrentAttributes.CurrentMana < CurrentAttributes.MaxMana)
    {
        CurrentAttributes.CurrentMana = FMath::Min(CurrentAttributes.MaxMana, CurrentAttributes.CurrentMana + (10.0f * DeltaTime));
    }
}

void UAuracronChampionsBridge::ProcessCooldowns(float DeltaTime)
{
    // Processar cooldowns das habilidades
    for (auto& CooldownPair : CurrentAbilities.AbilityCooldowns)
    {
        if (CooldownPair.Value > 0.0f)
        {
            CooldownPair.Value = FMath::Max(0.0f, CooldownPair.Value - DeltaTime);
        }
    }
}

FAuracronChampionConfiguration UAuracronChampionsBridge::GetChampionConfiguration(const FString& ChampionID) const
{
    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == ChampionID)
        {
            return Entry.Configuration;
        }
    }
    return FAuracronChampionConfiguration();
}

void UAuracronChampionsBridge::SetChampionConfiguration(const FString& ChampionID, const FAuracronChampionConfiguration& Configuration)
{
    for (auto& Entry : ChampionConfigurations)
    {
        if (Entry.ChampionID == ChampionID)
        {
            Entry.Configuration = Configuration;
            UE_LOG(LogTemp, Log, TEXT("AURACRON: ConfiguraÃ§Ã£o do campeÃ£o atualizada: %s"), *ChampionID);
            return;
        }
    }

    // Se nÃ£o encontrou, adiciona nova entrada
    FAuracronChampionConfigurationEntry NewEntry;
    NewEntry.ChampionID = ChampionID;
    NewEntry.Configuration = Configuration;
    ChampionConfigurations.Add(NewEntry);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Nova configuraÃ§Ã£o de campeÃ£o adicionada: %s"), *ChampionID);
}

bool UAuracronChampionsBridge::LoadDefaultChampionConfigurations()
{
    // Limpar configuraÃ§Ãµes existentes
    ChampionConfigurations.Empty();

    // Criar configuraÃ§Ãµes padrÃ£o para campeÃµes bÃ¡sicos

    // CampeÃ£o 1: Guerreiro
    {
        FAuracronChampionConfigurationEntry WarriorEntry;
        WarriorEntry.ChampionID = TEXT("Warrior");
        WarriorEntry.Configuration.ChampionName = FText::FromString(TEXT("Guerreiro"));
        WarriorEntry.Configuration.ChampionLevel = 1;
        WarriorEntry.Configuration.CurrentExperience = 0;
        WarriorEntry.Configuration.ExperienceToNextLevel = 280;
        WarriorEntry.Configuration.bUnlocked = true;
        WarriorEntry.Configuration.bAvailableForSelection = true;

        // Atributos base
        WarriorEntry.Configuration.BaseAttributes.MaxHealth = 120.0f;
        WarriorEntry.Configuration.BaseAttributes.CurrentHealth = 120.0f;
        WarriorEntry.Configuration.BaseAttributes.MaxMana = 80.0f;
        WarriorEntry.Configuration.BaseAttributes.CurrentMana = 80.0f;
        WarriorEntry.Configuration.BaseAttributes.AttackDamage = 15.0f;
        WarriorEntry.Configuration.BaseAttributes.AbilityPower = 5.0f;
        WarriorEntry.Configuration.BaseAttributes.Armor = 8.0f;
        WarriorEntry.Configuration.BaseAttributes.MagicResistance = 3.0f;

        // ConfiguraÃ§Ã£o visual
        WarriorEntry.Configuration.VisualConfig.bUseMetaHuman = false;
        WarriorEntry.Configuration.VisualConfig.bAllowFacialCustomization = false;
        WarriorEntry.Configuration.VisualConfig.bSupportsAlternativeSkins = true;

        ChampionConfigurations.Add(WarriorEntry);
    }

    // CampeÃ£o 2: Mago
    {
        FAuracronChampionConfigurationEntry MageEntry;
        MageEntry.ChampionID = TEXT("Mage");
        MageEntry.Configuration.ChampionName = FText::FromString(TEXT("Mago"));
        MageEntry.Configuration.ChampionLevel = 1;
        MageEntry.Configuration.CurrentExperience = 0;
        MageEntry.Configuration.ExperienceToNextLevel = 280;
        MageEntry.Configuration.bUnlocked = true;
        MageEntry.Configuration.bAvailableForSelection = true;

        // Atributos base
        MageEntry.Configuration.BaseAttributes.MaxHealth = 80.0f;
        MageEntry.Configuration.BaseAttributes.CurrentHealth = 80.0f;
        MageEntry.Configuration.BaseAttributes.MaxMana = 150.0f;
        MageEntry.Configuration.BaseAttributes.CurrentMana = 150.0f;
        MageEntry.Configuration.BaseAttributes.AttackDamage = 8.0f;
        MageEntry.Configuration.BaseAttributes.AbilityPower = 18.0f;
        MageEntry.Configuration.BaseAttributes.Armor = 3.0f;
        MageEntry.Configuration.BaseAttributes.MagicResistance = 8.0f;

        // ConfiguraÃ§Ã£o visual
        MageEntry.Configuration.VisualConfig.bUseMetaHuman = false;
        MageEntry.Configuration.VisualConfig.bAllowFacialCustomization = false;
        MageEntry.Configuration.VisualConfig.bSupportsAlternativeSkins = true;

        ChampionConfigurations.Add(MageEntry);
    }

    // CampeÃ£o 3: Arqueiro
    {
        FAuracronChampionConfigurationEntry ArcherEntry;
        ArcherEntry.ChampionID = TEXT("Archer");
        ArcherEntry.Configuration.ChampionName = FText::FromString(TEXT("Arqueiro"));
        ArcherEntry.Configuration.ChampionLevel = 1;
        ArcherEntry.Configuration.CurrentExperience = 0;
        ArcherEntry.Configuration.ExperienceToNextLevel = 280;
        ArcherEntry.Configuration.bUnlocked = true;
        ArcherEntry.Configuration.bAvailableForSelection = true;

        // Atributos base
        ArcherEntry.Configuration.BaseAttributes.MaxHealth = 100.0f;
        ArcherEntry.Configuration.BaseAttributes.CurrentHealth = 100.0f;
        ArcherEntry.Configuration.BaseAttributes.MaxMana = 100.0f;
        ArcherEntry.Configuration.BaseAttributes.CurrentMana = 100.0f;
        ArcherEntry.Configuration.BaseAttributes.AttackDamage = 12.0f;
        ArcherEntry.Configuration.BaseAttributes.AbilityPower = 8.0f;
        ArcherEntry.Configuration.BaseAttributes.Armor = 5.0f;
        ArcherEntry.Configuration.BaseAttributes.MagicResistance = 5.0f;

        // ConfiguraÃ§Ã£o visual
        ArcherEntry.Configuration.VisualConfig.bUseMetaHuman = false;
        ArcherEntry.Configuration.VisualConfig.bAllowFacialCustomization = false;
        ArcherEntry.Configuration.VisualConfig.bSupportsAlternativeSkins = true;

        ChampionConfigurations.Add(ArcherEntry);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d configuraÃ§Ãµes padrÃ£o de campeÃµes carregadas"), ChampionConfigurations.Num());
    return true;
}

TArray<FString> UAuracronChampionsBridge::GetAvailableChampions() const
{
    TArray<FString> AvailableChampions;

    for (const auto& Entry : ChampionConfigurations)
    {
        if (Entry.Configuration.bAvailableForSelection && Entry.Configuration.bUnlocked)
        {
            AvailableChampions.Add(Entry.ChampionID);
        }
    }

    return AvailableChampions;
}

void UAuracronChampionsBridge::OnRep_SelectedChampionID()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: ReplicaÃ§Ã£o - CampeÃ£o selecionado: %s"), *SelectedChampionID);
    OnChampionSelected.Broadcast(SelectedChampionID);
}

void UAuracronChampionsBridge::OnRep_ChampionState()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: ReplicaÃ§Ã£o - Estado do campeÃ£o alterado"));
    OnChampionStateChanged.Broadcast(CurrentChampionState);
}

void UAuracronChampionsBridge::OnRep_CurrentAttributes()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: ReplicaÃ§Ã£o - Atributos do campeÃ£o alterados"));
    OnAttributesChanged.Broadcast(CurrentAttributes);
}

#include "Modules/ModuleManager.h"

IMPLEMENT_MODULE(FDefaultModuleImpl, AuracronChampionsBridge);
