/**
 * AuracronGamePhaseSystem.cpp
 * 
 * Implementação completa do sistema de fases da partida usando UE 5.6 APIs modernas.
 * Gerencia a evolução temporal do jogo através das 4 fases específicas.
 */

#include "AuracronGamePhaseSystem.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronHardwareDetectionSystem.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/GameStateBase.h"
#include "Subsystems/SubsystemBlueprintLibrary.h"
#include "Engine/Engine.h"

// USubsystem interface implementation
void UAuracronGamePhaseSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Game Phase System"));
    
    // Initialize default configurations
    InitializeDefaultConfigurations();
    
    // Cache subsystem references
    if (UWorld* World = GetWorld())
    {
        CachedRealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>();

        // Hardware detection system is a game instance subsystem
        if (UGameInstance* GameInstance = World->GetGameInstance())
        {
            CachedHardwareSystem = GameInstance->GetSubsystem<UAuracronHardwareDetectionSystem>();
        }
    }
    
    // Initialize system state
    CurrentPhase = EGamePhase::None;
    CurrentPhaseStartTime = 0.0f;
    TotalMatchTime = 0.0f;
    bSystemInitialized = true;
    bMatchActive = false;
    TotalPhaseTransitions = 0;
    AverageTransitionTime = 0.0f;
    LastUpdateTime = 0.0f;
    
    // Set default transition duration
    DefaultTransitionDuration = 5.0f;
    bAutoAdaptToHardware = true;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Game Phase System initialized successfully"));
}

void UAuracronGamePhaseSystem::Deinitialize()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deinitializing Game Phase System"));
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.ClearTimer(PhaseUpdateTimer);
        TimerManager.ClearTimer(TransitionTimer);
        TimerManager.ClearTimer(MetricsUpdateTimer);
    }
    
    // Cleanup transition effects
    CleanupTransitionEffects();
    
    // Reset state
    bSystemInitialized = false;
    bMatchActive = false;
    
    Super::Deinitialize();
}

bool UAuracronGamePhaseSystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Only create in game worlds, not in editor or other contexts
    if (UWorld* World = Cast<UWorld>(Outer))
    {
        return World->IsGameWorld();
    }
    return false;
}

void UAuracronGamePhaseSystem::Tick(float DeltaTime)
{
    if (!bSystemInitialized || !bMatchActive)
    {
        return;
    }
    
    // Update total match time
    TotalMatchTime += DeltaTime;
    
    // Update phase system
    UpdatePhaseSystem(DeltaTime);
    
    // Update transition if active
    if (CurrentTransition.TransitionState != EPhaseTransitionState::Stable)
    {
        ProcessPhaseTransition(DeltaTime);
    }
    
    // Update phase intensity
    UpdatePhaseIntensity(DeltaTime);
    
    // Check for phase transition conditions
    CheckPhaseTransitionConditions();
    
    LastUpdateTime = TotalMatchTime;
}

// === Core Phase Management ===

void UAuracronGamePhaseSystem::InitializePhaseSystem()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Phase System"));
    
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot initialize phase system - subsystem not initialized"));
        return;
    }
    
    // Apply hardware-based configurations if enabled
    if (bAutoAdaptToHardware)
    {
        ApplyHardwareBasedConfigurations();
    }
    
    // Initialize metrics for all phases
    PhaseMetrics.Empty();
    for (int32 i = 0; i < static_cast<int32>(EGamePhase::Resolucao) + 1; ++i)
    {
        EGamePhase Phase = static_cast<EGamePhase>(i);
        if (Phase != EGamePhase::None)
        {
            PhaseMetrics.Add(Phase, FPhaseMetrics());
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase System initialized"));
}

void UAuracronGamePhaseSystem::StartMatch()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting match"));
    
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot start match - system not initialized"));
        return;
    }
    
    // Reset match state
    TotalMatchTime = 0.0f;
    CurrentPhaseStartTime = 0.0f;
    bMatchActive = true;
    
    // Start with first phase
    StartPhaseTransition(EGamePhase::None, EGamePhase::Despertar);
    
    // Setup update timer
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.SetTimer(PhaseUpdateTimer, 
            FTimerDelegate::CreateUObject(this, &UAuracronGamePhaseSystem::Tick, 0.1f), 
            0.1f, true);
    }
    
    // Trigger match started event
    OnMatchStarted();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Match started successfully"));
}

void UAuracronGamePhaseSystem::EndMatch()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ending match"));
    
    // Stop match
    bMatchActive = false;
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.ClearTimer(PhaseUpdateTimer);
        TimerManager.ClearTimer(TransitionTimer);
        TimerManager.ClearTimer(MetricsUpdateTimer);
    }
    
    // Cleanup effects
    CleanupTransitionEffects();
    
    // Deactivate current phase
    if (CurrentPhase != EGamePhase::None)
    {
        DeactivatePhase(CurrentPhase);
    }
    
    // Trigger match ended event
    OnMatchEnded();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Match ended"));
}

void UAuracronGamePhaseSystem::ForceNextPhase()
{
    if (!bMatchActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot force next phase - match not active"));
        return;
    }
    
    EGamePhase NextPhase = GetNextPhase(CurrentPhase);
    if (NextPhase != CurrentPhase)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Forcing transition to next phase: %s"), 
            *UEnum::GetValueAsString(NextPhase));
        ForcePhaseTransition(NextPhase);
    }
}

void UAuracronGamePhaseSystem::ForcePhaseTransition(EGamePhase TargetPhase)
{
    if (!bMatchActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot force phase transition - match not active"));
        return;
    }
    
    if (TargetPhase == CurrentPhase)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Target phase is same as current phase"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Forcing phase transition from %s to %s"), 
        *UEnum::GetValueAsString(CurrentPhase), *UEnum::GetValueAsString(TargetPhase));
    
    StartPhaseTransition(CurrentPhase, TargetPhase);
}

// === Phase Information ===

EGamePhase UAuracronGamePhaseSystem::GetCurrentPhase() const
{
    return CurrentPhase;
}

float UAuracronGamePhaseSystem::GetCurrentPhaseProgress() const
{
    return CalculatePhaseProgress();
}

float UAuracronGamePhaseSystem::GetTimeRemainingInPhase() const
{
    if (!bMatchActive || CurrentPhase == EGamePhase::None)
    {
        return 0.0f;
    }
    
    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    if (!Config)
    {
        return 0.0f;
    }
    
    float TimeInPhase = TotalMatchTime - CurrentPhaseStartTime;
    float RemainingTime = Config->MaxDurationSeconds - TimeInPhase;
    
    return FMath::Max(RemainingTime, 0.0f);
}

FGamePhaseConfig UAuracronGamePhaseSystem::GetCurrentPhaseConfig() const
{
    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    return Config ? *Config : FGamePhaseConfig();
}

bool UAuracronGamePhaseSystem::IsInTransition() const
{
    return CurrentTransition.TransitionState != EPhaseTransitionState::Stable;
}

FPhaseTransitionData UAuracronGamePhaseSystem::GetCurrentTransitionData() const
{
    return CurrentTransition;
}

// === Phase Metrics ===

FPhaseMetrics UAuracronGamePhaseSystem::GetCurrentPhaseMetrics() const
{
    const FPhaseMetrics* Metrics = PhaseMetrics.Find(CurrentPhase);
    return Metrics ? *Metrics : FPhaseMetrics();
}

TMap<EGamePhase, FPhaseMetrics> UAuracronGamePhaseSystem::GetAllPhaseMetrics() const
{
    return PhaseMetrics;
}

void UAuracronGamePhaseSystem::UpdatePhaseMetrics(int32 Kills, float Gold, int32 Objectives)
{
    if (CurrentPhase == EGamePhase::None)
    {
        return;
    }
    
    FPhaseMetrics* Metrics = PhaseMetrics.Find(CurrentPhase);
    if (Metrics)
    {
        Metrics->KillsInPhase += Kills;
        Metrics->GoldEarnedInPhase += Gold;
        Metrics->ObjectivesCompletedInPhase += Objectives;
        Metrics->TimeInPhase = TotalMatchTime - CurrentPhaseStartTime;
    }
}

// === Configuration ===

void UAuracronGamePhaseSystem::ConfigurePhase(EGamePhase Phase, const FGamePhaseConfig& Config)
{
    PhaseConfigurations.Add(Phase, Config);
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configured phase %s"), *UEnum::GetValueAsString(Phase));
}

FGamePhaseConfig UAuracronGamePhaseSystem::GetPhaseConfiguration(EGamePhase Phase) const
{
    const FGamePhaseConfig* Config = PhaseConfigurations.Find(Phase);
    return Config ? *Config : FGamePhaseConfig();
}

void UAuracronGamePhaseSystem::ApplyHardwareBasedConfigurations()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying hardware-based configurations"));
    
    if (!CachedHardwareSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Hardware detection system not available"));
        return;
    }
    
    // Apply hardware adaptations to all phase configurations
    for (auto& ConfigPair : PhaseConfigurations)
    {
        AdaptConfigurationToHardware(ConfigPair.Value);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Hardware-based configurations applied"));
}

// === Private Implementation Methods ===

void UAuracronGamePhaseSystem::InitializeDefaultConfigurations()
{
    // Initialize default phase configurations using UE 5.6 best practices
    PhaseConfigurations.Empty();

    // DESPERTAR Phase (0-15 min)
    FGamePhaseConfig DespertarConfig;
    DespertarConfig.Phase = EGamePhase::Despertar;
    DespertarConfig.MinDurationSeconds = 900.0f; // 15 minutes
    DespertarConfig.MaxDurationSeconds = 900.0f;
    DespertarConfig.ActiveLayers.Add(ERealmLayerType::PlanicieRadiante);
    DespertarConfig.EffectIntensity = 0.3f;
    DespertarConfig.RailSpeedMultiplier = 0.8f;
    DespertarConfig.PrismalFlowIntensity = 0.5f;
    DespertarConfig.ObjectiveSpawnFrequency = 0.7f;
    DespertarConfig.ExperienceMultiplier = 1.0f;
    DespertarConfig.GoldMultiplier = 1.0f;
    DespertarConfig.bAdaptToHardware = true;
    PhaseConfigurations.Add(EGamePhase::Despertar, DespertarConfig);

    // CONVERGÊNCIA Phase (15-25 min)
    FGamePhaseConfig ConvergenciaConfig;
    ConvergenciaConfig.Phase = EGamePhase::Convergencia;
    ConvergenciaConfig.MinDurationSeconds = 600.0f; // 10 minutes
    ConvergenciaConfig.MaxDurationSeconds = 600.0f;
    ConvergenciaConfig.ActiveLayers.Add(ERealmLayerType::PlanicieRadiante);
    ConvergenciaConfig.ActiveLayers.Add(ERealmLayerType::FirmamentoZephyr);
    ConvergenciaConfig.EffectIntensity = 0.6f;
    ConvergenciaConfig.RailSpeedMultiplier = 1.0f;
    ConvergenciaConfig.PrismalFlowIntensity = 0.7f;
    ConvergenciaConfig.ObjectiveSpawnFrequency = 1.0f;
    ConvergenciaConfig.ExperienceMultiplier = 1.2f;
    ConvergenciaConfig.GoldMultiplier = 1.1f;
    ConvergenciaConfig.bAdaptToHardware = true;
    PhaseConfigurations.Add(EGamePhase::Convergencia, ConvergenciaConfig);

    // INTENSIFICAÇÃO Phase (25-35 min)
    FGamePhaseConfig IntensificacaoConfig;
    IntensificacaoConfig.Phase = EGamePhase::Intensificacao;
    IntensificacaoConfig.MinDurationSeconds = 600.0f; // 10 minutes
    IntensificacaoConfig.MaxDurationSeconds = 600.0f;
    IntensificacaoConfig.ActiveLayers.Add(ERealmLayerType::PlanicieRadiante);
    IntensificacaoConfig.ActiveLayers.Add(ERealmLayerType::FirmamentoZephyr);
    IntensificacaoConfig.ActiveLayers.Add(ERealmLayerType::AbismoUmbrio);
    IntensificacaoConfig.EffectIntensity = 0.8f;
    IntensificacaoConfig.RailSpeedMultiplier = 1.2f;
    IntensificacaoConfig.PrismalFlowIntensity = 0.9f;
    IntensificacaoConfig.ObjectiveSpawnFrequency = 1.3f;
    IntensificacaoConfig.ExperienceMultiplier = 1.5f;
    IntensificacaoConfig.GoldMultiplier = 1.3f;
    IntensificacaoConfig.bAdaptToHardware = true;
    PhaseConfigurations.Add(EGamePhase::Intensificacao, IntensificacaoConfig);

    // RESOLUÇÃO Phase (35+ min)
    FGamePhaseConfig ResolucaoConfig;
    ResolucaoConfig.Phase = EGamePhase::Resolucao;
    ResolucaoConfig.MinDurationSeconds = 1800.0f; // 30 minutes (can go longer)
    ResolucaoConfig.MaxDurationSeconds = 3600.0f; // 60 minutes max
    ResolucaoConfig.ActiveLayers.Add(ERealmLayerType::PlanicieRadiante);
    ResolucaoConfig.ActiveLayers.Add(ERealmLayerType::FirmamentoZephyr);
    ResolucaoConfig.ActiveLayers.Add(ERealmLayerType::AbismoUmbrio);
    ResolucaoConfig.EffectIntensity = 1.0f;
    ResolucaoConfig.RailSpeedMultiplier = 1.5f;
    ResolucaoConfig.PrismalFlowIntensity = 1.0f;
    ResolucaoConfig.ObjectiveSpawnFrequency = 1.5f;
    ResolucaoConfig.ExperienceMultiplier = 2.0f;
    ResolucaoConfig.GoldMultiplier = 1.5f;
    ResolucaoConfig.bAdaptToHardware = true;
    PhaseConfigurations.Add(EGamePhase::Resolucao, ResolucaoConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Default phase configurations initialized"));
}

void UAuracronGamePhaseSystem::UpdatePhaseSystem(float DeltaTime)
{
    if (!bMatchActive)
    {
        return;
    }

    // Update current phase metrics
    if (CurrentPhase != EGamePhase::None)
    {
        FPhaseMetrics* Metrics = PhaseMetrics.Find(CurrentPhase);
        if (Metrics)
        {
            Metrics->TimeInPhase = TotalMatchTime - CurrentPhaseStartTime;
        }
    }

    // Update realm layers based on current phase
    UpdateRealmLayers();

    // Update gameplay multipliers
    UpdateGameplayMultipliers();
}

void UAuracronGamePhaseSystem::CheckPhaseTransitionConditions()
{
    if (!bMatchActive || CurrentPhase == EGamePhase::None || IsInTransition())
    {
        return;
    }

    // Check if current phase should transition
    if (ShouldTransitionToNextPhase())
    {
        EGamePhase NextPhase = GetNextPhase(CurrentPhase);
        if (NextPhase != CurrentPhase)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase transition conditions met - transitioning to %s"),
                *UEnum::GetValueAsString(NextPhase));
            StartPhaseTransition(CurrentPhase, NextPhase);
        }
    }
}

void UAuracronGamePhaseSystem::ProcessPhaseTransition(float DeltaTime)
{
    if (CurrentTransition.TransitionState == EPhaseTransitionState::Stable)
    {
        return;
    }

    // Update transition progress
    float ElapsedTime = TotalMatchTime - CurrentTransition.StartTime;
    CurrentTransition.TransitionProgress = FMath::Clamp(ElapsedTime / CurrentTransition.Duration, 0.0f, 1.0f);

    // Update transition effects
    UpdateTransitionEffects(DeltaTime);

    // Check if transition is complete
    if (CurrentTransition.TransitionProgress >= 1.0f)
    {
        CompletePhaseTransition();
    }
}

void UAuracronGamePhaseSystem::CompletePhaseTransition()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Completing phase transition from %s to %s"),
        *UEnum::GetValueAsString(CurrentTransition.FromPhase),
        *UEnum::GetValueAsString(CurrentTransition.ToPhase));

    // Deactivate old phase
    if (CurrentTransition.FromPhase != EGamePhase::None)
    {
        DeactivatePhase(CurrentTransition.FromPhase);
    }

    // Activate new phase
    ActivatePhase(CurrentTransition.ToPhase);

    // Update current phase
    EGamePhase OldPhase = CurrentPhase;
    CurrentPhase = CurrentTransition.ToPhase;
    CurrentPhaseStartTime = TotalMatchTime;

    // Cleanup transition
    CleanupTransitionEffects();
    CurrentTransition.TransitionState = EPhaseTransitionState::Stable;
    CurrentTransition.TransitionProgress = 0.0f;

    // Update transition statistics
    TotalPhaseTransitions++;
    float TransitionTime = TotalMatchTime - CurrentTransition.StartTime;
    AverageTransitionTime = ((AverageTransitionTime * (TotalPhaseTransitions - 1)) + TransitionTime) / TotalPhaseTransitions;

    // Trigger events
    OnPhaseTransitionCompleted(CurrentTransition.FromPhase, CurrentTransition.ToPhase);
    OnPhaseChanged(OldPhase, CurrentPhase);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase transition completed successfully"));
}

// === Phase Implementation ===

void UAuracronGamePhaseSystem::ActivatePhase(EGamePhase Phase)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Activating phase %s"), *UEnum::GetValueAsString(Phase));

    const FGamePhaseConfig* Config = PhaseConfigurations.Find(Phase);
    if (!Config)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No configuration found for phase %s"), *UEnum::GetValueAsString(Phase));
        return;
    }

    // Apply phase effects at full intensity
    ApplyPhaseEffects(Phase, Config->EffectIntensity);

    // Update realm layers
    if (CachedRealmSubsystem)
    {
        for (ERealmLayerType LayerType : Config->ActiveLayers)
        {
            // Activate realm layer (implementation would depend on realm subsystem)
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Activating realm layer %s"), *UEnum::GetValueAsString(LayerType));
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase %s activated"), *UEnum::GetValueAsString(Phase));
}

void UAuracronGamePhaseSystem::DeactivatePhase(EGamePhase Phase)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deactivating phase %s"), *UEnum::GetValueAsString(Phase));

    // Gradually reduce phase effects
    ApplyPhaseEffects(Phase, 0.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase %s deactivated"), *UEnum::GetValueAsString(Phase));
}

void UAuracronGamePhaseSystem::ApplyPhaseEffects(EGamePhase Phase, float Intensity)
{
    const FGamePhaseConfig* Config = PhaseConfigurations.Find(Phase);
    if (!Config)
    {
        return;
    }

    // Apply visual effects if available
    if (Config->PhaseVFX && Intensity > 0.0f)
    {
        // Spawn or update Niagara effects
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applying phase VFX with intensity %.2f"), Intensity);
    }

    // Apply audio effects if available
    if (Config->PhaseMusic && Intensity > 0.0f)
    {
        // Play or update phase music
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applying phase music with intensity %.2f"), Intensity);
    }

    // Apply gameplay effects
    float AdjustedIntensity = Intensity * Config->EffectIntensity;
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Applied phase effects for %s with intensity %.2f"),
        *UEnum::GetValueAsString(Phase), AdjustedIntensity);
}

void UAuracronGamePhaseSystem::UpdatePhaseIntensity(float DeltaTime)
{
    if (CurrentPhase == EGamePhase::None)
    {
        return;
    }

    // Calculate dynamic intensity based on phase progress
    float PhaseProgress = CalculatePhaseProgress();
    float DynamicIntensity = FMath::Lerp(0.5f, 1.0f, PhaseProgress);

    // Apply dynamic effects
    ApplyPhaseEffects(CurrentPhase, DynamicIntensity);
}

// === Transition Implementation ===

void UAuracronGamePhaseSystem::StartPhaseTransition(EGamePhase FromPhase, EGamePhase ToPhase)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting phase transition from %s to %s"),
        *UEnum::GetValueAsString(FromPhase), *UEnum::GetValueAsString(ToPhase));

    // Setup transition data
    CurrentTransition.FromPhase = FromPhase;
    CurrentTransition.ToPhase = ToPhase;
    CurrentTransition.TransitionState = EPhaseTransitionState::Preparing;
    CurrentTransition.TransitionProgress = 0.0f;
    CurrentTransition.StartTime = TotalMatchTime;
    CurrentTransition.Duration = DefaultTransitionDuration;

    // Cleanup any existing transition effects
    CleanupTransitionEffects();

    // Start transition effects
    CurrentTransition.TransitionState = EPhaseTransitionState::Transitioning;

    // Trigger transition started event
    OnPhaseTransitionStarted(FromPhase, ToPhase);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Phase transition started"));
}

void UAuracronGamePhaseSystem::UpdateTransitionEffects(float DeltaTime)
{
    if (CurrentTransition.TransitionState != EPhaseTransitionState::Transitioning)
    {
        return;
    }

    // Update transition visual effects based on progress
    float Progress = CurrentTransition.TransitionProgress;

    // Fade out old phase effects
    if (CurrentTransition.FromPhase != EGamePhase::None)
    {
        float FadeOutIntensity = 1.0f - Progress;
        ApplyPhaseEffects(CurrentTransition.FromPhase, FadeOutIntensity);
    }

    // Fade in new phase effects
    if (CurrentTransition.ToPhase != EGamePhase::None)
    {
        float FadeInIntensity = Progress;
        ApplyPhaseEffects(CurrentTransition.ToPhase, FadeInIntensity);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updated transition effects - Progress: %.2f"), Progress);
}

void UAuracronGamePhaseSystem::CleanupTransitionEffects()
{
    // Cleanup active Niagara effects
    for (TObjectPtr<UNiagaraComponent> Effect : CurrentTransition.ActiveEffects)
    {
        if (Effect && IsValid(Effect))
        {
            Effect->DestroyComponent();
        }
    }
    CurrentTransition.ActiveEffects.Empty();

    // Cleanup active audio
    if (CurrentTransition.ActiveAudio && IsValid(CurrentTransition.ActiveAudio))
    {
        CurrentTransition.ActiveAudio->Stop();
        CurrentTransition.ActiveAudio = nullptr;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Transition effects cleaned up"));
}

// === Hardware Adaptation ===

void UAuracronGamePhaseSystem::AdaptConfigurationToHardware(FGamePhaseConfig& Config)
{
    if (!CachedHardwareSystem || !Config.bAdaptToHardware)
    {
        return;
    }

    // Get hardware tier (implementation would depend on hardware detection system)
    int32 HardwareTier = 2; // Default to medium tier

    // Apply hardware-specific multipliers
    float HardwareMultiplier = GetHardwareMultiplier(HardwareTier);

    // Adjust configuration based on hardware
    Config.EffectIntensity *= HardwareMultiplier;
    Config.PrismalFlowIntensity *= HardwareMultiplier;

    // Apply tier-specific multipliers if available
    if (const float* TierMultiplier = Config.HardwareTierMultipliers.Find(HardwareTier))
    {
        Config.EffectIntensity *= *TierMultiplier;
        Config.PrismalFlowIntensity *= *TierMultiplier;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adapted configuration for hardware tier %d with multiplier %.2f"),
        HardwareTier, HardwareMultiplier);
}

float UAuracronGamePhaseSystem::GetHardwareMultiplier(int32 HardwareTier)
{
    // Return multiplier based on hardware tier
    switch (HardwareTier)
    {
        case 0: return 0.5f;  // Low-end hardware
        case 1: return 0.75f; // Medium-low hardware
        case 2: return 1.0f;  // Medium hardware
        case 3: return 1.25f; // Medium-high hardware
        case 4: return 1.5f;  // High-end hardware
        default: return 1.0f;
    }
}

// === Utility Methods ===

bool UAuracronGamePhaseSystem::ShouldTransitionToNextPhase() const
{
    if (CurrentPhase == EGamePhase::None || !bMatchActive)
    {
        return false;
    }

    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    if (!Config)
    {
        return false;
    }

    // Check if minimum duration has passed
    float TimeInPhase = TotalMatchTime - CurrentPhaseStartTime;
    if (TimeInPhase < Config->MinDurationSeconds)
    {
        return false;
    }

    // Check if maximum duration has been reached
    if (TimeInPhase >= Config->MaxDurationSeconds)
    {
        return true;
    }

    // Additional conditions could be added here (e.g., objectives completed, player actions, etc.)

    return false;
}

EGamePhase UAuracronGamePhaseSystem::GetNextPhase(EGamePhase Phase) const
{
    switch (Phase)
    {
        case EGamePhase::None:
            return EGamePhase::Despertar;
        case EGamePhase::Despertar:
            return EGamePhase::Convergencia;
        case EGamePhase::Convergencia:
            return EGamePhase::Intensificacao;
        case EGamePhase::Intensificacao:
            return EGamePhase::Resolucao;
        case EGamePhase::Resolucao:
            return EGamePhase::Resolucao; // Stay in final phase
        default:
            return EGamePhase::None;
    }
}

float UAuracronGamePhaseSystem::CalculatePhaseProgress() const
{
    if (CurrentPhase == EGamePhase::None || !bMatchActive)
    {
        return 0.0f;
    }

    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    if (!Config)
    {
        return 0.0f;
    }

    float TimeInPhase = TotalMatchTime - CurrentPhaseStartTime;
    float Progress = TimeInPhase / Config->MaxDurationSeconds;

    return FMath::Clamp(Progress, 0.0f, 1.0f);
}

void UAuracronGamePhaseSystem::UpdateRealmLayers()
{
    if (!CachedRealmSubsystem || CurrentPhase == EGamePhase::None)
    {
        return;
    }

    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    if (!Config)
    {
        return;
    }

    // Update realm layers based on current phase configuration
    // This would interact with the realm subsystem to activate/deactivate layers
    for (ERealmLayerType LayerType : Config->ActiveLayers)
    {
        // Implementation would depend on realm subsystem API
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating realm layer %s"), *UEnum::GetValueAsString(LayerType));
    }
}

void UAuracronGamePhaseSystem::UpdateGameplayMultipliers()
{
    if (CurrentPhase == EGamePhase::None)
    {
        return;
    }

    const FGamePhaseConfig* Config = PhaseConfigurations.Find(CurrentPhase);
    if (!Config)
    {
        return;
    }

    // Apply gameplay multipliers to game systems
    // This would typically interact with other subsystems to apply:
    // - Experience multipliers
    // - Gold multipliers
    // - Rail speed multipliers
    // - Objective spawn frequency

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updated gameplay multipliers for phase %s - XP: %.2f, Gold: %.2f, Rail Speed: %.2f"),
        *UEnum::GetValueAsString(CurrentPhase),
        Config->ExperienceMultiplier,
        Config->GoldMultiplier,
        Config->RailSpeedMultiplier);
}
